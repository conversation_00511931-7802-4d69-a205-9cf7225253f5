<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>kuaishou</groupId>
        <artifactId>kwaishop-qa-risk-center-parent</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>kwaishop-qa-risk-center</artifactId>
    <dependencies>
        <!-- 内部依赖 -->

        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-qa-risk-center-client</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-qa-risk-center-common</artifactId>
        </dependency>

        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-mq-api</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-mq-provider</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-mq-rocketmq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.kuaishou.infra.boot</groupId>
            <artifactId>ks-boot-starter-runner-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-intown-sdk</artifactId>
            <version>1.0.54</version>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.jeval</groupId>
            <artifactId>jeval</artifactId>
            <version>0.9.4</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-kafka</artifactId>
        </dependency>
        <!-- 电商依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-kconf-client</artifactId>
            <version>1.0.122</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-datasource</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-mmu-serving-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.7</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-core</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-kconf-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-intown-sdk</artifactId>
            <version>1.0.54</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kat-framework</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.squareup.okhttp</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>javax.annotation</groupId>
                    <artifactId>jsr250-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>hutool-all</artifactId>
                    <groupId>cn.hutool</groupId>
                </exclusion>
                <exclusion>
                    <groupId>io.grpc</groupId>
                    <artifactId>grpc-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-cache-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-cache-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-cache-redis-impl</artifactId>
            <version>1.0.476</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.python</groupId>
            <artifactId>jython-standalone</artifactId>
            <version>2.7.0</version>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>6.0.13.Final</version>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.infra.boot</groupId>
            <artifactId>ks-boot-jdbc</artifactId>
        </dependency>


        <!--es读取-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-kdump-caelus-center-client</artifactId>
            <version>1.0.14</version>
        </dependency>

        <!--open api-->
        <dependency>
            <groupId>com.kuaishou</groupId>
            <artifactId>openapi-retrofit2-sdk</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>

        <!--数据鉴权-->
        <dependency>
            <groupId>com.kuaishou.dataarch</groupId>
            <artifactId>themis-job-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-dp-auth-dsc-client</artifactId>
        </dependency>
        <!--data-management-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-sellerdata-management-service-client</artifactId>
            <version>1.0.58</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-apollo-pinocchio-center-client</artifactId>
        </dependency>

        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>krpc-common</artifactId>
            <version>1.0.202</version>
        </dependency>
        <!-- 素材中心 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-material-center-client</artifactId>
            <version>1.0.38</version>
        </dependency>
        <!-- 三方依赖 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.kwaishop</groupId>
            <artifactId>kwaishop-merchant-utils-adapter-utils</artifactId>
            <version>1.0.33</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-apollo-strategy-center-client</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-merchant-staff-sdk</artifactId>
            <version>1.0.22</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>passport-user-service-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.3</version>
        </dependency>

        <dependency>
            <groupId>com.offbytwo.jenkins</groupId>
            <artifactId>jenkins-client</artifactId>
            <version>0.3.8</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.13</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-merchant-user-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.security.codesec</groupId>
            <artifactId>server-security-protect</artifactId>
        </dependency>
        <!--店铺中心sdk-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-shop-center-rich-client</artifactId>
            <version>1.0.40</version>
        </dependency>
        <!--klink-sdk-->
        <dependency>
            <groupId>com.kwai.link</groupId>
            <artifactId>klink-pressure-test-sdk</artifactId>
            <version>1.0.49</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-access-control-rich-client</artifactId>
            <version>1.0.68</version>
            <exclusions>
                <exclusion>
                    <artifactId>kuaishop-access-control-sdk</artifactId>
                    <groupId>kuaishou</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--客服-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-merchant-customer-service-sdk</artifactId>
            <version>1.0.168</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-cs-session-center-client</artifactId>
            <version>1.0.42</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-cs-core-center-client</artifactId>
            <version>1.0.7</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-cs-biz-sdk</artifactId>
            <version>1.0.89</version>
        </dependency>
        <!--客服-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-im-cloud-sdk</artifactId>
            <version>1.0.486</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-access-control-rich-client</artifactId>
            <version>1.0.68</version>
            <exclusions>
                <exclusion>
                    <artifactId>kuaishop-access-control-sdk</artifactId>
                    <groupId>kuaishou</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishop-access-control-sdk</artifactId>
            <version>1.0.74</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
        </dependency>
        <!--KCDN-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-cdn-public-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <!--店铺-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-shop-page-service-client</artifactId>
            <version>1.1.7</version>
        </dependency>
        <dependency>
            <groupId>ru.yandex.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-dp-auth-dsc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.dp</groupId>
            <artifactId>one-service-rpc-client</artifactId>
        </dependency>
        <!--kwaiFlow事件-->
        <dependency>
            <groupId>com.kuaishou.dp</groupId>
            <artifactId>kuaishou-scheduler-sdk-proto</artifactId>
            <version>2.4.16-kwaiflow-lite</version>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.dp</groupId>
            <artifactId>kuaishou-workflow-proxy-sdk</artifactId>
            <version>1.2.38</version>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.ee</groupId>
            <artifactId>is-bpm-kafka-sdk</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-biz-account-center-client</artifactId>
            <version>1.0.15</version>
        </dependency>
        <!--灵筑spi-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-lingzhu-platform-spi</artifactId>
            <version>1.0.16</version>
        </dependency>

        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-lingzhu-platform-client</artifactId>
            <version>1.0.15</version>
        </dependency>

        <!--天河rpc-->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-tianhe-galax-center-client</artifactId>
            <version>1.0.21</version>
        </dependency>

        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-lang-bridge-sdk</artifactId>
            <version>1.0.17</version>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>9</source>
                    <target>9</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>