package com.kuaishou.kwaishop.qa.risk.center.tool;

import java.io.IOException;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.kuaishou.kwaishop.qa.risk.center.service.impl.ShopUiRerunService;
import com.kuaishou.kwaishop.qa.risk.center.utils.kim.KimMsg;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/7/17 19:45
 * @注释
 */
@SpringBootTest
public class KimTest {

    @Resource
    ShopUiRerunService shopUiRerunService;
    @Test
    public void sendUserMarkDownMessage() throws IOException {
        KimMsg kimMsg = new KimMsg();

        kimMsg.sendUserTextMessage("pengshengpu","test");

    }

    @Test
    public void rerun() {

        shopUiRerunService.rerun();

    }
}
