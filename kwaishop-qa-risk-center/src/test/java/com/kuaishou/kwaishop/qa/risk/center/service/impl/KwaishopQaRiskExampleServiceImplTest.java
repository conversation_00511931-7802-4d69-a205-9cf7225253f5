package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.krpc.config.annotation.KrpcReference;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.FundsRiskFeatureDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundsRiskFeatureStatesUpdateCondition;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.ExampleMethodRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.ExampleMethodResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.KrpcKwaishopQaRiskExampleServiceGrpc.IKwaishopQaRiskExampleService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.RiskExampleObject;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> <<EMAIL>>
 */
@Slf4j
//@SpringBootTest(classes = {KsloanCreditServiceApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.NONE)
//@RunWith(SpringRunner.class)
public class KwaishopQaRiskExampleServiceImplTest {

    /**
     * 调用示例
     */
    @KrpcReference(serviceName = "kwaishop-qa-risk-center")
    private IKwaishopQaRiskExampleService kwaishopQaRiskExampleService;


    @Test
    public void testExampleMethod() {
        RiskExampleObject obj = RiskExampleObject.newBuilder().setBody("OK").build();
        ExampleMethodRequest request = ExampleMethodRequest.newBuilder().setExampleObject(obj).build();
        ExampleMethodResponse response = kwaishopQaRiskExampleService.exampleMethod(request);
        log.info("testExampleMethod result:{}", response.getSuccess());
    }


    @Autowired
    private static FundsRiskFeatureDAO fundsRiskFeatureDAO;

    @Test
    public void test() {
//        FundsRiskFeatureDO fundsRiskFeatureDO = FundsRiskFeatureDO.builder().featureId("36694")
//                //.status(9)
//                //.updateTime(System.currentTimeMillis())
//                .build();
        //2、根据feature_id 改 funds_risk_feature_view 中的state
        // fundsRiskFeatureDAO.updateFundsRiskFeatureViewState(fundsRiskFeatureDO);
        //FundsRiskFeatureDO fundsRiskFeatureDO1 = fundsRiskFeatureDAO.queryByFeatureId(fundsRiskFeatureDO);
        //System.out.println("KwaishopQaRiskExampleServiceImplTest.test:" + fundsRiskFeatureDO1.toString());
        FundsRiskFeatureStatesUpdateCondition fundsRiskFeatureStatesUpdateCondition = FundsRiskFeatureStatesUpdateCondition.builder()
                .featureId("36694")
                .status(9)
                .updateTime(System.currentTimeMillis())
                .build();
        //2、根据feature_id 改 funds_risk_feature_view 中的state
        fundsRiskFeatureDAO.updateFundsRiskFeatureViewState(fundsRiskFeatureStatesUpdateCondition);

    }

}
