package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;

import com.google.common.primitives.Ints;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-30
 */
@Slf4j
public class DateUtils {
    public static final ThreadLocal<DateFormat> YYYY_MM_DD_FORMAT = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd");
        }
    };

    public static final ThreadLocal<DateFormat> YYYY_MM_DD_HH_MM_SS_FORMAT = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        }
    };

    private static final String DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS = "yyyy-MM-dd HH:mm:ss";
    /**
     * 格式yyyy-MM-dd
     */
    public static final DateTimeFormatter YYYY_MM_DD = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    /**
     * 格式yyyy/MM/dd
     */
    public static final DateTimeFormatter YYYY_MM_DD2 = DateTimeFormatter.ofPattern("yyyy/MM/dd");
    /**
     * 格式yyyyMMdd
     */
    public static final DateTimeFormatter YYYYMMDD = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter YYYYMMDD_COMMA = DateTimeFormatter.ofPattern("yyyy.MM.dd");
    public static final DateTimeFormatter FMT_SECOND = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String DEFAULT_ZONE_NAME = "Asia/Shanghai";
    private static final ZoneId DEFAULT_ZONE_ID = ZoneId.of(DEFAULT_ZONE_NAME);
    public static final String YEAR_MONTH_DAY_HOUR_MINUTE = "yyyy-MM-dd HH:mm";
    public static final DateTimeFormatter FMT_MONTH = DateTimeFormatter.ofPattern("yyyyMM");
    public static final String YEAR_MONTH_DAY_HOUR_MINUTE_SECONDS = "yyyy-MM-dd HH:mm:ss";
    public static final String HOUR_MINUTE_SECONDS = "HH:mm:ss";
    public static final DateTimeFormatter YYYYMMDDHHMINSS = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final Collection DEFAULT_PATTERNS =
            Arrays.asList("yyyy-MM-dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm");
    /**
     * 数字常量
     */
    public static final Integer TWENTY_FOUR = 24;
    public static final Integer SIXTY = 60;


    /**
     * 只支持天不支持时分秒 推荐formatDatetime
     */
    public static String formatDate(long timestamp, DateTimeFormatter formatter) {
        LocalDate date = timestamp2LocalDate(timestamp);
        return date.format(formatter);
    }


    public static String formatDatetime(long timestamp, DateTimeFormatter formatter) {
        LocalDateTime date = timestamp2LocalDateTime(timestamp);
        return date.format(formatter);
    }

    /**
     * 返回当前时间yyyyMMdd格式的数值类型
     *
     * @return omitted
     */
    public static int getYmdOfToday() {
        return Integer.parseInt(formatDate(System.currentTimeMillis(), YYYYMMDD));
    }

    /**
     * 根据时间戳获取时间字符串
     * yyyy-MM-dd HH:mm:ss
     * @param timestamp
     * @return
     */
    public static String formatDatetime(long timestamp) {
        LocalDateTime date = timestamp2LocalDateTime(timestamp);
        return date.format(FMT_SECOND);
    }

    public static long increaseTimestamp(long timestamp, long time, ChronoUnit timeUnit) {
        LocalDateTime localDateTime = timestamp2LocalDateTime(timestamp);
        ZonedDateTime zonedDateTime = ZonedDateTime.of(localDateTime.plus(time, timeUnit), ZoneId.systemDefault());
        return zonedDateTime.toInstant().toEpochMilli();
    }

    public static LocalDate timestamp2LocalDate(long timestamp) {
        return Instant.ofEpochMilli(timestamp).atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static LocalDateTime timestamp2LocalDateTime(long timestamp) {
        return Instant.ofEpochMilli(timestamp).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static long nowUnixMillisecond() {
        return LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    public static String getStrLocalDateTimeExcludeSecondByLong(long timeStamp) {
        Instant instant = Instant.ofEpochMilli(timeStamp);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, DEFAULT_ZONE_ID);
        return getStrLocalDateTimeExcludeSecond(localDateTime);
    }

    public static String getStrLocalDateTimeExcludeSecond(LocalDateTime localDateTime) {
        return localDateTime.format(DateTimeFormatter.ofPattern(YEAR_MONTH_DAY_HOUR_MINUTE));
    }

    public static String getStrLocalDateTimeWithSecondByLong(long timeStamp) {
        Instant instant = Instant.ofEpochMilli(timeStamp);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, DEFAULT_ZONE_ID);
        return getStrLocalDateTimeWithSecond(localDateTime);
    }

    public static String getStrLocalDateTimeWithSecond(LocalDateTime localDateTime) {
        return localDateTime.format(DateTimeFormatter.ofPattern(YEAR_MONTH_DAY_HOUR_MINUTE_SECONDS));
    }

    public static long getYearBeginMillis() {
        return LocalDateTime.of(LocalDate.now().getYear(), 1, 1, 0, 0)
                .atZone(ZoneId.systemDefault())
                .toInstant().toEpochMilli();
    }

    public static long getTodayBeginMillis() {
        LocalDate now = LocalDate.now();
        return LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), 0, 0)
                .atZone(ZoneId.systemDefault())
                .toInstant().toEpochMilli();
    }

    public static int nowUnixTime() {
        LocalDateTime now = LocalDateTime.now();
        ZoneId zoneId = ZoneId.systemDefault();
        return Ints.checkedCast(now.atZone(zoneId).toEpochSecond());
    }

    public static String formatMicroSecondByFormatter(long unixTime, DateTimeFormatter formatter) {
        Instant instant = Instant.ofEpochMilli(unixTime);
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime dt = LocalDateTime.ofInstant(instant, zoneId);
        return dt.format(formatter);
    }

    public static long getPreNDays(int nDays) {
        Calendar calendar1 = Calendar.getInstance();
        calendar1.add(Calendar.DATE, -nDays);
        return calendar1.getTimeInMillis();
    }


    public static String getPreNDaysV2(int nDays) {
        return formatDate(getPreNDays(nDays), YYYYMMDD);
    }

    /**
     * 返回上月。格式yyyyMM
     */
    public static String preMonthAsInt(int offset) {
        LocalDate nextMonth = LocalDate.now().minusMonths(offset);
        String nextMonthStr = FMT_MONTH.format(nextMonth);
        return nextMonthStr;
    }

    /**
     * 获取本周一的0时0分0秒
     */
    public static Date getThisWeekMonday() {
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 获得当前日期是一个星期的第几天
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (1 == dayWeek) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        // 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        // 获得当前日期是一个星期的第几天
        int day = cal.get(Calendar.DAY_OF_WEEK);
        // 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    /**
     * 返回日期所在周的周一 00:00:000
     */
    public static long getWeekBeginTime(LocalDate param) {
        LocalDate monday = param.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDateTime mondayTime = LocalDateTime.of(monday, LocalTime.MIN);
        return mondayTime.toInstant(OffsetDateTime.now().getOffset()).toEpochMilli();
    }

    /**
     * 返回日期所在周周日 23:59:999
     */
    public static long getWeekEndTime(LocalDate param) {
        LocalDate sunday = param.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        LocalDateTime sundayTime = LocalDateTime.of(sunday, LocalTime.MAX);
        return sundayTime.toInstant(OffsetDateTime.now().getOffset()).toEpochMilli();
    }

    /**
     * 返回日期所在月的第一天 00:00:000
     */
    public static long getMonthBeginTime(LocalDate param) {
        LocalDate firstDay = param.with(TemporalAdjusters.firstDayOfMonth());
        LocalDateTime mondayTime = LocalDateTime.of(firstDay, LocalTime.MIN);
        return mondayTime.toInstant(OffsetDateTime.now().getOffset()).toEpochMilli();
    }

    /**
     * 返回日期所在月的最后一天 23:59:999
     */
    public static long getMonthEndTime(LocalDate param) {
        LocalDate lastDay = param.with(TemporalAdjusters.lastDayOfMonth());
        LocalDateTime sundayTime = LocalDateTime.of(lastDay, LocalTime.MAX);
        return sundayTime.toInstant(OffsetDateTime.now().getOffset()).toEpochMilli();
    }


    /**
     * 获取今天剩余的时间（秒数）
     */
    public static int getToDayRemainingTime() {
        return getDayRemainingTime(new Date());
    }

    /**
     * 获取一天中剩余的时间（秒数）
     */
    public static int getDayRemainingTime(Date currentDate) {
        LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(),
                        ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0)
                .withSecond(0).withNano(0);
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(),
                ZoneId.systemDefault());
        long seconds = ChronoUnit.SECONDS.between(currentDateTime, midnight);
        return (int) seconds;
    }

    public static long getSomeDayMorning(long times) {
        Calendar cale = Calendar.getInstance();
        cale.setTimeInMillis(times);
        cale.set(Calendar.HOUR_OF_DAY, 0);
        cale.set(Calendar.MINUTE, 0);
        cale.set(Calendar.SECOND, 0);
        cale.set(Calendar.MILLISECOND, 0);
        return cale.getTimeInMillis();
    }

    public static final DateTimeFormatter FMT_DAY = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final int TEN_THOUSAND = 10000;

    public static int plusDays(int baseDate, int offset) {
        LocalDate baseLd = toLocalDate(baseDate);
        return toInt(baseLd.plusDays(offset));
    }

    public static int minusDays(int baseDate, int offset) {
        LocalDate baseLd = toLocalDate(baseDate);
        return toInt(baseLd.minusDays(offset));
    }

    public static LocalDate toLocalDate(int date) {
        return LocalDate.parse(String.valueOf(date), FMT_DAY);
    }

    public static int todayAsInt() {
        LocalDate today = LocalDate.now();
        String todayStr = FMT_DAY.format(today);
        return Integer.parseInt(todayStr);
    }

    public static int toInt(LocalDate ld) {
        return ld.getYear() * TEN_THOUSAND + ld.getMonthValue() * 100 + ld.getDayOfMonth();
    }

    public static long dayStartUnixMicroSecond(int date) {
        LocalDateTime dt = LocalDate.parse(String.valueOf(date), FMT_DAY).atStartOfDay();
        ZoneId zoneId = ZoneId.systemDefault();
        return dt.atZone(zoneId).toInstant().toEpochMilli();
    }

    public static LocalDate toLocalDate(Date date) {
        return date == null ? null : date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static LocalDate toLocalDate(long time) {
        return toLocalDate(new Date(time));
    }

    public static long diffDays(long startTime, long endTime) {
        return ChronoUnit.DAYS.between(toLocalDate(startTime), toLocalDate(endTime));
    }

    public static boolean isToday(long time) {
        return formatDate(System.currentTimeMillis(), YYYYMMDD)
                .equals(formatDate(time, YYYYMMDD));
    }

    public static long dayStartTime(long time) {
        int day = Integer.parseInt(DateUtils.formatDatetime(time, DateUtils.YYYYMMDD));
        return DateUtils.dayStartUnixMicroSecond(day);
    }

    public static long dayEndTime(long time) {
        int day = Integer.parseInt(DateUtils.formatDatetime(time, DateUtils.YYYYMMDD));
        int secondDay = DateUtils.plusDays(day, 1);
        return DateUtils.dayStartUnixMicroSecond(secondDay);
    }


    public static long dayEndTimeV2(long time) {
        // 将事件戳转换为日期时间对象
        Instant instant = Instant.ofEpochMilli(time);
        LocalDateTime eventDateTime = LocalDateTime.ofInstant(instant, ZoneOffset.UTC);
        LocalDateTime endOfDay = eventDateTime.with(LocalTime.MAX);
        Instant endOfDayInstant = endOfDay.atZone(ZoneOffset.UTC).toInstant();
        long endOfDayTimestamp = endOfDayInstant.toEpochMilli();
        return endOfDayTimestamp;
    }


    public static String getPreNDaysEx(int nDays) {
        Calendar calendar1 = Calendar.getInstance();
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMdd");
        calendar1.add(Calendar.DATE, -nDays);
        String daysAgo = sdf1.format(calendar1.getTime());
        return daysAgo;
    }

    /**
     *
     */
    public static String getPreDays(String beginDate, int nDays) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date dateParse = null;
        try {
            dateParse = sdf.parse(beginDate);
        } catch (ParseException e) {
            throw new RuntimeException("日期时间解析错误");
        }
        //获取三十天前日期
        Calendar theCa = Calendar.getInstance();
        theCa.setTime(dateParse);
        theCa.add(theCa.DATE, -nDays); //最后一个数字30可改，30天的意思
        Date start = theCa.getTime();
        String startDate = sdf.format(start); //三十天之前日期
        return startDate;
    }


    /**
     * 当前时间
     */
    public static String getNowFormatTime() {
        SimpleDateFormat sdf = new SimpleDateFormat(); // 格式化时间
        sdf.applyPattern(YEAR_MONTH_DAY_HOUR_MINUTE_SECONDS); // a为am/pm的标记
        Date date = new Date(); // 获取当前时间
        return sdf.format(date);
    }

    public static String getNowFormatHourTime(Long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat(); // 格式化时间
        sdf.applyPattern(HOUR_MINUTE_SECONDS); // a为am/pm的标记
        Date date = new Date(); // 获取当前时间
        return sdf.format(date);
    }


    public static String getNowFormatTime(Long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat(); // 格式化时间
        sdf.applyPattern(YEAR_MONTH_DAY_HOUR_MINUTE_SECONDS); // a为am/pm的标记
        Date date = new Date(timestamp);
        return sdf.format(date);
    }

    /**
     * 当前时间
     */
    public static String getNowFormatDate() {
        return LocalDate.now().format(YYYY_MM_DD);
    }

    public static Date formatYmdHms(String dataStr) {
        try {
            return YYYY_MM_DD_HH_MM_SS_FORMAT.get().parse(dataStr);
        } catch (Exception e) {
            log.error("DateUtils.formatYmdHms error", e);
        }
        return null;
    }

    public static String millisecondToYmdHms(long millisecond) {
        return YYYY_MM_DD_HH_MM_SS_FORMAT.get().format(Date.from(Instant.ofEpochMilli(millisecond)));
    }

    /**
     * 根据offsetFromToday确定出是哪一天，
     * 获得该天最晚时间【2022-01-01 23:59:59:999】的毫秒时间戳。
     *
     * @param offsetFromToday 相对于今天的偏移量。
     *                        例如：若offsetFromToday为-1，则代表昨天，返回昨天最晚时间的毫秒时间戳；
     *                        若offsetFromToday为1，则代表明天，返回明天最晚时间的毫秒时间戳。
     */
    public static long getDateMaxEpochMilli(int offsetFromToday) {
        return getDateMinEpochMilli(offsetFromToday + 1) - 1;
    }

    public static long getDateMinEpochMilli(int offsetFromToday) {
        LocalDate today = LocalDate.now();
        LocalDate requestedDay = today.plusDays(offsetFromToday);
        LocalDateTime localDateTime = requestedDay.atStartOfDay();
        return localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * 两个时间相差距离多少天多少小时多少分多少秒
     *
     * @param str1    时间参数 1 格式：2016-06-22 18:21:20
     * @param str2    时间参数 2 格式：2016-06-22 17:21:20
     * @param pattern 日期格式 yyyy-MM-dd HH:mm:ss 毫秒(yyyy-MM-dd HH:mm:ss.SSS)
     * @return String 返回值为：xx天xx小时xx分xx秒
     */
    public static String getDistanceTime(String str1, String str2, String pattern) {
        if (StringUtils.isBlank(str1) || StringUtils.isBlank(str2) || StringUtils.isBlank(pattern)) {
            return "";
        }
        DateFormat df = new SimpleDateFormat(pattern);
        Date one;
        Date two;
        long day = 0;
        long hour = 0;
        long min = 0;
        long sec = 0;
        try {
            one = df.parse(str1);
            two = df.parse(str2);
            long time1 = one.getTime();
            long time2 = two.getTime();
            long diff;
            if (time1 < time2) {
                diff = time2 - time1;
            } else {
                diff = time1 - time2;
            }
            day = diff / (TWENTY_FOUR * 60 * 60 * 1000);
            hour = (diff / (60 * 60 * 1000) - day * TWENTY_FOUR);
            min = ((diff / (60 * 1000)) - day * TWENTY_FOUR * 60 - hour * 60);
            sec = (diff / 1000 - day * TWENTY_FOUR * 60 * 60 - hour * 60 * 60 - min * 60);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return day + "天" + hour + "小时" + min + "分" + sec + "秒";
    }


    public static long getPreNMinutes(Integer n) {
        Calendar expireDate = Calendar.getInstance();
        expireDate.add(Calendar.MINUTE, -n);
        return expireDate.getTime().getTime();
    }

    /**
     * 获取当时时间的前n天时间戳
     */
    public static long getNowBeforeTime(int i, Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, -i);
        return calendar.getTimeInMillis();
    }

    /**
     * 计算time2减去time1的差值 差值只设置 几天 几个小时 或 几分钟
     * 根据差值返回多长之间前或多长时间后
     * 没有前后时间的限制
     *
     * @param time1 时间戳
     * @param time2 时间戳
     * @return String 返回值为：xx天xx小时xx分xx秒
     */
    public static String getDistanceBy2Timestamp(long time1, long time2) {
        long day = 0;
        long hour = 0;
        long min = 0;
        long sec = 0;
        long diff;

        if (time1 < time2) {
            diff = time2 - time1;
        } else {
            diff = time1 - time2;
        }
        day = diff / (TWENTY_FOUR * 60 * 60 * 1000);
        hour = (diff / (60 * 60 * 1000) - day * TWENTY_FOUR);
        min = ((diff / (60 * 1000)) - day * TWENTY_FOUR * 60 - hour * 60);
        sec = (diff / 1000 - day * TWENTY_FOUR * 60 * 60 - hour * 60 * 60 - min * 60);
        if (day != 0) {
            return day + "天" + hour + "小时" + min + "分钟" + sec + "秒";
        }
        if (hour != 0) {
            return hour + "小时" + min + "分钟" + sec + "秒";
        }
        if (min != 0) {
            return min + "分钟" + sec + "秒";
        }
        if (sec != 0) {
            return sec + "秒";
        }
        return "0秒";
    }

    /**
     * 基于时、分、秒获取当天对应的时间戳
     *
     * @param hour   时
     * @param minute 分
     * @param second 秒
     * @return
     */
    public static long getTodayHmsTimestamp(int hour, int minute, int second) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, hour);
        cal.set(Calendar.MINUTE, minute);
        cal.set(Calendar.SECOND, second);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis();
    }

    public static String getDateTimeFormatYyyyMmDdHhMiSs(long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
        return sdf.format(new Date(timestamp));
    }


    private static String getTimeWithUnit(long year, long month, long day, long hour, long minute, long second) {
        StringBuilder stringBuilder = new StringBuilder();
        if (year > 0) {
            stringBuilder.append(year + "年");
        }
        if (month > 0) {
            stringBuilder.append(month + "月");
        }
        if (day > 0) {
            stringBuilder.append(day + "天");
        }
        if (hour > 0) {
            stringBuilder.append(hour + "时");
        }
        if (minute > 0) {
            stringBuilder.append(minute + "分");
        }
        if (second > 0) {
            stringBuilder.append(second + "秒");
        }
        return stringBuilder.toString();
    }
}
