package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert.Column;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-25
 */
@Data
public class ReplayDetailClickHouseDto {

    @Column(name = "id")
    private String id;

    @Column(name = "flowReplayId")
    private Long flowReplayId;

    @Column(name = "sceneKey")
    private String sceneKey;

    @Column(name = "requestHitResult1")
    private Integer requestHitResult1;

    @Column(name = "hitStrategyList1")
    private String hitStrategyList1;

    @Column(name = "hitActionList1")
    private String hitActionList1;

    @Column(name = "requestHitResult2")
    private Integer requestHitResult2;

    @Column(name = "hitStrategyList2")
    private String hitStrategyList2;

    @Column(name = "hitActionList2")
    private String hitActionList2;

    @Column(name = "diffResult")
    private Integer diffResult;

    private String diffResultDesc;

    @Column(name = "createTime")
    private Long createTime;

    @Column(name = "replayPDate")
    private String replayPDate;

    @Column(name = "recordPDate")
    private String recordPDate;

    @Column(name = "bizKeyInfo")
    private String bizKeyInfo;

    private String featureKeyMap;
}
