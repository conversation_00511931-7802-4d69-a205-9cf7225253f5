package com.kuaishou.kwaishop.qa.risk.center.domain.account.service;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportAccountExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportTokenExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.BaseExcelModel;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
public interface AccountExcelImportService {

    Integer getCode();

    List<UserAccountDO> getAccountDOList(ImportAccountExcelBO excelBO);

    List<UserAccountDO> getAccountTokenDOList(ImportTokenExcelBO excelBO);

    List<UserAccountDO> buildAccountTokenDO(List<BaseExcelModel> excelModels, ImportTokenExcelBO excelBO);
}
