package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCaseBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-08
 */
public interface FaultCaseDAO {

    long insert(FaultCaseDO baseDO);

    FaultCaseDO queryById(Long id);

    Map<Long, FaultCaseDO> batchQueryByIds(Collection<Long> ids);

    int updateSelectiveById(FaultCaseDO baseDO);

    List<FaultCaseDO> queryFaultCaseList(FaultCaseBO faultCaseBO);

    PageBO<FaultCaseDO> queryPageFaultCaseList(FaultCaseBO faultCaseBO);

    boolean insertOrUpdate(FaultCaseDO faultCaseDO);

    int logicDeleted(Long id, String operator);

    boolean batchInsertOrUpdate(List<FaultCaseDO> faultCaseDOS);

    FaultCaseDO queryByUnique(String uniqueId);

    List<FaultCaseDO> querySyncCaseByKSN(Long centerId, Long teamId, String ksn);

    List<FaultCaseDO> queryFaultCaseByCenterId(Long centerId, Long st, Long et);

    List<FaultCaseDO> queryCaseByIds(Collection<Long> ids);

    int batchUpdateCaseTag(String operator, Integer tag, Collection<Long> ids);

    int batchUpdateBusinessCaseTag(String operator, String businessTag, Collection<Long> ids);

    int batchUpdateById(String operator, List<FaultCaseBO> faultCaseBOList);

    // 查询plan中已经删除的场景专用。
    List<FaultCaseDO> getFaultCaseByFaultPlan(FaultCaseBO faultCaseBO);

    Map<Long, FaultCaseDO> queryCaseByIdsAll(Set<Long> caseIds);

    FaultCaseDO queryCaseById(Long id);
}
