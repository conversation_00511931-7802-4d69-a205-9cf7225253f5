package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.biz.FaultPlanBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.service.FaultPlanService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.BatchUpdateFaultPlanRecordRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.BatchUpdateFaultPlanRecordResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.CheckfaultReportPicRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.CheckfaultReportPicResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.CreateFaultPlanRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.CreateFaultPlanResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.DeleteFaultPlanRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.DeleteFaultPlanResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.DeleteRelationCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.DeleteRelationCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.FaultPlanCaseDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.FaultPlanCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.FaultPlanCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.FaultPlanDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.KrpcFaultPlanDomainServiceGrpc.FaultPlanDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.PageFaultPlanDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.PageFaultPlanRecordDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.PlanRecordReportDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultPlanListByCaseIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultPlanListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultPlanListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultPlanPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultPlanPageListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultPlanRecordByIdsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultPlanRecordByIdsResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultPlanRecordListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultPlanRecordListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultPlanRecordPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultPlanRecordPageListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryPlanRecordReportRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryPlanRecordReportResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.StartFaultPlanRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.StartFaultPlanResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateFaultPlanRecordRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateFaultPlanRecordResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateFaultPlanRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateFaultPlanResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdatePlanRecordReportRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdatePlanRecordReportResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-01-05
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class FaultPlanDomainServiceImpl extends FaultPlanDomainServiceImplBaseV2 {

    @Autowired
    private FaultPlanBizService faultPlanBizService;

    @Autowired
    private FaultPlanService faultPlanService;

    @Override
    public CreateFaultPlanResponse createFaultPlan(CreateFaultPlanRequest request) {
        log.info("[FaultPlanDomainServiceImpl] createFaultPlan request: {}", protoToJsonString(request));
        try {
            faultPlanBizService.createFaultPlan(request);
            return CreateFaultPlanResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] createFaultPlan bizError, req: {}, exception: ", protoToJsonString(request), e);
            return CreateFaultPlanResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] createFaultPlan error, req: {}, exception: ", protoToJsonString(request), e);
            return CreateFaultPlanResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateFaultPlanResponse updateFaultPlan(UpdateFaultPlanRequest request) {
        log.info("[FaultPlanDomainServiceImpl] updateFaultPlan request: {}", protoToJsonString(request));
        try {
            faultPlanBizService.updateFaultPlan(request);
            return UpdateFaultPlanResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] updateFaultPlan bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateFaultPlanResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] updateFaultPlan error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateFaultPlanResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public DeleteFaultPlanResponse deleteFaultPlan(DeleteFaultPlanRequest request) {
        log.info("[FaultPlanDomainServiceImpl] deleteFaultPlan request: {}", protoToJsonString(request));
        try {
            faultPlanBizService.deleteFaultPlan(request);
            return DeleteFaultPlanResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] deleteFaultPlan bizError, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteFaultPlanResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] deleteFaultPlan error, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteFaultPlanResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryFaultPlanListResponse queryFaultPlanList(QueryFaultPlanListRequest request) {
        log.info("[FaultPlanDomainServiceImpl] queryFaultPlanList request: {}", protoToJsonString(request));
        try {
            List<FaultPlanDTO> res = faultPlanBizService.queryFaultPlanList(request);
            return QueryFaultPlanListResponse.newBuilder()
                    .addAllData(res)
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] queryFaultPlanPageList bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultPlanListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] queryFaultPlanPageList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultPlanListResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryFaultPlanPageListResponse queryFaultPlanPageListByCaseId(QueryFaultPlanListByCaseIdRequest request) {
        log.info("[FaultPlanDomainServiceImpl] queryFaultPlanPageList request: {}", protoToJsonString(request));
        try {
            PageFaultPlanDTO res = faultPlanBizService.queryFaultPlanByCaseId(request);
            return QueryFaultPlanPageListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(res)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] queryFaultPlanPageList bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultPlanPageListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] queryFaultPlanPageList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultPlanPageListResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryFaultPlanPageListResponse queryFaultPlanPageList(QueryFaultPlanPageListRequest request) {
        log.info("[FaultPlanDomainServiceImpl] queryFaultPlanPageList request: {}", protoToJsonString(request));
        try {
            PageFaultPlanDTO res = faultPlanBizService.queryFaultPlanPageList(request);
            return QueryFaultPlanPageListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(res)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] queryFaultPlanPageList bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultPlanPageListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] queryFaultPlanPageList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultPlanPageListResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public StartFaultPlanResponse startFaultPlan(StartFaultPlanRequest request) {
        log.info("[FaultPlanDomainServiceImpl] startFaultPlan request: {}", protoToJsonString(request));
        try {
            faultPlanBizService.startFaultPlan(request);
            return StartFaultPlanResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] startFaultPlan bizError, req: {}, exception: ", protoToJsonString(request), e);
            return StartFaultPlanResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] startFaultPlan error, req: {}, exception: ", protoToJsonString(request), e);
            return StartFaultPlanResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryFaultPlanRecordPageListResponse queryFaultPlanRecordPageList(QueryFaultPlanRecordPageListRequest request) {
        log.info("[FaultPlanDomainServiceImpl] queryFaultPlanRecordPageList request:  {}", protoToJsonString(request));
        try {
            PageFaultPlanRecordDTO res = faultPlanBizService.queryFaultPlanRecordPageList(request);
            return QueryFaultPlanRecordPageListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(res)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] queryFaultPlanRecordPageList bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultPlanRecordPageListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] queryFaultPlanRecordPageList error, req:{}, exception: ", protoToJsonString(request), e);
            return QueryFaultPlanRecordPageListResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateFaultPlanRecordResponse updateFaultPlanRecord(UpdateFaultPlanRecordRequest request) {
        log.info("[FaultPlanDomainServiceImpl] updateFaultPlanRecord request: {}", protoToJsonString(request));
        try {
            faultPlanBizService.updateFaultPlanRecord(request);
            return UpdateFaultPlanRecordResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] updateFaultPlanRecord bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateFaultPlanRecordResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] updateFaultPlanRecord error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateFaultPlanRecordResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public BatchUpdateFaultPlanRecordResponse batchUpdateFaultPlanRecord(BatchUpdateFaultPlanRecordRequest request) {
        log.info("[FaultPlanDomainServiceImpl] batchUpdateFaultPlanRecord request: {}", protoToJsonString(request));
        try {
            faultPlanBizService.batchUpdateFaultPlanRecord(request);
            return BatchUpdateFaultPlanRecordResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] batchUpdateFaultPlanRecord bizError, req: {}, exception: ", protoToJsonString(request), e);
            return BatchUpdateFaultPlanRecordResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] batchUpdateFaultPlanRecord error, req: {}, exception: ", protoToJsonString(request), e);
            return BatchUpdateFaultPlanRecordResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public FaultPlanCaseResponse faultPlanCase(FaultPlanCaseRequest request) {
        log.info("[FaultPlanDomainServiceImpl] faultPlanCase request: {}", protoToJsonString(request));
        try {
            List<FaultPlanCaseDTO> res = faultPlanBizService.queryPlanCaseInfo(request);
            return FaultPlanCaseResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(res)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] faultPlanCase bizError, req: {}, exception: ", protoToJsonString(request), e);
            return FaultPlanCaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] faultPlanCase error, req: {}, exception: ", protoToJsonString(request), e);
            return FaultPlanCaseResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryFaultPlanRecordListResponse queryFaultPlanRecordList(QueryFaultPlanRecordListRequest request) {
        log.info("[FaultPlanDomainServiceImpl] queryFaultPlanRecordList request: {}", protoToJsonString(request));
        try {
            return QueryFaultPlanRecordListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(faultPlanBizService.queryFaultPlanRecordListFromPlanProblem(request))
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] queryFaultPlanRecordList bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultPlanRecordListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] queryFaultPlanRecordList error, req:{}, exception: ", protoToJsonString(request), e);
            return QueryFaultPlanRecordListResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public DeleteRelationCaseResponse deleteRelationCase(DeleteRelationCaseRequest request) {
        log.info("[FaultPlanDomainServiceImpl] deleteRelationCase request: {}", protoToJsonString(request));
        try {
            faultPlanBizService.deleteRelationCase(request);
            return DeleteRelationCaseResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] deleteRelationCase bizError, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteRelationCaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] deleteRelationCase error, req:{}, exception: ", protoToJsonString(request), e);
            return DeleteRelationCaseResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryPlanRecordReportResponse queryPlanRecordReport(QueryPlanRecordReportRequest request) {
        log.info("[FaultPlanDomainServiceImpl] queryPlanRecordReport request: {}", protoToJsonString(request));
        try {
            PlanRecordReportDTO res = faultPlanBizService.queryPlanReport(request);
            return QueryPlanRecordReportResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(res)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] queryPlanRecordReport bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryPlanRecordReportResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] queryPlanRecordReport error, req:{}, exception: ", protoToJsonString(request), e);
            return QueryPlanRecordReportResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdatePlanRecordReportResponse updatePlanRecordReport(UpdatePlanRecordReportRequest request) {
        log.info("[FaultPlanDomainServiceImpl] updatePlanRecordReport request: {}", protoToJsonString(request));
        try {
            faultPlanBizService.updatePlanReport(request);
            return UpdatePlanRecordReportResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] updatePlanRecordReport bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdatePlanRecordReportResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] updatePlanRecordReport error, req:{}, exception: ", protoToJsonString(request), e);
            return UpdatePlanRecordReportResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryFaultPlanRecordByIdsResponse queryFaultPlanRecordListByIds(QueryFaultPlanRecordByIdsRequest request) {
        try {
            log.error("[FaultPlanDomainServiceImpl] queryFaultPlanRecordListByIds request: {}", protoToJsonString(request));
            return QueryFaultPlanRecordByIdsResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(faultPlanBizService.queryFaultPlanRecordListByIds(request))
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] queryFaultPlanRecordListByIds bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultPlanRecordByIdsResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] queryFaultPlanRecordListByIds error, req:{}, exception: ", protoToJsonString(request), e);
            return QueryFaultPlanRecordByIdsResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    @Override
    public CheckfaultReportPicResponse checkfaultReportPic(CheckfaultReportPicRequest request) {
        try {
            log.error("[FaultPlanDomainServiceImpl] checkfaultReportPic request: {}", protoToJsonString(request));
            return CheckfaultReportPicResponse.newBuilder()
                    .addAllWords(faultPlanService.ocrfaultReportPic(request.getUrl()))
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanDomainServiceImpl] checkfaultReportPic bizError, req: {}, exception: ", protoToJsonString(request), e);
            return CheckfaultReportPicResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanDomainServiceImpl] checkfaultReportPic error, req:{}, exception: ", protoToJsonString(request), e);
            return CheckfaultReportPicResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }
}
