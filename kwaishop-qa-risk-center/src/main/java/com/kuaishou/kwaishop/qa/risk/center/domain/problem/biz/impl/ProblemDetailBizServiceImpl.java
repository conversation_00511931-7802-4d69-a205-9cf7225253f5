package com.kuaishou.kwaishop.qa.risk.center.domain.problem.biz.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.OPERATOR_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.PROBLEM_DETAIL_NOT_FOUND_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.PROBLEM_LEVEL_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.PROBLEM_LINK_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.PROBLEM_MISSING_TYPE_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.PROBLEM_NAME_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.PROBLEM_TIME_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.PROBLEM_TYPE_ERROR;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.problem.ProblemDetailDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.auth.service.AuthService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.cache.EntityCacheService;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.biz.ProblemDetailBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.convert.ProblemDetailConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.bo.ProblemDetailBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.enums.ProblemDetailTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.enums.ProblemLevelTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.enums.ProblemMissingTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.service.ProblemDetailService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.CreateProblemRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.DeleteProblemRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.PageProblemDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.ProblemDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.QueryProblemDetailListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.QueryProblemDetailPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.UpdateProblemRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-06
 */
@Service
@Lazy
@Slf4j
public class ProblemDetailBizServiceImpl implements ProblemDetailBizService {

    @Autowired
    private ProblemDetailService problemDetailService;

    @Autowired
    private ProblemDetailConvert problemDetailConvert;

    @Autowired
    private AuthService authService;

    @Autowired
    private EntityCacheService entityCacheService;

    @Override
    public void createProblem(CreateProblemRequest request) {
        if (StringUtils.isBlank(request.getOperator())) {
            throw new BizException(OPERATOR_EMPTY_ERROR);
        }
        ProblemDetailBO detailBO = problemDetailConvert.buildCreateBO(request);
        checkRequest(detailBO);
        authService.authPreCheck(detailBO.getOperator());
        problemDetailService.createProblem(detailBO);
    }

    @Override
    public void updateProblem(UpdateProblemRequest request) {
        if (StringUtils.isBlank(request.getOperator())) {
            throw new BizException(OPERATOR_EMPTY_ERROR);
        }
        if (request.getId() <= 0) {
            throw new BizException(PROBLEM_DETAIL_NOT_FOUND_ERROR);
        }
        ProblemDetailBO detailBO = problemDetailConvert.buildUpdateBO(request);
        checkRequest(detailBO);
        authService.authPreCheck(detailBO.getOperator());
        problemDetailService.updateProblem(detailBO);
    }

    @Override
    public void deleteProblem(DeleteProblemRequest request) {
        if (StringUtils.isBlank(request.getOperator())) {
            throw new BizException(OPERATOR_EMPTY_ERROR);
        }
        if (request.getId() <= 0) {
            throw new BizException(PROBLEM_DETAIL_NOT_FOUND_ERROR);
        }
        authService.authPreCheck(request.getOperator());
        problemDetailService.deleteProblem(request.getId(), request.getOperator());
    }

    @Override
    public List<ProblemDTO> queryProblemDetailList(QueryProblemDetailListRequest request) {
        if (StringUtils.isBlank(request.getOperator())) {
            throw new BizException(OPERATOR_EMPTY_ERROR);
        }
        ProblemDetailBO detailBO = problemDetailConvert.buildQueryListBO(request);
        List<ProblemDetailDO> dataList = problemDetailService.queryProblemList(detailBO);
        return buildProblemDTO(dataList);
    }

    @Override
    public PageProblemDTO queryProblemDetailPageList(QueryProblemDetailPageListRequest request) {
        if (StringUtils.isBlank(request.getOperator())) {
            throw new BizException(OPERATOR_EMPTY_ERROR);
        }
        ProblemDetailBO detailBO = problemDetailConvert.buildQueryPageListBO(request);
        PageBO<ProblemDetailDO> pageBO = problemDetailService.queryPageProblemList(detailBO);
        return PageProblemDTO.newBuilder()
                .setTotal(pageBO.getTotal())
                .setPageNo(pageBO.getPageNo())
                .setPageSize(pageBO.getPageSize())
                .addAllDetails(buildProblemDTO(pageBO.getData()))
                .build();
    }

    private List<ProblemDTO> buildProblemDTO(List<ProblemDetailDO> dataList) {
        Set<Long> entityIds = new HashSet<>();
        dataList.forEach(p -> {
            entityIds.add(p.getCenterId());
            entityIds.add(p.getTeamId());
        });
        Map<Long, EntityDO> entityDOMap = entityCacheService.batchQueryByIds(entityIds);
        List<ProblemDTO> res = new ArrayList<>();
        for (ProblemDetailDO detailDO: dataList) {
            EntityDO centerDO = entityDOMap.get(detailDO.getCenterId());
            String centerName = centerDO == null ? StringUtils.EMPTY : centerDO.getName();
            EntityDO teamDO = entityDOMap.get(detailDO.getTeamId());
            String teamName = teamDO == null ? StringUtils.EMPTY : teamDO.getName();
            res.add(problemDetailConvert.buildProblemDTO(detailDO, centerName, teamName));
        }
        return res;
    }

    private void checkRequest(ProblemDetailBO detailBO) {
        if (StringUtils.isBlank(detailBO.getProblemLink())) {
            throw new BizException(PROBLEM_LINK_EMPTY_ERROR);
        }
        if (detailBO.getProblemStartTime() <= 0 || detailBO.getProblemEndTime() <= 0
                || detailBO.getProblemStartTime() > detailBO.getProblemEndTime()) {
            throw new BizException(PROBLEM_TIME_ERROR);
        }
        if (StringUtils.isBlank(detailBO.getName())) {
            throw new BizException(PROBLEM_NAME_EMPTY_ERROR);
        }
        if (detailBO.getDetailType() <= 0 || ProblemDetailTypeEnum.of(detailBO.getDetailType()) == null) {
            throw new BizException(PROBLEM_TYPE_ERROR);
        }
        if (detailBO.getLevel() <= 0
                || ProblemLevelTypeEnum.of(detailBO.getLevel(), detailBO.getDetailType()) == null) {
            throw new BizException(PROBLEM_LEVEL_ERROR);
        }
        if (detailBO.getMissingType() <= 0 || ProblemMissingTypeEnum.of(detailBO.getMissingType()) == null) {
            throw new BizException(PROBLEM_MISSING_TYPE_ERROR);
        }
    }
}
