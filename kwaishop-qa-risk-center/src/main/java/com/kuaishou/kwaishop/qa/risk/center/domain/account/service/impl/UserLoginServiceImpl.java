package com.kuaishou.kwaishop.qa.risk.center.domain.account.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.TOKEN_ACCOUNT_PROTECT;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.TOKEN_PASSWORD_ERROR;

import java.io.StringWriter;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.config.ConfigMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.AccountTokenBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserLoginTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserLoginService;
import com.kuaishou.kwaishop.qa.risk.center.utils.http.HttpUtils;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-19
 */
@Service
@Slf4j
public class UserLoginServiceImpl implements UserLoginService {

    @Resource
    private ConfigMapper configMapper;

    @Setter
    @Getter
    private static String IOS_SECRET;

    private static final String LOGIN_URL_PRT = "https://ks-server-api.prt.kuaishou.com/rest/n/user/login/";

    private static final String LOGIN_URL_STAGING = "https://ks-server-api.staging.kuaishou.com/rest/n/user/login/";

    /**
     * 定义iOS版本的常量
     */
    private static final String IOS_VERSION = "9.5.10.630";

    /**
     * 设备标识符（Device ID）常量，可能用于设备追踪或认证
     */
    private static final String IOS_DID = "6F46BF3D-04B5-4365-B86F-92890608A999";

    /**
     * iOS客户端的密钥，可能用于应用的身份验证或安全通信
     */
    private static final String IOS_CLIENT_KEY = "56c3713c";

    /**
     * 测试环境的PRT（可能是Proxy Refresh Token或某种测试标识）常量
     */
    private static final String PRT_TEST = "PRT.test";


    /**
     * 登录有三种形式，一种smscode登录，一种email形式，一种是mobile登录。
     * @param accountTokenBO
     */
    @Override
    public String getToken(AccountTokenBO accountTokenBO) {
        HttpUtils httpUtils = new HttpUtils();
        String response = "";
        String laneId = System.getenv("KWS_LANE_ID");
        log.info("laneId = {}", laneId);
        String loginUrl = PRT_TEST.equals(laneId) ? LOGIN_URL_PRT : LOGIN_URL_STAGING;
        String url = loginUrl + ((UserLoginTypeEnum.of(accountTokenBO.getLoginType())  == UserLoginTypeEnum.EMAIL_LOGIN)
                ? "email"
                : "mobile");
        try {
            Map<String, Object> params = buildData(accountTokenBO);
            Map<String, String> headers = buildHeaders();

            response = httpUtils.postByUrlencoded(url, headers, params);
            log.info("userLogin,url = {}, param={}, headers={}, response={}", url, params, headers, response);
        } catch (Exception e) {
            log.error("错误:{}", e.getMessage());
            throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR);
        }
        if (response.contains("账号或者密码错误")) {
            throw new BizException(TOKEN_PASSWORD_ERROR);
        }

        if (response.contains("账号保护")) {
            throw new BizException(TOKEN_ACCOUNT_PROTECT);
        }
        return response;
    }

    public Map<String, Object> buildData(AccountTokenBO accountTokenBO) {
        Map<String, Object> params = new HashMap<>();
        UserLoginTypeEnum loginType = UserLoginTypeEnum.of(accountTokenBO.getLoginType());
        params.put("password", hash("SHA-512", accountTokenBO.getPassword()));
        if (loginType != null) {
            switch (loginType) {
                case CITY_1264_LOGIN:
                    params.put("mobileCountryCode", "+1264");
                    params.put("mobile", accountTokenBO.getAccount());
                    break;
                case EMAIL_LOGIN:
                    params.put("email", accountTokenBO.getAccount());
                    params.put("mobileCountryCode", "+86");
                    break;
                case CITY_86_LOGIN:
                default:
                    params.put("mobile", accountTokenBO.getAccount());
                    params.put("mobileCountryCode", "+86");
                    break;
            }
        }
        //后续增加token和tokenClientSalt
        return getSignParam(params, null, null);
    }


    /**
     * 测试代码
     * @param args
     */
    public static void main(String[] args) {
        UserLoginServiceImpl userLoginBizService = new UserLoginServiceImpl();
//        String password = hash("SHA-512", userLoginBizService.decrypt("test123456"));
//        userLoginBizService.loginV2(GetAccountCookieRequest.newBuilder()
//                        .setAccount("***********")
//                        .setPassword(password)
//                        .setLoginType(1)
//                .build());

        // 测试
//        userLoginBizService.getToken(AccountTokenBO.builder()
//                .account("***********")
//                .password("test12345678")
//                .loginType(1)
//                .build());
    }

    /**
     * 解密函数
     * @param content
     * @return
     */
    public String decrypt(String content) {
        if (!content.startsWith("enc:")) {
            return content;
        }
        content = content.substring(4);
        int padding = content.charAt(0) - '0';
        content = content.substring(1) + new String(new char[padding]).replace("\0", "=");
        byte[] decoded = Base64.getDecoder().decode(content);
        return new String(decoded);
    }

    /**
     * 获取sig签名参数
     * @param param
     * @param token
     * @param tokenClientSalt
     * @return
     */
    public Map<String, Object> getSignParam(Map<String, Object> param, String token, String tokenClientSalt) {
        if (token != null) {
            param.put("token", token);
        }
        param.put("client_key", IOS_CLIENT_KEY);
        StringWriter argStrBuffer = new StringWriter();
        List<Map.Entry<String, Object>> sortedEntries = new ArrayList<>(param.entrySet());
        Collections.sort(sortedEntries, new Comparator<Map.Entry<String, Object>>() {
            public int compare(Map.Entry<String, Object> entry1, Map.Entry<String, Object> entry2) {
                return entry1.getKey().compareTo(entry2.getKey());
            }
        });
        for (Map.Entry<String, Object> entry : sortedEntries) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (key.equals("sig") || key.startsWith("__NS")) {
                continue;
            }
            argStrBuffer.write(key + "=" + value);
        }
        argStrBuffer.write(IOS_SECRET);
        String argStr = argStrBuffer.toString();
        String sig = hash("MD5", argStr);
        String nsTokenSig = null;
        if (tokenClientSalt != null) {
            nsTokenSig = hash("SHA-512", sig + tokenClientSalt);
        }
        String nsSig3 = "2159768269c7ffffffffffffffffffffffffffffff";
        log.info("测试下token, argStr = {}", argStr);
        param.put("client_key", IOS_CLIENT_KEY);
        param.put("sig", sig);
        param.put("__NS_sig3", nsSig3);
        if (token != null) {
            param.put("token", token);
        }
        if (nsTokenSig != null) {
            param.put("__NStokensig", nsTokenSig);
        }
        return param;
    }

    /**
     * 哈希函数
     * @param algorithm
     * @param data
     * @return
     */
    private String hash(String algorithm, String data) {
        try {
            MessageDigest md = MessageDigest.getInstance(algorithm);
            md.update(data.getBytes());
            byte[] digest = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b & 0xff));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            return data;
        }
    }

    public Map<String, String> buildHeaders() {
        return new HashMap<String, String>() {{
            put("Cookie", "appver=11.3.20.1334759;did=51B907F5-998C-4D89-9E8A-41377FF0724C;client_key=56c3713c");
            put("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_0) "
                    +
                    "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36");
            put("Accept", "application/json");
            put("Accept-Encoding", "gzip, deflate");
            put("Connection", "keep-alive");
            put("ptp-flag", "1");
            put("trace-context", "{\"laneId\":\"PRT.test\",\"bizContext\":{\"contexts\":{\"trafficType\":\"MQ==\","
                    +
                    "\"caseName\":\"dGVzdC1jYXNlL2Rpc3RyaWJ1dGUvbGVhZGVyX3JvbGUvdGVzdF9sZWFkZXJfcHJvbW90ZXJfbWFuYWdlLnB5O"
                    +
                    "jpUZXN0TGVhZGVyUHJvbW90ZXJNYW5hZ2U6OnRlc3RfbGVhZGVyX3Byb21vdGVyX21hbmFnZV9leHBvcnQ=\"}}}");
            put("X-KTrace-Id-Enabled", "1");
            put("Content-Length", "290");
            put("Content-Type", "application/x-www-form-urlencoded");
        }};
    }
}