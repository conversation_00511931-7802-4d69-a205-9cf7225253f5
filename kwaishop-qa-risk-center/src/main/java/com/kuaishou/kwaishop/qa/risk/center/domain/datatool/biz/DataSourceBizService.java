package com.kuaishou.kwaishop.qa.risk.center.domain.datatool.biz;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DATA_SOURCE_CREATE_PARAM_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DATA_SOURCE_ID_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DATA_SOURCE_QUERY_PARAM_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DATA_SOURCE_UPDATE_PARAM_ERROR;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataQueryExecuteDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataSourceDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataQueryDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataSourceDo;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.DataSourceBo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CreateDataSourceRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CreateDataSourceResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CreateQueryExeRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DataSourceDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DeleteDataSourceRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DeleteDataSourceResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.PageDataSourceDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataSourceByIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataSourceByIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataSourceLikeNameRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataSourceLikeNameResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataSourceRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataSourceResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryPageDataSourceRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryPageDataSourceResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.UpdateDataSourceRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.UpdateDataSourceResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-12
 */

@Service
@Slf4j
public class DataSourceBizService {

    @Autowired
    private DataSourceDAO dataSourceDAO;

    @Autowired
    private DataQueryExecuteDAO dataQueryExecuteDAO;

    public CreateDataSourceResponse createDataSource(CreateDataSourceRequest request) {
        if (StringUtils.isBlank(request.getName()) || StringUtils.isBlank(request.getCreatorName()) || StringUtils
                .isBlank(request.getContent())) {
            throw new BizException(DATA_SOURCE_CREATE_PARAM_ERROR);
        }
        if (request.getType() <= 0) {
            throw new BizException(DATA_SOURCE_CREATE_PARAM_ERROR);
        }
        try {
            long res = dataSourceDAO.insertDataSource(buildCreateDataSource(request));
            return CreateDataSourceResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] createDataSource bizError, exception: ", e);
            return CreateDataSourceResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[DataSourceBizService] createDataSource Error, exception: ", e);
            return CreateDataSourceResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }


    }

    public UpdateDataSourceResponse updateDataSource(UpdateDataSourceRequest request) {
        if (request.getId() <= 0) {
            throw new BizException(DATA_SOURCE_ID_ERROR);
        }
        if (StringUtils.isBlank(request.getName()) && StringUtils.isBlank(request.getCreatorName()) && StringUtils
                .isBlank(request.getContent()) && request.getType() <= 0) {
            return UpdateDataSourceResponse.newBuilder().setResult(DATA_SOURCE_UPDATE_PARAM_ERROR.getCode())
                    .setErrorMsg(DATA_SOURCE_UPDATE_PARAM_ERROR.getMessage()).build();
        }

        try {
            DataSourceDo dataSourceDo = dataSourceDAO.queryDataSourceById(request.getId());
            if (dataSourceDo == null) {
                throw new BizException(DATA_SOURCE_ID_ERROR);
            }
            dataSourceDo.setName(request.getName());
            dataSourceDo.setContent(request.getContent());
            dataSourceDo.setDescription(request.getDescription());
            dataSourceDo.setType(request.getType());
            dataSourceDAO.updateDataSource(dataSourceDo);
            return UpdateDataSourceResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] updateDataSource bizError, exception: ", e);
            return UpdateDataSourceResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[DataSourceBizService] updateDataSource Error, exception: ", e);
            return UpdateDataSourceResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    public QueryPageDataSourceResponse queryPageDataSource(QueryPageDataSourceRequest request) {
        try {
            PageBO<DataSourceDo> dataSourceDoPageBO =
                    dataSourceDAO.queryPageDataSourceList(buildQueryDataSourcePage(request));
            List<DataSourceDTO> dataSourceDTOList = dataSourceDoPageBO.getData().stream()
                    .map(this::convertToDTO).collect(Collectors.toList());
            PageDataSourceDTO pageDataSourceDTO = PageDataSourceDTO.newBuilder().addAllDatasourceList(dataSourceDTOList)
                    .setPageNo(dataSourceDoPageBO.getPageNo())
                    .setPageSize(dataSourceDoPageBO.getPageSize())
                    .setTotal(dataSourceDoPageBO.getTotal())
                    .build();
            return QueryPageDataSourceResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(pageDataSourceDTO).build();

        } catch (BizException e) {
            log.error("[DataSourceBizService] queryPageDataSource bizError, exception: ", e);
            return QueryPageDataSourceResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[DataSourceBizService] queryPageDataSource Error, exception: ", e);
            return QueryPageDataSourceResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage()).build();
        }
    }

    public QueryDataSourceResponse queryDataSource(QueryDataSourceRequest request) {
        if (StringUtils.isBlank(request.getName()) && StringUtils.isBlank(request.getCreatorName())
                && request.getType() == 0) {
            throw new BizException(DATA_SOURCE_QUERY_PARAM_ERROR);
        }
        try {
            List<DataSourceDo> dataSourceDos = dataSourceDAO.queryDataSourceList(buildQueryDataSource(request));
            log.info("[DataSourceBizService] queryDataSource", ObjectMapperUtils.toJSON(request));
            List<DataSourceDTO> dataSourceDTOList =
                    dataSourceDos.stream().map(this::convertToDTO).collect(Collectors.toList());
            return QueryDataSourceResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(dataSourceDTOList).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] queryDataSource bizError, exception: ", e);
            return QueryDataSourceResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[DataSourceBizService] queryDataSource Error, exception: ", e);
            return QueryDataSourceResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    private DataSourceDTO convertToDTO(DataSourceDo dataSourceDo) {
        return DataSourceDTO.newBuilder()
                .setCreateTime(dataSourceDo.getCreateTime())
                .setUpdateTime(dataSourceDo.getUpdateTime())
                .setType(dataSourceDo.getType())
                .setDescription(dataSourceDo.getDescription())
                .setName(dataSourceDo.getName())
                .setId(dataSourceDo.getId())
                .setCreatorName(dataSourceDo.getCreatorName())
                .setContent(dataSourceDo.getContent())
                .build();
    }

    public DeleteDataSourceResponse deleteDataSource(DeleteDataSourceRequest request) {
        DataSourceDo dataSourceDo = dataSourceDAO.queryDataSourceById(request.getId());
        if (dataSourceDo == null) {
            return DeleteDataSourceResponse.newBuilder().setResult(BizErrorCode.DATA_SOURCE_ID_ERROR.getCode())
                    .setErrorMsg(BizErrorCode.DATA_SOURCE_ID_ERROR.getMessage()).build();
        }
        try {
            dataSourceDAO.logicDeleted(request.getId(), dataSourceDo.getCreatorName());
            return DeleteDataSourceResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] deleteDataSource bizError, exception: ", e);
            return DeleteDataSourceResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[DataSourceBizService] deleteDataSource Error, exception: ", e);
            return DeleteDataSourceResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    public QueryDataSourceLikeNameResponse queryDataSourceLikeName(QueryDataSourceLikeNameRequest request) {
        if (StringUtils.isBlank(request.getName())) {
            throw new BizException(DATA_SOURCE_QUERY_PARAM_ERROR);
        }
        try {
            List<DataSourceDo> dataSourceDos = dataSourceDAO.queryDataSourceLikeName(request.getName());
            List<DataSourceDTO> dataSourceDTOList =
                    dataSourceDos.stream().map(this::convertToDTO).collect(Collectors.toList());
            return QueryDataSourceLikeNameResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(dataSourceDTOList).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] queryDataSourceLikeName bizError, exception: ", e);
            return QueryDataSourceLikeNameResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[DataSourceBizService] queryDataSourceLikeName Error, exception: ", e);
            return QueryDataSourceLikeNameResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    public QueryDataSourceByIdResponse queryDataSourceById(QueryDataSourceByIdRequest request) {
        if (request.getId() <= 0) {
            throw new BizException(DATA_SOURCE_ID_ERROR);
        }
        try {
            DataSourceDo dataSourceDo = dataSourceDAO.queryDataSourceById(request.getId());
            if (dataSourceDo == null) {
                return QueryDataSourceByIdResponse.newBuilder().setResult(BizErrorCode.DATA_QUERY_EMPTY.getCode())
                        .setErrorMsg(BizErrorCode.DATA_QUERY_EMPTY.getMessage()).build();
            }
            return QueryDataSourceByIdResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE)
                    .addData(convertToDTO(dataSourceDo)).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] queryDataSourceById bizError, exception: ", e);
            return QueryDataSourceByIdResponse.newBuilder().setResult(e.getCode()).setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[DataSourceBizService] queryDataSourceById Error, exception: ", e);
            return QueryDataSourceByIdResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage()).build();
        }

    }

    public DataSourceDo buildQueryDataSource(QueryDataSourceRequest request) {
        if (request.getType() == 0) {
            return DataSourceDo.builder()
                    .creatorName(request.getCreatorName())
                    .name(request.getName())
                    .build();
        } else {
            return DataSourceDo.builder()
                    .creatorName(request.getCreatorName())
                    .type(request.getType())
                    .name(request.getName())
                    .build();
        }
    }


    public DataSourceBo buildQueryDataSourcePage(QueryPageDataSourceRequest request) {
        if (request.getType() == 0) {
            return DataSourceBo.builder()
                    .creatorName(request.getCreatorName())
                    .name(request.getName())
                    .pageSize(request.getPageSize())
                    .pageNo(request.getPageNo())
                    .build();
        } else {
            return DataSourceBo.builder()
                    .creatorName(request.getCreatorName())
                    .type(request.getType())
                    .name(request.getName())
                    .pageSize(request.getPageSize())
                    .pageNo(request.getPageNo())
                    .build();
        }
    }

    public DataSourceDo buildCreateDataSource(CreateDataSourceRequest request) {

        return DataSourceDo.builder()
                .creatorName(request.getCreatorName())
                .name(request.getName())
                .content(request.getContent())
                .description(request.getDescription())
                .type(request.getType())
                .build();
    }

    public DataQueryDO createQueryExe(CreateQueryExeRequest request) {
        Long dataSourceId = request.getDataSourceId();
        if (dataSourceId == 0) {
            log.error("未知数据源Id");
        }
        try {
            Long timestamp = System.currentTimeMillis();
            DataQueryDO dataQueryDO = DataQueryDO.builder().dataSourceId(dataSourceId)
                    .name(request.getName())
                    .executorName(request.getExecutorName())
                    .status(1)
                    .createTime(timestamp).build();
            dataQueryExecuteDAO.insertDataQuery(dataQueryDO);
            return dataQueryDO;
        } catch (Exception e) {
            log.error("Create queryExe error: ", e);
            return null;
        }

    }

}
