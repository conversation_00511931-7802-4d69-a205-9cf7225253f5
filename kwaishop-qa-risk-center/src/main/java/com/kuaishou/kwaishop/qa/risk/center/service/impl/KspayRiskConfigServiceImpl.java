package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.KspayConfigScanService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KrpcKspayConfigScanServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayConfigScanByRuleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayConfigScanByRuleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayConfigScanByScriptRuleRequest;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class KspayRiskConfigServiceImpl extends KrpcKspayConfigScanServiceGrpc.KspayConfigScanServiceImplBaseV2 {
    @Autowired
    private KspayConfigScanService kspayConfigScanService;
    @Override
    public KspayConfigScanByRuleResponse kspayConfigScanByRule(KspayConfigScanByRuleRequest request) {
        log.info("[KspayConfigScanServiceImpl] kspayConfigScanByRule request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            return kspayConfigScanService.kspayConfigScanByRule(request);
        } catch (BizException e) {
            log.error("[KspayConfigScanServiceImpl] kspayConfigScanByRule bizError, exception: ", e);
            return KspayConfigScanByRuleResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayConfigScanServiceImpl] kspayConfigScanByRule error, exception: ", e);
            return KspayConfigScanByRuleResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayConfigScanByRuleResponse kspayConfigScanByScriptRule(KspayConfigScanByScriptRuleRequest request) {
        log.info("[kspayConfigScanByScriptRule] kspayConfigScanByScriptRule request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            return kspayConfigScanService.kspayConfigScanByScriptRule(request);
        } catch (BizException e) {
            log.error("[KspayConfigScanServiceImpl] kspayConfigScanByRule bizError, exception: ", e);
            return KspayConfigScanByRuleResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayConfigScanServiceImpl] kspayConfigScanByRule error, exception: ", e);
            return KspayConfigScanByRuleResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


}
