package com.kuaishou.kwaishop.qa.risk.center.db.dao.account.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.RentAccountDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.account.RentAccountMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.account.RentAccountQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.RentAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.RentalStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserLoginTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.utils.AESUtil;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-25
 */
@Repository
public class RentAccountDAOImpl extends BaseDAOImpl<RentAccountDO, RentAccountQueryCondition> implements RentAccountDAO {

    @Autowired
    private RentAccountMapper rentAccountMapper;

    @Override
    public long insert(RentAccountDO rentAccountDO) {
        return rentAccountMapper.insert(rentAccountDO);
    }

    @Override
    protected void fillQueryCondition(RentAccountQueryCondition condition, QueryWrapper<RentAccountDO> queryWrapper) {
        // 查询user_id
        if (condition.getUserId() != null && condition.getUserId() > 0L) {
            queryWrapper.and(q -> q.eq("user_id", condition.getUserId()));
        }
        if (condition.getId() != null && condition.getId() >= 0L) {
            queryWrapper.and(q -> q.eq("id", condition.getId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getUserIds())) {
            queryWrapper.and(q -> q.in("user_id", condition.getUserIds()));
        }
        if (UserLoginTypeEnum.of(condition.getLoginType()) != null && condition.getLoginType() > 0) {
            queryWrapper.and(q -> q.eq("login_type", condition.getLoginType()));
        }
        // 根据租借状态筛选
        if (RentalStatusEnum.of(condition.getRentalStatus()) != null) {
            queryWrapper.and(q -> q.eq("rental_status", condition.getRentalStatus()));
        }
        if (CollectionUtils.isNotEmpty(condition.getRentalStatuses())) {
            queryWrapper.and(q -> q.in("rental_status", condition.getRentalStatuses()));
        }
        if (CollectionUtils.isNotEmpty(condition.getCenterIds())) {
            queryWrapper.and(q -> q.in("center_id", condition.getCenterId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getTeamIds())) {
            queryWrapper.and(q -> q.in("team_id", condition.getTeamId()));
        }
        // 查询租借人
        if (condition.getBorrower() != null) {
            queryWrapper.and(q -> q.eq("borrower", condition.getBorrower()));
        }
    }

    @Override
    protected BaseMapper<RentAccountDO> getMapper() {
        return rentAccountMapper;
    }

    @Override
    public List<RentAccountDO> queryList(RentAccountBO rentAccountBo) {
        RentAccountQueryCondition condition = RentAccountQueryCondition.builder()
                .id(rentAccountBo.getId())
                .centerId(rentAccountBo.getCenterId())
                .ids(rentAccountBo.getIds())
                .teamId(rentAccountBo.getTeamId())
                .borrower(rentAccountBo.getBorrower() != null ? (rentAccountBo.getBorrower().isEmpty() ? null : rentAccountBo.getBorrower()) : null)
                .rentalStatus(rentAccountBo.getRentalStatus())
                .loginType(rentAccountBo.getLoginType())
                .loginTypes(rentAccountBo.getLoginTypes())
                .userId(rentAccountBo.getUserId())
                .build();
        return queryList(condition).stream().map(item -> {
            item.setAccount(item.getAccount());
            item.setPassword(AESUtil.decrypt(item.getPassword()));
            return item;

        }).collect(Collectors.toList());
    }

    @Override
    public void rentAccount(RentAccountBO rentAccountBo) {
        RentAccountDO rentAccountDO = RentAccountDO.builder()
                .loginType(rentAccountBo.getLoginType())
                .rentalStatus(rentAccountBo.getRentalStatus())
                .borrower(rentAccountBo.getBorrower())
                .centerId(rentAccountBo.getCenterId())
                .teamId(rentAccountBo.getTeamId())
                .duration(rentAccountBo.getDuration())
                .rentalTime(rentAccountBo.getRentalTime())
                .dueTime(rentAccountBo.getDueTime())
                .userId(rentAccountBo.getUserId())
                .creator(rentAccountBo.getBorrower())
                .modifier(rentAccountBo.getBorrower())
                .createTime(rentAccountBo.getRentalTime())
                .updateTime(rentAccountBo.getRentalTime())
                .account(rentAccountBo.getAccount())
                .password(rentAccountBo.getPassword())
                .build();
        rentAccountMapper.insert(rentAccountDO);
    }

    @Override
    public void returnAccount(RentAccountBO rentAccountBo) {
        long curTime = System.currentTimeMillis();
        rentAccountMapper.returnAccount(rentAccountBo.getRentalStatus(), curTime, rentAccountBo.getBorrower(),
                curTime, rentAccountBo.getId());
    }

    @Override
    public PageBO<RentAccountDO> queryPageList(RentAccountBO rentAccountBo) {
        RentAccountQueryCondition condition = RentAccountQueryCondition.builder()
                .id(rentAccountBo.getId())
                .centerId(rentAccountBo.getCenterId())
                .ids(rentAccountBo.getIds())
                .teamId(rentAccountBo.getTeamId())
                .borrower(rentAccountBo.getBorrower().isEmpty() ? null : rentAccountBo.getBorrower())
                .rentalStatus(rentAccountBo.getRentalStatus())
                .loginType(rentAccountBo.getLoginType())
                .loginTypes(rentAccountBo.getLoginTypes())
                .pageNo(rentAccountBo.getPageNo())
                .pageSize(rentAccountBo.getPageSize())
                .userId(rentAccountBo.getUserId())
                .build();
        PageBO<RentAccountDO> pageBO = queryPageList(condition);
        return pageBO;
    }

    @Override
    public void extendRent(RentAccountBO rentAccountBO) {
        rentAccountMapper.updateById(RentAccountDO.builder()
                .id(rentAccountBO.getId())
                .modifier(rentAccountBO.getModifier())
                .borrower(rentAccountBO.getBorrower())
                .duration(rentAccountBO.getDuration())
                .dueTime(rentAccountBO.getDueTime())
                .updateTime(rentAccountBO.getUpdateTime())
                .build());
    }

    @Override
    public List<RentAccountDO> queryListByOperator(String borrower) {

        List<RentAccountDO> rentAccountDOS = rentAccountMapper.queryListByOperator(borrower);
        if (rentAccountDOS.isEmpty()) {
            // 如果是新人没租借记录，则查询所有人
            rentAccountDOS = rentAccountMapper.queryListByAllOperator();
        }
        rentAccountDOS.stream().map(item -> {
            item.setAccount(AESUtil.decrypt(item.getAccount()));
            item.setPassword(AESUtil.decrypt(item.getPassword()));
            return item;
        }).collect(Collectors.toList());
        return rentAccountDOS;
    }
}
