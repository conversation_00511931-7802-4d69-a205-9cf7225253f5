package com.kuaishou.kwaishop.qa.risk.center.tianhe.Service;

import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskJsonMapConfigKey.lowCodeGrayConfig;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.krpc.config.annotation.KrpcReference;
import com.kuaishou.kwaishop.lingzhu.platform.service.protobuf.KrpcLingzhuPlatfromTestRpcServiceGrpc;
import com.kuaishou.kwaishop.lingzhu.platform.service.protobuf.QueryPageUrlRequest;
import com.kuaishou.kwaishop.lingzhu.platform.service.protobuf.QueryPageUrlResponse;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.BaseResponseInfo;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.CheckSubmitTestRequest;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.CheckSubmitTestResponse;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.CheckTestExitRequest;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.CheckTestExitResponse;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.DeployControlDetailInfo;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.DeployControlDetailNode;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.GetDeployControlInfoRequest;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.GetDeployControlInfoResponse;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.QuerySubmitTestCheckListRequest;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.QuerySubmitTestCheckListResponse;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.SubmitTestCheckDetail;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.service.LowCodeGrayService;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Sample.TianheExecuteSampleDO;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.Sample.TianheExecuteSampleMapper;
import com.kuaishou.kwaishop.tianhe.galax.center.client.enums.AppTestIntegrationLevelEnum;
import com.kuaishou.kwaishop.tianhe.galax.center.client.enums.ControlTypeEnum;
import com.kuaishou.kwaishop.tianhe.galax.center.client.enums.ReleaseControlConfigEnum;
import com.kuaishou.kwaishop.tianhe.galax.center.client.protobuf.GetDeployControlRequest;
import com.kuaishou.kwaishop.tianhe.galax.center.client.protobuf.GetDeployControlResponse;
import com.kuaishou.kwaishop.tianhe.galax.center.client.protobuf.GetValidationLevelRequest;
import com.kuaishou.kwaishop.tianhe.galax.center.client.protobuf.GetValidationLevelResponse;
import com.kuaishou.kwaishop.tianhe.galax.center.client.protobuf.KrpcKwaishopTianheGalaxAppServiceGrpc;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-12-16
 */
@Service
@Slf4j
public class PlatformInteractionService {
    @KrpcReference(serviceName = "kwaishop-tianhe-galax-center")
    private KrpcKwaishopTianheGalaxAppServiceGrpc.IKwaishopTianheGalaxAppService tianheGalaxAppService;

    @KrpcReference(serviceName = "kwaishop-lingzhu-platform-service")
    private KrpcLingzhuPlatfromTestRpcServiceGrpc.ILingzhuPlatfromTestRpcService lingzhuPlatfromTestRpcService;

    @Autowired
    private LowCodeGrayService lowCodeGrayService;

    @Autowired
    private TianheExecuteSampleMapper tianheExecuteSampleMapper;

    private BaseResponseInfo successBaseResponseInfo = BaseResponseInfo.newBuilder()
            .setRespCodeValue(ErrorCode.BasicErrorCode.SUCCESS.getCode())
            .setRespCodeRealValue(ErrorCode.BasicErrorCode.SUCCESS.getCode())
            .setSuccess(true).setDebugInfo("success")
            .build();

    public GetDeployControlInfoResponse getDeployControlInfoResponse(GetDeployControlInfoRequest request) {
        log.error("getDeployControlInfoResponse request {}", ObjectMapperUtils.toJSON(request));
        GetDeployControlInfoResponse.Builder responseBuilder =
                GetDeployControlInfoResponse.newBuilder();
        try {
            boolean isGray = lowCodeGrayService.checkGrayStatus(request.getTenant(),
                    request.getOuterAppKey());
            log.error("getDeployControlInfoResponse isGray {}", isGray);
            Map<String, Object> grayConfig = lowCodeGrayConfig.getMap(String.class, Object.class);
            if (isGray) {
                GetDeployControlRequest newRequest = GetDeployControlRequest.newBuilder()
                        .setAppKey(request.getOuterAppKey())
                        .build();
                GetDeployControlResponse response =
                        tianheGalaxAppService.getDeployControl(newRequest);
                log.error("getDeployControlInfoResponse response {}",
                        ObjectMapperUtils.toJSON(response));
                if (response.getResult().getCode() == 1) {
                    List<DeployControlDetailInfo> deployControlDetailInfoList = response.getDataList().stream()
                            .map(data -> {
                                List<DeployControlDetailNode> deployControlDetailNodeList = data.getDeployControlDetailNodeList().stream()
                                        .map(node -> DeployControlDetailNode.newBuilder()
                                                .setCode(ReleaseControlConfigEnum.of(node.getCode()).getType())
                                                .setName(ReleaseControlConfigEnum.of(node.getCode()).getDesc())
                                                .setSelected(node.getSelected())
                                                .build())
                                        .collect(Collectors.toList());
                                return DeployControlDetailInfo.newBuilder()
                                        .setControlType(ControlTypeEnum.of(data.getControlType()).getDesc())
                                        .addAllDeployControlDetailNode(deployControlDetailNodeList)
                                        .build();
                            })
                            .collect(Collectors.toList());
                    GetValidationLevelRequest getValidationLevelRequest =
                            GetValidationLevelRequest.newBuilder()
                                    .setAppKey(request.getOuterAppKey()).build();
                    GetValidationLevelResponse getValidationLevelResponse =
                            tianheGalaxAppService.getValidationLevel(getValidationLevelRequest);
                    String level = getValidationLevelResponse.getData().getValidationLevel();
                    if (level.equals(AppTestIntegrationLevelEnum.P0.getType())) {
                        responseBuilder.setBaseResponseInfo(successBaseResponseInfo)
                                .setConfigUrl((String) grayConfig.get("controlInfourl"))
                                .addAllDeployControlDetailInfo(deployControlDetailInfoList);
                    } else if (level.equals(AppTestIntegrationLevelEnum.P1.getType())) {
                        responseBuilder.setBaseResponseInfo(successBaseResponseInfo)
                                .setConfigUrl((String) grayConfig.get("controlInfourl"))
                                .addAllDeployControlDetailInfo(deployControlDetailInfoList);
                    } else if (level.equals(AppTestIntegrationLevelEnum.P2.getType())) {
                        responseBuilder.setBaseResponseInfo(successBaseResponseInfo)
                                .addAllDeployControlDetailInfo(deployControlDetailInfoList);
                    }
                    log.error("getDeployControlInfoResponse responseBuilder {}",
                            ObjectMapperUtils.toJSON(responseBuilder.build()));
                }
            } else {
                // 没有命中灰度的时候 不用返回测试平台链接
                responseBuilder.setBaseResponseInfo(successBaseResponseInfo)
                        .addDeployControlDetailInfo(DeployControlDetailInfo.newBuilder()
                                .setControlType(ControlTypeEnum.of(2).getDesc())
                                .addDeployControlDetailNode(DeployControlDetailNode.newBuilder()
                                        .setCode(ReleaseControlConfigEnum.of(5).getType())
                                        .setName(ReleaseControlConfigEnum.of(5).getDesc())
                                        .setSelected(true))
                                .addDeployControlDetailNode(DeployControlDetailNode.newBuilder()
                                        .setCode(ReleaseControlConfigEnum.of(6).getType())
                                        .setName(ReleaseControlConfigEnum.of(6).getDesc())
                                        .setSelected(true)));
            }
        } catch (Exception e) {
            log.error("getDeployControlInfoResponse error ", e);
            responseBuilder.setBaseResponseInfo(BaseResponseInfo.newBuilder()
                    .setRespCodeValue(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setRespCodeRealValue(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setSuccess(false).setDebugInfo("查询天河管控配置异常"));
        }
        return responseBuilder.build();
    }

    /*
        staging环境 默认返回通过，一期不做校验，保留口子,灵筑测也没有求调用
     */
    public CheckSubmitTestResponse checkSubmitTestResponse(CheckSubmitTestRequest request) {
        log.error("checkSubmitTestResponse request {}", ObjectMapperUtils.toJSON(request));
        CheckSubmitTestResponse.Builder checkSubmitTestResponse =
                CheckSubmitTestResponse.newBuilder();
        try {
            checkSubmitTestResponse.setBaseResponseInfo(BaseResponseInfo.newBuilder()
                            .setDebugInfo("")
                            .setSuccess(true)
                            .setRespCodeValue(ErrorCode.BasicErrorCode.SUCCESS.getCode())
                            .setRespCodeRealValue(ErrorCode.BasicErrorCode.SUCCESS.getCode())
                            .build())
                    .setCheckPass(true)
                    .setFailReason("")
                    .addNotification("当前页面用例全部确认通过")
                    .build();
        } catch (Exception e) {
            log.error("checkSubmitTestResponse error ",  e);
            checkSubmitTestResponse.setBaseResponseInfo(BaseResponseInfo.newBuilder()
                            .setDebugInfo("")
                            .setSuccess(false)
                            .setRespCodeValue(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                            .setRespCodeRealValue(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                            .build())
                    .setCheckPass(true)
                    .setFailReason("")
                    .addNotification("当前页面用例执行结果查询异常")
                    .build();
        }
        return checkSubmitTestResponse.build();
    }

    public CheckTestExitResponse checkTestExitResponse(CheckTestExitRequest request) {
        log.error("checkTestExitResponse request {}", ObjectMapperUtils.toJSON(request));
        CheckTestExitResponse.Builder checkTestExitResponse =
                CheckTestExitResponse.newBuilder();
        try {
            boolean isGray = lowCodeGrayService.checkGrayStatus(request.getTenant(),
                    request.getOuterAppKey());
            log.error("checkTestExitResponse isGray {}", isGray);
            if (isGray) {
                // 查询天河获取应用等级
                GetValidationLevelRequest getValidationLevelRequest =
                        GetValidationLevelRequest.newBuilder()
                                .setAppKey(request.getOuterAppKey()).build();
                GetValidationLevelResponse getValidationLevelResponse =
                        tianheGalaxAppService.getValidationLevel(getValidationLevelRequest);
                log.error("checkTestExitResponse getValidationLevelResponse {}", ObjectMapperUtils.toJSON(getValidationLevelResponse));
                String level = getValidationLevelResponse.getData().getValidationLevel();
                if (level != "" && level.equals(AppTestIntegrationLevelEnum.P0.getType())) {
                    QueryWrapper<TianheExecuteSampleDO> queryWrapper = new QueryWrapper<>();
                    String changeOrderId = request.getChangeOrderId() + "_" + request.getLastChangeDeployId() + "_"
                            + request.getLastDeployVersion();
                    queryWrapper.eq("change_order_id", changeOrderId);
                    List<TianheExecuteSampleDO> executeSampleList =
                            tianheExecuteSampleMapper.selectList(queryWrapper);
                    log.error("checkTestExitResponse executeSampleList {}", executeSampleList.toString());
                    boolean checkPass = true;
                    String failReason = "";
                    String notification = "";
                    for (TianheExecuteSampleDO record : executeSampleList) {
                        // 失败
                        if (record.getExecuteStatus() == 1) {
                            checkPass = false;
                            failReason = "页面用例比对失败，请前往测试平台确认";
                            notification = "页面用例比对失败，请前往测试平台确认";
                            break;
                        } else if (record.getExecuteStatus() == 0) { //执行中
                            notification = "用例未执行完成，请前往测试平台确认";
                        }
                    }
                    checkTestExitResponse.setBaseResponseInfo(successBaseResponseInfo)
                            .setCheckPass(checkPass)
                            .setFailReason(failReason)
                            .addNotification(notification)
                            .build();
                } else {
                    checkTestExitResponse.setBaseResponseInfo(successBaseResponseInfo)
                            .setCheckPass(true)
                            .setFailReason("")
                            .addNotification("非强校验应用,允许通过")
                            .build();
                }
            } else {
                checkTestExitResponse.setBaseResponseInfo(BaseResponseInfo.newBuilder()
                                .setRespCodeValue(1)
                                .setRespCodeRealValue(1)
                                .setSuccess(true)
                                .setDebugInfo("success")
                                .build())
                        .setCheckPass(true)
                        .setFailReason("")
                        .addNotification("未命中放量，允许直接通过")
                        .build();
            }
        } catch (Exception e) {
            log.error("checkTestExitResponse error ", e);
            checkTestExitResponse.setBaseResponseInfo(BaseResponseInfo.newBuilder()
                            .setDebugInfo("")
                            .setSuccess(false)
                            .setRespCodeValue(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                            .setRespCodeRealValue(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                            .build())
                    .setCheckPass(true)
                    .setFailReason("")
                    .addNotification("用例执行请前往测试平台确认")
                    .build();
        }
        log.error("checkTestExitResponse response {}",
                ObjectMapperUtils.toJSON(checkTestExitResponse.build()));
        return checkTestExitResponse.build();
    }

    public QuerySubmitTestCheckListResponse querySubmitTestCheckListResponse(QuerySubmitTestCheckListRequest request) {
        log.error("querySubmitTestCheckListResponse request {}", ObjectMapperUtils.toJSON(request));
        QuerySubmitTestCheckListResponse.Builder querySubmitTestCheckListResponse =
                QuerySubmitTestCheckListResponse.newBuilder();
        try {
            boolean isGray = lowCodeGrayService.checkGrayStatus(request.getTenant(),
                    request.getOuterAppKey());
            log.error("querySubmitTestCheckListResponse isGray {}", isGray);
            if (isGray) {
                Map<String, Object> grayConfig = lowCodeGrayConfig.getMap(String.class, Object.class);
                Long changeOrderId = request.getChangeOrderId();
                String lastChangeDeployId = String.valueOf(request.getLastChangeDeployId());
                String lastDeployVersion = String.valueOf(request.getLastDeployVersion());
                String appKey = request.getAppKey();
                String telent = request.getTenant();
                QueryPageUrlRequest queryPageUrlRequest =
                        QueryPageUrlRequest.newBuilder()
                                .setAppKey(appKey)
                                .setChangeOrderId(changeOrderId)
                                .setTenant(telent)
                                .build();
                log.error("querySubmitTestCheckListResponse queryPageUrlRequest {}",
                        ObjectMapperUtils.toJSON(queryPageUrlRequest));
                // 查询灵筑，获取当前变更的页面url信息
                QueryPageUrlResponse queryPageUrlResponse =
                        lingzhuPlatfromTestRpcService.queryPageUrl(queryPageUrlRequest);
                log.error("querySubmitTestCheckListResponse queryPageUrlResponse {}",
                        ObjectMapperUtils.toJSON(queryPageUrlResponse));
                int urlSize = queryPageUrlResponse.getPageUrlInfoList().size();
                if (urlSize == 0) {
                    // 当前变更里没有页面资源，所以就不返回卡点页面链接了，直接放行
                    querySubmitTestCheckListResponse.setBaseResponseInfo(BaseResponseInfo.newBuilder()
                            .setDebugInfo("")
                            .setSuccess(true)
                            .setRespCodeValue(ErrorCode.BasicErrorCode.SUCCESS.getCode())
                            .setRespCodeRealValue(ErrorCode.BasicErrorCode.SUCCESS.getCode())
                            .build());
                } else {
                    String testExitUrl =
                            (String) grayConfig.get("testExitUrl") + "changeOrderId=" + changeOrderId
                                    + "&lastChangeDeployId=" + lastChangeDeployId
                                    + "&lastDeployVersion=" + lastDeployVersion;
                    querySubmitTestCheckListResponse.setBaseResponseInfo(BaseResponseInfo.newBuilder()
                                    .setRespCodeValue(ErrorCode.BasicErrorCode.SUCCESS.getCode())
                                    .setRespCodeRealValue(ErrorCode.BasicErrorCode.SUCCESS.getCode())
                                    .setSuccess(true)
                                    .setDebugInfo("success")
                                    .build())
                            .addSubmitTestCheckDetail(SubmitTestCheckDetail.newBuilder()
                                    .setUrl(testExitUrl)
                                    .setText((String) grayConfig.get("testExitDesc"))
                                    .build());
                    log.error("querySubmitTestCheckListResponse querySubmitTestCheckListResponse {}",
                            querySubmitTestCheckListResponse);
                }
            } else {
                querySubmitTestCheckListResponse.setBaseResponseInfo(successBaseResponseInfo);
            }
        } catch (Exception e) {
            log.error("querySubmitTestCheckListResponse error {}", this.getClass().getName(),
                    e);
            querySubmitTestCheckListResponse.setBaseResponseInfo(BaseResponseInfo.newBuilder()
                    .setDebugInfo("")
                    .setSuccess(false)
                    .setRespCodeValue(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setRespCodeRealValue(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .build());
        }
        return querySubmitTestCheckListResponse.build();
    }
}
