package com.kuaishou.kwaishop.qa.risk.center.db.dao.account.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.RentTestAccountRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentTestAccountRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.account.RentTestAccountRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.account.RentAccountQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.RentAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TestAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TestAccountRentBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.RentalStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserLoginTypeEnum;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/15 15:48
 * @注释
 */
@Repository
public class RentTestAccountRecordDAOImpl extends BaseDAOImpl<RentTestAccountRecordDO, RentAccountQueryCondition>
        implements RentTestAccountRecordDAO {

    private static final int MUL = 24 * 60 * 60 * 1000;
    @Autowired
    private RentTestAccountRecordMapper rentTestAccountRecordMapper;

    @Override
    public List<RentTestAccountRecordDO> queryList(RentAccountBO rentAccountBo) {
        return rentTestAccountRecordMapper.queryList(rentAccountBo.getUserId(), rentAccountBo.getRentalStatus());
    }

    @Override
    public void rentAccount(RentAccountBO rentAccountBo) {
        RentTestAccountRecordDO rentTestAccountRecordDO = RentTestAccountRecordDO.builder()
                .loginType(rentAccountBo.getLoginType())
                .bId(rentAccountBo.getBId() == 0L ? 0L : rentAccountBo.getBId())
                .testAccountId(rentAccountBo.getUserId())
                .borrower(rentAccountBo.getBorrower())
                .duration(Long.valueOf(rentAccountBo.getDuration()))
                .rentalTime(rentAccountBo.getRentalTime())
                .dueTime(rentAccountBo.getDueTime())
                .kwaiId(rentAccountBo.getUserId())
                .creator(rentAccountBo.getBorrower())
                .modifier(rentAccountBo.getBorrower())
                .createTime(rentAccountBo.getRentalTime())
                .updateTime(rentAccountBo.getRentalTime())
                .account(rentAccountBo.getAccount())
                .proxyPassword(rentAccountBo.getPassword())
                .build();
        List<RentTestAccountRecordDO> rentTestAccountRecordDOS = rentTestAccountRecordMapper.queryByUid(rentAccountBo.getUserId());
        List<RentTestAccountRecordDO> list = rentTestAccountRecordDOS.stream()
                .filter(recordDO -> recordDO.getRentalStatus() == 1 && recordDO.getDeleted() == 0)
                .filter(recordDO -> recordDO.getBorrower().equals(rentAccountBo.getBorrower()))
                .collect(Collectors.toList());
        if (list.size() == 0) {
            rentTestAccountRecordMapper.insert(rentTestAccountRecordDO);
        }
    }

    @Override
    public void returnAccount(RentTestAccountRecordDO rentTestAccountRecordDO) {
        rentTestAccountRecordMapper.returnAccount(RentalStatusEnum.RETURNED.getCode()
                , rentTestAccountRecordDO.getModifier(), System.currentTimeMillis()
                , rentTestAccountRecordDO.getId());
    }

    @Override
    public PageBO<RentTestAccountRecordDO> queryPageList(TestAccountBO queryBO) {
        RentAccountQueryCondition condition = RentAccountQueryCondition.builder()
                .borrower(queryBO.getBorrower())
                .rentalStatus(1)
                .pageNo(queryBO.getPageNo())
                .pageSize(queryBO.getPageSize())
                .build();
        PageBO<RentTestAccountRecordDO> pageBO = queryPageList(condition);

        pageBO.setData(pageBO.getData().stream().map(item -> {
            item.setAccount(queryBO.getAccount());
            item.setProxyPassword(queryBO.getPassword());
            return item;
        }).collect(Collectors.toList()));

        return pageBO;
    }

    @Override
    public List<RentTestAccountRecordDO> queryRentTestAccountList(TestAccountRentBO testAccountRentBO) {
        RentAccountQueryCondition condition = RentAccountQueryCondition.builder()
                .borrower(testAccountRentBO.getBorrower())
                .rentalStatus(testAccountRentBO.getRentalStatus())
                .userId(testAccountRentBO.getUserId())
                .build();
        return queryList(condition);
    }


    @Override
    protected void fillQueryCondition(RentAccountQueryCondition condition,
                                      QueryWrapper<RentTestAccountRecordDO> queryWrapper) {
        // 查询user_id
        if (condition.getUserId() != null && condition.getUserId() > 0L) {
            queryWrapper.and(q -> q.eq("test_account_id", condition.getUserId()));
        }
        if (condition.getBId() != null && condition.getBId() > 0L) {
            queryWrapper.and(q -> q.eq("b_id", condition.getBId()));
        }
        if (condition.getId() != null && condition.getId() >= 0L) {
            queryWrapper.and(q -> q.eq("id", condition.getId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getUserIds())) {
            queryWrapper.and(q -> q.in("kwai_id", condition.getUserIds()));
        }
        if (UserLoginTypeEnum.of(condition.getLoginType()) != null && condition.getLoginType() > 0) {
            queryWrapper.and(q -> q.eq("login_type", condition.getLoginType()));
        }
        // 根据租借状态筛选
        if (RentalStatusEnum.of(condition.getRentalStatus()) != null) {
            queryWrapper.and(q -> q.eq("rental_status", condition.getRentalStatus()));
        }
        // 查询租借人
        if (condition.getBorrower() != null) {
            queryWrapper.and(q -> q.eq("borrower", condition.getBorrower()));
        }
    }

    @Override
    protected BaseMapper<RentTestAccountRecordDO> getMapper() {
        return rentTestAccountRecordMapper;
    }
}
