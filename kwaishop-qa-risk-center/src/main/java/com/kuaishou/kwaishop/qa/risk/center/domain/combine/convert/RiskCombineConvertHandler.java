package com.kuaishou.kwaishop.qa.risk.center.domain.combine.convert;

import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.QueryGroupByRiskSummaryDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.QueryRiskSummaryDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.RiskSummaryDataDTO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-26
 */
public interface RiskCombineConvertHandler {

    CombineQueryParam buildSummaryQueryParam(QueryRiskSummaryDataRequest request);

    CombineQueryParam buildGroupingByQueryParam(QueryGroupByRiskSummaryDataRequest request);

    RiskSummaryDataDTO buildSummaryDTO(CombineBO combineBO);
}
