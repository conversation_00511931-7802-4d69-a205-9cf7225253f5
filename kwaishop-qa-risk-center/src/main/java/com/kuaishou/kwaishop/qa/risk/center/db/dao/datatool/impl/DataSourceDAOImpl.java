package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.DataBaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataSourceDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataSourceDo;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.datatool.DataSourceMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.datatool.DataSourceQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.DataSourceBo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-11
 */
@Repository
public class DataSourceDAOImpl extends DataBaseDAO<DataSourceDo, DataSourceQueryCondition> implements DataSourceDAO {


    @Autowired
    private DataSourceMapper dataSourceMapper;

    @Override
    public List<DataSourceDo> queryDataSourceList(DataSourceDo dataSourceDo) {

        return dataSourceMapper.queryAllDataSource();

    }

    @Override
    public PageBO<DataSourceDo> queryPageDataSourceList(DataSourceBo dataSourceBo) {
        DataSourceQueryCondition queryCondition = DataSourceQueryCondition.builder()
                .creatorName(dataSourceBo.getCreatorName())
                .sourceType(dataSourceBo.getType())
                .sourceName(dataSourceBo.getName())
                .orderByCreateTimeDesc(true)
                .pageNo(dataSourceBo.getPageNo())
                .pageSize(dataSourceBo.getPageSize())
                .build();

        return queryPageList(queryCondition);
    }

    @Override
    public long insertDataSource(DataSourceDo dataSourceDo) {
        dataSourceDo.setCreateTime(System.currentTimeMillis());
        dataSourceDo.setUpdateTime(System.currentTimeMillis());
        return dataSourceMapper.insertDataSource(dataSourceDo);
    }

    @Override
    public long updateDataSource(DataSourceDo dataSourceDo) {
        //dataSourceDo.setUpdateTime(System.currentTimeMillis());
        return updateSelectiveById(dataSourceDo);
    }

    @Override
    public int logicDeleted(Long id, String operator) {
        return dataSourceMapper.logicDeleted(operator, id);
    }

    @Override
    public List<DataSourceDo> queryDataSourceLikeName(String name) {
        return dataSourceMapper.queryDataSourceLikeName(name);
    }


    @Override
    public DataSourceDo queryDataSourceById(Long id) {
        return dataSourceMapper.queryDataSourceById(id);
    }

    @Override
    protected void fillQueryCondition(DataSourceQueryCondition condition, QueryWrapper<DataSourceDo> queryWrapper) {

        // 只输入名称
        if (StringUtils.isNotBlank(condition.getSourceName())) {
            queryWrapper.like("name", condition.getSourceName());

        }

        if (condition.getSourceType() != null && condition.getSourceType() > 0) {
            queryWrapper.eq("type", condition.getSourceType());
        }

        if (StringUtils.isNotBlank(condition.getCreatorName())) {
            queryWrapper.eq("creator_name", condition.getCreatorName());
        }


    }

    @Override
    protected BaseMapper<DataSourceDo> getMapper() {
        return dataSourceMapper;
    }
}
