package com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundsRiskFeatureQueryCondition;
@Repository
@Component
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public class RiskFeatureDAO {
    private BeanPropertyRowMapper<FundsRiskFeatureDO> rowMapper = new BeanPropertyRowMapper<>(FundsRiskFeatureDO.class);

    private static final Logger logger = LoggerFactory.getLogger(RiskFeatureDAO.class);

    public int updateRiskFeatureTaskStatus(long featureId, int testType, NamedParameterJdbcTemplate jdbcTemplate) {
        String sql = String.format(
                "UPDATE %s SET test_type = :testType WHERE feature_id = :featureId",
                getTableName()
        );
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("featureId", featureId);
        params.addValue("testType", testType);
        int rowsUpdated = jdbcTemplate.update(sql, params);
        logger.info("Updated {} rows for featureId: {}, testType: {}", rowsUpdated, featureId, testType);
        return rowsUpdated;
    }

    public int updateRiskFeatureBusinessDomain(String featureId, String businessDomain, NamedParameterJdbcTemplate jdbcTemplate) {
        String sql = String.format(
                "UPDATE %s SET business_domain = :businessDomain WHERE feature_id = :featureId",
                getTableName()
        );
        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("featureId", featureId);
        params.addValue("businessDomain", businessDomain);
        int rowsUpdated = jdbcTemplate.update(sql, params);
        return rowsUpdated;
    }

    // timeType为true表示用updateTime查询, 否则用createTime
    public List<FundsRiskFeatureDO> queryFeatureList(boolean timeType, FundsRiskFeatureQueryCondition condition,
                                                     int offset, int limit, int defaultPageSize,
                                                     NamedParameterJdbcTemplate jdbcTemplate) {
        // 如果没有传递 offset 和 limit，则设置默认值
        if (offset < 0) {
            offset = 0;
        }
        if (limit <= 0) {
            limit = defaultPageSize;
        }
        StringBuilder sqlBuilder = new StringBuilder(String.format(
                "SELECT * FROM %s WHERE 1=1",
                getTableName()
        ));
        MapSqlParameterSource params = new MapSqlParameterSource();

        // 添加条件
        if (condition.getDepartment() != null && !condition.getDepartment().trim().isEmpty()) {
            sqlBuilder.append(" AND department = :department");
            params.addValue("department", condition.getDepartment().trim());
        }
        if (condition.getBusinessDomain() != null && !condition.getBusinessDomain().trim().isEmpty()) {
            sqlBuilder.append(" AND business_domain = :businessDomain");
            params.addValue("businessDomain", condition.getBusinessDomain().trim());
        }
        if (condition.getFeatureId() != null && !condition.getFeatureId().trim().isEmpty()) {
            sqlBuilder.append(" AND feature_id = :featureId");
            params.addValue("featureId", condition.getFeatureId().trim());
        }
        if (condition.getFeatureName() != null && !condition.getFeatureName().trim().isEmpty()) {
            sqlBuilder.append(" AND feature_name LIKE :featureName");
            params.addValue("featureName", "%" + condition.getFeatureName().trim() + "%");
        }
        if (condition.getTeamWorker() != null && !condition.getTeamWorker().trim().isEmpty()) {
            sqlBuilder.append(" AND team_worker LIKE :teamWorker");
            params.addValue("teamWorker", "%" + condition.getTeamWorker().trim() + "%");
        }
        if (condition.getCreator() != null && !condition.getCreator().trim().isEmpty()) {
            sqlBuilder.append(" AND creator = :creator");
            params.addValue("creator", condition.getCreator().trim());
        }
        if (condition.getStatus() != null && !condition.getStatus().trim().isEmpty()) {
            sqlBuilder.append(" AND status = :status");
            params.addValue("status", condition.getStatus());
        }
        if (condition.getRiskPassStatus() != null && !condition.getRiskPassStatus().trim().isEmpty()) {
            sqlBuilder.append(" AND risk_pass_status = :riskPassStatus");
            params.addValue("riskPassStatus", condition.getRiskPassStatus());
        }
        if (condition.getIsRisk() != null && (condition.getIsRisk() == 1 || condition.getIsRisk() == 2)) {
            sqlBuilder.append(" AND is_risk = :isRisk");
            params.addValue("isRisk", condition.getIsRisk());
        }

        // 处理 testType 的逻辑
        if (condition.getTestType() != null) {
            switch (condition.getTestType()) {
                case 0:
                    sqlBuilder.append(" AND (test_type = 1 OR test_type = 2 OR test_type IS NULL)");
                    break;
                case 1:
                    sqlBuilder.append(" AND test_type = :testType");
                    params.addValue("testType", 1);
                    break;
                case 2:
                    sqlBuilder.append(" AND (test_type = 2 OR test_type IS NULL)");
                    break;
                default:
                    // 其他情况不做处理
                    break;
            }
        }

        // 处理时间范围
        if (!timeType) {
            if (condition.getStartTime() != null && condition.getStartTime() > 0) {
                sqlBuilder.append(" AND update_time >= :startTime");
                params.addValue("startTime", condition.getStartTime());
            }
            if (condition.getEndTime() != null && condition.getEndTime() > 0) {
                sqlBuilder.append(" AND update_time <= :endTime");
                params.addValue("endTime", condition.getEndTime());
            }
        } else {
            if (condition.getStartTime() != null && condition.getStartTime() > 0) {
                sqlBuilder.append(" AND create_time >= :startTime");
                params.addValue("startTime", condition.getStartTime());
            }
            if (condition.getEndTime() != null && condition.getEndTime() > 0) {
                sqlBuilder.append(" AND create_time <= :endTime");
                params.addValue("endTime", condition.getEndTime());
            }
        }

        // 动态添加排序条件
        if (!timeType) {
            sqlBuilder.append(" ORDER BY update_time DESC");
        } else {
            sqlBuilder.append(" ORDER BY create_time DESC");
        }

        sqlBuilder.append(" LIMIT :limit OFFSET :offset");
        params.addValue("limit", limit);
        params.addValue("offset", offset);
        String sql = sqlBuilder.toString();
        logger.info("queryFeatureList sql: {}", sql);
        logger.info("queryFeatureList params: {}", params); // 打印参数
        return jdbcTemplate.query(sql, params, rowMapper);
    }

    public long countFeatures(boolean timeType, FundsRiskFeatureQueryCondition condition, NamedParameterJdbcTemplate jdbcTemplate) {
        StringBuilder sqlBuilder = new StringBuilder(String.format(
                "SELECT COUNT(*) FROM %s WHERE 1=1",
                getTableName()
        ));
        MapSqlParameterSource params = new MapSqlParameterSource();

        // 添加条件
        if (condition.getDepartment() != null && !condition.getDepartment().trim().isEmpty()) {
            sqlBuilder.append(" AND department = :department");
            params.addValue("department", condition.getDepartment().trim());
        }
        if (condition.getBusinessDomain() != null && !condition.getBusinessDomain().trim().isEmpty()) {
            sqlBuilder.append(" AND business_domain = :businessDomain");
            params.addValue("businessDomain", condition.getBusinessDomain().trim());
        }
        if (condition.getFeatureId() != null && !condition.getFeatureId().trim().isEmpty()) {
            sqlBuilder.append(" AND feature_id = :featureId");
            params.addValue("featureId", condition.getFeatureId().trim());
        }
        if (condition.getFeatureName() != null && !condition.getFeatureName().trim().isEmpty()) {
            sqlBuilder.append(" AND feature_name LIKE :featureName");
            params.addValue("featureName", "%" + condition.getFeatureName().trim() + "%");
        }
        if (condition.getTeamWorker() != null && !condition.getTeamWorker().trim().isEmpty()) {
            sqlBuilder.append(" AND team_worker LIKE :teamWorker");
            params.addValue("teamWorker", "%" + condition.getTeamWorker().trim() + "%");
        }
        if (condition.getCreator() != null && !condition.getCreator().trim().isEmpty()) {
            sqlBuilder.append(" AND creator = :creator");
            params.addValue("creator", condition.getCreator().trim());
        }
        if (condition.getStatus() != null && !condition.getStatus().trim().isEmpty()) {
            sqlBuilder.append(" AND status = :status");
            params.addValue("status", condition.getStatus());
        }
        if (condition.getRiskPassStatus() != null && !condition.getRiskPassStatus().trim().isEmpty()) {
            sqlBuilder.append(" AND risk_pass_status = :riskPassStatus");
            params.addValue("riskPassStatus", condition.getRiskPassStatus());
        }
        if (condition.getIsRisk() != null && (condition.getIsRisk() == 1 || condition.getIsRisk() == 2)) {
            sqlBuilder.append(" AND is_risk = :isRisk");
            params.addValue("isRisk", condition.getIsRisk());
        }

        // 处理 testType 的逻辑
        if (condition.getTestType() != null) {
            switch (condition.getTestType()) {
                case 0:
                    sqlBuilder.append(" AND (test_type = 1 OR test_type = 2 OR test_type IS NULL)");
                    break;
                case 1:
                    sqlBuilder.append(" AND test_type = :testType");
                    params.addValue("testType", 1);
                    break;
                case 2:
                    sqlBuilder.append(" AND (test_type = 2 OR test_type IS NULL)");
                    break;
                default:
                    // 其他情况不做处理
                    break;
            }
        }

        // 处理时间范围
        if (!timeType) {
            if (condition.getStartTime() != null && condition.getStartTime() > 0) {
                sqlBuilder.append(" AND update_time >= :startTime");
                params.addValue("startTime", condition.getStartTime());
            }
            if (condition.getEndTime() != null && condition.getEndTime() > 0) {
                sqlBuilder.append(" AND update_time <= :endTime");
                params.addValue("endTime", condition.getEndTime());
            }
        } else {
            if (condition.getStartTime() != null && condition.getStartTime() > 0) {
                sqlBuilder.append(" AND create_time >= :startTime");
                params.addValue("startTime", condition.getStartTime());
            }
            if (condition.getEndTime() != null && condition.getEndTime() > 0) {
                sqlBuilder.append(" AND create_time <= :endTime");
                params.addValue("endTime", condition.getEndTime());
            }
        }

        String sql = sqlBuilder.toString();
        logger.info("countFeatures sql: {}", sql);
        logger.info("countFeatures params: {}", params); // 打印参数
        Number count = jdbcTemplate.queryForObject(sql, params, Long.class);
        return count.longValue();
    }

    private String getTableName() {
        return "funds_risk_feature_view";
    }
}