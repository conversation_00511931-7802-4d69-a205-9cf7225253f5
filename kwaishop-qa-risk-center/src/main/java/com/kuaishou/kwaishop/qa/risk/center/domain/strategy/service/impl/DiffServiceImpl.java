package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.impl;

import java.util.List;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.dao.TaskDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.DiffResult;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.PolicyTestResponse;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTestDataPool;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTrafficRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.DiffService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.StrategyTrafficRecordService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-29
 */
@Service
@Slf4j
public class DiffServiceImpl implements DiffService {


    @Autowired
    private StrategyTrafficRecordService strategyTrafficRecordService;

    @Autowired
    private TaskDAOImpl taskDAO;

    @Override
    public DiffResult calculateDiff(StrategyTestDataPool strategyTestDataPool, PolicyTestResponse policyTestResponse) {
        //TODO 计算diff，后续可以配置一系列降噪
        DiffResult.DiffResultBuilder diffResultBuilder = null;
        if (isDiff(strategyTestDataPool, policyTestResponse)) {
            diffResultBuilder = DiffResult.builder()
                    .paramJson(strategyTestDataPool.getFeatureContext())
                    .expect(combineListsToJson(strategyTestDataPool.getActionIds(), strategyTestDataPool.getStrategyIds()))
                    .actual(combineListsToJson(policyTestResponse.getActionIds(), policyTestResponse.getStrategyIds()))
                    .sceneKey(strategyTestDataPool.getSceneKey())
                    .reason("返回的策略结果不一致");
        }
        if (diffResultBuilder == null) {
            return null;
        }

        return diffResultBuilder.build();
    }

    @Override
    public void calculateDiffToDataPool(StrategyTestDataPool strategyTestDataPool, PolicyTestResponse policyTestResponse, String laneId) {
        //TODO 计算diff，后续可以配置一系列降噪
        strategyTestDataPool.setActualActionIds(policyTestResponse.getActionIds());
        strategyTestDataPool.setActualStrategyIds(policyTestResponse.getStrategyIds());

        Boolean diff = isDiff(strategyTestDataPool, policyTestResponse);
        strategyTestDataPool.setDiffConclusion(diff ? 2 : 1);
        strategyTestDataPool.setReplayTime(System.currentTimeMillis());

        strategyTestDataPool.setReplayLaneId(laneId.isEmpty() ? "PRT.test" : laneId);
    }

    public Boolean isDiff(StrategyTestDataPool strategyTestDataPool, PolicyTestResponse policyTestResponse) {
//        return true; //先mock结果，默认都有diff
        return !(strategyTestDataPool.getStrategyIds().equals(policyTestResponse.getStrategyIds())
                &&
                strategyTestDataPool.getActionIds().equals(policyTestResponse.getActionIds()));
    }

    public String combineListsToJson(List<String> actionIds, List<String> strategyIds) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("actionIds", actionIds);
        jsonObject.put("strategyIds", strategyIds);
        return jsonObject.toString();
    }

    @Override
    public void changeDiffConclusion(ReplayRequest request) {
        //先拿到taskId
        String taskId = request.getTaskId().isEmpty() ? queryTaskIdByCaseId(request.getIdList().get(0)) : request.getTaskId();
        //然后把，task任务下所有case拿到
        List<StrategyTrafficRecordDO> strategyTrafficRecordDOS = strategyTrafficRecordService.queryAllCaseByTaskId(taskId);
        //然后更新diff的结论
        long totalCount = strategyTrafficRecordDOS.size();
        long diffCount = strategyTrafficRecordDOS.stream()
                .filter(record -> record.getDiffConclusion() == 2)
                .count();
        double diffRate = (totalCount == 0) ? 0 : (double) diffCount / totalCount;

        String res = String.format("%.2f%%", diffRate * 100);

        // 更新结论
        taskDAO.updateConclusion(taskId, res);
    }

    private String queryTaskIdByCaseId(Long caseId) {
        return strategyTrafficRecordService.queryTaskIdByCaseId(caseId);
    }

}
