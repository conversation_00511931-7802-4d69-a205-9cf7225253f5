package com.kuaishou.kwaishop.qa.risk.center.domain.datatool.biz;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DATA_SOURCE_CREATE_PARAM_ERROR;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.kuaishou.intown.json.JSON;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataQueryExecuteDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataSourceDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataQueryDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataSourceDo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.ExecuteGrpcQueryRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.ExecuteGrpcQueryResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryExeResult;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryExeResultByDataSourceIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryExeResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryExeResultResponse;

import kat.framework.format.model.EnvType;
import kat.framework.grpc2.KatGrpcClientV2;
import kat.framework.grpc2.model.KatGrpcEnvInfoParam;
import kat.framework.grpc2.model.KatGrpcFullRequest;
import kat.framework.grpc2.model.KatGrpcFullResponse;
import kat.framework.grpc2.model.KatGrpcRequestParam;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2022-11-14
 */
@Service
@Slf4j
public class DataQueryExeBizService {
    @Autowired
    private DataQueryExecuteDAO dataQueryExecuteDAO;

    @Autowired
    private DataSourceDAO dataSourceDAO;



    public ExecuteGrpcQueryResponse callKessMethod(ExecuteGrpcQueryRequest request) {
        if (request.getDataSourceId() == 0) {
            throw new BizException(DATA_SOURCE_CREATE_PARAM_ERROR);
        }
        DataSourceDo dataSourceDo = dataSourceDAO.queryDataSourceById((long) request.getDataSourceId());
        DataQueryDO dataQueryDO = dataQueryExecuteDAO.findById(request.getDataQueryExeId());

        this.executeGrpcQuery(dataSourceDo, dataQueryDO);
        // 获取结果
        return ExecuteGrpcQueryResponse.newBuilder().setResult(1).build();
    }

    public boolean executeGrpcQuery(DataSourceDo dataSourceDo, DataQueryDO dataQueryDO) {
        Long dataQueryId = dataQueryDO.getId();
        JSONObject content = JSONObject.fromObject(dataSourceDo.getContent());

        String bizDef = null;
        String serviceName = null;
        String methodName = null;

        String paramsString = null;

        EnvType envType = EnvType.PRODUCT;

        String laneId = null;

        if (content.containsKey("bizDef")) {
            bizDef = content.getString("bizDef");
        }
        if (content.containsKey("serviceName")) {
            serviceName = content.getString("serviceName");
        }
        if (content.containsKey("methodName")) {
            methodName = content.getString("methodName");
        }
        if (content.containsKey("queryParamsJSON")) {
            paramsString = content.getString("queryParamsJSON");
        }
        if (content.containsKey("envType")) {
            int env = content.getInt("envType");
            switch (env) {
                case 0:
                    envType = EnvType.STAGING;
                case 1:
                    envType = EnvType.CANDIDATE;
                case 2:
                    envType = EnvType.PRODUCT;
                default:
                    envType = EnvType.STAGING;
            }
        }

        if (content.containsKey("laneId")) {
            laneId = content.getString("laneId");
        }


        JSONObject jsonBody = JSONObject.fromObject(JSON.parseObject(paramsString));


        Map<String, Object> result = Maps.newHashMap();
        JSONObject requestContent = new JSONObject();
        JSONObject responseContent = new JSONObject();

        // 构建请求
        KatGrpcRequestParam katGrpcRequestParam = KatGrpcRequestParam.builder()
                .grpcServiceName(serviceName)
                .grpcFullMethodName(methodName)
                .bizDef(bizDef)
                .requestParam((Map<String, Object>) jsonBody)
                .build();
        KatGrpcEnvInfoParam katGrpcEnvInfoParam = KatGrpcEnvInfoParam.builder()
                .envType(envType)
                .laneId(laneId)
                // 目前没有权限，强制走中转
                .isTransit(true)
                .build();
        KatGrpcFullRequest katGrpcFullRequest = KatGrpcFullRequest.builder()
                .katGrpcRequestParam(katGrpcRequestParam)
                .katGrpcEnvInfoParam(katGrpcEnvInfoParam)
                .build();
        // 发起请求
        KatGrpcFullResponse katGrpcFullResponse = KatGrpcClientV2.call(katGrpcFullRequest);
        // 获取结果
        responseContent.put("responseBody", katGrpcFullResponse.getResponseBody());
        responseContent.put("originKatGrpcRequestParam", katGrpcFullResponse.getOriginKatGrpcRequestParam());


        // 更新结果
        result.put("request", requestContent);
        result.put("response", responseContent);
        dataQueryDO.setResult(new Gson().toJson(result));
        dataQueryExecuteDAO.updateSelectiveById(dataQueryDO);
        return false;
    }

    public QueryExeResultResponse queryExeResult(QueryExeResultRequest request)  {
        if (request.getQueryExeId() <= 0) {
            throw new BizException(DATA_SOURCE_CREATE_PARAM_ERROR);
        }

        DataQueryDO dataQueryDO = dataQueryExecuteDAO.findById(request.getQueryExeId());
        if (dataQueryDO.getResult() == null) {
            dataQueryDO.setResult("");
        }
        if (dataQueryDO.getLog() == null) {
            dataQueryDO.setLog("");
        }
        QueryExeResult result = QueryExeResult.newBuilder()
                .setId(dataQueryDO.getId())
                .setDataSourceId(dataQueryDO.getDataSourceId())
                .setName(dataQueryDO.getName())
                .setExecutorName(dataQueryDO.getExecutorName())
                .setStatus(dataQueryDO.getStatus())
                .setCreateTime(dataQueryDO.getCreateTime())
                .setStartTime(dataQueryDO.getStartTime())
                .setEndTime(dataQueryDO.getEndTime())
                .setResult(dataQueryDO.getResult())
                .setLog(dataQueryDO.getLog())
                .build();
        return QueryExeResultResponse.newBuilder()
                .setResult(1)
                .setData(result)
                .build();
    }

    public QueryExeResultResponse queryExeResultByDataSourceId(QueryExeResultByDataSourceIdRequest request)  {
        if (request.getDataSourceId() == 0) {
            throw new BizException(DATA_SOURCE_CREATE_PARAM_ERROR);
        }

        DataQueryDO dataQueryDO = dataQueryExecuteDAO.findByDataSourceId(request.getDataSourceId());
        if (dataQueryDO.getResult() == null) {
            dataQueryDO.setResult("");
        }
        if (dataQueryDO.getLog() == null) {
            dataQueryDO.setLog("");
        }
        QueryExeResult result = QueryExeResult.newBuilder()
                .setId(dataQueryDO.getId())
                .setDataSourceId(dataQueryDO.getDataSourceId())
                .setName(dataQueryDO.getName())
                .setExecutorName(dataQueryDO.getExecutorName())
                .setStatus(dataQueryDO.getStatus())
                .setCreateTime(dataQueryDO.getCreateTime())
                .setStartTime(dataQueryDO.getStartTime())
                .setEndTime(dataQueryDO.getEndTime())
                .setResult(dataQueryDO.getResult())
                .setLog(dataQueryDO.getLog())
                .build();
        return QueryExeResultResponse.newBuilder()
                .setResult(1)
                .setData(result)
                .build();
    }

}
