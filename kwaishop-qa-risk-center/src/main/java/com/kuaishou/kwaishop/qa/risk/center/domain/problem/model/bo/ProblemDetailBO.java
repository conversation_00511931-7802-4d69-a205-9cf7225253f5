package com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.bo;

import com.kuaishou.kwaishop.qa.risk.center.domain.detail.model.bo.DetailBaseBO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ProblemDetailBO extends DetailBaseBO {

    /**
     * id
     */
    private Long id;

    /**
     * 外部链接
     */
    private String problemLink;

    /**
     * 问题描述
     */
    private String problemDesc;

    /**
     * 问题原因
     */
    private String problemReason;

    /**
     * 漏测原因
     */
    private String missingReason;

    /**
     * 问题开始时间
     */
    private Long problemStartTime;

    /**
     * 问题结束时间
     */
    private Long problemEndTime;

    /**
     * 问题名称
     */
    private String name;

    /**
     * 问题类型， 1-故障，2-问题
     */
    private Integer detailType;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 是否漏测
     */
    private Integer missingType;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 页容量
     */
    private Integer pageSize;

    /**
     * 操作人
     */
    private String operator;
}
