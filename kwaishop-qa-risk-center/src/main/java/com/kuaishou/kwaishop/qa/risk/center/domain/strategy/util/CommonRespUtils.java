package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.util;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.CommonResponse;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-17
 */
public class CommonRespUtils {

    public static CommonResponse successResp() {
        return CommonResponse.newBuilder()
                .setResult(1)
                .setErrMsg("success")
                .build();
    }

    public static CommonResponse errorResp(Exception e) {
        return CommonResponse.newBuilder()
                .setResult(0)
                .setErrMsg(e.getMessage())
                .build();
    }
}
