package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.biz.DataScenarioBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CreateDataScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CreateDataScenarioResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DeleteCaseFromScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DeleteCaseFromScenarioResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.InsertCaseIntoScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.InsertCaseIntoScenarioResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.KrpcDataScenarioDomainServiceGrpc.DataScenarioDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryCaseExeByScenarioExeIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryCaseExeByScenarioExeIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseByScenarioIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseByScenarioIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryPageDataScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryPageDataScenarioResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.RunDataScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.RunDataScenarioResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.ScenarioExeResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.ScenarioExeResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.UpdateDataScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.UpdateDataScenarioResponse;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2023-03-16
 */

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class DataScenarioDomainServiceImpl extends DataScenarioDomainServiceImplBaseV2 {
    @Autowired
    private DataScenarioBizService dataScenarioBizService;

    @Override
    public CreateDataScenarioResponse createDataScenario(CreateDataScenarioRequest request) {
        log.info("[DataSourceDomainServiceImpl] createDataSource request: {}", ProtobufUtil.protoToJsonString(request));
        return dataScenarioBizService.createDataScenario(request);
    }

    @Override
    public UpdateDataScenarioResponse updateDataScenario(UpdateDataScenarioRequest request) {
        log.info("[DataSourceDomainServiceImpl] updateDataSource request: {}", ProtobufUtil.protoToJsonString(request));
        return dataScenarioBizService.updateDataScenario(request);
    }

    @Override
    public InsertCaseIntoScenarioResponse insertCaseIntoScenario(InsertCaseIntoScenarioRequest request) {
        log.info("[DataSourceDomainServiceImpl] updateDataSource request: {}", ProtobufUtil.protoToJsonString(request));
        return dataScenarioBizService.insertCaseIntoScenario(request);
    }

    @Override
    public DeleteCaseFromScenarioResponse deleteCaseFromScenario(DeleteCaseFromScenarioRequest request) {
        log.info("[DataSourceDomainServiceImpl] updateDataSource request: {}", ProtobufUtil.protoToJsonString(request));
        return dataScenarioBizService.deleteCaseFromScenario(request);
    }

    @Override
    public QueryDataCaseByScenarioIdResponse queryDataCaseByScenarioId(QueryDataCaseByScenarioIdRequest request) {
        log.info("[DataSourceDomainServiceImpl] updateDataSource request: {}", ProtobufUtil.protoToJsonString(request));
        return dataScenarioBizService.queryDataCaseByScenarioId(request);
    }

    @Override
    public QueryDataCaseByScenarioIdResponse queryDataCaseWithoutScenario(QueryDataCaseByScenarioIdRequest request) {
        log.info("[DataSourceDomainServiceImpl] updateDataSource request: {}", ProtobufUtil.protoToJsonString(request));
        return dataScenarioBizService.queryDataCaseWithoutScenario(request);
    }

    @Override
    public RunDataScenarioResponse runDataScenario(RunDataScenarioRequest request) {
        log.info("[DataSourceDomainServiceImpl] updateDataSource request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            return dataScenarioBizService.runDataScenario(request);
        } catch (Exception ex) {
            return RunDataScenarioResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ex.getMessage()).build();
        }
    }

    @Override
    public QueryPageDataScenarioResponse queryPageDataScenario(QueryPageDataScenarioRequest request) {
        log.info("[DataSourceDomainServiceImpl] queryPageDataScenario request: {}", ProtobufUtil.protoToJsonString(request));
        return dataScenarioBizService.queryPageDataScenario(request);
    }

    @Override
    public ScenarioExeResultResponse queryPageScenarioExeResult(ScenarioExeResultRequest request) {
        log.info("[DataSourceDomainServiceImpl] queryPageScenarioExeResult request: {}", ProtobufUtil.protoToJsonString(request));
        return dataScenarioBizService.queryPageScenarioExeResult(request);
    }

    @Override
    public QueryCaseExeByScenarioExeIdResponse queryCaseExeByScenarioExeId(QueryCaseExeByScenarioExeIdRequest request) {
        log.info("[DataSourceDomainServiceImpl] queryCaseExeByScenarioExeId request: {}", ProtobufUtil.protoToJsonString(request));
        return dataScenarioBizService.queryCaseExeByScenarioExeId(request);
    }

}
