package com.kuaishou.kwaishop.qa.risk.center.domain.errorcode;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.errorcode.BizAreaDO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.AddBizAreaRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.BizAreaListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.BizAreaListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.DeleteBizAreaRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.UpdateBizAreaRequest;

public interface BizAreaService {

    void addBizArea(AddBizAreaRequest areaRequest);

    BizAreaDO getBizAreaById(Long id);

    void updateBizArea(UpdateBizAreaRequest updateRequest);

    void deleteBizArea(DeleteBizAreaRequest deleteRequest);

    BizAreaListResponse getBizAreaList(BizAreaListRequest listRequest);

    BizAreaDO queryByAreaCode(String areaCode);

}
