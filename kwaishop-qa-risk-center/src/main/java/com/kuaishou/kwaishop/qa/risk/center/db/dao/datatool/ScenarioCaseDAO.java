package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.ScenarioCaseDO;

/**
 * <AUTHOR> <zhang<PERSON><EMAIL>>
 * Create on 2023-03-15
 */
public interface ScenarioCaseDAO {

    long insertScenarioCase(ScenarioCaseDO scenarioCaseDO);

    long updateScenarioCase(ScenarioCaseDO scenarioCaseDO);

    int logicDeleted(Long id, String operator);

    List<Long> queryCaseIdList(Long scenarioId);

    List<ScenarioCaseDO> queryScenarioCaseList(ScenarioCaseD<PERSON> scenarioCaseDO);

    Long queryIdByScenarioIdCaseId(Long scenarioId, Long caseId);

    ScenarioCaseDO queryScenarioCaseById(Long id);
}
