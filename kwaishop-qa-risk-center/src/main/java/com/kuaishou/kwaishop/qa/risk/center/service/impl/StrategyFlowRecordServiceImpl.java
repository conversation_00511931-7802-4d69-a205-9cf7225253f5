package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.kuaishou.kconf.common.json.JsonMapperUtils;
import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.config.constants.QueryTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordStartEndPDate;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.PageResult;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecodeDetailClickHouseDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowRecordBo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.HiveRecordFetchService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.RecordService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.hiveQuery.OneServiceSqlQueryClient;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.util.CommonRespUtils;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordAddRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordAddResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordDetailListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordDetailListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordDetailResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordVoResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetAllInstantsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetAllInstantsResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetDateFromCacheRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetDateFromCacheResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetFlowRecordAnalysisResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetFlowRecordAnalysisResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetFlowRecordAnalysisResultWithMdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetFlowRecordAnalysisResultWithMdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetFlowRecordDetailInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetFlowRecordDetailInfoResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.KrpcStrategyFlowRecordServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.OneServiceQueryTestRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.OneServiceQueryTestResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.SetRedisRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.SetRedisResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StrategyFlowTestRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StrategyFlowTestResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestOneServiceRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestOneServiceResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-14
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class StrategyFlowRecordServiceImpl
        extends KrpcStrategyFlowRecordServiceGrpc.StrategyFlowRecordServiceImplBaseV2 {

    @Autowired
    private HiveRecordFetchService hiveRecordFetchService;

    @Autowired
    private RecordService recordService;

    @Autowired
    private OneServiceSqlQueryClient oneServiceSqlQueryClient;

    @Override
    public FlowRecordListResponse flowRecordList(FlowRecordListRequest request) {
        log.info("StrategyFlowRecordServiceImpl.strategyFlowRecordList.request:{}", JsonMapperUtils.toJson(request));
        try {
            PageResult<StrategyFlowRecordBo> result = recordService.flowRecordList(request);
            return FlowRecordListResponse.newBuilder().setResp(CommonRespUtils.successResp())
                    .setData(JsonMapperUtils.toJson(result)).build();
        } catch (Exception e) {
            log.error("StrategyFlowRecordServiceImpl.strategyFlowRecordList.error.request:{}",
                    JsonMapperUtils.toJson(request), e);
            return FlowRecordListResponse.newBuilder().setResp(CommonRespUtils.errorResp(e)).build();
        }
    }

    @Override
    public FlowRecordDetailResponse flowRecordDetail(FlowRecordDetailRequest request) {
        log.info("StrategyFlowRecordServiceImpl.flowRecordDetail.request:{}", JsonMapperUtils.toJson(request));
        try {
            FlowRecordVoResponse response = recordService.flowRecordDetail(request.getId());
            return FlowRecordDetailResponse.newBuilder().setResp(CommonRespUtils.successResp())
                    .setData(JsonMapperUtils.toJson(response)).build();
        } catch (Exception e) {
            log.error("StrategyFlowRecordServiceImpl.flowRecordDetail.error.request:{}",
                    JsonMapperUtils.toJson(request), e);
            return FlowRecordDetailResponse.newBuilder().setResp(CommonRespUtils.errorResp(e)).build();
        }
    }

    @Override
    public FlowRecordAddResponse flowRecordAdd(FlowRecordAddRequest request) {
        log.info("StrategyFlowRecordServiceImpl.flowRecordAdd.request:{}", JsonMapperUtils.toJson(request));
        try {
            recordService.flowRecordAdd(request);
            return FlowRecordAddResponse.newBuilder().setResp(CommonRespUtils.successResp()).build();
        } catch (Exception e) {
            log.error("StrategyFlowRecordServiceImpl.flowRecordAdd.error.request:{}", JsonMapperUtils.toJson(request),
                    e);
            return FlowRecordAddResponse.newBuilder().setResp(CommonRespUtils.errorResp(e)).build();
        }
    }

    @Override
    public StrategyFlowTestResponse strategyFlowTest(StrategyFlowTestRequest request) {
        try {
            log.info("StrategyFlowRecordServiceImpl.strategyFlowTest.request:{}", JsonMapperUtils.toJson(request));
            List<Map<String, Object>> result =
                    hiveRecordFetchService.fetchIdpDataSync(QueryTypeEnum.HIVE, request.getSql(), "lidonghui03");
            log.info("StrategyFlowRecordServiceImpl.strategyFlowTest.result:{}", result.size());
            log.info("StrategyFlowRecordServiceImpl.strategyFlowTest111.result:{}", JsonMapperUtils.toJson(result));
        } catch (Exception e) {
            log.info("StrategyFlowRecordServiceImpl.strategyFlowTest.error.request:{}", JsonMapperUtils.toJson(request),
                    e);
        }
        return null;
    }

    @Override
    public FlowRecordDetailListResponse flowRecordDetailList(FlowRecordDetailListRequest request) {
        log.info("StrategyFlowRecordServiceImpl.flowRecordDetailList.request:{}", JsonMapperUtils.toJson(request));
        try {
            PageResult<RecodeDetailClickHouseDto> result = recordService.flowRecordDetailList(request);
            return FlowRecordDetailListResponse.newBuilder().setResp(CommonRespUtils.successResp())
                    .setData(JsonMapperUtils.toJson(result)).build();
        } catch (Exception e) {
            log.error("StrategyFlowRecordServiceImpl.flowRecordDetailList.error.request:{}",
                    JsonMapperUtils.toJson(request), e);
            return FlowRecordDetailListResponse.newBuilder().setResp(CommonRespUtils.errorResp(e)).build();
        }
    }

    @Override
    public GetFlowRecordDetailInfoResponse getFlowRecordDetailInfo(GetFlowRecordDetailInfoRequest request) {
        log.info("StrategyFlowRecordServiceImpl.getFlowRecordDetailInfo.request:{}", JsonMapperUtils.toJson(request));
        try {
            RecodeDetailClickHouseDto recodeDetail =
                    recordService.getRecodeDetail(request.getId(), request.getFlowRecordId());
            return GetFlowRecordDetailInfoResponse.newBuilder().setResp(CommonRespUtils.successResp())
                    .setData(JsonMapperUtils.toJson(recodeDetail)).build();
        } catch (Exception e) {
            log.error("StrategyFlowRecordServiceImpl.getFlowRecordDetailInfo.error.request:{}",
                    JsonMapperUtils.toJson(request), e);
            return GetFlowRecordDetailInfoResponse.newBuilder().setResp(CommonRespUtils.errorResp(e)).build();
        }
    }

    @Override
    public OneServiceQueryTestResponse oneServiceQueryTest(OneServiceQueryTestRequest request) {
        log.info("StrategyFlowRecordServiceImpl.oneServiceQueryTest.request:{}", JsonMapperUtils.toJson(request));
        try {
            List<Map<String, Object>> maps = Lists.newArrayList();
            log.info("StrategyFlowRecordServiceImpl.oneServiceQueryTest.result:{}", JsonMapperUtils.toJson(maps));
            return OneServiceQueryTestResponse.newBuilder().setResp(CommonRespUtils.successResp())
                    .setData(JsonMapperUtils.toJson(maps)).build();
        } catch (Exception e) {
            log.error("StrategyFlowRecordServiceImpl.oneServiceQueryTest.error.request:{}",
                    JsonMapperUtils.toJson(request), e);
        }
        return super.oneServiceQueryTest(request);
    }

    @Override
    public GetAllInstantsResponse getAllInstants(GetAllInstantsRequest request) {
        log.info("StrategyFlowRecordServiceImpl.getAllInstants.request:{}", JsonMapperUtils.toJson(request));
        try {

            return GetAllInstantsResponse.newBuilder().setResp(CommonRespUtils.successResp())
                    .addAllInstantInfo(recordService.getAllInstants(request)).build();
        } catch (Exception e) {
            log.error("StrategyFlowRecordServiceImpl.getAllInstants.error.request:{}", JsonMapperUtils.toJson(request),
                    e);
        }
        return null;
    }

    @Override
    public TestOneServiceResponse testOneService(TestOneServiceRequest request) {
        log.info("StrategyFlowRecordServiceImpl.testOneService.request:{}", JsonMapperUtils.toJson(request));
        recordService.testOneService(request.getDate());
        return TestOneServiceResponse.newBuilder().build();
    }

    @Override
    public SetRedisResponse setRedis(SetRedisRequest request) {
        recordService.setRedis(request.getKey(), request.getValue());
        return SetRedisResponse.newBuilder().build();
    }

    @Override
    public SetRedisResponse getRedis(SetRedisRequest request) {
        return SetRedisResponse.newBuilder().setData(recordService.getRedis(request.getKey())).build();
    }

    @Override
    public GetDateFromCacheResponse getDateFromCache(GetDateFromCacheRequest request) {
        FlowRecordStartEndPDate flowRecordId = recordService.getFlowRecordId(request.getId());
        return GetDateFromCacheResponse.newBuilder().setData(JsonMapperUtils.toJson(flowRecordId)).build();
    }

    @Override
    public GetFlowRecordAnalysisResultResponse getFlowRecordAnalysisResult(GetFlowRecordAnalysisResultRequest request) {
        log.info("StrategyFlowRecordServiceImpl.getFlowRecordAnalysisResult.request:{}",
                JsonMapperUtils.toJson(request));
        String result = recordService.getFlowRecordAnalysisResult(request.getId());
        return GetFlowRecordAnalysisResultResponse.newBuilder().setResp(CommonRespUtils.successResp()).setData(result)
                .build();
    }

    @Override
    public GetFlowRecordAnalysisResultWithMdResponse getFlowRecordAnalysisResultWithMd(
            GetFlowRecordAnalysisResultWithMdRequest request) {
        log.info("StrategyFlowRecordServiceImpl.getFlowRecordAnalysisResultWithMd.request:{}",
                JsonMapperUtils.toJson(request));
        String result = recordService.getFlowRecordAnalysisResultWithMd(request.getId());
        return GetFlowRecordAnalysisResultWithMdResponse.newBuilder().setResp(CommonRespUtils.successResp())
                .setData(result).build();
    }
}
