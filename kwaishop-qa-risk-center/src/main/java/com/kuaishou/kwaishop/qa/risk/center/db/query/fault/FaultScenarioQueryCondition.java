package com.kuaishou.kwaishop.qa.risk.center.db.query.fault;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.query.detail.DetailBaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FaultScenarioQueryCondition extends DetailBaseQueryCondition {

    private String subDomain;

    private String scenario;

    private String entry;

    private String ksn;

    private String level;

    private List<Integer> status;


}
