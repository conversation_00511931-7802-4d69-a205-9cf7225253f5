package com.kuaishou.kwaishop.qa.risk.center.domain.feature.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

public enum KspayRiskIntelligenceLevelEnum {
    UNKNOWN(0, "未知状态"),
    P0(1, "超高风险"),
    P1(2, "高风险"),
    ;

    private final Integer code;

    private final String desc;

    KspayRiskIntelligenceLevelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static KspayRiskIntelligenceLevelEnum of(Integer code) {
        for (KspayRiskIntelligenceLevelEnum kspayRiskIntelligenceLevelEnum: values()) {
            if (kspayRiskIntelligenceLevelEnum.getCode().equals(code) && kspayRiskIntelligenceLevelEnum.getCode() > 0) {
                return kspayRiskIntelligenceLevelEnum;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo() {
        List<EnumInfo> res = new ArrayList<>();
        for (KspayRiskIntelligenceLevelEnum typeEnum: values()) {
            if (typeEnum.getCode() > 0) {
                EnumInfo enumInfo = EnumInfo.newBuilder()
                        .setValue(typeEnum.getCode())
                        .setDesc(typeEnum.getDesc())
                        .build();
                res.add(enumInfo);
            }
        }
        return res;
    }
}
