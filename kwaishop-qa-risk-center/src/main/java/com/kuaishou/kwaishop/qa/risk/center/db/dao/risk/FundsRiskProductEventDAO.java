package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.KspayPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.detail.KspayDetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsRiskProductEventDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.FundsRiskProductEventQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;

/**
 * <AUTHOR>
 * Created on 2022-12-26
 */
public interface FundsRiskProductEventDAO extends KspayDetailDAO<FundsRiskProductEventDO, CombineQueryParam> {
    long insert(FundsRiskProductEventDO fundsRiskProductEventDO);
    KspayPageBO<FundsRiskProductEventDO> queryPageRiskDetailList(
            FundsRiskProductEventQueryCondition fundsRiskProductEventQueryCondition);
    List<FundsRiskProductEventDO> queryFundsRiskProductEvent(FundsRiskProductEventQueryCondition fundsRiskProductEventQueryCondition);
    FundsRiskProductEventDO queryById(Long id);
    List<FundsRiskProductEventDO> getAllList();

}
