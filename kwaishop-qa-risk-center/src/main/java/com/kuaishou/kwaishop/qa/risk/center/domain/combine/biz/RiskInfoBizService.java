package com.kuaishou.kwaishop.qa.risk.center.domain.combine.biz;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.QueryGroupByRiskSummaryDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.QueryRiskSummaryDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.RiskSummaryDataDTO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-22
 */
public interface RiskInfoBizService {

    RiskSummaryDataDTO queryRiskSummaryData(QueryRiskSummaryDataRequest request);

    List<RiskSummaryDataDTO> queryGroupByRiskSummaryData(QueryGroupByRiskSummaryDataRequest request);
}
