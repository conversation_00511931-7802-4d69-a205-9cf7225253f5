package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.DataBaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataQueryExecuteDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataQueryDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.datatool.DataQueryExecuteMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.datatool.DataQueryExeQueryCondition;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2022-11-14
 */
@Repository
public class DataQueryExecuteDAOImpl extends DataBaseDAO<DataQueryDO, DataQueryExeQueryCondition>
        implements DataQueryExecuteDAO {

    @Autowired
    private DataQueryExecuteMapper dataQueryExecuteMapper;


    @Override
    public long insertDataQuery(DataQueryDO dataQuery) {
        dataQuery.setCreateTime(System.currentTimeMillis());
        dataQuery.setUpdateTime(System.currentTimeMillis());
        dataQuery.setStartTime(0L);
        dataQuery.setEndTime(0L);
        return dataQueryExecuteMapper.insert(dataQuery);
    }

    @Override
    public Long queryIdByDataSourceId(Long dataSourceId) {
        return dataQueryExecuteMapper.queryIdByDataSourceId(dataSourceId);
    }

    @Override
    public int logicDeleted(Long id, String operator) {
        return dataQueryExecuteMapper.logicDeleted(operator, id);
    }

    @Override
    public DataQueryDO findById(long id) {
        DataQueryExeQueryCondition queryCondition = DataQueryExeQueryCondition.builder().id(id).build();
        return queryOne(queryCondition);
    }

    @Override
    public DataQueryDO findByDataSourceId(long id) {
        return dataQueryExecuteMapper.queryResultByDataSourceId(id);
    }

    @Override
    public List<DataQueryDO> findAllList(Map<String, Object> param) {
        return null;
    }

    @Override
    public void updateLog(Long id, String log) {
        dataQueryExecuteMapper.updateLog(id, log);
    }

    @Override
    public void clearLog(Long id) {
        dataQueryExecuteMapper.clearLog(id);
    }

    @Override
    public void updateResult(Long id, String result) {
        dataQueryExecuteMapper.updateResult(id, result);
    }

    @Override
    public void updateStatus(Long id, Integer status) {
        dataQueryExecuteMapper.updateStatus(id, status);
    }


    @Override
    protected void fillQueryCondition(DataQueryExeQueryCondition condition, QueryWrapper<DataQueryDO> queryWrapper) {

    }

    @Override
    protected BaseMapper<DataQueryDO> getMapper() {
        return dataQueryExecuteMapper;
    }
}
