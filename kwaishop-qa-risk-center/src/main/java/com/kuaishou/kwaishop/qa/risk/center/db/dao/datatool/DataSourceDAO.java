package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataSourceDo;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.DataSourceBo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-11
 */
public interface DataSourceDAO {

    /**
     * 查询数据源
     */
    List<DataSourceDo> queryDataSourceList(DataSourceDo dataSourceDo);

    PageBO<DataSourceDo> queryPageDataSourceList(DataSourceBo dataSourceBo);

    long insertDataSource(DataSourceDo dataSourceDo);

    long updateDataSource(DataSourceDo dataSourceDo);

    int logicDeleted(Long id, String operator);

    /**
     * 根据name模糊查询
     * @param name
     * @return
     */
    List<DataSourceDo> queryDataSourceLikeName(String name);

    DataSourceDo queryDataSourceById(Long id);

}
