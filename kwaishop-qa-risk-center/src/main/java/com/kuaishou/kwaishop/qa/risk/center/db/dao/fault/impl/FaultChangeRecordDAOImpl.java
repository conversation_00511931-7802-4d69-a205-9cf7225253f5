package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.impl;

import java.util.Collections;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.FaultScenarioChangeRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultScenarioChangeRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.fault.FaultScenarioChangeRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.fault.FaultChangeRecordQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultChangeRecordBO;


@Repository
public class FaultChangeRecordDAOImpl extends BaseDAOImpl<FaultScenarioChangeRecordDO, FaultChangeRecordQueryCondition>
        implements FaultScenarioChangeRecordDAO {


    @Autowired
    private FaultScenarioChangeRecordMapper faultChangeRecordMapper;

    @Override
    public FaultScenarioChangeRecordDO queryChangeRecordById(Long id) {

        return queryById(id);
    }


    @Override
    public FaultScenarioChangeRecordDO queryChangeRecord(FaultChangeRecordBO changeRecordBO) {
        FaultChangeRecordQueryCondition condition = FaultChangeRecordQueryCondition.builder()
                .branch(changeRecordBO.getBranch())
                .faultRecordId(changeRecordBO.getFaultRecordId())
                .type(changeRecordBO.getType())
                .scenarioId(changeRecordBO.getScenarioId())
                .orderByCreateTimeDesc(true)
                .build();
        List<FaultScenarioChangeRecordDO> res = queryList(condition);
        if (CollectionUtils.isEmpty(res)) {
            return null;
        }
        return res.get(0);
    }


    @Override
    public long insertOrUpdateChangeRecord(FaultScenarioChangeRecordDO changeRecordDO) {
        try {
            return saveOrUpdate(changeRecordDO);
        } catch (Exception ex) {
            return 0;
        }
    }

    @Override
    public long insertOrUpdateByFaultRecordId(FaultScenarioChangeRecordDO changeRecordDO) {
        try {
            FaultChangeRecordQueryCondition condition = FaultChangeRecordQueryCondition.builder()
                    .faultRecordId(changeRecordDO.getFaultRecordId())
                    .type(changeRecordDO.getType())
                    .scenarioId(changeRecordDO.getScenarioId())
                    .orderByCreateTimeDesc(true)
                    .build();
            List<FaultScenarioChangeRecordDO> res = queryList(condition);
            if (CollectionUtils.isNotEmpty(res)) {
                changeRecordDO.setId(res.get(0).getId());
            }
            return saveOrUpdate(changeRecordDO);
        } catch (Exception ex) {
            return 0;
        }
    }

    @Override
    public List<FaultScenarioChangeRecordDO> queryChangeRecordList(FaultChangeRecordBO recordBO) {
        try {
            FaultChangeRecordQueryCondition condition = FaultChangeRecordQueryCondition.builder()
                    .type(recordBO.getType())
                    .startTimeGe(recordBO.getSt())
                    .endTimeLe(recordBO.getEt())
                    .orderByCreateTimeDesc(true)
                    .build();
            return queryList(condition);
        } catch (Exception ex) {
            return Collections.emptyList();
        }
    }

    @Override
    public List<FaultScenarioChangeRecordDO> queryChangeRecordList(Long scenarioId,
                                                                   Boolean orderByCreateTimeDesc) {
        FaultChangeRecordQueryCondition condition = FaultChangeRecordQueryCondition.builder()
                .scenarioId(scenarioId)
                .orderByCreateTimeDesc(orderByCreateTimeDesc)
                .build();
        return queryList(condition);
    }

    @Override
    protected void fillQueryCondition(FaultChangeRecordQueryCondition condition,
                                      QueryWrapper<FaultScenarioChangeRecordDO> queryWrapper) {

        if (condition.getScenarioId() != null && condition.getScenarioId() > 0) {
            queryWrapper.and(q -> q.eq("scenario_id", condition.getScenarioId()));
        }
        if (condition.getBranch() != null && StringUtils.isNotBlank(condition.getBranch())) {
            queryWrapper.and(q -> q.eq("branch", condition.getBranch()));
        }
        if (condition.getType() != null && condition.getType() > 0) {
            queryWrapper.and(q -> q.eq("type", condition.getType()));
        }
        if (condition.getFaultRecordId() != null && condition.getFaultRecordId() > 0) {
            queryWrapper.and(q -> q.eq("fault_record_id", condition.getFaultRecordId()));
        }
        if (condition.getStartTimeGe() != null && condition.getStartTimeGe() > 0) {
            queryWrapper.and(q -> q.ge("create_time", condition.getStartTimeGe()));
        }
        if (condition.getEndTimeLe() != null && condition.getEndTimeLe() > 0) {
            queryWrapper.and(q -> q.le("create_time", condition.getEndTimeLe()));
        }

    }

    @Override
    protected BaseMapper<FaultScenarioChangeRecordDO> getMapper() {
        return faultChangeRecordMapper;
    }
}
