package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.KspayRiskInjectionBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KrpcKspayRiskInjectionServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayRiskInjectionRecordResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayRiskInjectionRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayRiskInjectionResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.ListRiskInjectionInfo;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class KspayRiskInjectionServiceImpl extends KrpcKspayRiskInjectionServiceGrpc.KspayRiskInjectionServiceImplBaseV2 {

    @Autowired
    private KspayRiskInjectionBizService kspayRiskInjectionBizService;

    @Override
    public KspayRiskInjectionResponse insertRiskInjection(
            KspayRiskInjectionRequest request) {
        log.info("[KspayRiskInjectionServiceImpl] KspayRiskInjectionRequest request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayRiskInjectionBizService.insertRiskInjection(request);
            return KspayRiskInjectionResponse.newBuilder()
                    .setErrorMsg("SUCCESS")
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskInjectionServiceImpl] KspayRiskInjectionRequest bizError, exception: ", e);
            return KspayRiskInjectionResponse.newBuilder()
                    .setResult(e.getCode())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskInjectionServiceImpl] KspayRiskInjectionRequest error, exception: ", e);
            return KspayRiskInjectionResponse.newBuilder()
                    .setErrorMsg(e.toString())
                    .build();
        }
    }


    @Override
    public KspayRiskInjectionResponse updateRiskInjection(
            KspayRiskInjectionRequest request) {
        log.info("[KspayRiskInjectionServiceImpl] KspayRiskInjectionRequest request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayRiskInjectionBizService.updateRiskInjection(request);
            return KspayRiskInjectionResponse.newBuilder()
                    .setErrorMsg("SUCCESS")
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskInjectionServiceImpl] KspayRiskInjectionRequest bizError, exception: ", e);
            return KspayRiskInjectionResponse.newBuilder()
                    .setResult(e.getCode())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskInjectionServiceImpl] KspayRiskInjectionRequest error, exception: ", e);
            return KspayRiskInjectionResponse.newBuilder()
                    .setErrorMsg(e.toString())
                    .build();
        }
    }



    @Override
    public KspayRiskInjectionRecordResponse queryRiskInjectionRecord(
                KspayRiskInjectionRequest request) {
        log.info("[KspayRiskInjectionServiceImpl] queryRiskInjectionRecord request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            ListRiskInjectionInfo.Builder response =  kspayRiskInjectionBizService.queryRiskInjectionRecord(request);
            return KspayRiskInjectionRecordResponse.newBuilder()
                    .setResult(1)
                    .setErrorMsg("SUCCESS")
                    .addListRiskInjectionInfo(response)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskInjectionServiceImpl] queryRiskInjectionRecord bizError, exception: ", e);
            return KspayRiskInjectionRecordResponse.newBuilder()
                    .setResult(e.getCode())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskInjectionServiceImpl] queryRiskInjectionRecord error, exception: ", e);
            return KspayRiskInjectionRecordResponse.newBuilder()
                    .setErrorMsg(e.toString())
                    .build();
        }
    }











}
