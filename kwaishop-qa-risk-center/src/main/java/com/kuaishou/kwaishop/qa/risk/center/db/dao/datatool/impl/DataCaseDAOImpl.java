package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.DataBaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataCaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.datatool.DataCaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.datatool.DataCaseQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.DataCaseBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-28
 */
@Repository
public class DataCaseDAOImpl extends DataBaseDAO<DataCaseDO, DataCaseQueryCondition> implements DataCaseDAO {

    @Autowired
    private DataCaseMapper dataCaseMapper;

    @Override
    public long insertDataCase(DataCaseDO dataCaseDO) {
        dataCaseDO.setCreateTime(System.currentTimeMillis());
        dataCaseDO.setUpdateTime(System.currentTimeMillis());
        return dataCaseMapper.insert(dataCaseDO);
    }

    @Override
    public long updateDataCase(DataCaseDO dataCaseDO) {
        return dataCaseMapper.updateById(dataCaseDO);
    }

    @Override
    public int logicDeleted(Long id, String operator) {
        return dataCaseMapper.logicDeleted(operator, id);
    }

    @Override
    public List<DataCaseDO> queryDataCaseLikeName(String name) {
        return dataCaseMapper.queryDataCaseLikeName(name);
    }

    @Override
    public PageBO<DataCaseDO> queryPageDataCaseList(DataCaseBO dataCaseBO) {
        DataCaseQueryCondition queryCondition = DataCaseQueryCondition.builder()
                .caseName(dataCaseBO.getName())
                .id(dataCaseBO.getId())
                .creatorName(dataCaseBO.getCreatorName())
                .orderByCreateTimeDesc(true)
                .pageNo(dataCaseBO.getPageNo())
                .pageSize(dataCaseBO.getPageSize())
                .build();

        PageBO<DataCaseDO> res = queryPageList(queryCondition);
        return res;
    }

    @Override
    public List<DataCaseDO> queryDataCaseList(DataCaseDO dataCaseDO) {
        return dataCaseMapper.queryDataCase();
    }

    @Override
    public DataCaseDO queryDataCaseById(Long id) {
        return dataCaseMapper.queryDataCaseById(id);
    }

    @Override
    protected void fillQueryCondition(DataCaseQueryCondition condition, QueryWrapper<DataCaseDO> queryWrapper) {
        // 只输入名称
        if (StringUtils.isNotBlank(condition.getCaseName())) {
            queryWrapper.like("name", condition.getCaseName());

        }
        if (StringUtils.isNotBlank(condition.getCreatorName())) {
            queryWrapper.eq("creator_name", condition.getCreatorName());
        }

    }

    @Override
    protected BaseMapper<DataCaseDO> getMapper() {
        return dataCaseMapper;
    }
}
