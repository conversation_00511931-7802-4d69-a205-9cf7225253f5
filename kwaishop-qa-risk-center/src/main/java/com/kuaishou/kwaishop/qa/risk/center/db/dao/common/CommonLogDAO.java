package com.kuaishou.kwaishop.qa.risk.center.db.dao.common;

import java.util.List;
import java.util.Map;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.BaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.common.CommonLogDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.common.model.bo.LogDataBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-24
 */
public interface CommonLogDAO extends BaseDAO<CommonLogDO> {

    List<CommonLogDO> queryLogList(LogDataBO logDataBO);

    long batchInsertOrUpdate(List<CommonLogDO> commonLogDOS);

    long insertOrUpdate(CommonLogDO commonLogDO);

    Map<String, CommonLogDO> queryLogDtMap(LogDataBO logDataBO);
}
