package com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressInterfaceBaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceBaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress.StressInterfaceBaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.stress.ScenarioQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressInterfaceBaseBO;

@Repository
public class StressInterfaceBaseDAOImpl extends BaseDAOImpl<StressInterfaceBaseDO, ScenarioQueryCondition> implements StressInterfaceBaseDAO {

    @Autowired
    private StressInterfaceBaseMapper stressInterfaceBaseMapper;

    @Override
    protected void fillQueryCondition(ScenarioQueryCondition condition, QueryWrapper<StressInterfaceBaseDO> queryWrapper) {
    }

    @Override
    protected BaseMapper<StressInterfaceBaseDO> getMapper() {
        return stressInterfaceBaseMapper;
    }

    @Override
    public PageBO<StressInterfaceBaseDO> queryStressInterfaceBaseList(QueryStressInterfaceBaseBO bo) {
        Integer pageNo = 0;
        Integer pageSize = 10;
        if (bo.getPageNo() >= 1) {
            pageNo = bo.getPageNo() - 1;
        }
        if (bo.getPageSize() > 100 || bo.getPageSize() <= 0) {
            pageSize = 100;
        }
        Integer startNo = pageNo * bo.getPageSize();
        Long count = stressInterfaceBaseMapper.countStressInterfaceBases(bo.getInterfaceId(), bo.getInterfaceName());
        List<StressInterfaceBaseDO> interfaceBaseList  = stressInterfaceBaseMapper.queryStressInterfaceBases(bo.getInterfaceId(),
                bo.getInterfaceName(), startNo, pageSize);
        return new PageBO<StressInterfaceBaseDO>(pageNo, pageSize,
                count, interfaceBaseList);
    }

    @Override
    public StressInterfaceBaseDO queryStressInterfaceBaseByInterfaceId(Long interfaceId) {
        return stressInterfaceBaseMapper.queryStressInterfaceBaseByInterfaceId(interfaceId);
    }
}
