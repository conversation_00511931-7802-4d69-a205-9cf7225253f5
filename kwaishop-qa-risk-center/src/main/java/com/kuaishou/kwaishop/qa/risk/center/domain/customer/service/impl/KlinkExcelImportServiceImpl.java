package com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.utils.excel.ExcelUtils.checkAndGetExcelDataV2;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.gson.JsonObject;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.klink.UserKlinkLoginDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportTokenExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.KlinkTokenImportExcelModel;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.KlinkExcelImportService;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.BaseExcelModel;

import lombok.extern.slf4j.Slf4j;


@Service
@Lazy
@Slf4j
public class KlinkExcelImportServiceImpl implements KlinkExcelImportService {

    @Override
    public List<UserKlinkLoginDO> getKlinkLoginDOList(ImportTokenExcelBO excelBO) {
        List<BaseExcelModel> excelModels = checkAndGetExcelDataV2(excelBO.getCdnUrl(), "klink", new KlinkTokenImportExcelModel(), null);
        return buildKlinkLoginDOList(excelModels, excelBO);
    }

    @Override
    public List<UserKlinkLoginDO> buildKlinkLoginDOList(List<BaseExcelModel> excelModels, ImportTokenExcelBO excelBO) {
        return excelModels.stream()
                .map(e -> tokenExcelKlinkLoginDO(e, excelBO))
                .collect(Collectors.toList());
    }

    public UserKlinkLoginDO tokenExcelKlinkLoginDO(BaseExcelModel baseExcelModel, ImportTokenExcelBO excelBO) {
        KlinkTokenImportExcelModel tokenModel = (KlinkTokenImportExcelModel) baseExcelModel;
        return UserKlinkLoginDO.builder()
                .userId(tokenModel.getUserId())
                .ext(buildJsonString(tokenModel))
                .build();
    }

    private String buildJsonString(KlinkTokenImportExcelModel tokenModel) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("apist", tokenModel.getSt());
        jsonObject.addProperty("ssecurity", tokenModel.getSsecurity());
        return jsonObject.toString();
    }

}
