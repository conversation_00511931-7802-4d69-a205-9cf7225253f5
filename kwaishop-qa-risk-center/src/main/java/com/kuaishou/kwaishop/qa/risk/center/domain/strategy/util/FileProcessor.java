package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.util;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;

public class FileProcessor {
    private String inputFileName;
    private String outputFileName;

    public FileProcessor(String inputFileName) {
        this.inputFileName = inputFileName;
        this.outputFileName = outputFileName;
    }

    public void processFile(Processor processor) {
        try (BufferedReader br = new BufferedReader(new FileReader(inputFileName))) {
            String line;
            while ((line = br.readLine()) != null) {
                String processedLine = processor.processLine(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public interface Processor {
        String processLine(String line) throws IOException;
    }
}