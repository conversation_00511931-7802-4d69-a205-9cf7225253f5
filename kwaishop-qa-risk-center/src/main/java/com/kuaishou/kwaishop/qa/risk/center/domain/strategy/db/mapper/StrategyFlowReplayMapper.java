package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowReplayListRequestPojo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowReplayDo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-17
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface StrategyFlowReplayMapper {

    @Select("SELECT * FROM strategy_flow_replay WHERE id = #{id}")
    StrategyFlowReplayDo selectById(@Param("id") Long id);

    @Insert("INSERT INTO strategy_flow_replay(name, flow_record_id, description, total, diff_result, "
            + "diff_detail_result, replay_qps, replay_config, replay_status, replay_start_time, replay_end_time, "
            + "create_user, update_user, create_time, update_time, extra, deleted) "
            + "VALUES (#{name}, #{flowRecordId}, #{description}, #{total}, #{diffResult}, #{diffDetailResult}, "
            + "#{replayQps}, #{replayConfig}, #{replayStatus}, #{replayStartTime}, #{replayEndTime}, #{createUser}, "
            + "#{updateUser}, #{createTime}, #{updateTime}, #{extra}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void addStrategyFlowReplay(StrategyFlowReplayDo strategyFlowReplayDo);

    @Select({"<script>", "select * from strategy_flow_replay where 1 = 1", "<if test='id != null and id != 0'>",
            "  and id = #{id}", "</if> ", "<if test='name != null and name != \"\" '>",
            "  and `name` like concat('%', #{name}, '%')", "</if>",
            "<if test='flowRecordId != null and flowRecordId != 0'>", "  and flow_record_id = #{flowRecordId}", "</if>",
            "<if test='replayStatus != null and replayStatus != 0'>", "  and replay_status = #{replayStatus}", "</if>",
            "<if test='replayStartTimeStart != null and replayStartTimeStart != 0 and replayStartTimeEnd != null and "
                    + "replayStartTimeEnd != 0'>",
            "  and (replay_start_time <![CDATA[ >= ]]> #{replayStartTimeStart} and  replay_start_time <![CDATA[ <= "
                    + "]]> #{replayStartTimeEnd})", "</if>",
            "<if test='replayEndTimeStart != null and replayEndTimeStart != 0 and replayEndTimeEnd != null and "
                    + "replayEndTimeEnd != 0'>",
            "  and (replay_end_time <![CDATA[ >= ]]> #{replayEndTimeStart} and  replay_end_time <![CDATA[ <= ]]> "
                    + "#{replayEndTimeEnd})", "</if>", "<if test='createUser != null and createUser!=\"\"'>",
            "  and create_user = #{createUser}", "</if>", "<if test='diffResult != null and diffResult != \"\"'>",
            "  and diff_result = #{diffResult}", "</if>", "<if test='sceneKey != null and sceneKey != \"\"'>",
            "  and JSON_UNQUOTE(JSON_EXTRACT(replay_config, '$.sceneKey')) LIKE CONCAT('%', #{sceneKey}, '%')",
            "</if>", " order by update_time desc", "</script>"})
    List<StrategyFlowReplayDo> strategyFlowReplayList(FlowReplayListRequestPojo requestPojo);

    @Select({"<script>", "select count(*) from strategy_flow_replay where 1 = 1", "<if test='id != null and id != 0'>",
            "  and id = #{id}", "</if> ", "<if test='name != null and name != \"\" '>",
            "  and `name` like concat('%', #{name}, '%')", "</if>",
            "<if test='flowRecordId != null and flowRecordId != 0'>", "  and flow_record_id = #{flowRecordId}", "</if>",
            "<if test='replayStatus != null and replayStatus != 0'>", "  and replay_status = #{replayStatus}", "</if>",
            "<if test='replayStartTimeStart != null and replayStartTimeStart != 0 and replayStartTimeEnd != null and "
                    + "replayStartTimeEnd != 0'>",
            "  and (replay_start_time <![CDATA[ >= ]]> #{replayStartTimeStart} and  replay_start_time <![CDATA[ <= "
                    + "]]> #{replayStartTimeEnd})", "</if>",
            "<if test='replayEndTimeStart != null and replayEndTimeStart != 0 and replayEndTimeEnd != null and "
                    + "replayEndTimeEnd != 0'>",
            "  and (replay_end_time <![CDATA[ >= ]]> #{replayEndTimeStart} and  replay_end_time <![CDATA[ <= ]]> "
                    + "#{replayEndTimeEnd})", "</if>", "<if test='createUser != null and createUser!=\"\"'>",
            "  and create_user = #{createUser}", "</if>", "</script>"})
    int strategyFlowReplayCount(FlowReplayListRequestPojo requestPojo);

    @Select("select * from strategy_flow_replay where id = #{id}")
    StrategyFlowReplayDo getFlowReplayById(Long id);

    @Select("select * from strategy_flow_replay where replay_status = 1 and flow_record_id in ( select id from "
            + "strategy_flow_record where FIND_IN_SET(#{sceneKey}, scene_key_list) and flow_type = 1)")
    List<StrategyFlowReplayDo> getAllRealTimeReplayList(String sceneKey);

    @Update("update strategy_flow_replay set replay_status = #{replayStatus}, replay_end_time = #{replayEndTime} "
            + "where id = #{id}")
    void updateReplayStatusAndReplayEndTimeById(Long id, int replayStatus, long replayEndTime);

    @Update("update strategy_flow_replay set diff_result = #{diffResult} where id = #{id}")
    void updateDiffResultById(Long id, Integer diffResult);

    @Update("update strategy_flow_replay set diff_result = #{diffResult} and diff_detail_result = #{diffDetailResult} where id = #{id}")
    void updateDiffResultById(Long id, Integer diffResult, String diffDetailResult);


}
