package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.dao;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper.StrategyRecordListMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper.StrategyTrafficRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyRecordListDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTrafficRecorQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTrafficRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayDetailRequest;

@Repository
public class StrategyTrafficRecordDAOImpl extends BaseDAOImpl<StrategyTrafficRecordDO, StrategyTrafficRecorQueryCondition>
        implements StrategyTrafficRecordDAO {

    @Autowired
    private StrategyTrafficRecordMapper strategyTrafficRecordMapper;

    @Autowired
    private StrategyRecordListMapper strategyRecordListMapper;

    public void updateStatus(StrategyRecordListDO record) {
        strategyRecordListMapper.updateStatusV1(record);
    }

    public void insertRecord(StrategyRecordListDO record) {
        strategyRecordListMapper.insert(record);
    }

    public StrategyTrafficRecordDO findById(Long id) {
        return strategyTrafficRecordMapper.findById(id);
    }

    public List<StrategyTrafficRecordDO> findAll() {
        return strategyTrafficRecordMapper.findAll();
    }

    public long insert(StrategyTrafficRecordDO record) {
        strategyTrafficRecordMapper.insert(record);
        return 0;
    }

    @Override
    protected void fillQueryCondition(StrategyTrafficRecorQueryCondition condition, QueryWrapper<StrategyTrafficRecordDO> queryWrapper) {
        if (condition.getTaskId() != null && !condition.getTaskId().isEmpty()) {
            queryWrapper.and(q -> q.eq("record_task_id", condition.getTaskId()));
        }
        if (condition.getDiffConclusion() != 0) {
            queryWrapper.and(q -> q.eq("diff_conclusion", condition.getDiffConclusion()));
        }
        if (!condition.getSceneKey().isEmpty()) {
            queryWrapper.and(q -> q.eq("scene_name", condition.getSceneKey()));
        }
    }

    @Override
    public PageBO<StrategyTrafficRecordDO> queryPageList(ReplayDetailRequest request) {
        StrategyTrafficRecorQueryCondition condition = StrategyTrafficRecorQueryCondition.builder()
                .diffConclusion(request.getDiffConclusion())
                .sceneKey(request.getSceneKey())
                .taskId(request.getTaskId())
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .build();
        return queryPageList(condition);
    }

    @Override
    public List<StrategyTrafficRecordDO> queryAllByTaskId(String taskId) {
        return strategyTrafficRecordMapper.queryAllByTaskId(taskId);
    }

    @Override
    public List<StrategyTrafficRecordDO> queryAllByIds(List<Long> ids) {
        return strategyTrafficRecordMapper.queryAllByIds(ids);
    }

    @Override
    protected BaseMapper<StrategyTrafficRecordDO> getMapper() {
        return strategyTrafficRecordMapper;
    }


    public void update(StrategyTrafficRecordDO record) {
        strategyTrafficRecordMapper.update(record);
    }

    public void delete(Long id) {
        strategyTrafficRecordMapper.delete(id);
    }

    public void batchInsert(List<StrategyTrafficRecordDO> records) {
        strategyTrafficRecordMapper.batchInsert(records);
    }

    public void batchUpdate(List<StrategyTrafficRecordDO> records) {
        strategyTrafficRecordMapper.batchUpdate(records);
    }


    public void updateDiffInfo(List<StrategyTrafficRecordDO> strategyTrafficRecordDOS) {
        strategyTrafficRecordDOS.forEach(s -> {
            strategyTrafficRecordMapper.updateDiffInfo(s);
        });
    }

    public String queryTrafficData(String caseId) {
        return strategyTrafficRecordMapper.queryTrafficData(caseId);
    }

    public String queryTaskIdByCaseId(Long caseId) {
        return strategyTrafficRecordMapper.queryTaskIdByCaseId(caseId);
    }

    public List<StrategyTrafficRecordDO> queryAllCaseByTaskId(String taskId) {
        return strategyTrafficRecordMapper.queryAllCaseByTaskId(taskId);
    }
}
