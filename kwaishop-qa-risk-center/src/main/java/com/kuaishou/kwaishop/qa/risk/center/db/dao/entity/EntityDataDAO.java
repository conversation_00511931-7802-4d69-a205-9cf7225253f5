package com.kuaishou.kwaishop.qa.risk.center.db.dao.entity;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.BaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDataDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityDataBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-29
 */
public interface EntityDataDAO extends BaseDAO<EntityDataDO> {

    EntityDataDO queryEntityData(EntityDataBO entityDataBO);

    List<EntityDataDO> queryEntityDataList(EntityDataBO entityDataBO);

    long insertOrUpdate(EntityDataDO entityDataDO);
}
