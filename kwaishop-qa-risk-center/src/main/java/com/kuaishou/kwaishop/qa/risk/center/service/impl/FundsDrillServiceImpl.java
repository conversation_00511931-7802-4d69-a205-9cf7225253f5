package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.drill.biz.FundsRiskDrillBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.drill.CreateFundsRiskDrillRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.drill.CreateFundsRiskDrillResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.drill.KrpcFundsDrillServiceGrpc.FundsDrillServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.drill.QueryRiskDrillListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.drill.QueryRiskDrillListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.drill.QueryRiskDrillRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.drill.QueryRiskDrillResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.drill.UpdateFundsRiskDrillRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.drill.UpdateFundsRiskDrillResponse;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class FundsDrillServiceImpl extends FundsDrillServiceImplBaseV2 {

    @Autowired
    private FundsRiskDrillBizService fundsRiskDrillBizService;

    @Override
    public CreateFundsRiskDrillResponse createFundsRiskDrill(CreateFundsRiskDrillRequest request) {
        log.info("[FundsRiskDrillBizService] createFundsRiskDrill request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            int result = (int) fundsRiskDrillBizService.createFundsRiskDrill(request);
            return CreateFundsRiskDrillResponse.newBuilder()
                    .setResult(result)
                    .build();
        } catch (Exception e) {
            return CreateFundsRiskDrillResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    @Override
    public UpdateFundsRiskDrillResponse updateFundsRiskDrill(UpdateFundsRiskDrillRequest request) {

        log.info("[FundsRiskDrillBizService] updateFundsRiskDrill request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            int result = fundsRiskDrillBizService.updateFundsRiskDrill(request);
            return UpdateFundsRiskDrillResponse.newBuilder()
                    .setResult(result)
                    .build();
        } catch (Exception e) {
            return UpdateFundsRiskDrillResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryRiskDrillResponse queryFundsRiskDrillDetail(QueryRiskDrillRequest request) {

        log.info("[FundsRiskDrillBizService] queryFundsRiskDrillDetail request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            return QueryRiskDrillResponse.newBuilder()
                    .setData(fundsRiskDrillBizService.queryFundsRiskDrillDetail(request))
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();

        } catch (Exception e) {
            return QueryRiskDrillResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryRiskDrillListResponse queryFundsRiskDrillList(QueryRiskDrillListRequest request) {

        log.info("[FundsRiskDrillBizService] queryFundsRiskDrillList request: {}", ProtobufUtil.protoToJsonString(request));
        try {

            return QueryRiskDrillListResponse.newBuilder()
                    .setData(fundsRiskDrillBizService.queryFundsRiskDrillList(request))
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (Exception e) {
            return QueryRiskDrillListResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

}
