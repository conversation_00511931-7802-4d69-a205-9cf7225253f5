package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.recordHandler;

import java.text.MessageFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.kuaishou.dp.one.service.rpc.client.sql.model.QueryResult;
import com.kuaishou.dp.one.service.rpc.client.sql.model.table.TableField;
import com.kuaishou.dp.one.service.rpc.client.sql.model.table.TableRow;
import com.kuaishou.dp.one.service.rpc.client.templateapi.ResultHandler;
import com.kuaishou.infra.framework.kafka.KafkaProducers;
import com.kuaishou.kconf.common.json.JsonMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.common.Enum.FlowStatus;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert.RecordConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper.StrategyFlowRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordExtraDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecordKafkaDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowRecordDo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.hiveQuery.TemplateApiQueryClient;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-19
 * 执行日志流量，业务自定义where条件
 */
@Slf4j
@Service
public class StrategyCustomWhereClauseLogHandler {

    private static final String WHERE_CLAUSE_WITH_PHOURMIN =
            " p_date in ({0}) and p_hourmin in ({1}) and scene_key in ({2}) and scene_execute_type = ''ONLINE'' ";
    private static final String WHERE_CLAUSE =
            " p_date in ({0}) and scene_key in ({1}) and scene_execute_type = ''ONLINE'' ";

    private static final List<String> COL_LIST =
            Arrays.asList("p_date", "p_hourmin", "trace_id", "scene_key", "scene_type", "scene_source_type",
                    "scene_execute_type", "scene_version", "strategy_id_map", "strategy_id_version_map", "dag_node_map",
                    "feature_key_map", "action_hit_ids", "action_hit_policy_code_map", "biz_key_info", "create_time",
                    "end_time", "request_hit_result", "action_hit_policy_codes", "condition_exec_detail_info",
                    "rule_exec_detail_info", "flow_record_id");

    @Autowired
    private TemplateApiQueryClient templateApiQueryClient;

    @Autowired
    private StrategyFlowRecordMapper strategyFlowRecordMapper;

    public String handle(Long id) {
        StrategyFlowRecordDo recordDo = strategyFlowRecordMapper.getFlowRecordById(id);
        FlowRecordExtraDto extraDto = JsonMapperUtils.fromJson(recordDo.getExtra(), FlowRecordExtraDto.class);
        List<String> pDatePartitionList = extraDto.getPDatePartitionList();
        List<String> pHourMinPartitionList = extraDto.getPHourMinPartitionList();
        List<String> sceneKeyList = Splitter.on(',').splitToList(recordDo.getSceneKeyList());

        List<String> esPDatePartitionList =
                pDatePartitionList.stream().map(x -> "'" + x + "'").collect(Collectors.toList());
        List<String> esPHourMinPartitionList =
                pHourMinPartitionList.stream().map(x -> "'" + x + "'").collect(Collectors.toList());
        List<String> esSceneKeyList = sceneKeyList.stream().map(x -> "'" + x + "'").collect(Collectors.toList());

        String whereClause = null;
        if (CollectionUtils.isEmpty(pHourMinPartitionList)) {
            whereClause = MessageFormat.format(WHERE_CLAUSE, Joiner.on(',').join(esPDatePartitionList),
                    Joiner.on(',').join(esSceneKeyList));
        } else {
            whereClause = MessageFormat.format(WHERE_CLAUSE_WITH_PHOURMIN, Joiner.on(',').join(esPDatePartitionList),
                    Joiner.on(',').join(esPHourMinPartitionList), Joiner.on(',').join(esSceneKeyList));
        }
        log.info("StrategyCustomWhereClauseLogHandler.whereClause:{}", whereClause);

        Map<String, String> param = Maps.newHashMap();
        param.put("flowRecordId", String.valueOf(id));
        param.put("whereClause", whereClause);
        param.put("offset", String.valueOf(NumberUtils.INTEGER_ZERO));
        param.put("limit", String.valueOf(recordDo.getTotal()));

        String apiName = "getStrategyRecordUse";

        ResultHandler<QueryResult<TableRow>> resultHandler = new ResultHandler<QueryResult<TableRow>>() {
            @Override
            public void handle(String queryId, QueryResult<TableRow> tableRowQueryResult) {
                log.info("StrategyCustomWhereClauseLogHandler.resultHandler.handle:{}", JsonMapperUtils.toJson(tableRowQueryResult));
                List<TableRow> rows = (List<TableRow>) tableRowQueryResult.getRows();
                List<Map<String, Object>> resultList = Lists.newArrayList();
                for (TableRow tableRow : rows) {
                    List<TableField> fields = (List<TableField>) tableRow.getFields();
                    Map<String, Object> resultMap = Maps.newHashMap();
                    for (TableField tableField : fields) {
                        String index =
                                tableField.getTableFieldName().substring(4, tableField.getTableFieldName().length());
                        resultMap.put(COL_LIST.get(Integer.parseInt(index)),
                                tableField.getTableFieldValue().getFieldValue());
                    }
                    resultList.add(resultMap);
                }
                List<RecordKafkaDto> recordKafkaDtos = RecordConvert.hiveResultList2KafkaDtos(resultList);
                log.info("StrategyCustomWhereClauseLogHandler.onNewResultArrive.kafkaRecord:{}", recordKafkaDtos.size());
                for (RecordKafkaDto dto : recordKafkaDtos) {
                    KafkaProducers.sendString("kwaishop_apollo_strategy_flow_record", JsonMapperUtils.toJson(dto));
                }
            }

            @Override
            public void onException(String queryId, Exception e) {
                log.error("StrategyCustomWhereClauseLogHandler.onException.queryId:{}", queryId, e);
                strategyFlowRecordMapper.updateFlowStatusById(id, FlowStatus.ERROR.getCode());
            }

            @Override
            public void onComplete(String queryId) {
                log.info("StrategyCustomWhereClauseLogHandler.onComplete.queryId:{}", queryId);
                strategyFlowRecordMapper.updateFlowStatusById(id, FlowStatus.FINISHED.getCode());
            }
        };
        return templateApiQueryClient.asyncQuery3(param, apiName, resultHandler, "23a221409d4749cc907eac4ea398abab");
    }
}
