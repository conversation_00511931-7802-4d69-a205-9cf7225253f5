package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service;

import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordStartEndPDate;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.PageResult;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.ReplayDetailClickHouseDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowRealtimeKafkaDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowReplayBo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.CheckReplayStatusByPipelineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.CheckReplayStatusByPipelineResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowReplayAddRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowReplayListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetFlowReplayDetailListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StartReplayByPipelineRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-17
 */
public interface ReplayService {

    FlowRecordStartEndPDate getFlowReplayId(Long id);

    void flowReplayAdd(FlowReplayAddRequest request);

    StrategyFlowReplayBo flowReplayDetail(Long id);

    PageResult<StrategyFlowReplayBo> flowReplayList(FlowReplayListRequest request);

    void flowReplayOfflineHandler(Long id, int startPageNum, int endPageNum);

    void flowReplayRealTimeHandler(StrategyFlowRealtimeKafkaDto message);

    void testQpsFlow(int qps, int time);

    PageResult<ReplayDetailClickHouseDto> getFlowReplayDetailList(GetFlowReplayDetailListRequest request);

    ReplayDetailClickHouseDto getFlowReplayDetail(String id, Long flowReplayId);

    String getReplayResultAnalysis(Long id);

    void stopReplay(Long id);

    void replayFinalMessage(Long id);

    String startReplayByPipeline(StartReplayByPipelineRequest request);

    CheckReplayStatusByPipelineResponse checkReplayStatusByPipeline(CheckReplayStatusByPipelineRequest request);

    String getRealTimeCache(String sceneKey);
}
