package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kuaishou.infra.boot.jdbc.datasource.TransactionalDataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.RiskDetailExtendDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.RiskDetailDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.RiskDetailMapper;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-20
 */
@Service
@TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
public class RiskDetailExtendDAOImpl extends ServiceImpl<RiskDetailMapper, RiskDetailDO> implements
        RiskDetailExtendDAO {
}
