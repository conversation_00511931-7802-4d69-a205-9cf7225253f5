package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-30
 */
@Data
public class StrategyFlowRealtimeKafkaDto {

    @JsonProperty(value = "trace_id")
    private String traceId;

    @JsonProperty(value = "scene_id")
    private Integer sceneId;

    @JsonProperty(value = "scene_key")
    private String sceneKey;

    @JsonProperty(value = "scene_type")
    private String sceneType;

    @JsonProperty(value = "scene_source_type")
    private String sceneSourceType;

    @JsonProperty(value = "scene_execute_type")
    private String sceneExecuteType;

    @JsonProperty(value = "scene_version")
    private String sceneVersion;

    @JsonProperty(value = "strategy_id_feature_map")
    private String strategyIdFeatureMap;

    @JsonProperty(value = "strategy_id_map")
    private String strategyIdMap;

    @JsonProperty(value = "strategy_id_version_map")
    private String strategyIdVersionMap;

    @JsonProperty(value = "dag_node_map")
    private String dagNodeMap;

    @JsonProperty(value = "feature_key_map")
    private String featureKeyMap;

    @JsonProperty(value = "action_hit_ids")
    private String actionHitIds;

    @JsonProperty(value = "action_hit_policy_code_map")
    private String actionHitPolicyCodeMap;

    @JsonProperty(value = "biz_key_info")
    private String bizKeyInfo;

    @JsonProperty(value = "strategy_info_snapshot")
    private String strategyInfoSnapshot;

    @JsonProperty(value = "create_time")
    private Long createTime;

    @JsonProperty(value = "end_time")
    private Long endTime;

    @JsonProperty(value = "key")
    private String key;

    @JsonProperty(value = "rule_exec_detail_info")
    private String ruleExecDetailInfo;

    @JsonProperty(value = "strategy_node_detail_and_exec_info")
    private String strategyNodeDetailAndExecInfo;

    @JsonProperty(value = "condition_exec_detail_info")
    private String conditionExecDetailInfo;

}
