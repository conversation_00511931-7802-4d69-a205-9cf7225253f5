package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.UUID;

import com.google.api.client.util.Lists;
import com.kuaishou.dp.auth.dsc.client.DscAccessTokenProvider;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert.ResultSetMapperUtil;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecodeDetailClickHouseDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.ReplayDetailClickHouseDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.util.StringEscapeUtils;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import ru.yandex.clickhouse.util.ClickHouseUserProfile;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-29
 */
@Slf4j
public class ClickHouseConnector {

    private static final String PRINCIPAL = "ks_ad_syt/<EMAIL>";
    @Setter
    @Getter
    private static String SECRET_KEY;
    @Setter
    @Getter
    private static String APP_KEY;
    @Setter
    @Getter
    private static String APP_SECRET;

    private static final Integer SYT_GROUP_ID = 548;

    private static final String AUTH_TYPE = "PROJECT";

    public static Connection getConnect() throws SQLException {
        return DriverManager.getConnection(
                "**********************************************************************************************");
    }

    public static ResultSet executeSimpleSql(String sql) {
        String uuid = UUID.randomUUID().toString();
        ClickHouseUserProfile.set(
                new ClickHouseUserProfile.Builder()
                        .ksAuthPrincipal(PRINCIPAL)
                        .ksAuthToken(getDSCToken())
                        .ksAuthType(AUTH_TYPE)
                        .ksQueryId(uuid)
                        .ksTaskGroupId(String.valueOf(SYT_GROUP_ID))
                        .build());
        log.info("ClickHouseConnector.executeSimpleSql.uuid:{} , executeSql:{}", uuid, sql);
        ResultSet resultSet = null;
        try (Connection connection = getConnect(); Statement stmt = connection.createStatement()) {

            resultSet = stmt.executeQuery(StringEscapeUtils.escapeSql(sql));
        } catch (Exception e) {
            log.error("ClickHouseConnector.executeSimpleSql.executeSql error, sql:{}", sql, e);
        } finally {
            ClickHouseUserProfile.remove();
        }
        return resultSet;
    }

    public static List executeSimpleSqlReturnClazz(String sql, Class clazz) {
        List replayDetailList = Lists.newArrayList();
        String uuid = UUID.randomUUID().toString();
        ClickHouseUserProfile.set(
                new ClickHouseUserProfile.Builder()
                        .ksAuthPrincipal(PRINCIPAL)
                        .ksAuthToken(getDSCToken())
                        .ksAuthType(AUTH_TYPE)
                        .ksQueryId(uuid)
                        .ksTaskGroupId(String.valueOf(SYT_GROUP_ID))
                        .build());
        log.info("ClickHouseConnector.executeSimpleSql.uuid:{} , executeSql:{}", uuid, sql);
        ResultSet resultSet = null;
        try (Connection connection = getConnect(); Statement stmt = connection.createStatement()) {
            resultSet = stmt.executeQuery(sql);
            replayDetailList =
                    ResultSetMapperUtil.mapResultSetToObject(resultSet, clazz);
        } catch (Exception e) {
            log.error("ClickHouseConnector.executeSimpleSql.executeSql error, sql:{}", sql, e);
        } finally {
            ClickHouseUserProfile.remove();
        }
        return replayDetailList;
    }

    public static List<ReplayDetailClickHouseDto> executeSimpleSqlReturnReplay(String sql) {
        List<ReplayDetailClickHouseDto> replayDetailList = Lists.newArrayList();
        String uuid = UUID.randomUUID().toString();
        ClickHouseUserProfile.set(
                new ClickHouseUserProfile.Builder()
                        .ksAuthPrincipal(PRINCIPAL)
                        .ksAuthToken(getDSCToken())
                        .ksAuthType(AUTH_TYPE)
                        .ksQueryId(uuid)
                        .ksTaskGroupId(String.valueOf(SYT_GROUP_ID))
                        .build());
        log.info("ClickHouseConnector.executeSimpleSql.uuid:{} , executeSql:{}", uuid, sql);
        ResultSet resultSet = null;
        try (Connection connection = getConnect(); Statement stmt = connection.createStatement()) {
            resultSet = stmt.executeQuery(sql);
            replayDetailList =
                    ResultSetMapperUtil.mapResultSetToObject(resultSet, ReplayDetailClickHouseDto.class);
        } catch (Exception e) {
            log.error("ClickHouseConnector.executeSimpleSql.executeSql error, sql:{}", sql, e);
        } finally {
            ClickHouseUserProfile.remove();
        }
        return replayDetailList;
    }

    public static List<RecodeDetailClickHouseDto> executeSimpleSqlReturnRecord(String sql) {
        List<RecodeDetailClickHouseDto> recodeDetailList = Lists.newArrayList();
        String uuid = UUID.randomUUID().toString();
        ClickHouseUserProfile.set(
                new ClickHouseUserProfile.Builder()
                        .ksAuthPrincipal(PRINCIPAL)
                        .ksAuthToken(getDSCToken())
                        .ksAuthType(AUTH_TYPE)
                        .ksQueryId(uuid)
                        .ksTaskGroupId(String.valueOf(SYT_GROUP_ID))
                        .build());
        log.info("ClickHouseConnector.executeSimpleSql.uuid:{} , executeSql:{}", uuid, sql);
        ResultSet resultSet = null;
        try (Connection connection = getConnect(); Statement stmt = connection.createStatement()) {
            resultSet = stmt.executeQuery(sql);
            recodeDetailList =
                    ResultSetMapperUtil.mapResultSetToObject(resultSet, RecodeDetailClickHouseDto.class);
        } catch (Exception e) {
            log.error("ClickHouseConnector.executeSimpleSql.executeSql error, sql:{}", sql, e);
        } finally {
            ClickHouseUserProfile.remove();
        }
        return recodeDetailList;
    }

    public static Long executeSimpleSqlReturnLong(String sql) {
        List<Long> total = Lists.newArrayList();
        String uuid = UUID.randomUUID().toString();
        ClickHouseUserProfile.set(
                new ClickHouseUserProfile.Builder()
                        .ksAuthPrincipal(PRINCIPAL)
                        .ksAuthToken(getDSCToken())
                        .ksAuthType(AUTH_TYPE)
                        .ksQueryId(uuid)
                        .ksTaskGroupId(String.valueOf(SYT_GROUP_ID))
                        .build());
        log.info("ClickHouseConnector.executeSimpleSql.uuid:{} , executeSql:{}", uuid, sql);
        ResultSet resultSet = null;
        try (Connection connection = getConnect(); Statement stmt = connection.createStatement()) {
            resultSet = stmt.executeQuery(sql);
            total = ResultSetMapperUtil.mapResultSetToLong(resultSet);
        } catch (Exception e) {
            log.error("ClickHouseConnector.executeSimpleSql.executeSql error, sql:{}", sql, e);
        } finally {
            ClickHouseUserProfile.remove();
        }
        return total.get(0);
    }

    public static String executeSimpleSqlReturnString(String sql) {
        List<String> total = Lists.newArrayList();
        String uuid = UUID.randomUUID().toString();
        ClickHouseUserProfile.set(
                new ClickHouseUserProfile.Builder()
                        .ksAuthPrincipal(PRINCIPAL)
                        .ksAuthToken(getDSCToken())
                        .ksAuthType(AUTH_TYPE)
                        .ksQueryId(uuid)
                        .ksTaskGroupId(String.valueOf(SYT_GROUP_ID))
                        .build());
        log.info("ClickHouseConnector.executeSimpleSql.uuid:{} , executeSql:{}", uuid, sql);
        ResultSet resultSet = null;
        try (Connection connection = getConnect(); Statement stmt = connection.createStatement()) {
            resultSet = stmt.executeQuery(sql);
            total = ResultSetMapperUtil.mapResultSetToString(resultSet);
        } catch (Exception e) {
            log.error("ClickHouseConnector.executeSimpleSql.executeSql error, sql:{}", sql, e);
        } finally {
            ClickHouseUserProfile.remove();
        }
        return total.get(0);
    }

    public static String getDSCToken() {
        log.info("ClickHouseConnector.getDSCToken.principal:{}, secretKey:{}", PRINCIPAL, SECRET_KEY);
        String token = DscAccessTokenProvider.getToken(PRINCIPAL, SECRET_KEY);
        log.info("ClickHouseConnector.getDSCToken.token:{}, principal:{}, secretKey:{}", token, PRINCIPAL, SECRET_KEY);
        return token;
    }

}
