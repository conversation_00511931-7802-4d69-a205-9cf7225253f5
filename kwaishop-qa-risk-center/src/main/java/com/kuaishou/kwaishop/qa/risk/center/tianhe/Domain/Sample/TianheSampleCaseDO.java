package com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Sample;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-19
 */
// TODO appkey 和 pagecode删掉 用 prtUrl 和 onlineUrl作为主键
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tianhe_risk_sample_config")
public class TianheSampleCaseDO implements Serializable {
    private static final long serialVersionUID = 5438209372564089212L;
    @TableId(value = "id", type = IdType.AUTO)
    private long id;
    /**
     * 用例名称
     */
    private String name;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 来源平台
     */
    private String platformSource;
    /**
     * 业务域Code
     */
    private String domainCode;
    /**
     * 应用key
     */
    private String appKey;
    /**
     * 页面code
     */
    private String pageCode;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 更新时间
     */
    private Long updateTime;
    /**
     * 是否被删除
     */
    private Integer deleted;
    /**
     * 指标创建人工号
     */
    private String creator;
    /**
     * 最后修改者工号
     */
    private String modifier;
    /**
     * 可编辑人群
     */
    private String operators;
    /**
     * 扩展字段
     */
    private String ext;
    /**
     * prt链接
     */
    private String prtUrl;
    /**
     * 线上链接
     */
    private String onlineUrl;
    /**
     * 执行动作
     */
    private String actionSet;
}
