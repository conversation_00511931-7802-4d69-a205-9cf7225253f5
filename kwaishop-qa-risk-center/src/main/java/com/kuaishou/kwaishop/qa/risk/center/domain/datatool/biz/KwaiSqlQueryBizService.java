package com.kuaishou.kwaishop.qa.risk.center.domain.datatool.biz;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DATA_SOURCE_ID_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DM_QUERY_CODE_EMPTY;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DM_QUERY_ID_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DM_SERVICE_CODE_ERROR;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.datarch.themis.sdk.vo.base.ColumnEntity;
import com.kuaishou.datarch.themis.sdk.vo.base.DatabaseEntity;
import com.kuaishou.datarch.themis.sdk.vo.base.TableEntity;
import com.kuaishou.datarch.themis.sdk.vo.job.ColumnAndType;
import com.kuaishou.datarch.themis.sdk.vo.job.PageVo;
import com.kuaishou.intown.json.JSON;
import com.kuaishou.intown.json.JSONArray;
import com.kuaishou.intown.json.JSONObject;
import com.kuaishou.krpc.config.annotation.KrpcReference;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.config.constants.QueryStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.config.kwaisql.DpAccessFetch;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataQueryExecuteDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataQueryDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataSourceDo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DataMeta;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Dm2GetByServiceCodeRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Dm2GetByServiceCodeResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Dm2QueryDebugListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Dm2QueryDebugListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DmDebugData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DmGetByQueryIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DmGetByQueryIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DmQueryRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DmQueryResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.GetColumnListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.GetColumnListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.GetMetaDatabaseListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.GetMetaDatabaseListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.GetTableListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.GetTableListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.KwaiSqlQueryServiceRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.KwaiSqlQueryServiceResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.MapField;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.ParamValue;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryByQueryIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryByQueryIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.utils.RegexUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.date.LocalDateUtil;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.DataQueryRequest;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.KrpcKwaishopSdmGeneralServiceGrpc;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.KrpcKwaishopSellerdataDataServiceGrpc;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.KrpcKwaishopSellerdataQueryDataServiceGrpc;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.KrpcKwaishopSellerdataVersionServiceGrpc;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.Param;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.QueryDebugRecord;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.QueryDebugRecordRequest;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.QueryDebugRecordResponse;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.SdmDataMeta;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.SdmDataRawResponse;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.SdmDataResultRow;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.SdmGeneralRequest;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.SdmGeneralResponse;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.SdmParamValue;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.SellerdataCommonPageResponse;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.SellerdataCommonResponse;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.ServiceCommonRequest;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.ServiceQueryRequest;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.VersionQueryRequest;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.admin.GetManagerApiRequest;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.admin.KrpcKwaishopSellerdataManagementAdminServiceGrpc;
import com.kuaishou.kwaishop.sellerdata.management.service.protobuf.admin.ManagerApiDTO;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-18
 */

@Service
@Slf4j
public class KwaiSqlQueryBizService {

    @Autowired
    private DpAccessFetch dpAccessFetch;

    @Autowired
    private DataQueryExecuteDAO dataQueryExecuteDAO;


    @KrpcReference(serviceName = "kwaishop-sellerdata-management-service")
    private KrpcKwaishopSellerdataManagementAdminServiceGrpc.IKwaishopSellerdataManagementAdminService
            managementAdminService;
    @KrpcReference(serviceName = "kwaishop-sellerdata-management-service")
    private KrpcKwaishopSdmGeneralServiceGrpc.IKwaishopSdmGeneralService sdmGeneralService;

    @KrpcReference(serviceName = "kwaishop-sellerdata-management-service")
    private KrpcKwaishopSellerdataQueryDataServiceGrpc.IKwaishopSellerdataQueryDataService queryDataService;

    @KrpcReference(serviceName = "kwaishop-sellerdata-management-service")
    private KrpcKwaishopSellerdataDataServiceGrpc.IKwaishopSellerdataDataService sellerdataDataService;

    @KrpcReference(serviceName = "kwaishop-sellerdata-management-service")
    private KrpcKwaishopSellerdataVersionServiceGrpc.IKwaishopSellerdataVersionService sellerdataVersionService;

    public KwaiSqlQueryServiceResponse fetchDpData(KwaiSqlQueryServiceRequest request) {
        if (StringUtils.isBlank(request.getQuerySql())) {
            return KwaiSqlQueryServiceResponse.newBuilder().setResult(BizErrorCode.KWAISQL_QUERY_SQL_EMPTY.getCode())
                    .setErrorMsg(BizErrorCode.KWAISQL_QUERY_SQL_EMPTY.getMessage()).build();
        }
        if (request.getQueryType() <= 0) {
            return KwaiSqlQueryServiceResponse.newBuilder().setResult(BizErrorCode.KWAISQL_QUERY_TYPE_ERROR.getCode())
                    .setErrorMsg(BizErrorCode.KWAISQL_QUERY_TYPE_ERROR.getMessage()).build();
        }
        try {
            String userName = request.getUser();
            if (userName.equals("") || StringUtils.isBlank(userName)) {
                userName = "chenlijun";
            }
            List<Map<String, Object>> mapList = dpAccessFetch
                    .fetchDpData(request.getQueryType(), request.getQuerySql(), request.getDatasourceId(), userName);
            log.info("[kwaiSqlQueryBizService] fetchDpData mapList 长度: ", mapList.size());

            if (mapList.size() > 0 && !mapList.get(0).containsKey("queryExeID")) {
                List<MapField> mapFieldList = mapList.stream().map(this::buildMapField).collect(Collectors.toList());
                return KwaiSqlQueryServiceResponse.newBuilder().addAllMaps(mapFieldList)
                        .setResult(BaseResultCode.SUCCESS_VALUE).build();
            }
            if (mapList.size() > 0 && mapList.get(0).containsKey("queryExeID")) {
                DataQueryDO dataQueryDO = dataQueryExecuteDAO.findById((Long) mapList.get(0).get("queryExeID"));
                if (dataQueryDO.getLog().contains("FAILED: AuthorizationFailedException")) {
                    return KwaiSqlQueryServiceResponse.newBuilder()
                            .setResult(BizErrorCode.META_QUERY_AUTH_ERROR.getCode())
                            .setErrorMsg(BizErrorCode.META_QUERY_AUTH_ERROR.getMessage())
                            .build();
                }
            }

            return KwaiSqlQueryServiceResponse.newBuilder()
                    .setResult(BizErrorCode.KWAISQL_QUERY_DATA_EMPTY.getCode())
                    .setErrorMsg(BizErrorCode.KWAISQL_QUERY_DATA_EMPTY.getMessage())
                    .build();

        } catch (BizException e) {
            log.error("[kwaiSqlQueryBizService] fetchDpData bizError, exception: ", e);
            return KwaiSqlQueryServiceResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[kwaiSqlQueryBizService] fetchDpData Error, exception: ", e);
            return KwaiSqlQueryServiceResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    public KwaiSqlQueryServiceResponse fetchSqlColumn(KwaiSqlQueryServiceRequest request) {
        if (StringUtils.isBlank(request.getQuerySql())) {
            return KwaiSqlQueryServiceResponse.newBuilder().setResult(BizErrorCode.KWAISQL_QUERY_SQL_EMPTY.getCode())
                    .setErrorMsg(BizErrorCode.KWAISQL_QUERY_SQL_EMPTY.getMessage()).build();
        }
        if (request.getQueryType() <= 0) {
            return KwaiSqlQueryServiceResponse.newBuilder().setResult(BizErrorCode.KWAISQL_QUERY_TYPE_ERROR.getCode())
                    .setErrorMsg(BizErrorCode.KWAISQL_QUERY_TYPE_ERROR.getMessage()).build();
        }
        try {
            String userName = request.getUser();
            if (userName.equals("") || StringUtils.isBlank(userName)) {
                userName = "zhangdongjun";
            }
            List<ColumnAndType> mapList = dpAccessFetch
                    .fetchSqlResultColumn(request.getQueryType(), request.getQuerySql(), request.getDatasourceId(), userName);
            log.info("[kwaiSqlQueryBizService] fetchDpData mapList 长度: ", mapList.size());
            List<MapField> mapFieldList = new ArrayList<>();
            if (mapList.size() > 0) {
                for (ColumnAndType columnAndType : mapList) {
                    Map<String, String> column = new HashMap<>();
                    column.put("columnName", columnAndType.getName());
                    column.put("columnType", columnAndType.getType());
                    mapFieldList.add(MapField.newBuilder().putAllStringMap(column).build());
                }
                return KwaiSqlQueryServiceResponse.newBuilder().addAllMaps(mapFieldList)
                        .setResult(BaseResultCode.SUCCESS_VALUE).build();
            }

            return KwaiSqlQueryServiceResponse.newBuilder()
                    .setResult(BizErrorCode.KWAISQL_QUERY_DATA_EMPTY.getCode())
                    .setErrorMsg(BizErrorCode.KWAISQL_QUERY_DATA_EMPTY.getMessage())
                    .build();

        } catch (BizException e) {
            log.error("[kwaiSqlQueryBizService] fetchSqlColumn bizError, exception: ", e);
            return KwaiSqlQueryServiceResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[kwaiSqlQueryBizService] fetchSqlColumn Error, exception: ", e);
            return KwaiSqlQueryServiceResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    public boolean doFetchDpData(DataSourceDo dataSourceDo, Long dataQueryId) {
        try {
            JSONObject queryContent = JSONObject.parseObject(dataSourceDo.getContent());
            JSONObject sqlContent = queryContent.getJSONObject("SQLContent");
            String sqlString = setSqlParams(sqlContent.getString("querySql"));
            String userName = "";
            if (userName.equals("") || StringUtils.isBlank(userName)) {
                userName = "chenlijun";
            }
            JSONObject responseJson = new JSONObject();
            List<Map<String, Object>> mapList = dpAccessFetch
                    .fetchDpData(dataSourceDo.getType(), sqlString, dataSourceDo.getId(), userName);
            if (mapList.size() > 0 && !mapList.get(0).containsKey("queryExeID")) {
                List<MapField> mapFieldList = mapList.stream().map(this::buildMapField).collect(Collectors.toList());
                String dataJsonString = JSONObject.toJSONString(mapList.get(0).get("string_map"));

                List<String> headers = new ArrayList<>(JSONObject.parseObject(dataJsonString).keySet());
                JSONArray dataRowList = new JSONArray();

                for (MapField mapField : mapFieldList) {
                    dataRowList.add(mapField.getStringMapMap());
                }
                responseJson.put("headers", headers);
                responseJson.put("result_row", dataRowList);
                responseJson.put("total", dataRowList.size());
                dataQueryExecuteDAO.updateLog(dataQueryId, "query:" + queryContent + "log:" + mapFieldList.toString());
                dataQueryExecuteDAO.updateResult(dataQueryId, responseJson.toString());
                dataQueryExecuteDAO.updateStatus(dataQueryId, QueryStatusEnum.SUCCESS.getValue());

                return true;
            }

            if (mapList.size() > 0 && mapList.get(0).containsKey("queryExeID")) {
                DataQueryDO dataQueryDO = dataQueryExecuteDAO.findById((Long) mapList.get(0).get("queryExeID"));
                if (dataQueryDO.getLog().contains("FAILED: AuthorizationFailedException")) {
                    responseJson.put("headers",
                            "无表权限" + RegexUtils.getMatchStr(dataQueryDO.getLog(), "[a-z_]+[.][a-z_0-9]+") + ",申请地址"
                                    + RegexUtils.getMatchStr(dataQueryDO.getLog(), "<(a)[^>]*>.*?|<.*? /> "));
                    responseJson.put("result_row", null);
                    responseJson.put("total", 0);
                    dataQueryExecuteDAO.updateResult(dataQueryId, responseJson.toString());
                    dataQueryExecuteDAO.updateStatus(dataQueryId, QueryStatusEnum.FAIL.getValue());
                    return false;
                }
            }

            responseJson.put("headers", null);
            responseJson.put("result_row", null);
            responseJson.put("total", 0);
            dataQueryExecuteDAO.updateLog(dataQueryId, "query:" + queryContent + "log:");
            dataQueryExecuteDAO.updateResult(dataQueryId, responseJson.toString());
            dataQueryExecuteDAO.updateStatus(dataQueryId, QueryStatusEnum.FAIL.getValue());
            return false;

        } catch (BizException e) {
            log.error("[kwaiSqlQueryBizService] fetchDpData bizError, exception: ", e);
            return false;
        } catch (Exception e) {
            log.error("[kwaiSqlQueryBizService] fetchDpData Error, exception: ", e);
            return false;
        }


    }

    public String setSqlParams(String orgSql) {
        try {
            if (null != orgSql && !"".equals(orgSql.trim())) {
                String regEx = "(?<=\\{)(.+?)(?=\\})";
                Pattern p = Pattern.compile(regEx);
                Matcher m = p.matcher(orgSql);
                List<String> paramsList = new ArrayList<>();
                List<Map<String, String>> replaceParamsMap = new ArrayList<>();
                while (m.find()) {
                    paramsList.add(m.group());
                    Map<String, String> dynamicParam = new HashMap<>();
                    dynamicParam.put("originParamStr", "${" + m.group() + "}");
                    String paramStr = m.group().replaceAll(" ", "");
                    String calculateDate = "";
                    if (paramStr.split("(\\+|-)").length == 1) {
                        calculateDate = LocalDateUtil.getYesterdayDateStringBasic();
                        System.out.println(calculateDate);
                    } else if (paramStr.split("(\\+|-)").length == 2) {
                        if (paramStr.contains("+")) {
                            System.out.println(Integer.parseInt(paramStr.split("(\\+|-)")[1]));
                            calculateDate = LocalDateUtil
                                    .getFeatureDateStringBasic(Integer.parseInt(paramStr.split("(\\+|-)")[1]));
                            System.out.println(calculateDate);

                        } else if (paramStr.contains("-")) {
                            calculateDate = LocalDateUtil
                                    .getPastDateStringBasic(Integer.parseInt(paramStr.split("(\\+|-)")[1]));
                            System.out.println(calculateDate);
                        }
                    }
                    dynamicParam.put("calculateDate", calculateDate);
                    replaceParamsMap.add(dynamicParam);
                }
                String paramReg = orgSql;
                for (Map<String, String> replaceParam
                        :
                        replaceParamsMap) {
                    paramReg = paramReg.replace(replaceParam.get("originParamStr"), replaceParam.get("calculateDate"));
                }

                return paramReg;
            }
            return null;
        } catch (BizException e) {
            log.error("[kwaiSqlQueryBizService] fetchDpData bizError, exception: ", e);
            return null;
        }

    }

    public SdmParamValue transToSdmParamValue(ParamValue paramValue) {
        List<String> paramValueList = paramValue.getValueList();

        return SdmParamValue.newBuilder().addAllValue(paramValueList)
                .build();
    }

    public DataMeta transToDataMeta(SdmDataMeta sdmDataMeta) {
        if (sdmDataMeta != null) {
            return DataMeta.newBuilder().setName(sdmDataMeta.getName())
                    .setValueType(sdmDataMeta.getValueType().toString()).build();
        }
        return null;

    }


    /**
     * dm2.0 取数sql调用，返回执行结果
     */
    public DmQueryResponse dmQuery(DmQueryRequest request) {
        if (StringUtils.isBlank(request.getServiceCode())) {
            throw new BizException(DM_QUERY_CODE_EMPTY);
        }
        try {
            Map<String, ParamValue> paramValueMap = request.getParamMap();
            Map<String, SdmParamValue> sdmParamValueMap = paramValueMap.entrySet().stream()
                    .collect(Collectors.toMap(Entry::getKey, a -> transToSdmParamValue(a.getValue())));

            Map<String, String> extInfo = request.getExtInfoMap();


            DataQueryRequest dataQueryRequest = DataQueryRequest.newBuilder()
                    .setServiceCode(request.getServiceCode())
                    .putAllParam(sdmParamValueMap)
                    .putAllExtInfo(extInfo)
                    .build();
            SdmDataRawResponse response = queryDataService.queryRawData(dataQueryRequest);
            log.info("[queryDataService] SdmDataRawResponse res:", JSON.toJSONString(response));


            List<DataMeta> sdmDataMetaList = response.getDataMetaList().stream()
                    .map(this::transToDataMeta).collect(
                            Collectors.toList());

            List<SdmDataResultRow> dataResultRows =
                    response.getResultRowList();

            DmQueryResponse dmQueryResponse;
            List<String> headerListInfo =
                    sdmDataMetaList.stream().map(a -> JSON.toJSONString(a)).collect(Collectors.toList());
            List<String> headerList = sdmDataMetaList.stream().map(DataMeta::getName).collect(Collectors.toList());

            // name 和value列表合并成mapList
            List<String> dataRowList = new ArrayList<>();
            for (SdmDataResultRow dataResultRow : dataResultRows) {
                Map<String, Object> objectMap = IntStream.range(0, sdmDataMetaList.size()).collect(HashMap::new,
                        (m, i) -> m.put(sdmDataMetaList.get(i).getName(), dataResultRow.getResultField(i).getValue()),
                        (m, n) -> {
                        });
                dataRowList.add(JSON.toJSONString(objectMap));
            }

            if (response.getResult() == 1) {
                dmQueryResponse = DmQueryResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE)
                        .addAllHeaders(headerList)
                        .addAllHeadersInfo(headerListInfo)
                        .addAllResultRow(dataRowList)
                        .setTotal(dataResultRows.size()).build();
            } else {
                dmQueryResponse =
                        DmQueryResponse.newBuilder().setResult(response.getResult()).setErrorMsg(response.getErrorMsg())
                                .build();
            }

            log.info("[queryDataService] dmQuery DmQueryResponse:", dataRowList.size());
            return dmQueryResponse;

        } catch (BizException e) {
            log.error("[kwaiSqlQueryBizService] dmQuery bizError, exception: ", e);
            return DmQueryResponse.newBuilder().setResult(e.getCode()).setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[kwaiSqlQueryBizService] dmQuery Error, exception: ", e);
            return DmQueryResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    public boolean doDm20Query(DataSourceDo dataSourceDo, Long dataQueryId) {
        if (dataSourceDo == null) {
            throw new BizException(DM_QUERY_ID_ERROR);
        }

        try {
            JSONObject queryContent = JSONObject.parseObject(dataSourceDo.getContent());
            String serviceCode = queryContent.getString("serviceCode");
            JSONObject paramJson = queryContent.getJSONObject("queryParams");
            Map<String, ParamValue> paramValueMap = new HashMap<>();
            for (String keyName : paramJson.keySet()) {
                JSONArray valueJsonArray = paramJson.getJSONObject(keyName).getJSONArray("value");
                List<String> valueList = new ArrayList<>();
                for (int i = 0; i < valueJsonArray.size(); i++) {
                    valueList.add(valueJsonArray.get(i).toString());
                }
                ParamValue pV = ParamValue.newBuilder()
                        .addAllValue(valueList)
                        .build();
                paramValueMap.put(keyName, pV);

            }
            log.info("[kwaiSqlQueryBizService] doDm20Query: {}", paramValueMap.toString());
            Map<String, String> extInfo = new HashMap<>();
            DmQueryRequest request = DmQueryRequest.newBuilder()
                    .setServiceCode(serviceCode)
                    .putAllParam(paramValueMap)
                    .putAllExtInfo(extInfo)
                    .build();
            log.info("[kwaiSqlQueryBizService] doDm20Query: {}", ProtobufUtil.protoToJsonString(request));
            DmQueryResponse dmQueryResponse = dmQuery(request);
            List<String> dataResultRows = dmQueryResponse.getResultRowList();
            JSONObject responseJson = new JSONObject();
            JSONArray dataRowList = new JSONArray();
            for (String dataResultRow : dataResultRows) {
                dataRowList.add(JSONObject.parseObject(dataResultRow));
                log.info("[kwaiSqlQueryBizService]  dmQueryResponse: {}", dataResultRow);
            }
            log.info("[KwaiSqlQueryDomainServiceImpl] dmQueryResponse: {}",
                    ProtobufUtil.protoToJsonString(dmQueryResponse));
            responseJson.put("headers", dmQueryResponse.getHeadersList());
            responseJson.put("result_row", dataRowList);
            responseJson.put("total", dataRowList.size());
            dataQueryExecuteDAO.updateLog(dataQueryId, "query:" + queryContent + "log:" + responseJson.toString());
            dataQueryExecuteDAO.updateResult(dataQueryId, responseJson.toString());
            if (dmQueryResponse.getResult() == 1) {
                dataQueryExecuteDAO.updateStatus(dataQueryId, QueryStatusEnum.SUCCESS.getValue());
            } else {
                dataQueryExecuteDAO.updateStatus(dataQueryId, QueryStatusEnum.FAIL.getValue());
            }
            return true;

        } catch (BizException e) {
            log.error("[kwaiSqlQueryBizService] dmQueryByQueryId bizError, exception: ", e);
            return false;
        } catch (Exception e) {
            log.error("[kwaiSqlQueryBizService] dmQuery Error, exception: ", e);
            return false;
        }
    }

    public Param transToSdmParam(com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Param param) {
        if (param != null) {
            List<String> valueList = param.getValueList();
            return Param.newBuilder().setName(param.getName()).addAllValue(valueList).build();
        }
        return null;
    }

    /**
     * 查询DM2。0调试记录
     */
    public Dm2QueryDebugListResponse queryDebugList(Dm2QueryDebugListRequest request) {
        if (StringUtils.isBlank(request.getServiceCode())) {
            throw new BizException(DM_SERVICE_CODE_ERROR);
        }

        int page = 0;
        int pageSize = 0;

        if (request.getPageSize() == 0 || request.getPage() == 0) {
            page = 1;
            pageSize = 15;
        } else {
            page = request.getPage();
            pageSize = request.getPageSize();
        }

        try {
            QueryDebugRecordRequest debugRecordRequest =
                    QueryDebugRecordRequest.newBuilder().setServiceCode(request.getServiceCode()).setPageSize(pageSize)
                            .setPage(page).build();
            //查询serviceCode的服务入参，如果debugRecord 返回params和服务入参一致，则直接返回，否则服务入参不存在的字段
            QueryDebugRecordResponse response = queryDataService.queryDebugRecord(debugRecordRequest);
            List<QueryDebugRecord> dataList = response.getDataList();


            List<DmDebugData> debugDataList =
                    dataList.stream().map(this::transToDmDebugData)
                            .collect(Collectors.toList());
            log.info("[kwaiSqlQueryBizService] debugDataList context: {}", JSON.toJSONString(debugDataList));
            Dm2QueryDebugListResponse dm2QueryDebugListResponse = null;
            if (dataList.size() > 0) {
                dm2QueryDebugListResponse =
                        Dm2QueryDebugListResponse.newBuilder().addAllDebugList(debugDataList).setTotal(
                                String.valueOf(debugDataList.size())).setResult(BaseResultCode.SUCCESS_VALUE).build();
            } else {
                dm2QueryDebugListResponse =
                        Dm2QueryDebugListResponse.newBuilder().setTotal("0").setResult(BaseResultCode.SUCCESS_VALUE)
                                .build();
            }

            return dm2QueryDebugListResponse;

        } catch (BizException e) {
            log.error("[kwaiSqlQueryBizService] queryDebugList bizError, exception: ", e);
            return Dm2QueryDebugListResponse.newBuilder().setTotal("0").build();
        } catch (Exception e) {
            log.error("[kwaiSqlQueryBizService] queryDebugList Error, exception: ", e);
            return Dm2QueryDebugListResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .build();
        }
    }


    public DmDebugData transToDmDebugData(QueryDebugRecord queryDebugRecord) {

        if (!StringUtils.isBlank(queryDebugRecord.getResponse())) {
            String time = queryDebugRecord.getTime();
            JSONObject request = JSONObject.parseObject(queryDebugRecord.getRequest());
            JSONObject params = request.getJSONObject("param");

            //直接返回record的参数，需要用户手动替换，场景太多了
            List<com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Param> paramList = new ArrayList<>();
            for (String key : params.keySet()) {
                List<String> valueList = new ArrayList<>();
                JSONArray values = params.getJSONArray(key);
                for (int i = 0; i < values.size(); i++) {
                    valueList.add(values.get(i).toString());
                }
                com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Param param =
                        com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Param.newBuilder().setName(key)
                                .addAllValue(valueList).build();
                paramList.add(param);
            }
            log.info("[kwaiSqlQueryBizService] param context: {}", JSON.toJSONString(paramList));
            return DmDebugData.newBuilder().addAllParam(paramList).setTime(time).build();

        } else {
            return DmDebugData.newBuilder().setTime("2070-01-01").build();
        }
    }


    /**
     * Dm2。0配置查询
     */
    public Dm2GetByServiceCodeResponse dm2GetByServiceCode(Dm2GetByServiceCodeRequest request) {
        if (StringUtils.isBlank(request.getServiceCode()) && StringUtils.isBlank(request.getVersionId())) {
            throw new BizException(DM_SERVICE_CODE_ERROR);
        }
        try {

            String serviceCode = "";

            int versionId = 0;
            if (!StringUtils.isBlank(request.getVersionId())) {
                versionId = Integer.parseInt(request.getVersionId());

            } else {
                serviceCode = request.getServiceCode();
                // 查询所有服务列表，查询serviceId
                ServiceQueryRequest serviceQueryRequest =
                        ServiceQueryRequest.newBuilder().setUserCode("qa_risk").build();
                SellerdataCommonPageResponse sellerdataCommonPageResponse =
                        sellerdataDataService.query(serviceQueryRequest);
                String data = sellerdataCommonPageResponse.getData();
                if (StringUtils.isBlank(data)) {
                    log.info("[kwaiSqlQueryBizService] dm2GetByServiceCode ServiceCode不存在:", data);
                    return Dm2GetByServiceCodeResponse.newBuilder().setServiceCode(request.getServiceCode()).build();
                }
                //匹配serviceCode名称
                JSONArray dataJsonArray = JSONArray.parseArray(data);
                long serviceId = 0;
                for (int i = 0; i < dataJsonArray.size(); i++) {
                    if (dataJsonArray.getJSONObject(i).get("code").equals(request.getServiceCode())) {
                        serviceId = (Integer) dataJsonArray.getJSONObject(i).get("id");
                        break;
                    }
                }

                if (serviceId != 0) {
                    // 根据serverId查询versionID
                    ServiceCommonRequest serviceCommonRequest =
                            ServiceCommonRequest.newBuilder().setServiceId(serviceId).build();
                    SellerdataCommonResponse sellerdataCommonResponse =
                            sellerdataVersionService.queryList(serviceCommonRequest);
                    JSONArray versionJsonArray = JSONArray.parseArray(sellerdataCommonResponse.getData());

                    if (versionJsonArray.size() == 0) {
                        log.info("[kwaiSqlQueryBizService] dm2GetByServiceCode 未查询到版本id:", versionJsonArray);
                        return Dm2GetByServiceCodeResponse.newBuilder().setServiceCode(request.getServiceCode())
                                .setVersionId(versionId).build();
                    }
                    versionId = (int) versionJsonArray.getJSONObject(0).get("id");
                } else {
                    log.info("[kwaiSqlQueryBizService] dm2GetByServiceCode serviceCode查询到serviceId为空:", serviceId);
                    return Dm2GetByServiceCodeResponse.newBuilder().setServiceCode(request.getServiceCode())
                            .setVersionId(versionId).build();
                }

                if (versionId == 0) {
                    log.info("[kwaiSqlQueryBizService] dm2GetByServiceCode serviceCode查询到versionId为空:", versionId);
                    return Dm2GetByServiceCodeResponse.newBuilder().setServiceCode(request.getServiceCode())
                            .setVersionId(versionId).build();
                }

            }


            // 根据versionID 查询取数配置
            VersionQueryRequest versionQueryRequest = VersionQueryRequest.newBuilder().setVersionId(versionId).build();

            SellerdataCommonResponse response = sellerdataVersionService.query(versionQueryRequest);
            if (StringUtils.isBlank(response.getData()) || response.getResult() != 1) {
                log.info("[kwaiSqlQueryBizService] dm2GetByServiceCode 取数配置为空:", response.getData());
                return Dm2GetByServiceCodeResponse.newBuilder().setServiceCode(request.getServiceCode()).build();
            }

            JSONObject dataConfig = JSONObject.parseObject(response.getData());


            JSONObject configText = JSONObject.parseObject(dataConfig.get("configText").toString());
            JSONObject requestParams = JSONObject.parseObject(configText.get("requestParams").toString());
            String dataSourceUrl =
                    "https://lego.corp.kuaishou.com/page/fangzhou/sdm/version/edit?id=" + dataConfig.get("id")
                            + "&serviceId=" + dataConfig.get("serviceId");

            log.info(String.format(
                    "[kwaiSqlQueryBizService] dm2GetByServiceCode: requestParams,serviceId,versionId,p, %s%s%s%s, ",
                    requestParams, dataConfig.get("serviceId"), dataConfig.get("id"), serviceCode));
            if (serviceCode.equals("")) {
                // 查询所有服务列表，查询serviceId
                ServiceQueryRequest serviceQueryRequest =
                        ServiceQueryRequest.newBuilder().setUserCode("qa_risk").build();
                SellerdataCommonPageResponse sellerdataCommonPageResponse =
                        sellerdataDataService.query(serviceQueryRequest);
                String data = sellerdataCommonPageResponse.getData();
                //匹配serviceId 后面的ServiceCOde
                JSONArray dataJsonArray = JSONArray.parseArray(data);
                for (int i = 0; i < dataJsonArray.size(); i++) {
                    if (dataJsonArray.getJSONObject(i).get("id").equals(dataConfig.get("serviceId"))) {
                        serviceCode = (String) dataJsonArray.getJSONObject(i).get("code");
                        break;
                    }
                }
            }


            return Dm2GetByServiceCodeResponse.newBuilder().setServiceCode(serviceCode)
                    .setServiceId((int) dataConfig.get("serviceId"))
                    .setVersionId((int) dataConfig.get("id"))
                    .setCreatePerson(dataConfig.get("createPerson").toString())
                    .setUpdatePerson(dataConfig.get("updatePerson").toString())
                    .setCreatedAt(dataConfig.get("createdAt").toString())
                    .setUpdatedAt(dataConfig.get("updatedAt").toString())
                    .setRequestParams(requestParams.toJSONString())
                    .setConfigText(configText.toString())
                    .setDataSourceUrl(dataSourceUrl)
                    .setVersionStatus(dataConfig.get("versionStatus").toString()).build();


        } catch (BizException e) {
            log.error("[kwaiSqlQueryBizService] dm2GetByServiceCode bizError, exception: ", e);
        } catch (Exception e) {
            log.error("[kwaiSqlQueryBizService] dm2GetByServiceCode Error, exception: ", e);
        }
        return Dm2GetByServiceCodeResponse.newBuilder().setServiceCode(request.getServiceCode()).build();
    }


    /**
     * dm 查询
     */
    public QueryByQueryIdResponse dmQueryByQueryId(QueryByQueryIdRequest request) {
        if (request.getQueryId() <= 0) {
            throw new BizException(DM_QUERY_ID_ERROR);
        }

        try {

            List<Param> paramList =
                    request.getParamList().stream().map(this::transToSdmParam).collect(Collectors.toList());
            Map<String, String> extInfo = request.getExtInfoMap();


            SdmGeneralRequest sdmGeneralRequest = SdmGeneralRequest.newBuilder()
                    .setQueryId(request.getQueryId())
                    .addAllParam(paramList)
                    .putAllExtInfo(extInfo).build();

            SdmGeneralResponse sdmGeneralResponse = sdmGeneralService.queryNormal(sdmGeneralRequest);

            List<com.kuaishou.kwaishop.sellerdata.management.service.protobuf.DataMeta> dataMetaList =
                    sdmGeneralResponse.getDataMetaList();
            List<com.kuaishou.kwaishop.sellerdata.management.service.protobuf.DataResultRow> dataResultRows =
                    sdmGeneralResponse.getResultRowList();


            QueryByQueryIdResponse response;
            List<String> headerListInfo =
                    dataMetaList.stream().map(a -> JSON.toJSONString(a)).collect(Collectors.toList());

            List<String> headerList = dataMetaList.stream().map(
                            com.kuaishou.kwaishop.sellerdata.management.service.protobuf.DataMeta::getName)
                    .collect(Collectors.toList());

            // name 和value列表合并成mapList

            List<String> dataRowList = new ArrayList<>();
            for (com.kuaishou.kwaishop.sellerdata.management.service.protobuf.DataResultRow
                    dataResultRow : dataResultRows) {
                Map<String, Object> objectMap = IntStream.range(0, dataMetaList.size())
                        .collect(HashMap::new, (m, i) -> m
                                        .put(dataMetaList.get(i).getName(), dataResultRow.getResultField(i).getValue()),
                                (m, n) -> {
                                });
                dataRowList.add(JSON.toJSONString(objectMap));
            }

            if (sdmGeneralResponse.getBaseRsp().getStatus().getNumber() == 0) {
                response = QueryByQueryIdResponse.newBuilder()
                        .setResult(BaseResultCode.SUCCESS_VALUE)
                        .addAllHeaders(headerList)
                        .addAllHeadersInfo(headerListInfo)
                        .addAllResultRow(dataRowList)
                        .setTotal(dataResultRows.size())
                        .build();
                return response;
            } else {
                response = QueryByQueryIdResponse.newBuilder()
                        .setResult(sdmGeneralResponse.getBaseRsp().getStatus().getNumber())
                        .setErrorMsg(sdmGeneralResponse.getBaseRsp().getErrorMsg())
                        .build();
                return response;
            }
        } catch (BizException e) {
            log.error("[kwaiSqlQueryBizService] dmQueryByQueryId bizError, exception: ", e);
            return QueryByQueryIdResponse.newBuilder().setResult(e.getCode()).setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[kwaiSqlQueryBizService] dmQuery Error, exception: ", e);
            return QueryByQueryIdResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    public Param transJSONToSdmParam(JSONObject param) {
        if (param != null) {
            JSONArray jsonValueList = param.getJSONArray("value");
            List<String> valueList = new ArrayList<>();
            for (int index = 0; index < jsonValueList.size(); index++) {
                valueList.add(jsonValueList.getObject(index, String.class));
            }

            return Param.newBuilder().setName(param.getString("name")).addAllValue(valueList).build();
        }
        return null;
    }

    public com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Param transJsonToRiskParam(JSONObject param) {
        if (param != null) {
            for (String key : param.keySet()) {
                List<String> valueList = new ArrayList<>();
                JSONArray values = param.getJSONArray(key);
                for (int i = 0; i < values.size(); i++) {
                    valueList.add(values.get(i).toString());
                }
                com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Param param1 =
                        com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Param.newBuilder().setName(key)
                                .addAllValue(valueList).build();
            }
        }
        return null;
    }


    public boolean doQueryByQueryId(DataSourceDo dataSourceDo, Long dataQueryId) {
        if (dataSourceDo == null) {
            throw new BizException(DM_QUERY_ID_ERROR);
        }

        try {
            JSONObject queryContent = JSONObject.parseObject(dataSourceDo.getContent());
            JSONObject queryConfigDetail = queryContent.getJSONObject("queryConfigDetail");
            Long queryId = queryContent.getLong("queryId");
            JSONArray jsonParamList = queryContent.getJSONArray("queryParams");
            List<Param> paramList = new ArrayList<>();
            for (int index = 0; index < jsonParamList.size(); index++) {
                paramList.add(this.transJSONToSdmParam(jsonParamList.getJSONObject(index)));
            }

            Map<String, String> extInfo = new HashMap<>();

            SdmGeneralRequest sdmGeneralRequest = SdmGeneralRequest.newBuilder()
                    .setQueryId(queryId)
                    .addAllParam(paramList)
                    .putAllExtInfo(extInfo).build();

            SdmGeneralResponse sdmGeneralResponse = sdmGeneralService.queryNormal(sdmGeneralRequest);

            List<com.kuaishou.kwaishop.sellerdata.management.service.protobuf.DataMeta> dataMetaList =
                    sdmGeneralResponse.getDataMetaList();
            List<com.kuaishou.kwaishop.sellerdata.management.service.protobuf.DataResultRow> dataResultRows =
                    sdmGeneralResponse.getResultRowList();

            QueryByQueryIdResponse response;
            JSONObject responseJson = new JSONObject();
            List<String> headerListInfo =
                    dataMetaList.stream().map(a -> JSON.toJSONString(a)).collect(Collectors.toList());

            List<String> headerList = dataMetaList.stream().map(
                            com.kuaishou.kwaishop.sellerdata.management.service.protobuf.DataMeta::getName)
                    .collect(Collectors.toList());

            // name 和value列表合并成mapList

            //            List<String> dataRowList = new ArrayList<>();
            JSONArray dataRowList = new JSONArray();
            for (com.kuaishou.kwaishop.sellerdata.management.service.protobuf.DataResultRow
                    dataResultRow : dataResultRows) {
                Map<String, Object> objectMap = IntStream.range(0, dataMetaList.size())
                        .collect(HashMap::new, (m, i) -> m
                                        .put(dataMetaList.get(i).getName(), dataResultRow.getResultField(i).getValue()),
                                (m, n) -> {
                                });
                //                dataRowList.add(JSON.toJSONString(objectMap));
                dataRowList.add(objectMap);
            }

            if (sdmGeneralResponse.getBaseRsp().getStatus().getNumber() == 0) {

                responseJson.put("headers", headerList);
                responseJson.put("result_row", dataRowList);
                responseJson.put("total", dataRowList.size());
                dataQueryExecuteDAO.updateLog(dataQueryId, "query:" + queryContent + "log:" + responseJson.toString());
                dataQueryExecuteDAO.updateResult(dataQueryId, responseJson.toString());
                dataQueryExecuteDAO.updateStatus(dataQueryId, QueryStatusEnum.SUCCESS.getValue());
                return true;
            } else {
                responseJson.put("headers", null);
                responseJson.put("result_row", null);
                responseJson.put("total", 0);
                dataQueryExecuteDAO.updateLog(dataQueryId, "query:" + queryContent + "log:" + responseJson.toString());
                dataQueryExecuteDAO.updateResult(dataQueryId, responseJson.toString());
                dataQueryExecuteDAO.updateStatus(dataQueryId, QueryStatusEnum.FAIL.getValue());
                return false;
            }
        } catch (BizException e) {
            log.error("[kwaiSqlQueryBizService] dmQueryByQueryId bizError, exception: ", e);
            return false;
        } catch (Exception e) {
            log.error("[kwaiSqlQueryBizService] dmQuery Error, exception: ", e);
            return false;
        }
    }

    public DmGetByQueryIdResponse dmGetByQueryId(DmGetByQueryIdRequest request) {
        if (request.getQueryId() <= 0) {
            throw new BizException(DATA_SOURCE_ID_ERROR);
        }
        try {
            GetManagerApiRequest getManagerApiRequest = GetManagerApiRequest.newBuilder().setId(request.getQueryId())
                    .setCurrentUser(request.getCurrentUser()).build();

            ManagerApiDTO managerApiDTO = managementAdminService.getManagerApi(getManagerApiRequest);
            if (managerApiDTO.getSql().equals("")) {
                return DmGetByQueryIdResponse.newBuilder().setQueryId(managerApiDTO.getId())
                        .setCreator(managerApiDTO.getCreator()).setSql(managerApiDTO.getSql()).build();
            }
            String paramStr = "";
            String sqlStr = managerApiDTO.getSql();
            Pattern pattern = Pattern.compile("[${]\\w+[}]");
            Matcher matcher = pattern.matcher(sqlStr);
            while (matcher.find()) {
                paramStr = paramStr + matcher.group();
            }

            return DmGetByQueryIdResponse.newBuilder().setQueryId(managerApiDTO.getId())
                    .setCreator(managerApiDTO.getCreator())
                    .setCreatedAt(managerApiDTO.getCreatedAt())
                    .setDesc(managerApiDTO.getDesc())
                    .setSql(managerApiDTO.getSql())
                    .setUpdatedAt(managerApiDTO.getUpdatedAt())
                    .setStatus(managerApiDTO.getStatus())
                    .setProductId((int) managerApiDTO.getProductId())
                    .setProductName(managerApiDTO.getProductName())
                    .setParamStr(paramStr)
                    .build();


        } catch (BizException e) {
            log.error("[kwaiSqlQueryBizService] dmGetByQueryId bizError, exception: ", e);
            return DmGetByQueryIdResponse.newBuilder().setSql("").setQueryId(request.getQueryId())
                    .build();
        } catch (Exception e) {
            log.error("[kwaiSqlQueryBizService] dmGetByQueryId Error, exception: ", e);
            return DmGetByQueryIdResponse.newBuilder().setSql("").setQueryId(request.getQueryId())
                    .build();
        }
    }

    public GetMetaDatabaseListResponse getMetaDatabaseList(GetMetaDatabaseListRequest request) {
        if (StringUtils.isBlank(request.getKeyword())) {
            return GetMetaDatabaseListResponse.newBuilder().setResult(BizErrorCode.META_QUERY_KEY_EMPTY.getCode())
                    .setErrorMsg(BizErrorCode.META_QUERY_KEY_EMPTY.getMessage()).build();
        }

        try {
            PageVo<DatabaseEntity> databaseEntityPageVo =
                    dpAccessFetch.getMetaDatabaseList(request.getUser(), request.getKeyword());

            List<DatabaseEntity> databaseEntityList = databaseEntityPageVo.getDataList();
            if (databaseEntityList.size() > 0) {
                return GetMetaDatabaseListResponse.newBuilder()
                        .addAllDatabases(
                                databaseEntityList.stream().map(this::buildDatabaseEntity).collect(Collectors.toList()))
                        .setResult(BaseResultCode.SUCCESS_VALUE).build();
            } else {
                return GetMetaDatabaseListResponse.newBuilder()
                        .setResult(BizErrorCode.META_QUERY_RESULT_EMPTY.getCode())
                        .setErrorMsg(BizErrorCode.META_QUERY_RESULT_EMPTY.getMessage()).build();
            }


        } catch (BizException e) {
            log.error("[kwaiSqlQueryBizService] getMetaDatabaseList bizError, exception: ", e);
            return GetMetaDatabaseListResponse.newBuilder().setResult(e.getCode()).setErrorMsg(e.getMessage()).build();

        } catch (Exception e) {
            log.error("[kwaiSqlQueryBizService] getMetaDatabaseList Error, exception: ", e);
            return GetMetaDatabaseListResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage()).build();
        }
    }

    public GetTableListResponse getTableList(GetTableListRequest request) {
        if (StringUtils.isBlank(request.getKeyword()) || StringUtils.isBlank(request.getDbName())
                || request.getQueryType() <= 0) {
            return GetTableListResponse.newBuilder().setResult(BizErrorCode.META_QUERY_KEY_EMPTY.getCode())
                    .setErrorMsg(BizErrorCode.META_QUERY_KEY_EMPTY.getMessage()).build();

        }
        try {
            List<TableEntity> tableEntityList = dpAccessFetch
                    .getTableList(request.getUser(), request.getKeyword(), request.getDbName(), request.getQueryType())
                    .getDataList();

            if (tableEntityList.size() > 0) {
                return GetTableListResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE)
                        .addAllTableList(
                                tableEntityList.stream().map(this::buildTableEntity).collect(Collectors.toList()))
                        .build();
            } else {
                return GetTableListResponse.newBuilder().setResult(BizErrorCode.META_QUERY_RESULT_EMPTY.getCode())
                        .setErrorMsg(BizErrorCode.META_QUERY_RESULT_EMPTY.getMessage())
                        .build();
            }
        } catch (BizException e) {
            log.error("[kwaiSqlQueryBizService] getTableList bizError, exception: ", e);
            return GetTableListResponse.newBuilder().setResult(e.getCode()).setErrorMsg(e.getMessage()).build();

        } catch (Exception e) {
            log.error("[kwaiSqlQueryBizService] getTableList Error, exception: ", e);
            return GetTableListResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage()).build();
        }
    }


    public GetColumnListResponse getColumnList(GetColumnListRequest request) {
        if (StringUtils.isBlank(request.getTbName()) || StringUtils.isBlank(request.getDbName())
                || request.getQueryType() <= 0) {
            return GetColumnListResponse.newBuilder().setResult(BizErrorCode.META_QUERY_KEY_EMPTY.getCode())
                    .setErrorMsg(BizErrorCode.META_QUERY_KEY_EMPTY.getMessage()).build();
        }
        try {
            List<ColumnEntity> columnEntityList = dpAccessFetch
                    .getColumnList(request.getUser(), request.getTbName(), request.getDbName(), request.getQueryType())
                    .getDataList();
            if (columnEntityList.size() > 0) {
                return GetColumnListResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE)
                        .addAllColumnList(
                                columnEntityList.stream().map(this::buildColumnEntity).collect(Collectors.toList()))
                        .build();
            } else {
                return GetColumnListResponse.newBuilder().setResult(BizErrorCode.META_QUERY_RESULT_EMPTY.getCode())
                        .setErrorMsg(BizErrorCode.META_QUERY_RESULT_EMPTY.getMessage())
                        .build();
            }

        } catch (BizException e) {
            log.error("[kwaiSqlQueryBizService] getColumnList bizError, exception: ", e);
            return GetColumnListResponse.newBuilder().setResult(e.getCode()).setErrorMsg(e.getMessage()).build();

        } catch (Exception e) {
            log.error("[kwaiSqlQueryBizService] getColumnList Error, exception: ", e);
            return GetColumnListResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage()).build();
        }
    }

    private com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Column buildColumnEntity(
            ColumnEntity columnEntity) {
        if (columnEntity.getDescription() == null) {
            columnEntity.setDescription("");
        }
        if (columnEntity.getUrn() == null) {
            columnEntity.setUrn("");
        }
        if (columnEntity.getComment() == null) {
            columnEntity.setComment("");
        }
        if (columnEntity.getDataFullType() == null) {
            columnEntity.setDataFullType("");
        }
        return com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Column.newBuilder()
                .setName(columnEntity.getName())
                .setDatabaseName(columnEntity.getDatabaseName())
                .setTableName(columnEntity.getTableName())
                .setComment(columnEntity.getComment())
                .setCatalog(columnEntity.getCatalog())
                .setDataType(columnEntity.getDataType())
                .setDescription(columnEntity.getDescription())
                .setUrn(columnEntity.getUrn())
                .setFullType(columnEntity.getDataFullType())
                .setPartitioned(columnEntity.isPartitioned())
                .setStatistics(columnEntity.isStatistics())
                .build();
    }

    private com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Table buildTableEntity(
            TableEntity tableEntity) {

        if (tableEntity.getDescription() == null) {
            tableEntity.setDescription("");
        }
        if (tableEntity.getNameZh() == null) {
            tableEntity.setNameZh("");
        }
        if (tableEntity.getUrn() == null) {
            tableEntity.setUrn("");
        }
        return com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Table.newBuilder()
                .setName(tableEntity.getName())
                .setNameZh(tableEntity.getNameZh())
                .setDatabaseName(tableEntity.getDatabaseName())
                .setCatalog(tableEntity.getCatalog())
                .setUrn(tableEntity.getUrn())
                .setDescription(tableEntity.getDescription())
                .build();
    }

    private com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Database buildDatabaseEntity(
            DatabaseEntity databaseEntity) {
        if (databaseEntity.getNameZh() == null) {
            databaseEntity.setNameZh("");
        }
        if (databaseEntity.getDescription() == null) {
            databaseEntity.setDescription("");
        }

        return com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Database.newBuilder()
                .setName(databaseEntity.getName())
                .setNameZh(databaseEntity.getNameZh())
                .setDescription(databaseEntity.getDescription())
                .setType(databaseEntity.getType())
                .setCatalog(databaseEntity.getCatalog())
                .build();
    }


    private MapField buildMapField(Map<String, Object> map) {
        Map<String, String> stringMap = new HashMap<>();
        for (String i : map.keySet()) {
            String objectToJson = JSON.toJSONString(map.get(i));
            if (StringUtils.isNotBlank(objectToJson)) {
                stringMap.put(i, objectToJson.replaceAll("\"", ""));
            }
        }
        return MapField.newBuilder().putAllStringMap(stringMap).build();
    }

}

