package com.kuaishou.kwaishop.qa.risk.center.domain.combine.service;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.RiskInfoCombineBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-22
 */
public interface RiskInfoCombineService {

    RiskInfoCombineBO getSummaryData(CombineQueryParam param);

    List<RiskInfoCombineBO> getGroupingByData(CombineQueryParam param);

}
