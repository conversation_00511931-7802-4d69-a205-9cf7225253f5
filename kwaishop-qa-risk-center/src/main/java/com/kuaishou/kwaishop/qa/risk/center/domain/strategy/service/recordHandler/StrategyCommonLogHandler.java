package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.recordHandler;

import static com.kuaishou.framework.supplier.DynamicSuppliers.dynamicRateLimiter;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskIntegerConfigKey.oneServiceQpsLimitConfig;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskLongConfigKey.flowRecordAddMsgDelayMillions;

import java.time.Duration;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.supplier.DynamicRateLimiter;
import com.kuaishou.infra.framework.kafka.KafkaProducers;
import com.kuaishou.kconf.common.json.JsonMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.common.Enum.FlowStatus;
import com.kuaishou.kwaishop.qa.risk.center.config.mq.StrategyFlowRecallMq;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert.RecordConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper.StrategyFlowRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordExtraDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecordKafkaDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowMqCommonDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowRecordDo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.redis.ThemisToolsRedisClient;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.hiveQuery.OneServiceSqlQueryClient;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-12
 * 执行日志流量，普通流量
 */
@Slf4j
@Service
public class StrategyCommonLogHandler {

    @Autowired
    private OneServiceSqlQueryClient oneServiceSqlQueryClient;

    @Autowired
    private StrategyFlowRecordMapper strategyFlowRecordMapper;

    @Autowired
    private ThemisToolsRedisClient themisToolsRedisClient;
    @Autowired
    private StrategyFlowRecallMq strategyFlowRecallMq;

    private static final int PAGE_SIZE = 2000;

    private static final int MAX_OFFSET = 100000000;

    private static final int MAX_LOOP = MAX_OFFSET / PAGE_SIZE;

    private static final String PREFIX = "flow_record_";

    private static final String PREFIX_NUM_COUNT = "flow_record_num_count_";

    private static final String PAGE_NUM_PREFIX = "flow_record_page_num_";

    private static final long DELAY_TIME = flowRecordAddMsgDelayMillions.get();

    private DynamicRateLimiter oneServiceQpsLimit = dynamicRateLimiter(oneServiceQpsLimitConfig);

    @SneakyThrows
    public void handle(Long id, int pageNum, StrategyFlowRecordDo recordDo, String sceneKey, String pDate,
            String pHourmin) {
        FlowRecordExtraDto extraDto = JsonMapperUtils.fromJson(recordDo.getExtra(), FlowRecordExtraDto.class);

        int offset = PAGE_SIZE * (pageNum - 1);

        List<Map<String, Object>> resultList;
        if (StringUtils.isEmpty(pHourmin)) {
            String redisKey = PAGE_NUM_PREFIX + recordDo.getId() + "_" + sceneKey + "_" + pDate;
            Long nextPageNum = themisToolsRedisClient.incr(redisKey);
            StrategyFlowMqCommonDto dto = new StrategyFlowMqCommonDto();
            dto.setType("flowRecordAdd");
            dto.setId(recordDo.getId());
            strategyFlowRecallMq.sendMsg(dto, DELAY_TIME);

            oneServiceQpsLimit.acquire();
            resultList = oneServiceSqlQueryClient.queryStrategyRecord(pDate, sceneKey, offset, PAGE_SIZE);
        } else {
            String redisKey = PAGE_NUM_PREFIX + recordDo.getId() + "_" + sceneKey + "_" + pDate + "_" + pHourmin;
            Long nextPageNum = themisToolsRedisClient.incr(redisKey);
            StrategyFlowMqCommonDto dto = new StrategyFlowMqCommonDto();
            dto.setType("flowRecordAdd");
            dto.setId(recordDo.getId());
            strategyFlowRecallMq.sendMsg(dto, DELAY_TIME);

            oneServiceQpsLimit.acquire();
            resultList = oneServiceSqlQueryClient.queryStrategyRecord(pDate, pHourmin, sceneKey, offset, PAGE_SIZE);
        }

        for (Map<String, Object> resultMap : resultList) {
            if ("ONLINE".equals(resultMap.get("scene_execute_type")) && sceneKey.equals(resultMap.get("scene_key"))) {
                String redisTotalKey = PREFIX + id;
                Long incr = themisToolsRedisClient.incr(redisTotalKey);
                if (incr < recordDo.getTotal()) {
                    RecordKafkaDto recordKafkaDtos = RecordConvert.hiveResult2KafkaDto(resultMap, id);
                    KafkaProducers.sendString("kwaishop_apollo_strategy_flow_record",
                            JsonMapperUtils.toJson(recordKafkaDtos));
                    KafkaProducers.sendString("stratetgy_flow_recall_realtime",
                            JsonMapperUtils.toJson(recordKafkaDtos));

                    strategyFlowRecordMapper.updateExtraById(id, JsonMapperUtils.toJson(extraDto));

                } else if (incr.intValue() == recordDo.getTotal()) {
                    log.info("StrategyCommonLogHandler.handle.here2");
                    sendFinalMsg(id);
                    strategyFlowRecordMapper.updateFlowStatusAndFlowEndTimeById(id, FlowStatus.FINISHED.getCode(),
                            System.currentTimeMillis());
                }
            }
        }

        Long incr = themisToolsRedisClient.incr(PREFIX_NUM_COUNT + id);
        if (incr == MAX_LOOP) {
            log.info("StrategyCommonLogHandler.handle.here3");
            sendFinalMsg(id);
            strategyFlowRecordMapper.updateFlowStatusAndFlowEndTimeById(id, FlowStatus.FINISHED.getCode(),
                    System.currentTimeMillis());
        }

    }


    private void sendFinalMsg(Long id) {
        StrategyFlowMqCommonDto dto = new StrategyFlowMqCommonDto();
        dto.setType("flowRecordFinalMessage");
        dto.setId(id);
        strategyFlowRecallMq.sendMsg(dto, Duration.ofSeconds(15).toMillis());
        strategyFlowRecallMq.sendMsg(dto, Duration.ofSeconds(30).toMillis());
        strategyFlowRecallMq.sendMsg(dto, Duration.ofMinutes(1).toMillis());
        strategyFlowRecallMq.sendMsg(dto, Duration.ofMinutes(5).toMillis());
    }
}
