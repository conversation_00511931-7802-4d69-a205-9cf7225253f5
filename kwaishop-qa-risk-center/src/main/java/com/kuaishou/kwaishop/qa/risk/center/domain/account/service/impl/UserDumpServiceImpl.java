package com.kuaishou.kwaishop.qa.risk.center.domain.account.service.impl;


import javax.annotation.Resource;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.infra.framework.kafka.KafkaProducers;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.UserDumpDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserDbForDumpDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserDumpService;

import lombok.extern.slf4j.Slf4j;


@Lazy
@Service
@Slf4j
public class UserDumpServiceImpl implements UserDumpService {

    @Resource
    private UserDumpDAO userDumpDAO;

    @Override
    public void insertToDump(UserDbForDumpDO userDbForDumpDO) {
        userDumpDAO.insertDump(userDbForDumpDO);

    }

    @Override
    public UserDbForDumpDO getDumpUser(Long userId) {
        return userDumpDAO.getDumpUser(userId);
    }

    @Override
    public void sendKafkaMsg(String topic, String message) {
        KafkaProducers.sendString(topic, message);

    }
}
