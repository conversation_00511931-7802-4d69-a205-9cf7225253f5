package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert;

import java.lang.reflect.Field;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;

import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-30
 */
@Slf4j
public class ResultSetMapperUtil<T> {

    /**
     * 增加下划线和驼峰法支持
     * 优先注解，后驼峰，效率高点
     */
    public static <T> List<T> mapResultSetToObject(ResultSet rs, Class<T> outputClass) {
        List<T> outputList = Lists.newArrayList();
        try {
            if (Objects.isNull(rs)) {
                return outputList;
            }
            ResultSetMetaData rsmd = rs.getMetaData();
            Field[] fields = outputClass.getDeclaredFields();
            // 注解
            Map<String, Field> columnFieldMap = Maps.newHashMap();
            // 驼峰
            Map<String, Field> camelFieldMap = Maps.newHashMap();
            for (Field field : fields) {
                String fileName = field.getName();
                camelFieldMap.put(StringOptUtils.underscoreName(fileName), field);
                if (field.isAnnotationPresent(Column.class)) {
                    Column column = field.getAnnotation(Column.class);
                    columnFieldMap.put(column.name().toLowerCase(), field);
                }
            }
            int rsCount = 0;
            while (rs.next()) {
                rsCount++;
                T bean = (T) outputClass.getDeclaredConstructor().newInstance();
                for (int i = 0; i < rsmd.getColumnCount(); i++) {
                    // 获取列名称
                    String columnName = rsmd.getColumnName(i + 1);
                    // 获取列值
                    Object columnValue = rs.getObject(i + 1);
                    //if (print) {
                    //    log.info("count:{}", rsCount);
                    //    log.info("columnName:{},value:{}", columnName, columnValue);
                    //}
                    if (columnValue == null) {
                        continue;
                    }
                    String lowerCaseColumnName = columnName.toLowerCase();
                    // 优先读取注解的
                    if (columnFieldMap.containsKey(lowerCaseColumnName)) {
                        BeanUtils.setProperty(bean, columnFieldMap.get(lowerCaseColumnName).getName(), columnValue);
                        continue;
                    }
                    // 其次读取驼峰法的
                    if (camelFieldMap.containsKey(lowerCaseColumnName)) {
                        BeanUtils.setProperty(bean, camelFieldMap.get(lowerCaseColumnName).getName(), columnValue);
                        continue;
                    }
                }
                if (CollectionUtils.isEmpty(outputList)) {
                    outputList = Lists.newArrayList();
                }
                outputList.add(bean);
            }
        } catch (Exception e) {
            log.error("ResultSetMapperUtil mapRersultSetToObject error", e);
        }
        return outputList;
    }

    //    public static <T> List<T> mapResultSetToObject(ResultSet rs, Class<T> outputClass) {
    //        List<T> outputList = Lists.newArrayList();
    //        try {
    //            if (Objects.isNull(rs)) {
    //                return outputList;
    //            }
    //            ResultSetMetaData rsmd = rs.getMetaData();
    //            Field[] fields = outputClass.getDeclaredFields();
    //            while (rs.next()) {
    //                T bean = (T) outputClass.getDeclaredConstructor().newInstance();
    //                for (int i = 0; i < rsmd.getColumnCount(); i++) {
    //                    // 获取列名称
    //                    String columnName = rsmd.getColumnName(i + 1);
    //                    // 获取列值
    //                    Object columnValue = rs.getObject(i + 1);
    //                    if (EnvUtils.offline()) {
    //                        log.info("columnName:" + columnName + ",value:" + columnValue);
    //                    }
    //                    for (Field field : fields) {
    //                        if (field.isAnnotationPresent(Column.class)) {
    //                            Column column = field.getAnnotation(Column.class);
    //                            if (column.name().equalsIgnoreCase(columnName) && columnValue != null) {
    //                                BeanUtils.setProperty(bean, field.getName(), columnValue);
    //                                break;
    //                            }
    //                        }
    //                    }
    //                }
    //                if (CollectionUtils.isEmpty(outputList)) {
    //                    outputList = Lists.newArrayList();
    //                }
    //                outputList.add(bean);
    //            }
    //        } catch (Exception e) {
    //            log.error("ResultSetMapperUtil mapRersultSetToObject error", e);
    //        }
    //        return outputList;
    //    }

    public static List<Long> mapResultSetToLong(ResultSet rs) {
        List<Long> resList = new ArrayList<>();
        try {
            if (Objects.isNull(rs)) {
                return resList;
            }
            while (rs.next()) {
                resList.add(rs.getLong(1));
            }
            return resList;
        } catch (Exception e) {
            log.error("ResultSetMapperUtil mapResultSetToLong error", e);
        }
        return resList;
    }

    public static List<String> mapResultSetToString(ResultSet rs) {
        List<String> resList = new ArrayList<>();
        try {
            if (Objects.isNull(rs)) {
                return resList;
            }
            while (rs.next()) {
                resList.add(rs.getString(1));
            }
            return resList;
        } catch (Exception e) {
            log.error("ResultSetMapperUtil mapResultSetToLong error", e);
        }
        return resList;
    }
}
