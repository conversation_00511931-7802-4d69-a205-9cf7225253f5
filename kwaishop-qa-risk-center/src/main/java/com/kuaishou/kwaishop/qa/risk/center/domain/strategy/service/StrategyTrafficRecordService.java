package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyRecordListDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTestDataPool;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTrafficRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayDetailRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-26
 */
public interface StrategyTrafficRecordService {
    void insertData(List<StrategyTrafficRecordDO> strategyTrafficRecordDOS);

    void insert(StrategyTrafficRecordDO strategyTrafficRecordDO);

    void updateStatus(String taskId, int status);

    void insertRecord(StrategyRecordListDO record);

    PageBO<StrategyTrafficRecordDO> queryReplayDetail(ReplayDetailRequest request);

    void updateDiffInfo(List<StrategyTestDataPool> dataPoolParams);

    String queryTrafficData(String caseId);

    String queryTaskIdByCaseId(Long caseId);

    List<StrategyTrafficRecordDO> queryAllCaseByTaskId(String taskId);
}
