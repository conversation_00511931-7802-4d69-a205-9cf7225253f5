package com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.impl;


import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.klink.UserKlinkLoginDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.klink.UserKlinkLoginDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.auth.service.AuthService;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.model.KlinkLoginExtBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.model.UserKlinkLoginBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.SearchInfoService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.PageUserKlinkLoginDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.PageUserKlinkLoginListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.UpdateKlinkLoginInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.UserKlinkLoginDTO;

import lombok.extern.slf4j.Slf4j;

;

@Service
@Lazy
@Slf4j
public class SearchInfoImpl implements SearchInfoService {

    @Autowired
    private UserKlinkLoginDAO userKlinkLoginDAO;

    @Autowired
    private AuthService authService;

    @Override
    public PageUserKlinkLoginDTO queryUserKlinkLoginInfo(PageUserKlinkLoginListRequest request) {
        UserKlinkLoginBO userKlinkLoginBO = buildQueryPageBO(request);
        PageBO<UserKlinkLoginDO> pageBO = userKlinkLoginDAO.queryUserLoginInfoList(userKlinkLoginBO);

        return PageUserKlinkLoginDTO.newBuilder()
                .setTotal(pageBO.getTotal())
                .setPageSize(pageBO.getPageSize())
                .setPageNo(pageBO.getPageNo())
                .addAllDetails(buildUserKlinkLoginDTO(pageBO.getData()))
                .build();
    }

    @Override
    public long updateUserKlinkLoginInfo(UpdateKlinkLoginInfoRequest request) {
        authService.authPreCheck(request.getOperator());
        UserKlinkLoginDO userKlinkLoginDO = buildUserKlinkLoginDO(request);
        return updateUserKlinkLoginInfo(userKlinkLoginDO);
    }

    @Override
    public long updateUserKlinkLoginInfo(UserKlinkLoginDO cur) {
        UserKlinkLoginDO last = userKlinkLoginDAO.queryUserLoginInfoByUserId(cur.getUserId());
        if (last != null) {
            cur.setId(last.getId());
        }
        return userKlinkLoginDAO.insertOrUpdateUserKlinkLogin(cur);
    }

    private UserKlinkLoginDO buildUserKlinkLoginDO(UpdateKlinkLoginInfoRequest request) {
        KlinkLoginExtBO klinkLoginExtBO = KlinkLoginExtBO.builder()
                .apist(request.getApist().trim())
                .ssecurity(request.getSsecurity().trim())
                .build();
        return UserKlinkLoginDO.builder()
                .nickname(request.getNickname())
                .userId(request.getUserId())
                .type(request.getType())
                .ext(ObjectMapperUtils.toJSON(klinkLoginExtBO))
                .modifier(request.getOperator())
                .build();
    }

    private UserKlinkLoginBO buildQueryPageBO(PageUserKlinkLoginListRequest request) {
        UserKlinkLoginBO.UserKlinkLoginBOBuilder builder = UserKlinkLoginBO.builder()
                .pageSize(request.getPageSize())
                .pageNo(request.getPageNo());
        if (request.getUserId() > 0) {
            builder.userId(request.getUserId());
        }
        if (StringUtils.isNotEmpty(request.getNickname())) {
            builder.nickname(request.getNickname());
        }
        return builder.build();
    }

    private List<UserKlinkLoginDTO> buildUserKlinkLoginDTO(List<UserKlinkLoginDO> userKlinkLoginDOList) {
        List<UserKlinkLoginDTO> res = new ArrayList<>();
        userKlinkLoginDOList.forEach(e -> {
            KlinkLoginExtBO klinkLoginExtBO = ObjectMapperUtils.fromJSON(e.getExt(), KlinkLoginExtBO.class);
            UserKlinkLoginDTO.Builder builder = UserKlinkLoginDTO.newBuilder()
                    .setNikename(e.getNickname())
                    .setType(e.getType())
                    .setUserId(e.getUserId())
                    .setUpdateTime(e.getUpdateTime());
            if (klinkLoginExtBO != null) {
                builder.setApist(klinkLoginExtBO.getApist()).setSsecurity(klinkLoginExtBO.getSsecurity());
            }
            res.add(builder.build());
        });
        return res;
    }

}
