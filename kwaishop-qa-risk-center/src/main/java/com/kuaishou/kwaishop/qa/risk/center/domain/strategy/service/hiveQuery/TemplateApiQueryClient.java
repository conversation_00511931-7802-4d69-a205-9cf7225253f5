package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.hiveQuery;

import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import org.springframework.stereotype.Service;

import com.kuaishou.dp.one.service.rpc.client.sql.model.QueryResult;
import com.kuaishou.dp.one.service.rpc.client.sql.model.table.TableRow;
import com.kuaishou.dp.one.service.rpc.client.templateapi.QueryStatus;
import com.kuaishou.dp.one.service.rpc.client.templateapi.ResultHandler;
import com.kuaishou.dp.one.service.rpc.client.templateapi.TemplateApiClient;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-08
 */
@Service
@Slf4j
public class TemplateApiQueryClient {

    /**
     * 同步查询，适用于Druid、ClickHouse等高速引擎，Hive引擎必须使用异步查询
     */
    @SneakyThrows
    public void syncQuery(Map<String, String> param, String apiName, String token) {
        // 构造client，传入token
        TemplateApiClient client = new TemplateApiClient(token);

        // 同步查询，默认10s超时
        QueryResult<TableRow> result = client.query(apiName, param);
    }

    /**
     * 异步提交 + 客户端自己轮询状态 + 一次性取回结果（结果集 <= 100000条）
     */
    @SneakyThrows
    public void asyncQuery1(Map<String, String> param, String apiName, String token) throws InterruptedException {
        // 构造client，传入token
        TemplateApiClient client = new TemplateApiClient(token);

        // 提交查询
        String queryId = client.asyncQuery(apiName, param);

        // 轮询状态
        QueryStatus status;
        while (true) {
            status = client.getStatus(queryId);
            if (status == QueryStatus.RUNNING) {
                TimeUnit.SECONDS.sleep(3);
                continue;
            }
            break;
        }

        // 获取结果
        QueryResult<TableRow> result = client.getResult(queryId);
    }

    /**
     * 异步提交 + 客户端自己轮询状态 + 流式取回结果（结果集 > 100000条）
     */
    @SneakyThrows
    public void asyncQuery2(Map<String, String> param, String apiName,
            ResultHandler<QueryResult<TableRow>> resultHandler, String token) {
        // 构造client，传入token
        TemplateApiClient client = new TemplateApiClient(token);

        // 提交查询
        String queryId = client.asyncQuery(apiName, param);
        log.info("TemplateApiQueryClient.asyncQuery2 queryId:{}", queryId);

        // 轮询状态
        QueryStatus status;
        while (true) {
            status = client.getStatus(queryId);
            if (status == QueryStatus.RUNNING) {
                TimeUnit.SECONDS.sleep(3);
                continue;
            }
            break;
        }

        // 流式获取结果并处理
        client.streamGetAndHandleResult(queryId, resultHandler);
    }

    /**
     * 异步提交 + 回调函数
     */
    @SneakyThrows
    public String asyncQuery3(Map<String, String> param, String apiName,
            ResultHandler<QueryResult<TableRow>> resultHandler, String token) {
        // 构造client，传入token
        TemplateApiClient client = new TemplateApiClient(token);

        return client.asyncQuery(apiName, param, resultHandler);
    }

    /**
     * 异步提交 + 回调函数 + 阻塞等待
     */
    @SneakyThrows
    public void asyncQuery4(Map<String, String> param, String apiName, String token,
            ResultHandler<QueryResult<TableRow>> resultHandler) throws InterruptedException {
        // 构造client，传入token
        TemplateApiClient client = new TemplateApiClient(token);


        AtomicBoolean isEnd = new AtomicBoolean(false);
        String queryId = client.asyncQuery(apiName, param, resultHandler);

        while (!isEnd.get()) {
            Thread.sleep(1000);
        }
        System.out.println("处理完成");
    }
}