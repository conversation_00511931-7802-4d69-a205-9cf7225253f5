package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.KspayTeamResourceService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.FeatureInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.FeatureInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.FeatureInfoResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.KrpcKspayTeamResourceServiceGrpc.KspayTeamResourceServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.QueryTeamTaskRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.QueryTeamTaskResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.TaskInfo;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-27
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class KspayTeamKdevResourceServiceImpl extends KspayTeamResourceServiceImplBaseV2 {
    @Autowired
    private KspayTeamResourceService kspayTeamResourceService;

    @Override
    public QueryTeamTaskResponse queryTeamTask(QueryTeamTaskRequest request) {
        log.info("[KspayTeamKdevResourceServiceImpl] queryTeamTask request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            List<TaskInfo> result = kspayTeamResourceService.queryTeamTask(request);
            log.error("[KspayTeamKdevResourceServiceImpl] queryTeamTask bizError, exception: ");
            return QueryTeamTaskResponse.newBuilder()
                    .setCode(BaseResultCode.SUCCESS_VALUE)
                    .addAllResult(result)
                    .build();
        } catch (BizException e) {
            log.error("[KspayTeamKdevResourceServiceImpl] queryTeamTask bizError, exception: ", e);
            return QueryTeamTaskResponse.newBuilder()
                    .setCode(e.getCode())
                    .setMessage(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayTeamKdevResourceServiceImpl] queryTeamTask error, exception: ", e);
            return QueryTeamTaskResponse.newBuilder()
                    .setCode(BasicErrorCode.SERVER_ERROR.getCode())
                    .setMessage(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public FeatureInfoResponse queryFeatureInfo(FeatureInfoRequest request) {
        log.info("[KspayTeamKdevResourceServiceImpl] queryFeatureInfo request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            List<FeatureInfo> result = kspayTeamResourceService.queryFeatureInfo(request);
            return FeatureInfoResponse.newBuilder()
                    .setCode(BaseResultCode.SUCCESS_VALUE)
                    .addAllResult(result)
                    .build();
        } catch (BizException e) {
            log.error("[KspayTeamKdevResourceServiceImpl] queryFeatureInfo bizError, exception: ", e);
            return FeatureInfoResponse.newBuilder()
                    .setCode(e.getCode())
                    .setMessage(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayTeamKdevResourceServiceImpl] queryFeatureInfo error, exception: ", e);
            return FeatureInfoResponse.newBuilder()
                    .setCode(BasicErrorCode.SERVER_ERROR.getCode())
                    .setMessage(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

}
