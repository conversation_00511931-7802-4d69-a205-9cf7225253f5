package com.kuaishou.kwaishop.qa.risk.center.domain.account.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.UserAccountConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.EmailImportExcelModel;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportAccountExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportTokenExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserImportTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.AccountExcelImportService;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.BaseExcelModel;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-30
 */
@Service
@Lazy
@Slf4j
public class EmailExcelImportServiceImpl extends AbstractExcelImportService implements AccountExcelImportService {

    @Autowired
    private UserAccountConvert userAccountConvert;


    @Override
    public Integer getCode() {
        return UserImportTypeEnum.EMAIL_IMPORT.getCode();
    }

    @Override
    public List<UserAccountDO> buildAccountTokenDO(List<BaseExcelModel> excelModels, ImportTokenExcelBO excelBO) {
        return null;
    }

    @Override
    public BaseExcelModel getModel() {
        return new EmailImportExcelModel();
    }


    @Override
    public String getScene() {
        return "邮箱类型Excel导入";
    }

    @Override
    protected List<UserAccountDO> buildAccountDO(List<BaseExcelModel> excelModels, ImportAccountExcelBO excelBO) {
        return excelModels.stream()
                .map(e -> userAccountConvert.emailExcelAccountDO(e, excelBO))
                .collect(Collectors.toList());
    }
}
