package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.STRATEGY_TRAFFIC_RECORD_TABLE_NAME;

import com.baomidou.mybatisplus.annotation.TableName;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@SuperBuilder
@AllArgsConstructor
@TableName(STRATEGY_TRAFFIC_RECORD_TABLE_NAME)
public class StrategyTrafficRecordDO extends BaseDO {
    private Long id;
    private String sceneName;
    private Long recordTime;
    private String trafficData;
    private String expectedResult;
    private String ext;
    private Long createTime;
    private Long updateTime;
    private String creator;
    private String modifier;
    private Integer deleted;
    private Long version;
    private Long recordTaskId;
    private String actualResult;
    private Integer diffConclusion;
    private Long replayTime;
    private String replayLaneId;
}

