package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.KspayBaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.ConfigRulesCheckResultDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.ConfigRulesCheckResultDo;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.ConfigRulesCheckResultMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.ConfigRulesCheckResulQueryCondition;

@Repository
public class ConfigRulesCheckResultDAOImpl extends KspayBaseDAOImpl<ConfigRulesCheckResultDo,
        ConfigRulesCheckResulQueryCondition> implements ConfigRulesCheckResultDAO {

    @Autowired
    private ConfigRulesCheckResultMapper configRulesCheckResultMapper;

    @Override
    public long insert(ConfigRulesCheckResultDo configRulesCheckResultDo) {
        return configRulesCheckResultMapper.insert(configRulesCheckResultDo);
    }

    @Override
    protected void fillLikeQueryCondition(ConfigRulesCheckResulQueryCondition condition,
            QueryWrapper<ConfigRulesCheckResultDo> queryWrapper) {

    }

    @Override
    protected void fillQueryCondition(ConfigRulesCheckResulQueryCondition condition,
            QueryWrapper<ConfigRulesCheckResultDo> queryWrapper) {

    }

    @Override
    protected BaseMapper<ConfigRulesCheckResultDo> getMapper() {
        return configRulesCheckResultMapper;
    }

    @Override
    public List<ConfigRulesCheckResultDo> queryConfigRulesCheckResult(
            ConfigRulesCheckResulQueryCondition configRulesCheckResulQueryCondition) {
            return configRulesCheckResultMapper.queryConfigRulesCheckResultList(
                    configRulesCheckResulQueryCondition.getReportName(),
                    configRulesCheckResulQueryCondition.getResult(),
                    configRulesCheckResulQueryCondition.getType(),
                    configRulesCheckResulQueryCondition.getStartTime(),
                    configRulesCheckResulQueryCondition.getEndTime());
    }

    @Override
    public List<ConfigRulesCheckResultDo> queryAllConfigRulesCheckResult() {
        return configRulesCheckResultMapper.queryConfigRulesCheckResultList();
    }

    @Override
    public ConfigRulesCheckResultDo queryKconfConfigRulesCheckResult(String key) {
        return configRulesCheckResultMapper.queryKconfConfigRulesCheckResult(key);
    }
}
