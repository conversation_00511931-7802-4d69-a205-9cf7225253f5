package com.kuaishou.kwaishop.qa.risk.center.domain.account.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.client.config.kafka.KwaishopQaRiskAccountTopic.auto_return_account_topic;

import java.util.concurrent.DelayQueue;

import org.springframework.stereotype.Service;

import com.kuaishou.infra.framework.kafka.KafkaProducers;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.AutoReturnTask;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.AutoReturnService;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-03
 */
@Service
public class AutoReturnServiceImpl implements AutoReturnService {
    private static DelayQueue<AutoReturnTask> delayQueue = new DelayQueue<>();

    // 添加任务到队列
    @Override
    public void addTask(AutoReturnTask autoReturnTask) {
        KafkaProducers.sendString(auto_return_account_topic.getTopicName(), "");
    }

    // 从队列中取出任务
    @Override
    public void fetchTaskAndExecute() {
        try {
            AutoReturnTask task = delayQueue.take();
            task.execute();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void autoReturnAccount() {

    }


}
