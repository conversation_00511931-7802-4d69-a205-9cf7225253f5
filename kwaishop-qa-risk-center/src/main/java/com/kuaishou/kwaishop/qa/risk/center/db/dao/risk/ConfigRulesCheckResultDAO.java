package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.ConfigRulesCheckResultDo;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.ConfigRulesCheckResulQueryCondition;

public interface ConfigRulesCheckResultDAO {
    long insert(ConfigRulesCheckResultDo configRulesCheckResultDo);
    List<ConfigRulesCheckResultDo> queryConfigRulesCheckResult(
            ConfigRulesCheckResulQueryCondition configRulesCheckResulQueryCondition);
    List<ConfigRulesCheckResultDo> queryAllConfigRulesCheckResult();

    ConfigRulesCheckResultDo queryKconfConfigRulesCheckResult(String key);
}
