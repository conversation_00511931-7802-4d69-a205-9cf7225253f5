package com.kuaishou.kwaishop.qa.risk.center.db.dao.account;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.BaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAuthDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAuthBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-22
 */
public interface UserAuthDAO extends BaseDAO<UserAuthDO> {

    PageBO<UserAuthDO> queryPageList(UserAuthBO userAuthBO);

    List<UserAuthDO> queryList(UserAuthBO userAuthBO);

}
