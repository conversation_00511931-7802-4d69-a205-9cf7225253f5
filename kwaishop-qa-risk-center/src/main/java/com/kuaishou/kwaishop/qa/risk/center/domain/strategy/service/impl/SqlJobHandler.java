package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

import com.kuaishou.datarch.themis.sdk.enums.EmJobStatus;
import com.kuaishou.datarch.themis.sdk.facade.IHandler;
import com.kuaishou.datarch.themis.sdk.vo.job.JobInstanceQueryRequestVo;
import com.kuaishou.intown.json.JSONObject;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-25
 */
@Slf4j
@Data
public class SqlJobHandler implements IHandler {

    private String resultMethod;

    SqlJobHandler() {
    }

    SqlJobHandler(String resultMethod) {
        this.resultMethod = resultMethod;
    }

    private final AtomicReference<String> statusRef = new AtomicReference<>();
    private final List<Map<String, Object>> allResults = new ArrayList<>();
    private final AtomicReference<Exception> errorRef = new AtomicReference<>();
    private final AtomicReference<Boolean> canReturn = new AtomicReference<>(false);

    @Override
    public void onNewResultArrive(List<Map<String, Object>> list, Boolean isAllResultArrived, int i, Exception e,
            JobInstanceQueryRequestVo<?> jobInstanceQueryRequestVo) {
        log.info("SqlJobHandler.onNewResultArrive.size:{}", list.size());
        if ("kafka".equals(resultMethod)) {
//            List<RecordKafkaDto> recordKafkaDtos = RecordConvert.hiveResultList2KafkaDtos(list);
//            log.info("SqlJobHandler.onNewResultArrive.kafkaRecord:{}", JsonMapperUtils.toJson(recordKafkaDtos));
//            for (RecordKafkaDto dto : recordKafkaDtos) {
//                KafkaProducers.sendString("kwaishop_apollo_strategy_flow_record", JsonMapperUtils.toJson(dto));
//            }
        } else {
            if (e != null) {
                log.info(String.format("执行失败原因:e %s, RequestVo %s", e,
                        JSONObject.toJSONString(jobInstanceQueryRequestVo)));
                errorRef.set(e);
                canReturn.set(true);

            } else {
                allResults.addAll(list);

                if (isAllResultArrived) {
                    log.info(String.format("执行成功: RequestVo %s",
                            JSONObject.toJSONString(jobInstanceQueryRequestVo)));
                    canReturn.set(true);
                }
            }
        }
    }

    @Override
    public void onError(Exception e, JobInstanceQueryRequestVo<?> jobInstanceQueryRequestVo) {
        log.info(String.format("执行失败原因:e %s, RequestVo %s", e,
                JSONObject.toJSONString(jobInstanceQueryRequestVo)));
        errorRef.set(e);

    }

    @Override
    public void onJobStatusChange(String status, JobInstanceQueryRequestVo<?> jobInstanceQueryRequestVo) {
        statusRef.set(status);
        if (EmJobStatus.isError(status)) {
            // 出现错误了 可以返回
            canReturn.set(true);
        }
        log.info("SQL queryInfo->" + jobInstanceQueryRequestVo.getJobInstanceId() + ",status->" + status);
    }

    @Override
    public void onPollStatus(String s, JobInstanceQueryRequestVo<?> jobInstanceQueryRequestVo) {

    }
}