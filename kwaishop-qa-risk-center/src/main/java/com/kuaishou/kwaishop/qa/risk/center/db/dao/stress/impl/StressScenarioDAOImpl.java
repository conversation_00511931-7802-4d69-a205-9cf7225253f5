package com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressScenarioDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress.StressScenarioMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.stress.ScenarioQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressScenarioBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.StressScenarioBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-16
 */
@Repository
public class StressScenarioDAOImpl extends BaseDAOImpl<StressScenarioDO, ScenarioQueryCondition> implements StressScenarioDAO {

    @Autowired
    private StressScenarioMapper stressScenarioMapper;
    @Override
    protected void fillQueryCondition(ScenarioQueryCondition condition, QueryWrapper<StressScenarioDO> queryWrapper) {

    }

    @Override
    protected BaseMapper<StressScenarioDO> getMapper() {
        return stressScenarioMapper;
    }

    @Override
    public PageBO<StressScenarioDO> queryPageList(QueryStressScenarioBO bo) {
        Integer pageNo = 0;
        Integer pageSize = 10;
        if (bo.getPageNo() >= 1) {
            pageNo = bo.getPageNo() - 1;
        }
        if (bo.getPageSize() > 100 || bo.getPageSize() <= 0) {
            pageSize = 100;
        }
        Integer startNo = pageNo * bo.getPageSize();
        Long count = stressScenarioMapper.countStressScenarios(bo.getId(), bo.getName());
        List<StressScenarioDO> scenarios  = stressScenarioMapper.queryStressScenarios(bo.getId(), bo.getName(), startNo, pageSize);
        return new PageBO<StressScenarioDO>(pageNo, pageSize,
                count, scenarios);
    }

    /**
     * 改方案了 暂不使用
     * @param bo
     * @return
     */
    @Override
    public PageBO<StressScenarioDO> queryPageListWithInterface(QueryStressScenarioBO bo) {
        Integer pageNo = 0;
        Integer pageSize = 10;
        if (bo.getPageNo() >= 1) {
            pageNo = bo.getPageNo() - 1;
        }
        if (bo.getPageSize() > 100 || bo.getPageSize() <= 0) {
            pageSize = 100;
        }
        Integer startNo = pageNo * bo.getPageSize();
        Long count = stressScenarioMapper.countStressScenarios(bo.getId(), bo.getName());
        List<StressScenarioDO> scenarios  = stressScenarioMapper.queryStressScenariosWithInterface(bo.getId(), bo.getName(), startNo, pageSize);
        return new PageBO<StressScenarioDO>(pageNo, pageSize,
                count, scenarios);
    }

    @Override
    public StressScenarioDO createStressScenarioOrUpdate(StressScenarioBO stressScenarioBO) {
        if (stressScenarioBO.getOutId() == null) {
            throw new RuntimeException();
        }
        StressScenarioDO stressScenarioDO = stressScenarioMapper.selectByOutId(stressScenarioBO.getOutId());

        if (stressScenarioDO != null && stressScenarioDO.getId() > 0) {
            //update
            stressScenarioDO.setUpdateTime(System.currentTimeMillis());
            stressScenarioDO.setOwnerId(stressScenarioBO.getOwnerId());
            stressScenarioDO.setOutUrl(stressScenarioBO.getOutUrl());
            stressScenarioDO.setOutId(stressScenarioBO.getOutId());
            stressScenarioDO.setStatus(stressScenarioBO.getStatus());
            stressScenarioDO.setName(stressScenarioBO.getName());
            stressScenarioMapper.updateById(stressScenarioDO);
        } else {
            //create
            stressScenarioDO = convertStressScenarioBOToDO(stressScenarioBO);
            stressScenarioMapper.insertScenario(stressScenarioDO);
        }
        return stressScenarioDO;
    }

    private StressScenarioDO convertStressScenarioBOToDO(StressScenarioBO stressScenarioBO) {
        StressScenarioDO stressScenarioDO = new StressScenarioDO();
        stressScenarioDO.setName(stressScenarioBO.getName().trim());
        stressScenarioDO.setStatus(stressScenarioBO.getStatus());
        stressScenarioDO.setOutId(stressScenarioBO.getOutId());
        stressScenarioDO.setOutUrl(stressScenarioBO.getOutUrl());
        stressScenarioDO.setOwnerId(stressScenarioBO.getOwnerId());
        return stressScenarioDO;
    }


}
