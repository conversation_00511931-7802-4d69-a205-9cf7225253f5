package com.kuaishou.kwaishop.qa.risk.center.db.dao.account.impl;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.UserAuthDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAuthDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.account.UserAuthMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.account.UserAuthQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAuthBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-23
 */
@Repository
public class UserAuthDAOImpl extends BaseDAOImpl<UserAuthDO, UserAuthQueryCondition> implements UserAuthDAO {

    @Autowired
    private UserAuthMapper userAuthMapper;


    @Override
    protected void fillQueryCondition(UserAuthQueryCondition condition, QueryWrapper<UserAuthDO> queryWrapper) {
// 查询user_id
        if (condition.getUserId() != null && condition.getUserId() > 0L) {
            queryWrapper.and(q -> q.eq("user_id", condition.getUserId()));
        }
        if (condition.getId() != null && condition.getId() >= 0L) {
            queryWrapper.and(q -> q.eq("id", condition.getId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getUserIds())) {
            queryWrapper.and(q -> q.in("user_id", condition.getUserIds()));
        }
        // 根据角色权限来筛选
        if (condition.getDistributorPermission() != null && condition.getDistributorPermission() != 2) {
            queryWrapper.and(q -> q.eq("distributor_permission", condition.getDistributorPermission()));
        }
        if (condition.getMerchantPermission() != null && condition.getMerchantPermission() != 2) {
            queryWrapper.and(q -> q.eq("merchant_permission", condition.getMerchantPermission()));
        }
        if (condition.getCelebrityPermission() != null && condition.getCelebrityPermission() != 2) {
            queryWrapper.and(q -> q.eq("celebrity_permission", condition.getCelebrityPermission()));
        }
        if (condition.getRecruitingLeader() != null && condition.getRecruitingLeader() != 2) {
            queryWrapper.and(q -> q.eq("recruiting_leader", condition.getRecruitingLeader()));
        }
        if (condition.getStoreType() != null && !condition.getStoreType().isEmpty()) {
            queryWrapper.and(q -> q.eq("store_type", condition.getStoreType()));
        }
    }

    private Integer filterBoolean(Boolean value) {
        if (value == null) {
            return null;
        }
        return value ? 1 : 0;
    }

    @Override
    public PageBO<UserAuthDO> queryPageList(UserAuthBO userAuthBO) {
        UserAuthQueryCondition condition = UserAuthQueryCondition.builder()
                .userId(userAuthBO.getUserId())
                .distributorPermission(userAuthBO.getDistributorPermission())
                .recruitingLeader(userAuthBO.getRecruitingLeader())
                .merchantPermission(userAuthBO.getMerchantPermission())
                .celebrityPermission(userAuthBO.getCelebrityPermission())
                .pageNo(userAuthBO.getPageNo())
                .pageSize(userAuthBO.getPageSize())
                .storeType(userAuthBO.getStoreType())
                .build();
        return super.queryPageList(condition);
    }

    @Override
    public List<UserAuthDO> queryList(UserAuthBO userAuthBO) {
        UserAuthQueryCondition condition = UserAuthQueryCondition.builder()
                .userId(userAuthBO.getUserId())
                .distributorPermission(userAuthBO.getDistributorPermission())
                .recruitingLeader(userAuthBO.getRecruitingLeader())
                .merchantPermission(userAuthBO.getMerchantPermission())
                .celebrityPermission(userAuthBO.getCelebrityPermission())
                .storeType(userAuthBO.getStoreType())
                .build();
        return super.queryList(condition);
    }


    @Override
    protected BaseMapper<UserAuthDO> getMapper() {
        return userAuthMapper;
    }
}
