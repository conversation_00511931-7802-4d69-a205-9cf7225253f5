package com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz.impl;

import static com.google.common.base.Preconditions.checkState;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.FUNDS_RISK_FIELDS_NOT_EXIST;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.KspayQaRiskMapConfig.BIZ_CODE_NAME_MAP;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.KspayQaRiskMapConfig.BIZ_SPACE_MAP;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskStringListConfigKey.riskTraitList;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.KspayPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.FundRiskIntelligenceFindDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundRiskIntelligenceFindDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.auth.service.AuthService;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz.KspayRiskIntelligenceFindBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.convert.KspayRiskIntelligenceFindConvert;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayQuerySymbolListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskIntelligenceFindListParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.PageKspayRiskIntelligenceFindListDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryPageRiskFieldsRequest;

import lombok.extern.slf4j.Slf4j;

@Service
@Lazy
@Slf4j
public class KspayRiskIntelligenceFindBizServiceImpl implements KspayRiskIntelligenceFindBizService {
    @Autowired
    private AuthService authService;

    @Autowired
    private KspayRiskIntelligenceFindBizService kspayRiskIntelligenceFindBizService;

    @Autowired
    private FundRiskIntelligenceFindDAO fundRiskIntelligenceFindDAO;

    @Autowired
    private KspayRiskIntelligenceFindConvert kspayRiskIntelligenceFindConvert;
    private final HashMap<String, String> bizSpaceMap = BIZ_SPACE_MAP.get();

    @Override
    public PageKspayRiskIntelligenceFindListDTO queryPageRiskIntelligenceFindList(QueryPageRiskFieldsRequest request) {
        Map<String, String> bizCodeNameMapRevert = new HashMap<>();
        HashMap<String, String> bizCodeNameMap = BIZ_CODE_NAME_MAP.get();
        BIZ_CODE_NAME_MAP.get().forEach((key, value) -> {
            bizCodeNameMapRevert.put(value, key);
        });
        String bizCode = bizCodeNameMap.getOrDefault(request.getBizCode(), "");
        request = request.toBuilder().setBizCode(bizCode).build();
        KspayPageBO<FundRiskIntelligenceFindDO> fundRiskIntelligenceFindDO = fundRiskIntelligenceFindDAO.queryPageRiskIntelligenceFindList(
                kspayRiskIntelligenceFindConvert.buildRiskIntelligenceListQueryCondition(request));
        log.info("fundRiskIntelligenceFindDO: {}", toJSON(fundRiskIntelligenceFindDO));
        List<KspayRiskIntelligenceFindListParam> kspayRiskIntelligenceFindListParams =
                buildKspayRiskIntelligenceFindParams(fundRiskIntelligenceFindDO.getData());
        log.info("kspayRiskIntelligenceFindListParams: {}", toJSON(kspayRiskIntelligenceFindListParams));
        // 对数据进行映射
        kspayRiskIntelligenceFindListParams = kspayRiskIntelligenceFindListParams.stream()
                .map(d -> d.toBuilder().setBizCode(bizSpaceMap.get(bizCodeNameMapRevert.getOrDefault(d.getBizCode(), "30"))).build())
                .collect(Collectors.toList());
        // 分页数据
        Integer pageNo = fundRiskIntelligenceFindDO.getPageNo();
        Integer pageSize = fundRiskIntelligenceFindDO.getPageSize();
        Long total = fundRiskIntelligenceFindDO.getTotal();
        return kspayRiskIntelligenceFindConvert.convertToIntelligencePageDTO(kspayRiskIntelligenceFindListParams, pageNo, pageSize, total);
    }

    /**
     * 1、通过主键id查询记录并update
     * 2、通过biz_def & source_name & table_name & column_name 查询记录并更新
     */
    @Override
    public String updateRiskLevelForRiskFields(KspayRiskIntelligenceFindListParam request) {
        FundRiskIntelligenceFindDO fundRiskIntelligenceFindDO = fundRiskIntelligenceFindDAO.queryRiskFieldsByMainId(request.getId());
        log.info("fundRiskIntelligenceFindDO:{}", toJSON(fundRiskIntelligenceFindDO));
//        String riskLevel = "P0";  // 注释

        if (fundRiskIntelligenceFindDO == null) {
            log.info("表:funds_risk_fields数据不存在:{}", toJSON(request));
            throw new BizException(FUNDS_RISK_FIELDS_NOT_EXIST);
        } else {
            log.info("表:funds_risk_fields字段riskLevel更新:{}", toJSON(request));
            int row = fundRiskIntelligenceFindDAO.updateRiskLevelByMainId(kspayRiskIntelligenceFindConvert
                    .buildUpdateRiskLevelParam(fundRiskIntelligenceFindDO, request, request.getRiskLevel()));
            checkState(row == 1, "数据库更新条数不符合预期 实际插入数据:{}", row);
        }
        FundRiskIntelligenceFindDO fundRiskIntelligenceFindDO1 = fundRiskIntelligenceFindDAO.queryRiskFieldsByMainId(request.getId());
        log.info("表:funds_risk_fields更新后数据:{}", toJSON(fundRiskIntelligenceFindDO1));
        return request.getId();
    }

    // 查询kconf特征值列表
    @Override
    public List<String> querySymbolList(KspayQuerySymbolListRequest request) {
        List<String> symbolList = riskTraitList.get();
        log.info("kconf特征值列表: symbolList:{}", toJSON(symbolList.stream().distinct().collect(Collectors.toList())));
        return symbolList.stream()
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
    }

    private List<KspayRiskIntelligenceFindListParam> buildKspayRiskIntelligenceFindParams(List<FundRiskIntelligenceFindDO> data) {
        List<KspayRiskIntelligenceFindListParam> res = new ArrayList<>();
        for (FundRiskIntelligenceFindDO fundRiskIntelligenceFindDO : data) {
            Long mainId = fundRiskIntelligenceFindDO.getId();
            log.info("mainId: {}", toJSON(mainId));
            KspayRiskIntelligenceFindListParam kspayRiskIntelligenceFindListParam = kspayRiskIntelligenceFindConvert.buildRiskIntelligenceParam(
                    fundRiskIntelligenceFindDO);
            res.add(kspayRiskIntelligenceFindListParam);
        }
        return res;
    }

    // 风险打标操作


}
