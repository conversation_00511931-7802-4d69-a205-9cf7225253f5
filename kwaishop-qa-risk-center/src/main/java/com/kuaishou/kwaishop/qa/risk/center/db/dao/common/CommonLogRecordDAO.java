package com.kuaishou.kwaishop.qa.risk.center.db.dao.common;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.BaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.common.CommonLogRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.common.model.bo.LogRecordBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-24
 */
public interface CommonLogRecordDAO extends BaseDAO<CommonLogRecordDO> {

    List<CommonLogRecordDO> queryRecordList(LogRecordBO logRecordBO);

    List<CommonLogRecordDO> queryLogRecordListByCursor(Integer logType, String dt, Long cursor, Integer limit);
}
