package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.DataCaseBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-28
 */
public interface DataCaseDAO {
    long insertDataCase(DataCaseDO dataCaseDO);

    long updateDataCase(DataCaseDO dataCaseDO);

    int logicDeleted(Long id, String operator);

    /**
     * 根据name模糊查询
     * @param name
     * @return
     */
    List<DataCaseDO> queryDataCaseLikeName(String name);

    PageBO<DataCaseDO> queryPageDataCaseList(DataCaseBO dataCaseBO);

    List<DataCaseDO> queryDataCaseList(DataCaseDO dataCaseDO);
    DataCaseDO queryDataCaseById(Long id);
}
