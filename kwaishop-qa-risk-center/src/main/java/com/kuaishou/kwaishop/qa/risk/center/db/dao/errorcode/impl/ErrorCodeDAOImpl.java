package com.kuaishou.kwaishop.qa.risk.center.db.dao.errorcode.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.errorcode.ErrorCodeDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.errorcode.ErrorCodeDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.errorcode.ErrorCodeMapper;

@Repository
public class ErrorCodeDAOImpl implements ErrorCodeDAO {


    @Autowired
    private ErrorCodeMapper errorCodeMapper;

    @Override
    public void insert(ErrorCodeDO errorCodeDO) {
        long time = System.currentTimeMillis();
        errorCodeDO.setCreateTime(time);
        errorCodeDO.setUpdateTime(time);
        int num = errorCodeMapper.insert(errorCodeDO);
        if (num != 1) {
            throw new RuntimeException("insert errorCodeDO error");
        }
    }

    @Override
    public void updateById(ErrorCodeDO errorCodeDO) {
        long time = System.currentTimeMillis();
        errorCodeDO.setUpdateTime(time);
        Wrapper<ErrorCodeDO> where = new LambdaQueryWrapper<ErrorCodeDO>()
                .eq(ErrorCodeDO::getId, errorCodeDO.getId());
        int num = errorCodeMapper.update(errorCodeDO, where);
        if (num != 1) {
            throw new RuntimeException("updateById errorCodeDO error");
        }
    }

    @Override
    public void deleteById(Long id) {
        if (id == null || id <= 0) {
            throw new RuntimeException("updateById errorCodeDO error for id is illegal");
        }
        int num = errorCodeMapper.deleteById(id);
        if (num != 1) {
            throw new RuntimeException("updateById errorCodeDO error");
        }
    }

    @Override
    public IPage<ErrorCodeDO> getErrorCodeList(String creator, Long current, Long size) {
        IPage<ErrorCodeDO> page = new Page<>(current, size);
        LambdaQueryWrapper<ErrorCodeDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(creator)) {
            queryWrapper
                    .eq(ErrorCodeDO::getCreator, creator);
        }
        IPage<ErrorCodeDO> selectPage = errorCodeMapper.selectPage(page, queryWrapper);
        return selectPage;
    }

    @Override
    public ErrorCodeDO getById(Long id) {
        ErrorCodeDO errorCodeDO = errorCodeMapper.selectById(id);
        return errorCodeDO;
    }

    @Override
    public ErrorCodeDO queryByErrorCode(String errorCode) {

        Wrapper<ErrorCodeDO> where = new LambdaQueryWrapper<ErrorCodeDO>()
                .eq(ErrorCodeDO::getErrorCode, errorCode);
        return errorCodeMapper.selectOne(where);

    }
}
