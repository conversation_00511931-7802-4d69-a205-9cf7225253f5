package com.kuaishou.kwaishop.qa.risk.center.domain.problem.biz;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.CreateProblemRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.DeleteProblemRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.PageProblemDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.ProblemDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.QueryProblemDetailListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.QueryProblemDetailPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.UpdateProblemRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-06
 */
public interface ProblemDetailBizService {

    void createProblem(CreateProblemRequest request);

    void updateProblem(UpdateProblemRequest request);

    void deleteProblem(DeleteProblemRequest request);

    List<ProblemDTO> queryProblemDetailList(QueryProblemDetailListRequest request);

    PageProblemDTO queryProblemDetailPageList(QueryProblemDetailPageListRequest request);
}
