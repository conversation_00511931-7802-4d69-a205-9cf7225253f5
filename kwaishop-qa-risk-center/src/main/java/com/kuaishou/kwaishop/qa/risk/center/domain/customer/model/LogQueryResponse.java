package com.kuaishou.kwaishop.qa.risk.center.domain.customer.model;

import java.util.List;

import lombok.Data;


@Data
public class LogQueryResponse {

    private String msg;

    private Integer code;

    private LogPageData data;

    @Data
    public static class LogPageData {

        private Integer pageIndex;
        private Integer totalCount;
        private List<LogFields> logList;


    }

    @Data
    public static class LogFields {
        private List<Field> logFieldList;
    }

    @Data
    public static class Field {
        private String field;
        private String value;
    }


}
