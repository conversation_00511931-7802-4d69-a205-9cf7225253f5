package com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.impl;

import static com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressScenarioRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress.StressScenarioRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.stress.ScenarioRecordQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressScenarioRecordBO;


@Repository
public class StressScenarioRecordDAOImpl extends BaseDAOImpl<StressScenarioRecordDO, ScenarioRecordQueryCondition>
        implements StressScenarioRecordDAO {
    @Autowired
    private StressScenarioRecordMapper stressScenarioRecordMapper;


    @Override
    protected void fillQueryCondition(ScenarioRecordQueryCondition condition, QueryWrapper<StressScenarioRecordDO> queryWrapper) {

    }

    @Override
    protected BaseMapper<StressScenarioRecordDO> getMapper() {
        return stressScenarioRecordMapper;
    }


    @Override
    public PageBO<StressScenarioRecordDO> queryPageList(QueryStressScenarioRecordBO bo) {
        Integer pageNo = 0;
        Integer pageSize = 10;
        if (bo.getPageNo() >= 1) {
            pageNo = bo.getPageNo() - 1;
        }
        if (bo.getPageSize() > 100 || bo.getPageSize() <= 0) {
            pageSize = 100;
        }
        Integer startNo = pageNo * bo.getPageSize();
        Long count = stressScenarioRecordMapper.countStressScenarioRecords(bo.getId(), bo.getScenarioName(), bo.getScenarioId());
        List<StressScenarioRecordDO> scenarioRecords  = stressScenarioRecordMapper.queryStressScenarioRecords(bo.getId(), bo.getScenarioName(),
                bo.getScenarioId(), startNo, pageSize);
        return new PageBO<StressScenarioRecordDO>(pageNo, pageSize,
                count, scenarioRecords);
    }

    @Override
    public StressScenarioRecordDO getById(Long id) {
        if (id < 0) {
           throw new BizException(SERVER_ERROR, "查询id不能小于0");
        }
        return stressScenarioRecordMapper.selectScenarioRecordById(id);

    }
}
