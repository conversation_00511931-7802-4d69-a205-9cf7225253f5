package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.ScriptRuleService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KrpcKspayConfigPythonServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayConfigPythonQueryRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayConfigPythonQueryResponse;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-03
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class KspayConfigPythonServiceImpl extends KrpcKspayConfigPythonServiceGrpc.KspayConfigPythonServiceImplBaseV2 {
    @Autowired
    private ScriptRuleService scriptRuleService;
    @Override
    public KspayConfigPythonQueryResponse kspayConfigRiskQuery(KspayConfigPythonQueryRequest request) {

        log.info("[KspayConfigPythonServiceImpl] kspayConfigRiskQuery request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            return scriptRuleService.executeResult(request);
        } catch (BizException e) {
            log.error("[KspayConfigPythonServiceImpl] kspayConfigRiskQuery bizError, exception: ", e);
            return KspayConfigPythonQueryResponse.newBuilder()
                    .setResult(false)
                    .setMessage(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayConfigPythonServiceImpl] kspayConfigRiskQuery error, exception: ", e);
            return KspayConfigPythonQueryResponse.newBuilder()
                    .setResult(false)
                    .setMessage(e.getMessage())
                    .build();
        }
    }

}
