package com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.dto;

import java.util.List;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-20
 *
 * 目前是一个用例里关联一对数据源，2期支持相同校验规则1个用例对应多对数据源
 */
@Data
@Builder
public class DataSourceFormat {
    /**
     * 目前只支持 DM 和 表 ，查询类型= QueryTypeEnum
     * 表: {type:1,"header":"ks_db.shengyitong_user_di","inParam":"select * from","outParam":["列1","列2"]}
     * dm: {type:4,"header":"queryId/serviceCode","inParam":"{ddd}","outParam":["data[0].user","data[1].address"}
     */
    //private Integer queryType;
    private String header;
    private String inParam;
    private List<String> outParam;

}
