package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.CheckReplayStatusByPipelineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.CheckReplayStatusByPipelineResponseFixed;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StartReplayByPipelineRequest;

public interface PipeLineService {
    String startReplayByPipeline(StartReplayByPipelineRequest request);

    CheckReplayStatusByPipelineResponseFixed checkReplayStatusByPipeline(CheckReplayStatusByPipelineRequest request);
}