package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.ConfigRulesBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.ScriptRuleDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.ScriptRuleMapper;



/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-03
 */
@Repository
public class ScriptRuleDAOImpl  implements ScriptRuleDAO {

    @Autowired
    private ScriptRuleMapper scriptRuleMapper;
    @Override
    public String getScriptRuleById(long id) {
        ConfigRulesBO configRulesBO = scriptRuleMapper.getScriptRuleById(id);
        return configRulesBO.getScriptRule();
    }
}
