package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.DataBaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.ScenarioExecuteDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.ScenarioExecuteDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.datatool.ScenarioExecuteMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.datatool.ScenarioExecuteCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.ScenarioExecuteBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2023-03-15
 */
@Repository
public class ScenarioExecuteDAOImpl extends DataBaseDAO<ScenarioExecuteDO, ScenarioExecuteCondition> implements ScenarioExecuteDAO {

    @Autowired
    private ScenarioExecuteMapper scenarioExecuteMapper;

    @Override
    protected void fillQueryCondition(ScenarioExecuteCondition condition, QueryWrapper<ScenarioExecuteDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getName())) {
            queryWrapper.like("name", condition.getName());
        }
        if (condition.getScenarioId() > 0) {
            queryWrapper.eq("scenario_id", condition.getScenarioId());
        }
        if (StringUtils.isNotBlank(condition.getExecutorName())) {
            queryWrapper.eq("executor_name", condition.getExecutorName());
        }
    }

    @Override
    protected BaseMapper<ScenarioExecuteDO> getMapper() {
        return scenarioExecuteMapper;
    }

    @Override
    public long insertScenarioExecute(ScenarioExecuteDO scenarioExecuteDO) {
        scenarioExecuteDO.setCreateTime(System.currentTimeMillis());
        scenarioExecuteDO.setUpdateTime(System.currentTimeMillis());
        scenarioExecuteDO.setStartTime(System.currentTimeMillis());
        return scenarioExecuteMapper.insert(scenarioExecuteDO);
    }

    @Override
    public long updateScenarioExecute(ScenarioExecuteDO scenarioExecuteDO) {
        scenarioExecuteDO.setUpdateTime(System.currentTimeMillis());
        return scenarioExecuteMapper.updateById(scenarioExecuteDO);
    }

    @Override
    public int logicDeleted(Long id, String operator) {
        return scenarioExecuteMapper.logicDeleted(operator, id);
    }

    @Override
    public List<ScenarioExecuteDO> queryScenarioExecuteLikeName(String name) {
        return scenarioExecuteMapper.queryScenarioExecuteLikeName(name);
    }

    @Override
    public PageBO<ScenarioExecuteDO> queryPageScenarioExecuteList(ScenarioExecuteBO scenarioExecuteBO) {
        ScenarioExecuteCondition queryCondition = ScenarioExecuteCondition.builder()
                .id(scenarioExecuteBO.getId())
                .scenarioId(scenarioExecuteBO.getScenarioId())
                .executorName(scenarioExecuteBO.getExecutorName())
                .orderByCreateTimeDesc(true)
                .pageNo(scenarioExecuteBO.getPageNo())
                .pageSize(scenarioExecuteBO.getPageSize())
                .build();
        return queryPageList(queryCondition);
    }

    @Override
    public List<ScenarioExecuteDO> queryScenarioExecuteList(ScenarioExecuteDO scenarioExecuteDO) {
        return scenarioExecuteMapper.queryScenarioExecute();
    }

    @Override
    public ScenarioExecuteDO queryScenarioExecuteById(Long id) {
        return scenarioExecuteMapper.queryScenarioExecuteById(id);
    }
}
