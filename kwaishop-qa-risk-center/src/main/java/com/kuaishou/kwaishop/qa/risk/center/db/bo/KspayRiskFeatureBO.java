package com.kuaishou.kwaishop.qa.risk.center.db.bo;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.KspayBaseDO;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
public class KspayRiskFeatureBO<T extends KspayBaseDO> {
    private long pageNo;
    private long pageSize;
    private long total;
    private List<T> dataList;

    public KspayRiskFeatureBO(long pageNo, long pageSize, long total, List<T> dataList) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.total = total;
        this.dataList = dataList;
    }

    public long getPageNo() {
        return pageNo;
    }

    public void setPageNo(long pageNo) {
        this.pageNo = pageNo;
    }

    public long getPageSize() {
        return pageSize;
    }

    public void setPageSize(long pageSize) {
        this.pageSize = pageSize;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public List<T> getDataList() {
        return dataList;
    }

    public void setDataList(List<T> dataList) {
        this.dataList = dataList;
    }
}