package com.kuaishou.kwaishop.qa.risk.center.domain.errorcode.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCodeError;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.errorcode.ErrorCodeDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.errorcode.ErrorCodeDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.errorcode.ErrorCodeService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.AddErrorCodeRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.DeleteErrorCodeRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.ErrorCodeEntity;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.ErrorCodeListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.ErrorCodeListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.ErrorInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.UpdateErrorCodeRequest;

import lombok.extern.slf4j.Slf4j;

@Service
@Lazy
@Slf4j
public class ErrorCodeServiceImpl implements ErrorCodeService {

    @Autowired
    private ErrorCodeDAO errorCodeDAO;

    @Override
    public void addErrorCode(AddErrorCodeRequest addErrorCodeRequest) {

        ErrorCodeDO errorCodeDO = new ErrorCodeDO();
        errorCodeDO.setErrorCode(addErrorCodeRequest.getErrorCode());
        errorCodeDO.setErrorCodeDesc(addErrorCodeRequest.getErrorCodeDesc());
        errorCodeDO.setCreator(addErrorCodeRequest.getCurUser());
        errorCodeDAO.insert(errorCodeDO);


    }

    @Override
    public void updateErrorCode(UpdateErrorCodeRequest updateRequest) {
        ErrorCodeDO errorCodeDOInDB = errorCodeDAO.getById(updateRequest.getId());
        if (!errorCodeDOInDB.getCreator().equals(updateRequest.getCurUser())) {
            throw new BizException(ErrorCodeError.NOT_PERMIT);
        }
        ErrorCodeDO errorCodeDO = new ErrorCodeDO();
        errorCodeDO.setId(updateRequest.getId());
        errorCodeDO.setErrorCode(updateRequest.getErrorCode());
        errorCodeDO.setErrorCodeDesc(updateRequest.getErrorCodeDesc());
        errorCodeDAO.updateById(errorCodeDO);
    }

    @Override
    public void deleteErrorCode(DeleteErrorCodeRequest deleteRequest) {
        ErrorCodeDO errorCodeDOInDB = errorCodeDAO.getById(deleteRequest.getId());
        if (errorCodeDOInDB == null) {
            throw new BizException(ErrorCodeError.DATA_NOT_EXSIT);
        }
        if (!errorCodeDOInDB.getCreator().equals(deleteRequest.getCurUser())) {
            throw new BizException(ErrorCodeError.NOT_PERMIT);
        }
        errorCodeDAO.deleteById(deleteRequest.getId());
    }

    @Override
    public ErrorCodeDO queryByErrorCode(ErrorInfoRequest errorInfoRequest) {
        return errorCodeDAO.queryByErrorCode(errorInfoRequest.getErrorCode());

    }

    @Override
    public ErrorCodeListResponse getErrorCodeList(ErrorCodeListRequest listRequest) {
        IPage<ErrorCodeDO> bizAreaList;
        if (listRequest.getCreatedByMe()) {
            bizAreaList = errorCodeDAO.getErrorCodeList(listRequest.getCurUser(), listRequest.getPageNum(),
                    listRequest.getPageSize());
        } else {
            bizAreaList = errorCodeDAO.getErrorCodeList(null, listRequest.getPageNum(),
                    listRequest.getPageSize());
        }

        ErrorCodeListResponse.Builder builder = ErrorCodeListResponse.newBuilder();
        List<ErrorCodeDO> records = bizAreaList.getRecords();
        List<ErrorCodeEntity> errorCodeEntities = records.stream().map(errorCodeDO ->
                ErrorCodeEntity
                        .newBuilder()
                        .setId(errorCodeDO.getId())
                        .setErrorCode(errorCodeDO.getErrorCode())
                        .setErrorCodeDesc(errorCodeDO.getErrorCodeDesc())
                        .setCreator(errorCodeDO.getCreator())
                        .build()
        ).collect(Collectors.toList());

        builder.addAllErrorCodeEntity(errorCodeEntities);
        builder.setPageNum(bizAreaList.getCurrent());
        builder.setTotalPage(bizAreaList.getTotal());
        return builder.build();
    }
}
