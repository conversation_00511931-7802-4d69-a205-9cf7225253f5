package com.kuaishou.kwaishop.qa.risk.center.tianhe.Enums;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-31
 */
public enum TianheErrCodeEnum implements ErrorCode {
    UNKNOWN(0, "未知"),
    SUCCESS(1, "成功"),
    SERVER_ERROR(11, "服务端错误"),
    NO_PERMISSION(1001, "没有操作权限"),
    INSERT_FAIL(1002, "插入记录失败"),
    PARAM_INVALID(1003, "参数错误"),
    RECORD_DELETED(1004, "记录已删除"),
    UPDATE_FAIL(1005, "更新记录失败"),
    QUERY_FAIL(1006, "查询记录失败");


    private final int code;
    private final String message;

    TianheErrCodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    @Override
    public boolean isLogErrorMsg() {
        return false;
    }
}
