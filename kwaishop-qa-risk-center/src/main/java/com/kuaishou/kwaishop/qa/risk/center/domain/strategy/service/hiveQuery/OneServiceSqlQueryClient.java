package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.hiveQuery;

import static com.kuaishou.dp.one.service.rpc.client.sql.model.table.Field.field;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.dp.one.service.rpc.client.hash.base.ClientErrorCode;
import com.kuaishou.dp.one.service.rpc.client.sql.OneServiceSqlClient;
import com.kuaishou.dp.one.service.rpc.client.sql.model.QueryResult;
import com.kuaishou.dp.one.service.rpc.client.sql.model.table.Table;
import com.kuaishou.dp.one.service.rpc.client.sql.model.table.TableField;
import com.kuaishou.dp.one.service.rpc.client.sql.model.table.TableFieldValue;
import com.kuaishou.dp.one.service.rpc.client.sql.model.table.TableRow;
import com.kuaishou.dp.one.service.rpc.client.sql.wrapper.StreamCloseableIterator;
import com.kuaishou.kconf.common.json.JsonMapperUtils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class OneServiceSqlQueryClient {

    private OneServiceSqlClient oneServiceSqlClient = new OneServiceSqlClient();

    /**
     * 简单查询
     * 更多详情请参考 https://wiki.corp.kuaishou.com/pages/viewpage.action?pageId=738644014
     */
    public List<Map<String, Object>> queryStrategyRecord(String pdate, String sceneKey, int offset, int limit) throws Exception {
        List result = Lists.newArrayList();
        oneServiceSqlClient.setToken("23a221409d4749cc907eac4ea398abab");
        try (StreamCloseableIterator<QueryResult> streamCloseableIterator = oneServiceSqlClient.select(
                        field("condition_exec_detail_info"), field("trace_id"), field("action_hit_policy_code_map"),
                        field("create_time"), field("rule_exec_detail_info"), field("dag_node_map"), field("p_hourmin"),
                        field("end_time"), field("scene_source_type"), field("action_hit_policy_codes"), field(
                                "scene_type"),
                        field("strategy_id_version_map"), field("strategy_id_map"), field("scene_key"),
                        field("request_hit_result"), field("p_date"), field("feature_key_map"), field("action_hit_ids"),
                        field("biz_key_info"), field("scene_execute_type"), field("scene_version"),
                        field("action_hit_strategy_policy_codes"), field("action_remarke_map"))
                .from(Table.getTableByApiName("getStrategyRecordSceneKey"))
                .where(field("p_date").equal(pdate).and(field("scene_key_partition").equal(sceneKey)))
                .groupBy()  // 即使不需要聚合，也需要这一行
                .orderBy()  // 排序同理
                .offset(offset)   // offset同理
                .limit(limit).fetchAsStream()) {
            while (streamCloseableIterator.hasNext()) {
                QueryResult queryResult = streamCloseableIterator.next();
                if (ClientErrorCode.OK == queryResult.getResponseCode()) {
                    Collection<TableRow> rows = queryResult.getRows();
                    // 一共多少行返回
                    log.info("OneServiceSqlQueryClient.rowscount:{}", rows.size());

                    for (TableRow row : rows) {
                        Map<String, Object> resultMap = Maps.newHashMap();
                        log.info("OneServiceSqlQueryClient.rowsMap:{}", JsonMapperUtils.toJson(resultMap));
                        // 遍历每一行数据
                        Collection<TableField> columns = row.getFields();
                        for (TableField column : columns) {
                            // 针对每行的各个列进行解析
                            String columnName = column.getTableFieldName();
                            TableFieldValue columnValue = column.getTableFieldValue();
                            resultMap.put(columnName, columnValue.getFieldValue());
                            Object actualValue = null;
                            // 列的数据类型可能不一样，去解析出真正的数据类型
                            switch (columnValue.getFieldType()) {
                                case BOOLEAN:
                                    actualValue = columnValue.getFieldValueAsBoolean();
                                    break;
                                case INTEGER:
                                    actualValue = columnValue.getFieldValueAsInteger();
                                    break;
                                case DOUBLE:
                                    actualValue = columnValue.getFieldValueAsDouble();
                                    break;
                                case STRING:
                                    actualValue = columnValue.getFieldValueAsString();
                                    break;
                                case FLOAT:
                                    actualValue = columnValue.getFieldValueAsFloat();
                                    break;
                                case LONG:
                                    actualValue = columnValue.getFieldValueAsLong();
                                    break;
                                default:
                                    break;
                            }
                        }
                        result.add(resultMap);
                    }
                } else {
                    // 打印错误信息
                    log.info("OneServiceSqlQueryClient.queryStrategyRecord.error:{}", queryResult.getErrMessage());
                }
            }
        }
        return result;
    }

    public List<Map<String, Object>> queryStrategyRecord(String pdate, String pHourmin, String sceneKey, int offset, int limit) throws Exception {
        List result = Lists.newArrayList();
        oneServiceSqlClient.setToken("23a221409d4749cc907eac4ea398abab");
        try (StreamCloseableIterator<QueryResult> streamCloseableIterator = oneServiceSqlClient.select(
                        field("condition_exec_detail_info"), field("trace_id"), field("action_hit_policy_code_map"),
                        field("create_time"), field("rule_exec_detail_info"), field("dag_node_map"), field("p_hourmin"),
                        field("end_time"), field("scene_source_type"), field("action_hit_policy_codes"), field(
                                "scene_type"),
                        field("strategy_id_version_map"), field("strategy_id_map"), field("scene_key"),
                        field("request_hit_result"), field("p_date"), field("feature_key_map"), field("action_hit_ids"),
                        field("biz_key_info"), field("scene_execute_type"), field("scene_version"),
                        field("action_hit_strategy_policy_codes"), field("action_remarke_map"))
                .from(Table.getTableByApiName("getStrategyRecordSceneKey"))
                .where(field("p_date").equal(pdate).and(field("scene_key_partition").equal(sceneKey)).and(field("p_hourmin").equal(pHourmin)))
                .groupBy()  // 即使不需要聚合，也需要这一行
                .orderBy()  // 排序同理
                .offset(offset)   // offset同理
                .limit(limit).fetchAsStream()) {
            while (streamCloseableIterator.hasNext()) {
                QueryResult queryResult = streamCloseableIterator.next();
                if (ClientErrorCode.OK == queryResult.getResponseCode()) {
                    Collection<TableRow> rows = queryResult.getRows();
                    // 一共多少行返回
                    log.info("OneServiceSqlQueryClient.rowscount:{}", rows.size());

                    for (TableRow row : rows) {
                        Map<String, Object> resultMap = Maps.newHashMap();
                        log.info("OneServiceSqlQueryClient.rowsMap:{}", JsonMapperUtils.toJson(resultMap));
                        // 遍历每一行数据
                        Collection<TableField> columns = row.getFields();
                        for (TableField column : columns) {
                            // 针对每行的各个列进行解析
                            String columnName = column.getTableFieldName();
                            TableFieldValue columnValue = column.getTableFieldValue();
                            resultMap.put(columnName, columnValue.getFieldValue());
                            Object actualValue = null;
                            // 列的数据类型可能不一样，去解析出真正的数据类型
                            switch (columnValue.getFieldType()) {
                                case BOOLEAN:
                                    actualValue = columnValue.getFieldValueAsBoolean();
                                    break;
                                case INTEGER:
                                    actualValue = columnValue.getFieldValueAsInteger();
                                    break;
                                case DOUBLE:
                                    actualValue = columnValue.getFieldValueAsDouble();
                                    break;
                                case STRING:
                                    actualValue = columnValue.getFieldValueAsString();
                                    break;
                                case FLOAT:
                                    actualValue = columnValue.getFieldValueAsFloat();
                                    break;
                                case LONG:
                                    actualValue = columnValue.getFieldValueAsLong();
                                    break;
                                default:
                                    break;
                            }
                        }
                        result.add(resultMap);
                    }
                } else {
                    // 打印错误信息
                    log.info("OneServiceSqlQueryClient.queryStrategyRecord.error:{}", queryResult.getErrMessage());
                }
            }
        }
        return result;
    }
}