package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.FundsMeasureConfigDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsMeasureConfigDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.FundsMeasureConfigMapper;

import groovy.util.logging.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024/3/7 16:09
 */
@Repository
@Slf4j
public class FundsMeasureConfigDAOImpl implements FundsMeasureConfigDAO {
    @Autowired
    private FundsMeasureConfigMapper fundsMeasureConfigMapper;

    @Override
    public List<FundsMeasureConfigDO> getConfigList(String moduleName) {
        return fundsMeasureConfigMapper.getConfigList(moduleName);
    }
}
