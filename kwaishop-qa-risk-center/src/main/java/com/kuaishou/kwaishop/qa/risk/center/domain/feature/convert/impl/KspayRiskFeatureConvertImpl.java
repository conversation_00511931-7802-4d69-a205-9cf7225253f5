package com.kuaishou.kwaishop.qa.risk.center.domain.feature.convert.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.qa.risk.center.domain.risk.service.impl.IncrementRiskMeasureServiceImpl.getScoreFromData;
import static org.apache.commons.lang3.StringUtils.trimToEmpty;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.json.JSONArray;
import org.springframework.stereotype.Component;

import com.kuaishou.blobstore.common.utils.StringUtils;
import com.kuaishou.kspay.util.GsonUtils;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.ChainKeyBO;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.QueryCheckBO;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.RiskFieldBO;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.RiskInfoBO;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.RiskInfoQueryBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureBranchDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundRiskFeatureDetailQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundsRiskFeatureQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.convert.KspayRiskFeatureConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.impl.KspayTeamResourceServiceImpl;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.AssertReportList;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayBranchAndViewIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetail;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetailSimple;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetailSimpleV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureListParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureViewParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskUserMarkInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.PageKspayRiskFeatureListDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryPageRiskFeatureListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryRiskFeatureDetailByIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryRiskFeatureDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayBranchRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayBranchRequestV2;
import com.kuaishou.kwaishop.qa.risk.center.ral.client.QueryCheckAndRulesClient;
//import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.MethodCovInfo;


@Component
public class KspayRiskFeatureConvertImpl implements KspayRiskFeatureConvert {
    @Override
    public FundsRiskFeatureQueryCondition buildFundsRiskFeatureListQueryCondition(QueryPageRiskFeatureListRequest request) {
        return FundsRiskFeatureQueryCondition.builder()
                .creator(request.getCreator().trim())
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .department(request.getDepartment())
                .businessDomain(request.getBusinessDomain())
                .featureName(request.getFeatureName())
                .featureId(request.getFeatureId())
                .teamWorker(request.getTeamWorker())
                .status(request.getStatus())
                .isRisk(request.getIsRisk())
                .startTime(request.getStartTime())
                .endTime(request.getEndTime())
                .testType(request.getTestType())
                .isQueryOrderByCreateTime(request.getIsQueryByCreateTime())
                .build();
    }

    @Override
    public FundRiskFeatureDetailQueryCondition buildFundsRiskFeatureBranchQueryCondition(QueryPageRiskFeatureListRequest request) {
        return FundRiskFeatureDetailQueryCondition.builder()
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .build();
    }

    @Override
    public FundRiskFeatureDetailQueryCondition buildFundsRiskRiskQueryCondition(KspayBranchRequest request) {
        return FundRiskFeatureDetailQueryCondition.builder()
                .repoName(trimToEmpty(request.getRepoName()))
                .branchName(trimToEmpty(request.getBranchName()))
                .build();
    }

    @Override
    public FundRiskFeatureDetailQueryCondition buildFundsRiskRiskQueryConditionV2(KspayBranchRequestV2 request) {
        return FundRiskFeatureDetailQueryCondition.builder()
                .repoName(trimToEmpty(request.getRepoName()))
                .branchName(trimToEmpty(request.getBranchName()))
                .build();
    }

    @Override
    public FundRiskFeatureDetailQueryCondition buildFundsRiskBranchQueryCondition(KspayBranchAndViewIdRequest request) {
        return FundRiskFeatureDetailQueryCondition.builder()
                .repoName(trimToEmpty(request.getRepoName()))
                .branchName(trimToEmpty(request.getBranchName()))
                .featureViewId(Long.valueOf(trimToEmpty(request.getFeatureViewId())))
                .build();
    }

    @Override
    public FundRiskFeatureDetailQueryCondition buildFundsRiskFeatureDetailQueryCondition(QueryRiskFeatureDetailRequest request) {
        return FundRiskFeatureDetailQueryCondition.builder()
                .featureViewId(Long.parseLong(request.getFeatureId()))
                .build();
    }

    @Override
    public FundRiskFeatureDetailQueryCondition buildFundsRiskFeatureDetailQueryCondition(KspayRiskFeatureDetail request) {
        return FundRiskFeatureDetailQueryCondition.builder()
                .featureViewId(Long.parseLong(request.getFeatureId()))
                .build();
    }

    @Override
    public FundRiskFeatureDetailQueryCondition buildFundsRiskBranchQueryConditionId(QueryRiskFeatureDetailByIdRequest request) {
        return FundRiskFeatureDetailQueryCondition.builder()
                .id(Long.parseLong(request.getId()))
                .build();
    }

    @Override
    public KspayRiskFeatureDetail buildKspayRiskFeatureDetail(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO) {
        return KspayRiskFeatureDetail.newBuilder()
                .setId(fundsRiskFeatureBranchDO.getId().toString())
                .setFeatureViewId(Math.toIntExact(fundsRiskFeatureBranchDO.getFeatureViewId()))
                .setRepoName(trimToEmpty(fundsRiskFeatureBranchDO.getRepoName()))
                .setBranchName(trimToEmpty(fundsRiskFeatureBranchDO.getBranchName()))
                .setMethodEntrance(trimToEmpty(getFullMethodName(fundsRiskFeatureBranchDO.getMethodEntrance())))
                .setAccuracyUrl(trimToEmpty(fundsRiskFeatureBranchDO.getAccuracyUrl()))
                .setChangeMethodCount(Optional.ofNullable(fundsRiskFeatureBranchDO.getChangeMethodAmount()).orElse(0))
                .setFundRiskMethodCount(Optional.ofNullable(fundsRiskFeatureBranchDO.getFundRiskMethodAmount()).orElse(0))
                .setTestResult(fundsRiskFeatureBranchDO.getTestResult())
                .setRiskStatus(Optional.ofNullable(fundsRiskFeatureBranchDO.getRiskStatus()).orElse(0))  // 6.25新增字段
                .setAuditCover(Optional.ofNullable(fundsRiskFeatureBranchDO.getAuditCover()).orElse(0))
                .setCreateTime(fundsRiskFeatureBranchDO.getCreateTime())
                .setUpdateTime(fundsRiskFeatureBranchDO.getUpdateTime())
                .setCreator(fundsRiskFeatureBranchDO.getCreator())
                .setUpdater(trimToEmpty(fundsRiskFeatureBranchDO.getUpdater()))
                .setRiskStatusUpdater(trimToEmpty(fundsRiskFeatureBranchDO.getRiskStatusUpdater()))
                .setAuditCoverUpdater(trimToEmpty(fundsRiskFeatureBranchDO.getAuditCoverUpdater()))
                .setRiskTableFields(trimToEmpty(fundsRiskFeatureBranchDO.getRiskTableFields()))
                .build();
    }

    @Override
    public List<KspayRiskFeatureDetail> buildKspayRiskFeatureBranch(List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS) {
        List<KspayRiskFeatureDetail> kspayRiskFeatureDetails = new ArrayList<>();
        for (FundsRiskFeatureBranchDO request : fundsRiskFeatureBranchDOS
        ) {
            KspayRiskFeatureDetail build = KspayRiskFeatureDetail.newBuilder()
                    .setId(request.getId().toString())
                    .setFeatureViewId(Math.toIntExact(request.getFeatureViewId()))
                    .setRepoName(trimToEmpty(request.getRepoName()))
                    .setBranchName(trimToEmpty(request.getBranchName()))
                    .setMethodEntrance(trimToEmpty(getFullMethodName(request.getMethodEntrance())))
                    .setAccuracyUrl(trimToEmpty(request.getAccuracyUrl()))
                    .setChangeMethodCount(Optional.ofNullable(request.getChangeMethodAmount()).orElse(0))
                    .setFundRiskMethodCount(Optional.ofNullable(request.getFundRiskMethodAmount()).orElse(0))
                    .setRiskTableFields(trimToEmpty(request.getRiskTableFields()))
                    .setTestResult(trimToEmpty(request.getTestResult()))
                    .setRiskStatus(Optional.ofNullable(request.getRiskStatus()).orElse(0))
                    .setAuditCover(Optional.ofNullable(request.getAuditCover()).orElse(0))
                    .setCreateTime(request.getCreateTime())
                    .setUpdateTime(request.getUpdateTime())
                    .setCreator(request.getCreator())
                    .setUpdater(trimToEmpty(request.getUpdater()))
                    .setRiskStatusUpdater(trimToEmpty(request.getRiskStatusUpdater()))
                    .setAuditCoverUpdater(trimToEmpty(request.getAuditCoverUpdater()))
                    .setScore(StringUtils.isBlank(getScoreFromData(request)) ? "--" : getScoreFromData(request) + "分")
                    .setKatRecord(trimToEmpty(request.getKatRecord()))
                    .build();
            kspayRiskFeatureDetails.add(build);
        }
        return kspayRiskFeatureDetails;
    }


    /**
     * 下面获取链的代码有耗时问题, 先注释掉
     */
    public List<KspayRiskFeatureDetail> buildKspayRiskFeatureBranchError(List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS) {
        List<KspayRiskFeatureDetail> kspayRiskFeatureDetails = new ArrayList<>();
        //构造请求体，获取仓库名和风险表字段
        List<RiskInfoBO> requestBody = fundsRiskFeatureBranchDOS.stream().map(fundsRiskFeatureBranchDO -> RiskInfoBO.builder()
                        .riskFields(GsonUtils.fromJSON(fundsRiskFeatureBranchDO.getRiskTableFields(), ArrayList.class, RiskFieldBO.class))
                        .warehouse(fundsRiskFeatureBranchDO.getRepoName())
                        .build())
                .collect(Collectors.toList());
        List<RiskInfoQueryBO> checkRequest = requestBody.stream().map(body -> RiskInfoQueryBO.builder()
                        .warehouse(body.getWarehouse())
                        .riskFields(body.getRiskFields().stream().map(field -> QueryCheckBO.builder()
                                        .riskElements(Arrays.asList(field.getRiskElements().split(",")))
                                        .tableName(field.getTableName())
                                        .build())
                                .collect(Collectors.toList()))
                        .build())
                .collect(Collectors.toList());
        Map<String, List<ChainKeyBO>> checkChain = QueryCheckAndRulesClient.queryCheckChain(checkRequest);
        for (FundsRiskFeatureBranchDO request : fundsRiskFeatureBranchDOS) {
            //解析chainKeys
            List<ChainKeyBO> chainKeys = null;
            if (checkChain != null) {
                chainKeys = checkChain.get(request.getRepoName());
            }
            List<String> collect;
            if (chainKeys != null) {
                collect = chainKeys.stream().map(chainKeyBO ->
                                new String("核对链" + chainKeyBO.getChainId() + ":"
                                        + chainKeyBO.getChainName() + (chainKeyBO.getState() == 0 ? "规则待确认" : "规则已确认"))
                        )
                        .collect(Collectors.toList());
            } else {
                collect = new ArrayList<>();
            }
            String chainKeyRes = String.join(",", collect);
            KspayRiskFeatureDetail build = KspayRiskFeatureDetail.newBuilder()
                    .setId(request.getId().toString())
                    .setFeatureViewId(Math.toIntExact(request.getFeatureViewId()))
                    .setRepoName(trimToEmpty(request.getRepoName()))
                    .setBranchName(trimToEmpty(request.getBranchName()))
                    .setMethodEntrance(trimToEmpty(getFullMethodName(request.getMethodEntrance())))
                    .setAccuracyUrl(trimToEmpty(request.getAccuracyUrl()))
                    .setChangeMethodCount(Optional.ofNullable(request.getChangeMethodAmount()).orElse(0))
                    .setFundRiskMethodCount(Optional.ofNullable(request.getFundRiskMethodAmount()).orElse(0))
                    .setRiskTableFields(trimToEmpty(request.getRiskTableFields()))
                    .setTestResult(trimToEmpty(request.getTestResult()))
                    .setRiskStatus(Optional.ofNullable(request.getRiskStatus()).orElse(0))
                    .setAuditCover(Optional.ofNullable(request.getAuditCover()).orElse(0))
                    .setCreateTime(request.getCreateTime())
                    .setUpdateTime(request.getUpdateTime())
                    .setCreator(request.getCreator())
                    .setUpdater(trimToEmpty(request.getUpdater()))
                    .setRiskStatusUpdater(trimToEmpty(request.getRiskStatusUpdater()))
                    .setAuditCoverUpdater(trimToEmpty(request.getAuditCoverUpdater()))
                    .setScore(StringUtils.isBlank(getScoreFromData(request)) ? "--" : getScoreFromData(request) + "分")
                    .setCheckChains(chainKeyRes)
                    .build();
            kspayRiskFeatureDetails.add(build);
        }
        return kspayRiskFeatureDetails;
    }

    @Override
    public KspayRiskFeatureListParam buildKspayRiskFeatureListParam(FundsRiskFeatureDO fundsRiskFeatureDO,
                                                                    List<KspayRiskFeatureDetail> kspayRiskFeatureDetails) {
//        HashMap<String, String> data = isEmpty(fundsRiskFeatureDO.getExtraInfo())
//                ? Maps.newHashMap()
//                : fromJSON(fundsRiskFeatureDO.getExtraInfo(), Map.class, String.class, String.class);
        // 获取所有的分数
        List<String> scoreList = kspayRiskFeatureDetails.stream()
                .filter(d -> StringUtils.isNotBlank(d.getScore()) && !d.getScore().equals("--"))
                .map(KspayRiskFeatureDetail::getScore)
                .collect(Collectors.toList());
        double allScore = 0.0;
        boolean isZero = false;
        if (scoreList.size() != 0) {
            isZero = true;
            for (String score : scoreList) {
                String replaceScore = score.replace("分", "");
                allScore += Double.parseDouble(replaceScore);
            }
            allScore = allScore / scoreList.size();
        }
        // 保留一位小数
        allScore = new BigDecimal(allScore).setScale(1, RoundingMode.HALF_UP).doubleValue();

        return KspayRiskFeatureListParam.newBuilder()
                .setId(fundsRiskFeatureDO.getId().toString())
                .addAllKspayRiskFeatureDetail(kspayRiskFeatureDetails)
                .setDepartment(fundsRiskFeatureDO.getDepartment())
                .setBusinessDomain(trimToEmpty(fundsRiskFeatureDO.getBusinessDomain()))
                .setFeatureId(fundsRiskFeatureDO.getFeatureId())
                .setFeatureName(trimToEmpty(fundsRiskFeatureDO.getFeatureName()))
                .setRiskCoe(trimToEmpty(fundsRiskFeatureDO.getRiskCoe()))
                .setRiskPassStatus(Optional.ofNullable(fundsRiskFeatureDO.getRiskPassStatus()).orElse(0))  // 6.25新增字段
                .setStatus(fundsRiskFeatureDO.getStatus())
                .setTeamId(trimToEmpty(fundsRiskFeatureDO.getTeamId()))
                .setTeamName(trimToEmpty(fundsRiskFeatureDO.getTeamName()))
                .setTeamWorker(trimToEmpty(fundsRiskFeatureDO.getTeamWorker()))
                .setCreator(trimToEmpty(fundsRiskFeatureDO.getCreator()))
                .setCreateTime(fundsRiskFeatureDO.getCreateTime())
                .setUpdater(trimToEmpty(fundsRiskFeatureDO.getUpdater()))
                .setUpdateTime(fundsRiskFeatureDO.getUpdateTime())
                .setScore(allScore != 0.0 || isZero ? Double.toString(allScore) + "分" : "--")
                .setIsRisk(fundsRiskFeatureDO.getIsRisk())
                .setTestType(Optional.ofNullable(fundsRiskFeatureDO.getTestType()).orElse(0))
                .build();

    }

    @Override
    public FundRiskFeatureDetailQueryCondition buildFundRiskFeatureBranchQueryCondition(String featureId) {
        return FundRiskFeatureDetailQueryCondition.builder()
                .featureViewId(Long.valueOf(featureId))
                .build();
    }

    @Override
    public FundRiskFeatureDetailQueryCondition buildFundRiskFeatureBranchQueryCondition(String featureId, Integer isRisk) {
        return FundRiskFeatureDetailQueryCondition.builder()
                .featureViewId(Long.valueOf(featureId))
                .fundRiskMethodAmount(isRisk)
                .build();
    }

    @Override
    public PageKspayRiskFeatureListDTO convertToFeaturePageDTO(List<KspayRiskFeatureListParam> kspayRiskFeatureListParams, Integer pageNo,
                                                               Integer pageSize, Long total) {
        return PageKspayRiskFeatureListDTO.newBuilder()
                .setPageNo(pageNo)
                .setPageSize(pageSize)
                .setTotal(total)
                .addAllRiskFeatureList(kspayRiskFeatureListParams)
                .build();
    }

    @Override
    public KspayRiskFeatureDetailSimple convertToRiskDetailDTO(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDOS,
                                                               KspayBranchRequest request) {
        return KspayRiskFeatureDetailSimple.newBuilder()
                .setId(Optional.ofNullable(fundsRiskFeatureBranchDOS.getId()).orElse(0L).toString())
                .setFeatureViewId(Optional.ofNullable(fundsRiskFeatureBranchDOS.getFeatureViewId()).orElse(0))
                .setFeatureId(Optional.ofNullable(fundsRiskFeatureBranchDOS.getFeatureViewId()).orElse(0))
                .setRepoName(request.getRepoName())
                .setBranchName(request.getBranchName())
                .setAccuracyUrl(trimToEmpty(fundsRiskFeatureBranchDOS.getAccuracyUrl()))
//                .addAllMethodCovInfos(methodCovInfos)
                .setMethodEntrance(trimToEmpty(getFullMethodName(fundsRiskFeatureBranchDOS.getMethodEntrance())))
                .setRiskStatus(Optional.ofNullable(fundsRiskFeatureBranchDOS.getRiskStatus()).orElse(0))
                .setAuditCover(Optional.ofNullable(fundsRiskFeatureBranchDOS.getAuditCover()).orElse(0))
                .setTestResult(trimToEmpty(fundsRiskFeatureBranchDOS.getTestResult()))
                .setChangeMethodCount(Optional.ofNullable(fundsRiskFeatureBranchDOS.getChangeMethodAmount()).orElse(0))
                .setFundRiskMethodCount(Optional.ofNullable(fundsRiskFeatureBranchDOS.getFundRiskMethodAmount()).orElse(0))
                .setRiskTableFields(trimToEmpty(trimToEmpty(fundsRiskFeatureBranchDOS.getRiskTableFields())))
                .build();
    }


    @Override
    public KspayRiskFeatureDetailSimpleV2 convertToRiskDetailDTOV2(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDOS,
                                                                   KspayBranchRequestV2 request, ArrayList<String> qaMangelist,
                                                                   boolean isSendMsg) {
        String riskStatus = "";
        if (fundsRiskFeatureBranchDOS.getRiskStatus() == 1) {
            riskStatus = "通过";
        } else {
            riskStatus = "未通过";
        }
        String auditCover = "";
        if (fundsRiskFeatureBranchDOS.getAuditCover() == 1) {
            auditCover = "是";
        } else {
            auditCover = "否";
        }


        /**
         *
         * 1、通过应用 + 分支名 获取featureId
         * 2、通过featureId 获取 branchId
         * 3、branchId 作为入参调用kdev 平台的开放API ，获得QAList
         */

        if (Optional.ofNullable(fundsRiskFeatureBranchDOS.getFundRiskMethodAmount()).orElse(0) > 0) {
            if (isSendMsg) {
                //消息号通知
                new KspayTeamResourceServiceImpl().sendMessage(new JSONArray(qaMangelist),
                        fundsRiskFeatureBranchDOS.getRepoName(),
                        fundsRiskFeatureBranchDOS.getBranchName(),
                        fundsRiskFeatureBranchDOS.getFeatureViewId(),
                        riskStatus);
                return KspayRiskFeatureDetailSimpleV2.newBuilder()
                        .setFailReason("理论上不会失败")
                        .setViewUrlTitle("资金安全风险")
                        .setViewContent("<div>\n"
                                + "  <div style=\"display:flex;align-items:center;\">\n"
                                + "  \t<div>资金风险方法数：</div>\n"
                                + "    <div style=\"color:red;\">"
                                + fundsRiskFeatureBranchDOS.getFundRiskMethodAmount()
                                + "</div>\n"
                                + "  </div>\n"
                                + "  <div style=\"display:flex;align-items:center;\">\n"
                                + "  \t<div>资金风险测试结果：</div>\n"
                                + "    <div style=\"color:red;\">"
                                + riskStatus
                                + "</div>\n"
                                + "  </div>\n"
                                + "  <div style=\"display:flex;align-items:center;\">\n"
                                + "  \t<div>核对是否覆盖：</div>\n"
                                + "    <div style=\"color:red;\">"
                                + auditCover
                                + "</div>\n"
                                + "  </div>\n"
                                + "</div>")
                        .addAssertReportList(AssertReportList.newBuilder()
                                .setTitle("资金安全风险平台")//统一页面
                                .setUrl("https://kwaishop-risk.staging.kuaishou.com/kspay/kspayRiskFeatureQuery?featureId="
                                        + fundsRiskFeatureBranchDOS.getFeatureViewId()
                                ).build())
                        .setKdevNoticeUsers(String.join(",", qaMangelist))//通知用户列表，字符串，多值使用逗号或分号分隔
                        .setKdevNoticeContent("资金安全风险结果-" + riskStatus)
                        .build();
            } else {
                // 只返回数据, 不消息通知
                return KspayRiskFeatureDetailSimpleV2.newBuilder()
                        .setFailReason("理论上不会失败")
                        .setViewUrlTitle("资金安全风险")
                        .setViewContent("<div>\n"
                                + "  <div style=\"display:flex;align-items:center;\">\n"
                                + "  \t<div>资金风险方法数：</div>\n"
                                + "    <div style=\"color:red;\">"
                                + fundsRiskFeatureBranchDOS.getFundRiskMethodAmount()
                                + "</div>\n"
                                + "  </div>\n"
                                + "  <div style=\"display:flex;align-items:center;\">\n"
                                + "  \t<div>资金风险测试结果：</div>\n"
                                + "    <div style=\"color:red;\">"
                                + riskStatus
                                + "</div>\n"
                                + "  </div>\n"
                                + "  <div style=\"display:flex;align-items:center;\">\n"
                                + "  \t<div>核对是否覆盖：</div>\n"
                                + "    <div style=\"color:red;\">"
                                + auditCover
                                + "</div>\n"
                                + "  </div>\n"
                                + "</div>")
                        .addAssertReportList(AssertReportList.newBuilder()
                                .setTitle("资金安全风险平台")//统一页面
                                .setUrl("https://kwaishop-risk.staging.kuaishou.com/kspay/kspayRiskFeatureQuery?featureId="
                                        + fundsRiskFeatureBranchDOS.getFeatureViewId()
                                ).build())
                        .setKdevNoticeUsers(String.join(",", qaMangelist))//通知用户列表，字符串，多值使用逗号或分号分隔
                        .setKdevNoticeContent("资金安全风险结果-" + riskStatus)
                        .build();
            }
        } else {
            // 通过就不发通知消息了
            return KspayRiskFeatureDetailSimpleV2.newBuilder()
                    .setFailReason("理论上不会失败")
                    .setViewUrlTitle("资金安全风险")
                    .setViewContent("</div>\n"
                            + "  </div>\n"
                            + "  <div style=\"display:flex;align-items:center;\">\n"
                            + "  \t<div>资金安全风险：</div>\n"
                            + "    <div style=\"color:green;\">"
                            + "通过"
                            + "</div>\n"
                            + "  </div>\n"
                            + "</div>")
                    .addAssertReportList(AssertReportList.newBuilder()
                            .setTitle("资金安全风险平台")
                            .setUrl("https://kwaishop-risk.staging.kuaishou.com/kspay/kspayRiskFeatureQuery?featureId="
                                    + fundsRiskFeatureBranchDOS.getFeatureViewId()
                            ).build())
                    .setKdevNoticeUsers(String.join(",", qaMangelist))//通知用户字符串，多值使用逗号或分号分隔
                    .setKdevNoticeContent("资金安全风险结果-" + riskStatus)
                    .build();
        }
    }

    public FundsRiskFeatureDO buildUpdateFeatureViewTestType(KspayRiskFeatureViewParam request) {
        KspayRiskUserMarkInfo extraData = request.getExtraData();

        return FundsRiskFeatureDO.builder()
                .testType(request.getTestType())
                .featureId(request.getFeatureId())
                .build();
    }

    @Override
    public FundsRiskFeatureDO buildNoRiskUpdateFeatureView(KspayRiskFeatureViewParam request) {
        KspayRiskUserMarkInfo extraData = request.getExtraData();
        Boolean isFindRisk = extraData.getIsFindRisk(); // 是否有风险未识别到
        Boolean isAddKatCase = extraData.getIsAddKatCase();
        Boolean isHasDrill = extraData.getIsHasDrill();
        Boolean isUpdateDrill = extraData.getIsUpdateDrill();
        int addKatNum = extraData.getAddKatNum();

        return FundsRiskFeatureDO.builder()
                // .id(Long.parseLong(request.getId()))
                .department(request.getDepartment())
                .businessDomain(request.getBusinessDomain())
                .featureId(request.getFeatureId())
                .featureName(request.getFeatureName())
                .teamId(request.getTeamId())
                .teamName(request.getTeamName())
                .riskCoe(request.getRiskCoe())
                .riskPassStatus(request.getRiskPassStatus())
                .teamWorker(request.getTeamWorker())
                .status(request.getStatus())
                .updater(request.getUpdater())
                .updateTime(System.currentTimeMillis())
                .extraData(generateExtraDataJsonForFindRisk(isFindRisk, isAddKatCase,
                        isHasDrill, isUpdateDrill, addKatNum)) // 根据请求参数的extra_data写入相关字段
                .build();
    }

    @Override
    public FundsRiskFeatureDO buildHasRiskUpdateFeatureView(KspayRiskFeatureViewParam request) {
        KspayRiskUserMarkInfo extraData = request.getExtraData();
        int isValidRiskFind = extraData.getIsValidRiskFind();  // 本次识别是否全部有效
        Boolean isUpdateDrill = extraData.getIsUpdateDrill();  // 是否有更新 || 新增对账
        Boolean isAddKatCase = extraData.getIsAddKatCase();  // 是否新增自动化
        Boolean isHasDrill = extraData.getIsHasDrill();  // 是否演练
        Boolean isHasReinforced = extraData.getIsHasReinforced();  // 风险是否加固
        Boolean isFindRisk = extraData.getIsFindRisk(); // 是否有风险未识别到
        int addKatNum = extraData.getAddKatNum();

        return FundsRiskFeatureDO.builder()
                // .id(Long.parseLong(request.getId()))
                .department(request.getDepartment())
                .businessDomain(request.getBusinessDomain())
                .featureId(request.getFeatureId())
                .featureName(request.getFeatureName())
                .teamId(request.getTeamId())
                .teamName(request.getTeamName())
                .riskCoe(request.getRiskCoe())
                .riskPassStatus(request.getRiskPassStatus())
                .teamWorker(request.getTeamWorker())
                .status(request.getStatus())
                .updater(request.getUpdater())
                .updateTime(System.currentTimeMillis())
                .extraData(generateExtraDataJsonForHasRisk(isValidRiskFind, isUpdateDrill, isAddKatCase,
                        isHasDrill, addKatNum, isHasReinforced, isFindRisk)) // 根据请求参数的extra_data写入其他字段
                .build();
    }

    @Override
    public FundsRiskFeatureBranchDO buildUpdateFeatureViewBranch(KspayRiskFeatureDetail request) {
        return FundsRiskFeatureBranchDO.builder()
                .id(Optional.of(Long.parseLong(request.getId())).orElse(0L))  // 兼容为空 || 无需这些参数的情况
                .featureViewId(Optional.of(request.getFeatureViewId()).orElse(0))
                .branchName(trimToEmpty(request.getBranchName()))
                .methodEntrance(trimToEmpty(request.getMethodEntrance()))
                .repoName(trimToEmpty(request.getRepoName()))
                .accuracyUrl(trimToEmpty(request.getAccuracyUrl()))
                .changeMethodAmount(Optional.of(request.getChangeMethodCount()).orElse(0))
                .fundRiskMethodAmount(Optional.of(request.getFundRiskMethodCount()).orElse(0))
                .riskStatus(Math.toIntExact(request.getRiskStatus()))
                .auditCover(Math.toIntExact(request.getAuditCover()))
                .testResult(trimToEmpty(request.getTestResult()))
                .updater(trimToEmpty(request.getUpdater()))
                .updateTime(System.currentTimeMillis())
                .auditCoverUpdater(trimToEmpty(request.getAuditCoverUpdater()))
                .riskStatusUpdater(trimToEmpty(request.getRiskStatusUpdater()))
                .build();
    }

    @Override
    public FundsRiskFeatureBranchDO buildUpdateFeatureViewBranchByFeatureId(KspayRiskFeatureDetail request,
                                                                            Integer toRiskStatus, Integer toAuditCover) {
        return FundsRiskFeatureBranchDO.builder()
                .featureViewId(request.getFeatureViewId())
                .riskStatus(toRiskStatus)
                .auditCover(toAuditCover)
                .updater(request.getUpdater())
                .updateTime(System.currentTimeMillis())
                .auditCoverUpdater(request.getUpdater())
                .riskStatusUpdater(request.getUpdater())
                .build();
    }

    @Override
    public FundsRiskFeatureBranchDO buildUpdateFeatureViewBranchById(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO,
                                                                     KspayRiskFeatureDetail request,
                                                                     Integer toRiskStatus, Integer toAuditCover) {
        return FundsRiskFeatureBranchDO.builder()
                .id(fundsRiskFeatureBranchDO.getId())
                .riskStatus(toRiskStatus)
                .auditCover(toAuditCover)
                .updater(request.getUpdater())
                .updateTime(System.currentTimeMillis())
                .auditCoverUpdater(request.getUpdater())
                .riskStatusUpdater(request.getUpdater())
                .build();
    }

    // 目前只有系统自动更新会用
    @Override
    public FundsRiskFeatureBranchDO buildAutoUpdateBranchById(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO,
                                                              String updater,
                                                              Integer toRiskStatus, Integer toAuditCover) {
        return FundsRiskFeatureBranchDO.builder()
                .id(fundsRiskFeatureBranchDO.getId())
                .riskStatus(toRiskStatus)
                .auditCover(toAuditCover)
                .updater(updater)
                .updateTime(System.currentTimeMillis())
                .auditCoverUpdater(updater)
                .riskStatusUpdater(updater)
                .build();
    }

    // 主表extra_data是否写入
    private String generateExtraDataJsonForFindRisk(Boolean isFindRisk, Boolean isAddKatCase, Boolean isHasDrill,
                                                    Boolean isUpdateDrill, int addKatNum) {
        Map<String, Object> extraDataMap = new HashMap<>();
        extraDataMap.put("is_find_risk", isFindRisk);
        extraDataMap.put("is_add_kat_case", isAddKatCase);
        extraDataMap.put("is_has_drill", isHasDrill);
        extraDataMap.put("is_update_drill", isUpdateDrill);
        extraDataMap.put("add_kat_num", addKatNum);
        return toJSON(extraDataMap);
    }

    private String generateExtraDataJsonForHasRisk(int isValidRiskFind, Boolean isUpdateDrill,
                                                   Boolean isAddKatCase, Boolean isHasDrill,
                                                   int addKatNum,
                                                   boolean isHasReinforced, boolean isFindRisk) {
        Map<String, Object> extraDataMap = new HashMap<>();
        extraDataMap.put("is_valid_risk_find", isValidRiskFind);
        extraDataMap.put("is_update_drill", isUpdateDrill);
        extraDataMap.put("is_add_kat_case", isAddKatCase);
        extraDataMap.put("is_has_drill", isHasDrill);
        extraDataMap.put("add_kat_num", addKatNum);
        extraDataMap.put("is_has_reinforced", isHasReinforced);
        extraDataMap.put("is_find_risk", isFindRisk);
        return toJSON(extraDataMap);
    }

    // 截取MethodEntrance方法名
    public static String getFullMethodName(String str) {
        List<String> fullMethodNameList = new ArrayList<>();
        List<String> simpleClassAndMethodNameList = new ArrayList<>();
        int index = -1;
        while ((index = str.indexOf("com.")) >= 0) {
            str = str.substring(index);
            int endIndex = str.indexOf("(");
            if (endIndex < 0) {
                break;
            }
            int endIndex2 = str.indexOf(")");
            if (endIndex2 < 0) {
                break;
            }
            if (endIndex2 < endIndex) {
                str = str.substring(endIndex2);
                continue;
            }

            String item = str.substring(0, endIndex);
            fullMethodNameList.add(item);
            int lastIndex = item.lastIndexOf(".");
            if (lastIndex >= 0 && lastIndex < item.length()) {
                simpleClassAndMethodNameList.add(item.substring(lastIndex + 1));
            }
            str = str.substring(endIndex);
        }

//        System.out.println(fullMethodNameList);
//        System.out.println(simpleClassAndMethodNameList);
        HashSet<String> simpleNotRepeatSet = new HashSet<>(simpleClassAndMethodNameList);
//        return StringUtils.strip(String.join(",", simpleNotRepeatSet), "[]");
//        return StringUtils.strip(simpleNotRepeatSet.toString(), "[]").replaceAll(",", ",\n");
        return String.join(",\n", simpleNotRepeatSet);
    }

    // 度量分数计算
    public static double calculateAverageScore(List<KspayRiskFeatureDetail> kspayRiskFeatureDetails) {
        List<String> scoreList = kspayRiskFeatureDetails.stream()
                .filter(d -> StringUtils.isNotBlank(d.getScore()) && !d.getScore().equals("--") && !d.getScore().equals("0"))
                .map(KspayRiskFeatureDetail::getScore)
                .collect(Collectors.toList());

        double allScore = 0.0;
        if (scoreList.size() != 0) {
            for (String score : scoreList) {
                String replaceScore = score.replace("分", "");
                allScore += Double.parseDouble(replaceScore);
            }
            allScore = allScore / scoreList.size();
        }
        allScore = new BigDecimal(allScore).setScale(1, RoundingMode.HALF_UP).doubleValue();
        return allScore;
    }
}
