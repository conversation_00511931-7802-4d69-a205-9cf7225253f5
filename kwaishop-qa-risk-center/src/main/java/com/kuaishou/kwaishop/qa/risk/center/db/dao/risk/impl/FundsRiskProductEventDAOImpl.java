package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import java.util.Collection;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.KspayPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.KspayBaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.FundsRiskProductEventDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsRiskProductEventDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.FundsRiskProductEventMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.FundsRiskProductEventQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;

/**
 * <AUTHOR>
 * Created on 2022-12-26
 */
@Repository
public class FundsRiskProductEventDAOImpl extends KspayBaseDAOImpl<FundsRiskProductEventDO, FundsRiskProductEventQueryCondition>
        implements FundsRiskProductEventDAO {
    @Autowired
    private FundsRiskProductEventMapper fundsRiskProductEventMapper;

    @Override
    protected void fillLikeQueryCondition(FundsRiskProductEventQueryCondition condition,
            QueryWrapper<FundsRiskProductEventDO> queryWrapper) {
    }

    @Override
    protected void fillQueryCondition(FundsRiskProductEventQueryCondition condition, QueryWrapper<FundsRiskProductEventDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getRiskProduct())) {
            queryWrapper.and(q -> q.eq("risk_product", condition.getRiskProduct()));
        }
        if (StringUtils.isNotBlank(condition.getRiskEvent())) {
            queryWrapper.and(q -> q.eq("risk_event", condition.getRiskEvent()));
        }
        if (StringUtils.isNotBlank(condition.getBusinessDomain())) {
            queryWrapper.and(q -> q.eq("business_domain", condition.getBusinessDomain()));
        }
        if (StringUtils.isNotBlank(condition.getDepartment())) {
            queryWrapper.and(q -> q.eq("department", condition.getDepartment()));
        }

    }

    @Override
    protected BaseMapper<FundsRiskProductEventDO> getMapper() {
        return fundsRiskProductEventMapper;
    }

    @Override
    public List<FundsRiskProductEventDO> queryByCenterIds(Collection<Long> centerIds, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsRiskProductEventDO> queryByTeamIds(Long centerId, Collection<Long> teamIds,
            CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsRiskProductEventDO> queryByCenterId(Long centerId, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsRiskProductEventDO> queryByTeamId(Long centerId, Long teamId, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsRiskProductEventDO> queryByCenterIdsTeamIds(Collection<Long> centerIds, Collection<Long> teamIds,
            CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public KspayPageBO<FundsRiskProductEventDO> queryPageRiskDetailList(
            FundsRiskProductEventQueryCondition fundsRiskProductEventQueryCondition) {
        return queryPageList(fundsRiskProductEventQueryCondition);
    }

    @Override
    public List<FundsRiskProductEventDO> queryFundsRiskProductEvent(
            FundsRiskProductEventQueryCondition fundsRiskProductEventQueryCondition) {
        return queryList(fundsRiskProductEventQueryCondition);
    }

    @Override
    public List<FundsRiskProductEventDO> getAllList() {
        return fundsRiskProductEventMapper.getAllList();
    }
}
