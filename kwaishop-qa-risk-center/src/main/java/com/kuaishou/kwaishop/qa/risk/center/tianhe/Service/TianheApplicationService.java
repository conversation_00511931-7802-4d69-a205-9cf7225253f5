package com.kuaishou.kwaishop.qa.risk.center.tianhe.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AddApplicationRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AddApplicationResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AddSampleSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteApplicationRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteApplicationResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListApplicationData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListApplicationRecord;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListApplicationRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListApplicationResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListSampleSetRecord;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateApplicationRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateApplicationResponse;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Application.AppJoinSetDO;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Application.TianheApplicationDO;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.SampleSet.TianheSampleSetDO;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Enums.TianheErrCodeEnum;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.Application.TianheApplicationMapper;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.SampleSet.TianheSampleSetMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-19
 */
@Service
@Slf4j
public class TianheApplicationService {

    private final TianheApplicationMapper tianheApplicationMapper;

    private final TianheSampleSetMapper tianheSampleSetMapper;

    private final TianheSampleSetService tianheSampleSetService;

    @Autowired
    public TianheApplicationService(TianheApplicationMapper tianheApplicationMapper, TianheSampleSetMapper tianheSampleSetMapper,
                                    TianheSampleSetService tianheSampleSetService) {
        this.tianheApplicationMapper = tianheApplicationMapper;
        this.tianheSampleSetMapper = tianheSampleSetMapper;
        this.tianheSampleSetService = tianheSampleSetService;
    }

    public AddApplicationResponse addApplication(AddApplicationRequest request) {
        return handleAddApplicationResponse(request);
    }

    private AddApplicationResponse handleAddApplicationResponse(AddApplicationRequest request) {
        log.info("AddApplicationRequest {}", ObjectMapperUtils.toJSON(request));
        AddApplicationResponse.Builder addApplicationResponse = AddApplicationResponse.newBuilder();
        try {
            // 校验入参是否完全
            if (request.getName().equals("") || request.getCreator().equals("")
                    || request.getAppKey().equals("") || request.getOperators(0).equals("")) {
                return addApplicationResponse
                        .setResult(TianheErrCodeEnum.PARAM_INVALID.getCode())
                        .setSuccess(false)
                        .setErrorMsg("参数不合法")
                        .build();
            }
            long timestamp = System.currentTimeMillis();

            TianheApplicationDO data = new TianheApplicationDO();
            data.setAppName(request.getName());
            data.setCreator(request.getCreator());
            data.setOperators(ObjectMapperUtils.toJSON(request.getOperatorsList()));
            data.setAppKey(request.getAppKey());
            data.setCreateTime(timestamp);
            data.setUpdateTime(timestamp);
            data.setPlatformSource("天河"); //先塞个天河
            data.setDeleted(0);

            int insertCount = tianheApplicationMapper.insertApplication(data);
            boolean flag = insertCount > 0;
            int result = flag ? TianheErrCodeEnum.SUCCESS.getCode() : TianheErrCodeEnum.INSERT_FAIL.getCode();
            String errMsg = flag ? "success" : "创建应用失败";

            // 生成默认用例集，默认不巡检
            long appId = data.getId();
            tianheSampleSetService.addSampleSetResponse(AddSampleSetRequest.newBuilder()
                    .setName("主干回归用例集").setCreator("System").setAppId((int) appId).build());

            return addApplicationResponse
                    .setResult(result)
                    .setSuccess(flag)
                    .setErrorMsg(errMsg)
                    .build();

        } catch (Exception e) {
            log.error("AddApplication error ", e);
            return AddApplicationResponse.newBuilder()
                    .setResult(TianheErrCodeEnum.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("新增应用异常")
                    .build();
        }
    }

    public ListApplicationResponse listApplication(ListApplicationRequest request) {
        log.info("ListApplication request {}", ObjectMapperUtils.toJSON(request));
        try {
            ListApplicationResponse.Builder listApplicationResponse = ListApplicationResponse.newBuilder();

            List<AppJoinSetDO> allData = tianheApplicationMapper.queryAllApplication();

            ListApplicationData listApplicationData = buildListApplicationData(allData, request.getOperator());

            boolean flag = listApplicationData.getTotal() > 0;
            int result = flag ? TianheErrCodeEnum.SUCCESS.getCode() : TianheErrCodeEnum.QUERY_FAIL.getCode();
            String errMsg = flag ? "success" : "获取应用列表失败";

            return listApplicationResponse
                    .setResult(result)
                    .setErrorMsg(errMsg)
                    .setSuccess(flag)
                    .setData(listApplicationData)
                    .build();
        } catch (Exception e) {
            log.error("listApplication error ", e);
            return ListApplicationResponse.newBuilder()
                    .setResult(TianheErrCodeEnum.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("获取应用列表异常")
                    .build();
        }

    }

    private ListApplicationData buildListApplicationData(List<AppJoinSetDO> appJoinSetList, String operator) {
        // 用map存储每个应用对应的用例集
        Map<Long, ListApplicationRecord.Builder> appMap = new HashMap<>();

        for (AppJoinSetDO appJoinSet : appJoinSetList) {
            long appId = appJoinSet.getAppId();
            List<String> operators = new ArrayList<>(List.of(ObjectMapperUtils.fromJSON(appJoinSet.getOperators(), String[].class)));
            boolean permission = operators.contains(operator) || operator.equals(appJoinSet.getCreator());

            operators.add(appJoinSet.getCreator());
            Set<String> operatorSet = new HashSet<>(operators);
            String operatorStr = String.join(",", operatorSet);

            // 获取或创建应用记录
            ListApplicationRecord.Builder appRecordBuilder = appMap.computeIfAbsent(appId, k -> ListApplicationRecord.newBuilder())
                    .setAppId((int) appId)
                    .setName(appJoinSet.getAppName())
                    .setHasPermission(permission)
                    .setAppKey(appJoinSet.getAppKey())
                    .setOperators(operatorStr);

            // 添加用例集数据
            appRecordBuilder.addSetList(ListSampleSetRecord.newBuilder()
                    .setSetId(Math.toIntExact(appJoinSet.getSetId()))
                    .setName(appJoinSet.getSetName())
                    .build());
        }

        // 构建最终的 ListApplicationData
        List<ListApplicationRecord> applicationRecordList = appMap.values().stream()
                .map(ListApplicationRecord.Builder::build)
                .collect(Collectors.toList());

        return ListApplicationData.newBuilder()
                .setTotal(applicationRecordList.size())
                .addAllAppList(applicationRecordList)
                .build();
    }

    public UpdateApplicationResponse updateApplication(UpdateApplicationRequest request) {
        return handleUpdateApplication(request);
    }

    private UpdateApplicationResponse handleUpdateApplication(UpdateApplicationRequest request) {
        log.info("UpdateApplication request {}", ObjectMapperUtils.toJSON(request));
        UpdateApplicationResponse.Builder updateApplicationResponse = UpdateApplicationResponse.newBuilder();
        try {
            // 校验入参
            if (request.getName().equals("") || request.getId() <= 0 || request.getAppKey().equals("")
                    || request.getOperators(0).equals("") || request.getOperator().equals("")) {
                return updateApplicationResponse
                        .setResult(TianheErrCodeEnum.PARAM_INVALID.getCode())
                        .setSuccess(false)
                        .setErrorMsg("参数不合法")
                        .build();
            }
            TianheApplicationDO application = tianheApplicationMapper.selectById(request.getId());
            // 校验操作权限
            List<String> list = List.of(ObjectMapperUtils.fromJSON(application.getOperators(), String[].class));
            if (!list.contains(request.getOperator()) && !request.getOperator().equals(application.getCreator())) {
                return updateApplicationResponse
                        .setResult(TianheErrCodeEnum.NO_PERMISSION.getCode())
                        .setSuccess(false)
                        .setErrorMsg("没有操作权限")
                        .build();
            }
            // 校验应用是否存在
            if (application.getDeleted().equals(1)) {
                return updateApplicationResponse
                        .setResult(TianheErrCodeEnum.RECORD_DELETED.getCode())
                        .setSuccess(false)
                        .setErrorMsg("应用已删除")
                        .build();
            }
            // 更新应用
            long timestamp = System.currentTimeMillis();
            TianheApplicationDO data = new TianheApplicationDO();
            UpdateWrapper<TianheApplicationDO> updateWrapper = new UpdateWrapper<>();

            updateWrapper.eq("id", request.getId());
            data.setAppName(request.getName());
            data.setAppKey(request.getAppKey());
            data.setOperators(ObjectMapperUtils.toJSON(request.getOperatorsList()));
            data.setUpdateTime(timestamp);

            int updateCount = tianheApplicationMapper.update(data, updateWrapper);
            boolean flag = updateCount > 0;
            int result = flag ? TianheErrCodeEnum.SUCCESS.getCode() : TianheErrCodeEnum.UPDATE_FAIL.getCode();
            String errMsg = flag ? "success" : "更新应用失败";
            return updateApplicationResponse
                    .setResult(result)
                    .setSuccess(flag)
                    .setErrorMsg(errMsg)
                    .build();

        } catch (Exception e) {
            log.error("UpdateApplication error ", e);
            return updateApplicationResponse
                    .setResult(TianheErrCodeEnum.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("更新应用异常")
                    .build();
        }
    }

    public DeleteApplicationResponse deleteApplication(DeleteApplicationRequest request) {
        return handleDeleteApplication(request);
    }

    private DeleteApplicationResponse handleDeleteApplication(DeleteApplicationRequest request) {
        log.info("DeleteApplication request {}", ObjectMapperUtils.toJSON(request));
        DeleteApplicationResponse.Builder deleteApplicationResponse = DeleteApplicationResponse.newBuilder();
        try {
            // 校验入参
            if (request.getId() <= 0 || request.getOperator().equals("")) {
                return deleteApplicationResponse
                        .setResult(TianheErrCodeEnum.PARAM_INVALID.getCode())
                        .setSuccess(false)
                        .setErrorMsg("参数不合法")
                        .build();
            }
            TianheApplicationDO application = tianheApplicationMapper.selectById(request.getId());
            // 校验操作权限
            List<String> list = List.of(ObjectMapperUtils.fromJSON(application.getOperators(), String[].class));
            if (!list.contains(request.getOperator()) && !request.getOperator().equals(application.getCreator())) {
                return deleteApplicationResponse
                        .setResult(TianheErrCodeEnum.NO_PERMISSION.getCode())
                        .setSuccess(false)
                        .setErrorMsg("没有操作权限")
                        .build();
            }
            // 应用是否存在
            if (application.getDeleted().equals(1)) {
                return deleteApplicationResponse
                        .setResult(TianheErrCodeEnum.RECORD_DELETED.getCode())
                        .setSuccess(false)
                        .setErrorMsg("应用已删除")
                        .build();
            }
            // 更新应用
            long timestamp = System.currentTimeMillis();
            TianheApplicationDO data = new TianheApplicationDO();
            UpdateWrapper<TianheApplicationDO> updateWrapper = new UpdateWrapper<>();

            // 删除应用下所有用例集
            TianheSampleSetDO sampleSetDO = new TianheSampleSetDO();
            UpdateWrapper<TianheSampleSetDO> setDOUpdateWrapper = new UpdateWrapper<>();
            sampleSetDO.setDeleted(1);
            sampleSetDO.setSamples("");
            setDOUpdateWrapper.eq("app_id", request.getId());
            tianheSampleSetMapper.update(sampleSetDO, setDOUpdateWrapper);

            updateWrapper.eq("id", request.getId());
            data.setDeleted(1);
            data.setUpdateTime(timestamp);

            int updateCount = tianheApplicationMapper.update(data, updateWrapper);
            boolean flag = updateCount > 0;
            int result = flag ? TianheErrCodeEnum.SUCCESS.getCode() : TianheErrCodeEnum.UPDATE_FAIL.getCode();
            String errMsg = flag ? "success" : "删除应用失败";
            return deleteApplicationResponse
                    .setResult(result)
                    .setSuccess(flag)
                    .setErrorMsg(errMsg)
                    .build();
        } catch (Exception e) {
            log.error("DeleteApplication error ", e);
            return deleteApplicationResponse
                    .setResult(TianheErrCodeEnum.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("删除应用异常")
                    .build();
        }
    }

}
