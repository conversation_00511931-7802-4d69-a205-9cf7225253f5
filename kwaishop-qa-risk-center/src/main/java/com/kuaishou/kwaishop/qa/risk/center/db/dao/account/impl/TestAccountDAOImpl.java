package com.kuaishou.kwaishop.qa.risk.center.db.dao.account.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.TestAccountDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TestAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.account.TestAccountMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.account.TestAccountQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TestAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserAccountTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserDataTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserLoginTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserStatusEnum;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/15 15:49
 * @注释
 */
@Repository
public class TestAccountDAOImpl extends BaseDAOImpl<TestAccountDO, TestAccountQueryCondition>
        implements TestAccountDAO {

    @Autowired
    private TestAccountMapper testAccountMapper;

    @Override
    protected void fillQueryCondition(TestAccountQueryCondition condition, QueryWrapper<TestAccountDO> queryWrapper) {
        if (condition.getTeamId() != null && condition.getTeamId() > 0L) {
            queryWrapper.and(q -> q.eq("team_id", condition.getTeamId()));
        }
        if (condition.getUserId() != null && condition.getUserId() > 0L) {
            queryWrapper.and(q -> q.eq("kwai_id", condition.getUserId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getUserIds())) {
            queryWrapper.and(q -> q.in("kwai_id", condition.getUserIds()));
        }
        if (UserAccountTypeEnum.of(condition.getAccountType()) != null) {
            queryWrapper.and(q -> q.eq("account_type", condition.getAccountType()));
        }
        if (condition.getAccount() != null && !condition.getAccount().isEmpty()) {
            queryWrapper.and(q -> q.eq("account", condition.getAccount()));
        }
        if (CollectionUtils.isNotEmpty(condition.getAccountTypes())) {
            queryWrapper.and(q -> q.in("account_type", condition.getAccountTypes()));
        }
        if (UserDataTypeEnum.of(condition.getDataType()) != null) {
            queryWrapper.and(q -> q.eq("data_type", condition.getDataType()));
        }
        if (CollectionUtils.isNotEmpty(condition.getDataTypes())) {
            queryWrapper.and(q -> q.in("data_type", condition.getDataTypes()));
        }
        if (UserLoginTypeEnum.of(condition.getLoginType()) != null && condition.getLoginType() > 0) {
            queryWrapper.and(q -> q.eq("login_type", condition.getLoginType()));
        }
        if (CollectionUtils.isNotEmpty(condition.getLoginTypes())) {
            queryWrapper.and(q -> q.in("login_type", condition.getLoginTypes()));
        }
        if (UserStatusEnum.of(condition.getStatus()) != null) {
            queryWrapper.and(q -> q.eq("status", condition.getStatus()));
        }
        if (CollectionUtils.isNotEmpty(condition.getStatuses())) {
            queryWrapper.and(q -> q.in("status", condition.getStatuses()));
        }
        if (!condition.getStoreType().isEmpty()) {
            queryWrapper.and(q -> q.in("store_type", condition.getStoreType()));
        }
    }

    @Override
    protected BaseMapper<TestAccountDO> getMapper() {
        return testAccountMapper;
    }

    @Override
    public PageBO<TestAccountDO> queryPageList(TestAccountBO testAccountBO, List<Long> userIds) {
        TestAccountQueryCondition condition;
        if (userIds != null && !userIds.isEmpty()) {
            condition = TestAccountQueryCondition.builder()
                    .userIds(userIds)
                    .loginType(testAccountBO.getLoginType())
                    .storeType(testAccountBO.getStoreType() == null ? "" : testAccountBO.getStoreType())
                    .pageNo(testAccountBO.getPageNo())
                    .pageSize(testAccountBO.getPageSize())
                    .build();
        } else {
            condition = TestAccountQueryCondition.builder()
                    .loginType(testAccountBO.getLoginType())
                    .id(testAccountBO.getId())
                    .userId(testAccountBO.getUserId())
                    .pageNo(testAccountBO.getPageNo())
                    .pageSize(testAccountBO.getPageSize())
                    .account(testAccountBO.getAccount())
                    .teamId(testAccountBO.getTeamId())
                    .storeType(testAccountBO.getStoreType() == null ? "" : testAccountBO.getStoreType())
                    .build();
        }
        PageBO<TestAccountDO> pageBO = queryPageList(condition);
        pageBO.setData(pageBO.getData().stream().map(item -> {
            item.setAccount(testAccountBO.getAccount());
            item.setPassword(testAccountBO.getPassword());
            return item;
        }).collect(Collectors.toList()));
        return pageBO;

    }


    @Override
    public List<TestAccountDO> queryList(UserAccountBO userAccountBO) {
        return null;
    }

    @Override
    public List<TestAccountDO> queryByUid(Long uid) {
        return testAccountMapper.queryUserAccountByUserId(uid);
    }

    @Override
    public TestAccountDO queryByBid(Long bid) {
        return null;
    }


    @Override
    public List<TestAccountDO> queryUserAccountByUserId(Long userId) {
        return null;
    }

    @Override
    public List<TestAccountDO> queryUserAccountByIds(List<Long> ids) {
        if (ids.isEmpty()) {
            return new ArrayList<>();
        }
        List<TestAccountDO> list = testAccountMapper.getByInterfaceListByIds(ids);
        return list;
    }

    @Override
    public void deleteTestAccount(Long uid) {
        testAccountMapper.deleteAccount(uid);
    }


}
