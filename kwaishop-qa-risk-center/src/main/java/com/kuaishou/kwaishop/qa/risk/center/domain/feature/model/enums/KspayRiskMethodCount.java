package com.kuaishou.kwaishop.qa.risk.center.domain.feature.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

public enum KspayRiskMethodCount {
    NONE(null, "无资损风险方法"),
    HAVING(1, "有资损风险方法"),

    UNKNOWN(2, "未知状态"),
    ;

    private final Integer code;

    private final String desc;

    KspayRiskMethodCount(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static KspayRiskMethodCount of(Integer code) {
        for (KspayRiskMethodCount kspayRiskMethodCount: values()) {
            if (kspayRiskMethodCount.getCode().equals(code) && kspayRiskMethodCount.getCode() > 0) {
                return kspayRiskMethodCount;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo() {
        List<EnumInfo> res = new ArrayList<>();
        for (KspayRiskMethodCount typeEnum: values()) {
            if (typeEnum.getCode() > 0) {
                EnumInfo enumInfo = EnumInfo.newBuilder()
                        .setValue(typeEnum.getCode())
                        .setDesc(typeEnum.getDesc())
                        .build();
                res.add(enumInfo);
            }
        }
        return res;
    }
}
