package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.ConfigCheckResultRecordDo;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.ConfigRulesCheckResultDo;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.ConfigCheckResulRecordQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.ConfigRulesCheckResulQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.KspayConfigRiskBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.ConfigCheckResultRecordData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.ConfigRiskDetail;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.ConfigRulesCheckResultData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KconfData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KrpcKspayConfigRiskServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayConfigRiskQueryRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayConfigRiskQueryResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayKconfKey;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayKconfKeyRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayKconfKeyResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayKconfListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayKconfListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayQueryConfigCheckResultRecordRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayQueryConfigCheckResultRecordResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayQueryConfigRulesCheckResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayQueryConfigRulesCheckResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.QueryKconfConfigRulesCheckResponse;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class KspayConfigRiskImpl extends KrpcKspayConfigRiskServiceGrpc.KspayConfigRiskServiceImplBaseV2 {
    @Autowired
    private KspayConfigRiskBizService kspayConfigRiskBizService;

    @Override
    public KspayConfigRiskQueryResponse kspayConfigRiskQuery(KspayConfigRiskQueryRequest request) {
        log.info("[KspayConfigRiskImpl] kspayConfigRuleQuery request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            List<ConfigRiskDetail> configRiskDetails = kspayConfigRiskBizService.getConfigRule(request);
            return KspayConfigRiskQueryResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(configRiskDetails)
                    .build();
        } catch (BizException e) {
            log.error("[KspayConfigRiskImpl] kspayConfigRuleQuery bizError, exception: ", e);
            return KspayConfigRiskQueryResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayConfigRiskImpl] kspayConfigRuleQuery error, exception: ", e);
            return KspayConfigRiskQueryResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayKconfKeyResponse kspayKconfValueQuery(KspayKconfKeyRequest request) {
        log.info("[KspayConfigRiskImpl] kspayConfigRuleQuery request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            KconfData kconfData = kspayConfigRiskBizService.getKconfValue(request);
            if (kconfData != null) {
                return KspayKconfKeyResponse.newBuilder()
                        .setResult(BaseResultCode.SUCCESS_VALUE)
                        .setData(kconfData)
                        .build();
            } else {
                return KspayKconfKeyResponse.newBuilder()
                        .setResult(BaseResultCode.SUCCESS_VALUE)
                        .build();
            }
        } catch (BizException e) {
            log.error("[KspayConfigRiskImpl] KspayKconfValueQuery bizError, exception: ", e);
            return KspayKconfKeyResponse.newBuilder()
                    .setResult(e.getCode())
                    .setMessage(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayConfigRiskImpl] KspayKconfValueQuery error, exception: ", e);
            return KspayKconfKeyResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setMessage(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayKconfListResponse kspayKconfListQuery(KspayKconfListRequest request) {
        log.info("[KspayConfigRiskImpl] kspayKconfListQuery request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            List<String> kconfData = kspayConfigRiskBizService.getKconfList(request);
            if (kconfData != null) {
                return KspayKconfListResponse.newBuilder()
                        .setResult(BaseResultCode.SUCCESS_VALUE)
                        .setMessage(ErrorCode.BasicErrorCode.SUCCESS.getMessage())
                        .addAllData(kconfData)
                        .build();
            } else {
                return KspayKconfListResponse.newBuilder()
                        .setResult(BaseResultCode.SUCCESS_VALUE)
                        .build();
            }
        } catch (BizException e) {
            log.error("[KspayConfigRiskImpl] KspayKconfValueQuery bizError, exception: ", e);
            return KspayKconfListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setMessage(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayConfigRiskImpl] KspayKconfValueQuery error, exception: ", e);
            return KspayKconfListResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setMessage(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayQueryConfigCheckResultRecordResponse kspayQueryConfigCheckResultRecord(
            KspayQueryConfigCheckResultRecordRequest request) {
        log.info("[KspayConfigRiskImpl] kspayQueryConfigCheckResultRecord request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            ConfigCheckResulRecordQueryCondition configCheckResulRecordQueryCondition = new ConfigCheckResulRecordQueryCondition();
            configCheckResulRecordQueryCondition.setId(request.getId());
            configCheckResulRecordQueryCondition.setReportId(request.getReportId());
            List<ConfigCheckResultRecordDo> configCheckResultRecords =
                    kspayConfigRiskBizService.queryConfigCheckResultRecord(configCheckResulRecordQueryCondition);
            if (configCheckResultRecords != null) {
                List<ConfigCheckResultRecordData> datas = new ArrayList<>();
                for (ConfigCheckResultRecordDo configCheckResultRecordDo:configCheckResultRecords
                ) {
                    ConfigCheckResultRecordData data = ConfigCheckResultRecordData.newBuilder()
                            .setId(configCheckResultRecordDo.getId())
                            .setConfigDataType(configCheckResultRecordDo.getConfigDataType())
                            .setConfigInfo(configCheckResultRecordDo.getConfigInfo())
                            .setScanRuleName(configCheckResultRecordDo.getScanRuleName())
                            .setRemarks(configCheckResultRecordDo.getRemarks())
                            .setReportId(configCheckResultRecordDo.getReportId())
                            .setUpdateInterval(configCheckResultRecordDo.getUpdateInterval())
                            .setErrorDataInfo(configCheckResultRecordDo.getErrorDataInfo())
                            .setGrayStatus(configCheckResultRecordDo.getGrayStatus())
                            .setIsIssue(configCheckResultRecordDo.getIsIssue())
                            .build();
                    datas.add(data);
                }
                return KspayQueryConfigCheckResultRecordResponse.newBuilder()
                        .setResult(BaseResultCode.SUCCESS_VALUE)
                        .setMessage(ErrorCode.BasicErrorCode.SUCCESS.getMessage())
                        .addAllData(datas)
                        .build();
            } else {
                return KspayQueryConfigCheckResultRecordResponse.newBuilder()
                        .setResult(BaseResultCode.SUCCESS_VALUE)
                        .build();
            }
        } catch (BizException e) {
            log.error("[KspayConfigRiskImpl] kspayQueryConfigCheckResultRecord bizError, exception: ", e);
            return KspayQueryConfigCheckResultRecordResponse.newBuilder()
                    .setResult(e.getCode())
                    .setMessage(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayConfigRiskImpl] kspayQueryConfigCheckResultRecord error, exception: ", e);
            return KspayQueryConfigCheckResultRecordResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setMessage(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayQueryConfigRulesCheckResultResponse kspayQueryConfigRulesCheckResult(
            KspayQueryConfigRulesCheckResultRequest request) {
        log.info("[KspayConfigRiskImpl] KspayQueryConfigRulesCheckResult request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            ConfigRulesCheckResulQueryCondition configRulesCheckResulQueryCondition = new ConfigRulesCheckResulQueryCondition();
            configRulesCheckResulQueryCondition.setReportName(request.getReportName());
            if (request.getStartTime() == 0) {
                configRulesCheckResulQueryCondition.setStartTime(0L);
            }
            if (request.getEndTime() == 0) {
                configRulesCheckResulQueryCondition.setStartTime(System.currentTimeMillis());
            }
            if (request.getResult() == -1) {
                configRulesCheckResulQueryCondition.setResult(Arrays.asList(2, 1));
            } else {
                configRulesCheckResulQueryCondition.setResult(Arrays.asList(request.getResult()));
            }

            if (request.getType() == -1) {
                configRulesCheckResulQueryCondition.setType(Arrays.asList(0, 1));
            } else {
                configRulesCheckResulQueryCondition.setType(Arrays.asList(request.getType()));
            }

            configRulesCheckResulQueryCondition.setStartTime(request.getStartTime());
            configRulesCheckResulQueryCondition.setEndTime(request.getEndTime());

            List<ConfigRulesCheckResultDo> configRulesCheckResults =
                    kspayConfigRiskBizService.queryConfigRulesCheckResult(configRulesCheckResulQueryCondition);
            if (configRulesCheckResults != null) {
                List<ConfigRulesCheckResultData> datas = new ArrayList<>();
                for (ConfigRulesCheckResultDo configRulesCheckResultDo:configRulesCheckResults
                ) {

                    ConfigRulesCheckResultData data = ConfigRulesCheckResultData.newBuilder()
                            .setId(configRulesCheckResultDo.getId())
                            .setReportName(configRulesCheckResultDo.getReportName())
                            .setResult(configRulesCheckResultDo.getResult())
                            .setScanRule(configRulesCheckResultDo.getScanRule())
                            .setFailureContent(configRulesCheckResultDo.getFailureContent())
                            .setCreateTime(configRulesCheckResultDo.getCreateTime())
                            .setType(configRulesCheckResultDo.getType())
                            .setRuleId(configRulesCheckResultDo.getRuleId() == null ? "" : String.valueOf(
                                    configRulesCheckResultDo.getRuleId()))
                            .build();
                    datas.add(data);
                }
                return KspayQueryConfigRulesCheckResultResponse.newBuilder()
                        .setResult(BaseResultCode.SUCCESS_VALUE)
                        .setMessage(ErrorCode.BasicErrorCode.SUCCESS.getMessage())
                        .addAllData(datas)
                        .build();
            } else {
                return KspayQueryConfigRulesCheckResultResponse.newBuilder()
                        .setResult(BaseResultCode.SUCCESS_VALUE)
                        .build();
            }
        } catch (BizException e) {
            log.error("[KspayConfigRiskImpl] KspayQueryConfigRulesCheckResult bizError, exception: ", e);
            return KspayQueryConfigRulesCheckResultResponse.newBuilder()
                    .setResult(e.getCode())
                    .setMessage(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayConfigRiskImpl] KspayQueryConfigRulesCheckResult error, exception: ", e);
            return KspayQueryConfigRulesCheckResultResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setMessage(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    @Override
    public QueryKconfConfigRulesCheckResponse queryKconfConfigRulesCheckResult(KspayKconfKey request) {
        ConfigRulesCheckResultDo resultDo =  kspayConfigRiskBizService.queryKconfConfigRulesCheckResult(request.getKey());
        if (resultDo != null) {
            ConfigRulesCheckResultData data = ConfigRulesCheckResultData.newBuilder()
                    .setId(resultDo.getId())
                    .setReportName(resultDo.getReportName())
                    .setResult(resultDo.getResult())
                    .setScanRule(resultDo.getScanRule())
                    .setFailureContent(resultDo.getFailureContent())
                    .setCreateTime(resultDo.getCreateTime())
                    .setType(resultDo.getType())
                    .setRuleId(resultDo.getRuleId() == null ? "" : String.valueOf(
                            resultDo.getRuleId()))
                    .build();

            return QueryKconfConfigRulesCheckResponse.newBuilder()
                    .addData(data)
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setMessage(ErrorCode.BasicErrorCode.SUCCESS.getMessage())
                    .build();
        } else {
            return QueryKconfConfigRulesCheckResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        }


    }




}
