package com.kuaishou.kwaishop.qa.risk.center.db.bo;


import com.google.gson.annotations.SerializedName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;



@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskFieldBO {

    @SerializedName(value = "table")
    private String tableName;

    @SerializedName(value = "field")
    private String riskElements;
}
