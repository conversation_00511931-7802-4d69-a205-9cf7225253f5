package com.kuaishou.kwaishop.qa.risk.center.db.dao.common.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.common.CommonLogRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.common.CommonLogRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.common.CommonLogRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.common.CommonLogRecordQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.common.model.bo.LogRecordBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.common.model.enums.LogTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-24
 */
@Repository
public class CommonLogRecordDAOImpl extends BaseDAOImpl<CommonLogRecordDO, CommonLogRecordQueryCondition> implements
        CommonLogRecordDAO {

    @Autowired
    private CommonLogRecordMapper commonLogRecordMapper;

    @Override
    protected void fillQueryCondition(CommonLogRecordQueryCondition condition,
            QueryWrapper<CommonLogRecordDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getDt())) {
            queryWrapper.and(q -> q.eq("dt", condition.getDt()));
        }
        if (StringUtils.isNotBlank(condition.getDtStart())) {
            queryWrapper.and(q -> q.ge("dt", condition.getDt()));
        }
        if (StringUtils.isNotBlank(condition.getDtEnd())) {
            queryWrapper.and(q -> q.le("dt", condition.getDt()));
        }
        if (LogTypeEnum.of(condition.getLogType()) != null) {
            queryWrapper.and(q -> q.eq("log_type", condition.getLogType()));
        }
        if (StringUtils.isNotBlank(condition.getName())) {
            queryWrapper.and(q -> q.eq("name", condition.getName()));
        }
    }

    @Override
    protected BaseMapper<CommonLogRecordDO> getMapper() {
        return commonLogRecordMapper;
    }

    @Override
    public List<CommonLogRecordDO> queryRecordList(LogRecordBO logRecordBO) {
        CommonLogRecordQueryCondition queryCondition = CommonLogRecordQueryCondition.builder()
                .dt(logRecordBO.getDt())
                .dtStart(logRecordBO.getDtStart())
                .dtEnd(logRecordBO.getDtEnd())
                .logType(logRecordBO.getLogType())
                .name(logRecordBO.getName())
                .build();
        return queryList(queryCondition);
    }

    @Override
    public List<CommonLogRecordDO> queryLogRecordListByCursor(Integer logType, String dt, Long cursor, Integer limit) {
        return commonLogRecordMapper.queryRecordByCursor(logType, dt, cursor, limit);
    }
}
