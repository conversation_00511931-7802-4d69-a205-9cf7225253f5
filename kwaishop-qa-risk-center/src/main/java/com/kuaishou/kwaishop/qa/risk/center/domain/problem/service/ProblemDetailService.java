package com.kuaishou.kwaishop.qa.risk.center.domain.problem.service;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.problem.ProblemDetailDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.bo.ProblemDetailBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-06
 */
public interface ProblemDetailService {

    ProblemDetailDO createProblem(ProblemDetailBO problemDetailBO);

    void updateProblem(ProblemDetailBO problemDetailBO);

    void deleteProblem(Long id, String operator);

    List<ProblemDetailDO> queryProblemList(ProblemDetailBO problemDetailBO);

    PageBO<ProblemDetailDO> queryPageProblemList(ProblemDetailBO problemDetailBO);
}
