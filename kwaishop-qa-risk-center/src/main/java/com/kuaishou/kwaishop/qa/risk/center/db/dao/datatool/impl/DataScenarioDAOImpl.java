package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.DataBaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataScenarioDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataScenarioDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.datatool.DataScenarioMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.datatool.DataScenarioQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.DataScenarioBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2023-03-15
 */
@Repository
public class DataScenarioDAOImpl extends DataBaseDAO<DataScenarioDO, DataScenarioQueryCondition> implements DataScenarioDAO {

    @Autowired
    private DataScenarioMapper dataScenarioMapper;

    @Override
    public long insertDataScenario(DataScenarioDO dataScenarioDO) {
        dataScenarioDO.setCreateTime(System.currentTimeMillis());
        dataScenarioDO.setUpdateTime(System.currentTimeMillis());
        return dataScenarioMapper.insert(dataScenarioDO);
    }

    @Override
    public long updateDataScenario(DataScenarioDO dataScenarioDO) {
        return dataScenarioMapper.updateById(dataScenarioDO);
    }

    @Override
    public List<Long> queryAutoScenarioIdList() {
        return dataScenarioMapper.queryAutoScenarioIdList();
    }

    @Override
    public int logicDeleted(Long id, String operator) {
        return dataScenarioMapper.logicDeleted(operator, id);
    }

    @Override
    public List<DataScenarioDO> queryDataScenarioLikeName(String name) {
        return dataScenarioMapper.queryDataScenarioLikeName(name);
    }

    @Override
    public PageBO<DataScenarioDO> queryPageDataScenarioList(DataScenarioBO dataScenarioBO) {
        DataScenarioQueryCondition queryCondition = DataScenarioQueryCondition.builder()
                .scenarioName(dataScenarioBO.getName())
                .id(dataScenarioBO.getId())
                .creatorName(dataScenarioBO.getCreatorName())
                .orderByCreateTimeDesc(true)
                .pageNo(dataScenarioBO.getPageNo())
                .pageSize(dataScenarioBO.getPageSize())
                .build();

        PageBO<DataScenarioDO> res = queryPageList(queryCondition);
        return res;
    }

    @Override
    public List<DataScenarioDO> queryDataScenarioList(DataScenarioDO dataScenarioDO) {
        return dataScenarioMapper.queryDataScenario();
    }

    @Override
    public DataScenarioDO queryDataScenarioById(Long id) {
        return dataScenarioMapper.queryDataScenarioById(id);
    }

    @Override
    protected void fillQueryCondition(DataScenarioQueryCondition condition, QueryWrapper<DataScenarioDO> queryWrapper) {
        // 只输入名称
        if (StringUtils.isNotBlank(condition.getScenarioName())) {
            queryWrapper.like("name", condition.getScenarioName());

        }
        if (StringUtils.isNotBlank(condition.getCreatorName())) {
            queryWrapper.eq("creator_name", condition.getCreatorName());
        }
    }

    @Override
    protected BaseMapper<DataScenarioDO> getMapper() {
        return dataScenarioMapper;
    }
}
