package com.kuaishou.kwaishop.qa.risk.center.db.query.risk;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-01-05
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FundsRiskDefenseStyleQueryCondition extends BaseQueryCondition {

    private String subRiskId;
    private String defenseType;
}
