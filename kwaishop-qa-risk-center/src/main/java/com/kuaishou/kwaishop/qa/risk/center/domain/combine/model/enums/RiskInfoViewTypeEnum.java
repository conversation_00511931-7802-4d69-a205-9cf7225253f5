package com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-22
 */
public enum RiskInfoViewTypeEnum {

    TEAM_VIEW(1, "团队视角"),
    ALL_TEAM_VIEW(2, "全团队视角"),
    ;

    private final Integer code;
    private final String desc;

    RiskInfoViewTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static RiskInfoViewTypeEnum of(Integer code) {
        for (RiskInfoViewTypeEnum riskInfoViewTypeEnum: values()) {
            if (riskInfoViewTypeEnum.getCode().equals(code)) {
                return riskInfoViewTypeEnum;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo() {
        List<EnumInfo> res = new ArrayList<>();
        for (RiskInfoViewTypeEnum typeEnum: values()) {
            EnumInfo enumInfo = EnumInfo.newBuilder()
                    .setValue(typeEnum.getCode())
                    .setDesc(typeEnum.getDesc())
                    .build();
            res.add(enumInfo);
        }
        return res;
    }
}
