package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert.Column;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-15
 */
@Data
public class ReplayStrategyHitResultCkDto {

    @Column(name = "hitStrategyList")
    private String hitStrategyList;

    @Column(name = "oneTotal")
    private Long oneTotal;

    @Column(name = "twoTotal")
    private Long twoTotal;
}
