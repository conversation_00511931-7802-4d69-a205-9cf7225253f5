package com.kuaishou.kwaishop.qa.risk.center.domain.datatool.biz;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.BasicErrorCode;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DATA_SOURCE_CREATE_PARAM_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DATA_SOURCE_ID_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DATA_SOURCE_QUERY_PARAM_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DATA_SOURCE_UPDATE_PARAM_ERROR;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataCaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataCaseExecuteDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataSourceDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.DataCaseBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CaseExeResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CaseExeResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CreateDataCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CreateDataCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DataCaseDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DeleteDataCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DeleteDataCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.PageDataCaseDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseByIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseByIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseDayRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseDayResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseLikeNameRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseLikeNameResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryPageDataCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryPageDataCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.RunDataCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.RunDataCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.UpdateDataCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.UpdateDataCaseResponse;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-23
 */
@Service
@Slf4j
public class DataCaseBizService {

    @Autowired
    private DataCaseDAO dataCaseDAO;

    @Autowired
    private DataCaseExeBizService dataCaseExeBizService;

    @Autowired
    private DataSourceDAO dataSourceDAO;

    @Autowired
    private DataCaseExecuteDAO dataCaseExecuteDAO;

    public CreateDataCaseResponse createDataCase(CreateDataCaseRequest request) {
        if (StringUtils.isBlank(request.getName()) || StringUtils.isBlank(request.getCreatorName()) || StringUtils
                .isBlank(request.getCheckContent())) {
            throw new BizException(DATA_SOURCE_CREATE_PARAM_ERROR);
        }
        if (request.getDataSourceId() <= 0) {
            throw new BizException(DATA_SOURCE_CREATE_PARAM_ERROR);
        }

        try {
            long res = dataCaseDAO.insertDataCase(buildCreateDataSource(request));
            return CreateDataCaseResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] createDataSource bizError, exception: ", e);
            return CreateDataCaseResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[DataSourceBizService] createDataSource Error, exception: ", e);
            return CreateDataCaseResponse.newBuilder().setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    public UpdateDataCaseResponse updateDataCase(UpdateDataCaseRequest request) {
        if (request.getId() <= 0) {
            throw new BizException(DATA_SOURCE_ID_ERROR);
        }
        if (StringUtils.isBlank(request.getName()) && StringUtils.isBlank(request.getName()) && StringUtils
                .isBlank(request.getCheckContent()) && request.getDataSourceId() <= 0) {
            return UpdateDataCaseResponse.newBuilder().setResult(DATA_SOURCE_UPDATE_PARAM_ERROR.getCode())
                    .setErrorMsg(DATA_SOURCE_UPDATE_PARAM_ERROR.getMessage()).build();
        }
        try {
            DataCaseDO dataCaseDO = dataCaseDAO.queryDataCaseById(request.getId());
            if (dataCaseDO == null) {
                throw new BizException(DATA_SOURCE_ID_ERROR);
            }
            dataCaseDO.setName(request.getName());
            dataCaseDO.setDataSourceId(request.getDataSourceId());
            dataCaseDO.setCheckContent(request.getCheckContent());
            dataCaseDO.setDescription(request.getDescription());
            dataCaseDO.setDiffQueryContent(request.getDiffQueryContent());
            dataCaseDAO.updateDataCase(dataCaseDO);
            return UpdateDataCaseResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] updateDataSource bizError, exception: ", e);
            return UpdateDataCaseResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        }
    }

    public QueryDataCaseDayResponse queryDateCase(QueryDataCaseDayRequest request) {
        int caseNum = dataCaseDAO.queryDataCaseList(null).size();
        int dataSourceNum = dataSourceDAO.queryDataSourceList(null).size();
        int reportNum = dataCaseExecuteDAO.queryCaseExecuteList(null).size();
        return QueryDataCaseDayResponse.newBuilder().setCaseNum(caseNum)
                .setSourceNum(dataSourceNum)
                .setReportNum(reportNum)
                .build();
    }

    public DeleteDataCaseResponse deleteDataCase(DeleteDataCaseRequest request) {
        DataCaseDO dataCaseDO = dataCaseDAO.queryDataCaseById(request.getId());
        if (dataCaseDO == null) {
            return DeleteDataCaseResponse.newBuilder().setResult(BizErrorCode.DATA_SOURCE_ID_ERROR.getCode())
                    .setErrorMsg(BizErrorCode.DATA_SOURCE_ID_ERROR.getMessage()).build();
        }
        try {
            dataCaseDAO.logicDeleted(request.getId(), dataCaseDO.getCreatorName());
            return DeleteDataCaseResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] deleteDataSource bizError, exception: ", e);
            return DeleteDataCaseResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[DataSourceBizService] deleteDataSource Error, exception: ", e);
            return DeleteDataCaseResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    public QueryDataCaseLikeNameResponse queryDataCaseLikeName(QueryDataCaseLikeNameRequest request) {
        if (StringUtils.isBlank(request.getName())) {
            throw new BizException(DATA_SOURCE_QUERY_PARAM_ERROR);
        }
        try {
            List<DataCaseDO> dataCaseDos = dataCaseDAO.queryDataCaseLikeName(request.getName());
            List<DataCaseDTO> dataSourceDTOList =
                    dataCaseDos.stream().map(this::convertToDTO).collect(Collectors.toList());
            return QueryDataCaseLikeNameResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(dataSourceDTOList).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] queryDataSourceLikeName bizError, exception: ", e);
            return QueryDataCaseLikeNameResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[DataSourceBizService] queryDataSourceLikeName Error, exception: ", e);
            return QueryDataCaseLikeNameResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    public QueryDataCaseByIdResponse queryDataCaseById(QueryDataCaseByIdRequest request) {
        if (request.getId() <= 0) {
            throw new BizException(DATA_SOURCE_ID_ERROR);
        }
        try {
            DataCaseDO dataCaseDO = dataCaseDAO.queryDataCaseById(request.getId());
            if (dataCaseDO == null) {
                return QueryDataCaseByIdResponse.newBuilder().setResult(BizErrorCode.DATA_QUERY_EMPTY.getCode())
                        .setErrorMsg(BizErrorCode.DATA_QUERY_EMPTY.getMessage()).build();
            }
            return QueryDataCaseByIdResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE)
                    .addData(convertToDTO(dataCaseDO)).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] queryDataSourceById bizError, exception: ", e);
            return QueryDataCaseByIdResponse.newBuilder().setResult(e.getCode()).setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[DataSourceBizService] queryDataSourceById Error, exception: ", e);
            return QueryDataCaseByIdResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage()).build();
        }

    }

    public QueryDataCaseResponse queryDataCase(QueryDataCaseRequest request) {
        try {
            List<DataCaseDO> dataCaseDOS = dataCaseDAO.queryDataCaseList(buildQueryDataCase(request));
            log.info("[DataSourceBizService] queryDataSource", ObjectMapperUtils.toJSON(request));
            List<DataCaseDTO> dataCaseDTOList =
                    dataCaseDOS.stream().map(this::convertToDTO).collect(Collectors.toList());
            return QueryDataCaseResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(dataCaseDTOList).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] queryDataSource bizError, exception: ", e);
            return QueryDataCaseResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[DataSourceBizService] queryDataSource Error, exception: ", e);
            return QueryDataCaseResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    public QueryPageDataCaseResponse queryPageDataCase(QueryPageDataCaseRequest request) {
        try {
            PageBO<DataCaseDO> dataCaseDOPageBO =
                    dataCaseDAO.queryPageDataCaseList(buildQueryDataCasePage(request));
            List<DataCaseDTO> dataCaseDTOList = dataCaseDOPageBO.getData().stream()
                    .map(this::convertToDTO).collect(Collectors.toList());
            PageDataCaseDTO pageDataCaseDTO = PageDataCaseDTO.newBuilder().addAllDataCaseList(dataCaseDTOList)
                    .setPageNo(dataCaseDOPageBO.getPageNo())
                    .setPageSize(dataCaseDOPageBO.getPageSize())
                    .setTotal(dataCaseDOPageBO.getTotal())
                    .build();
            return QueryPageDataCaseResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(pageDataCaseDTO).build();

        } catch (BizException e) {
            log.error("[DataSourceBizService] queryPageDataSource bizError, exception: ", e);
            return QueryPageDataCaseResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[DataSourceBizService] queryPageDataSource Error, exception: ", e);
            return QueryPageDataCaseResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage()).build();
        }
    }

    public DataCaseBO buildQueryDataCasePage(QueryPageDataCaseRequest request) {
        if (request.getId() <= 0) {
            return DataCaseBO.builder()
                    .creatorName(request.getCreatorName())
                    .name(request.getName())
                    .pageNo(request.getPageNo())
                    .pageSize(request.getPageSize())
                    .build();
        } else {
            return DataCaseBO.builder()
                    .creatorName(request.getCreatorName())
                    .id(request.getId())
                    .name(request.getName())
                    .pageNo(request.getPageNo())
                    .pageSize(request.getPageSize())
                    .build();
        }
    }

    public DataCaseDO buildQueryDataCase(QueryDataCaseRequest request) {
        if (request.getId() <= 0) {
            return DataCaseDO.builder()
                    .creatorName(request.getCreatorName())
                    .name(request.getName())
                    .build();
        } else {
            return DataCaseDO.builder()
                    .creatorName(request.getCreatorName())
                    .id(request.getId())
                    .name(request.getName())
                    .build();
        }
    }

    private DataCaseDTO convertToDTO(DataCaseDO dataCaseDO) {
        return DataCaseDTO.newBuilder()
                .setCreateTime(dataCaseDO.getCreateTime())
                .setUpdateTime(dataCaseDO.getUpdateTime())
                .setId(dataCaseDO.getId())
                .setName(dataCaseDO.getName())
                .setDataSourceId(dataCaseDO.getDataSourceId())
                .setCheckContent(dataCaseDO.getCheckContent())
                .setDescription(dataCaseDO.getDescription())
                .setDiffQueryContent(dataCaseDO.getDiffQueryContent())
                .setCreatorName(dataCaseDO.getCreatorName())
                .build();
    }

    public DataCaseDO buildCreateDataSource(CreateDataCaseRequest request) {

        return DataCaseDO.builder()
                .creatorName(request.getCreatorName())
                .name(request.getName())
                .checkContent(request.getCheckContent())
                .dataSourceId(request.getDataSourceId())
                .description(request.getDescription())
                .diffQueryContent(request.getDiffQueryContent())
                .build();
    }

    public RunDataCaseResponse runDataCase(RunDataCaseRequest request) {

        try {
            dataCaseExeBizService.runDataCase(request.getCaseId(), request.getOperator());
            log.info("success");
        } catch (Exception e) {
            return RunDataCaseResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage()).build();
        }
        return RunDataCaseResponse.newBuilder().setResult(1)
                .build();
    }

    public CaseExeResultResponse queryPageCaseReportList(CaseExeResultRequest request) {
        return dataCaseExeBizService.queryPageCaseReports(request);
    }
}
