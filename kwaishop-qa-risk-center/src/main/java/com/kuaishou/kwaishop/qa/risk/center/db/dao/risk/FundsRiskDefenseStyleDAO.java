package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.KspayPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.detail.KspayDetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsRiskDefenseStyleDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.FundsRiskDefenseStyleQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;

/**
 * <AUTHOR>
 * Created on 2022-12-26
 */
public interface FundsRiskDefenseStyleDAO extends KspayDetailDAO<FundsRiskDefenseStyleDO, CombineQueryParam> {
    long insert(FundsRiskDefenseStyleDO fundsRiskDefenseStyleDO);
    KspayPageBO<FundsRiskDefenseStyleDO> queryPageRiskDetailList(FundsRiskDefenseStyleQueryCondition fundsRiskDefenseStyleQueryCondition);

    List<FundsRiskDefenseStyleDO> queryRiskDetailList(FundsRiskDefenseStyleQueryCondition fundsRiskDefenseStyleQueryCondition);
    List<FundsRiskDefenseStyleDO> queryAllDefenseInfo(String checkId);

    FundsRiskDefenseStyleDO queryByDefenseId(String defenseId);
    int updateSelectiveById(FundsRiskDefenseStyleDO fundsRiskDefenseStyleDO);
    List<FundsRiskDefenseStyleDO> queryAllBySubRiskId(String subRiskId);
}
