package com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz.impl;

import static com.kuaishou.infra.falcon.util.JsonUtils.toJSON;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.CHECK_INFO_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.CHECK_INFO_NULL;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kspay.util.GsonUtils;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.FundsRiskDefenseStyleDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.FundsSubRiskDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsRiskDefenseStyleDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsSubRiskDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.auth.service.AuthService;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz.KspayCheckInfoResourceService;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz.KspayRiskFeatureBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.KspayFundsRiskFeatureViewService;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.convert.KspayRiskDetailConvert;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.CheckInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.CheckInfoResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.FieldCheckInfoList;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayAuditRiskEventResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRelateRiskEventRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetailSimple;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.RiskEventList;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.TableData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayBranchRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayRiskDetailParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.MethodCovInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.PayFinanceLossPoint;
import com.kuaishou.kwaishop.qa.risk.center.utils.http.HttpUtils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;

@Service
@Lazy
@Slf4j
public class KspayCheckInfoResourceServiceImpl implements KspayCheckInfoResourceService {
    private static HttpUtils HTTP_UTILS = new HttpUtils();
    @Autowired
    private FundsRiskDefenseStyleDAO fundsRiskDefenseStyleDAO;
    @Autowired
    private FundsSubRiskDAO fundsSubRiskDAO;

    @Autowired
    private AuthService authService;
    @Autowired
    private KspayRiskDetailConvert kspayRiskDetailConvert;
    @Autowired
    private KspayFundsRiskFeatureViewService kspayFundsRiskFeatureViewService;

    @Autowired
    private KspayRiskFeatureBizService kspayRiskFeatureBizService;

    @PostConstruct
    public void init() {
        log.info("KspayCheckInfoResourceServiceImpl init.");
    }

    // 根据风险字段关联存量风险点
    @Override
    public List<KspayRiskDetailParam> queryRelateHistoryRiskEvent(KspayRelateRiskEventRequest request) {
        List<MethodCovInfo> methodCovInfos =
                kspayFundsRiskFeatureViewService.kspayQueryBranchAccuracyFMethodsData(request.getRepoName(),
                        request.getBranchName());
        KspayBranchRequest kspayBranchRequest = KspayBranchRequest.newBuilder()
                .setBranchName(request.getBranchName())
                .setRepoName(request.getRepoName())
                .build();
        // 如果查询精准没有返回，则取数据库中的数据，后续逻辑保持不变
        if (CollectionUtils.isEmpty(methodCovInfos)) {
            log.info("精准返回方法覆盖信息为空, 降级取db数据");
            List<KspayRiskFeatureDetailSimple> kspayRiskFeatureDetailSimples =
                    kspayRiskFeatureBizService.queryRiskByRepoAndBranch(kspayBranchRequest);

            List<PayFinanceLossPoint> dbPayFinanceLossPoints = new ArrayList<>();
            for (KspayRiskFeatureDetailSimple kspayRiskFeatureDetailSimple : kspayRiskFeatureDetailSimples) {
                String riskTableFields = kspayRiskFeatureDetailSimple.getRiskTableFields();
                if (StringUtils.isNotBlank(riskTableFields)) {
                    try {
                        List<RiskTableFieldsBO> riskTableFieldsList = GsonUtils.fromJSON(riskTableFields, ArrayList.class, RiskTableFieldsBO.class);
                        int processedItemsCount = 0;
                        for (RiskTableFieldsBO riskTableFieldsItem : riskTableFieldsList) {
                            dbPayFinanceLossPoints.add(PayFinanceLossPoint.newBuilder()
                                    .setField(riskTableFieldsItem.getField()).setTable(riskTableFieldsItem.getTable()).build());
                            processedItemsCount++;
                        }
                        log.info("已处理 {}项, 结果得到的dbPayFinanceLossPoints列表大小为 {}", processedItemsCount, dbPayFinanceLossPoints.size());
                    } catch (Exception e) {
                        log.error("An IO error occurred while parsing riskTableFields", e);
                    }
                }
            }
            if (!dbPayFinanceLossPoints.isEmpty()) {
                // 使用protobuf的构建器模式创建MethodCovInfo对象
                List<MethodCovInfo> simulatedMethodCovInfos = dbPayFinanceLossPoints.stream()
                        .map(payFinanceLossPoint -> {
                            MethodCovInfo.Builder builder = MethodCovInfo.newBuilder();
                            builder.addPayFinanceLossPoint(payFinanceLossPoint);
                            return builder.build();
                        })
                        .collect(Collectors.toList());

                methodCovInfos = simulatedMethodCovInfos;
            } else {
                log.info("无法从数据库中获取有效的PayFinanceLossPoint数据，无法继续处理");
                return Collections.emptyList();
            }
        }

        List<String> checkIdList = new ArrayList<>();
        log.info("资损方法覆盖信息: methodCovInfos:{}", ObjectMapperUtils.toJSON(methodCovInfos));
        int i = 1;
        for (MethodCovInfo methodCovInfo : methodCovInfos) {
            List<PayFinanceLossPoint> payFinanceLossPoints = methodCovInfo.getPayFinanceLossPointList();
            if (CollectionUtils.isEmpty(payFinanceLossPoints)) {
                log.warn("资损风险字段为空:{}", ObjectMapperUtils.toJSON(payFinanceLossPoints));
                // throw new BizException(PAY_FINANCE_LOSS_POINTS_EMPTY);
                continue;
            }
            // 请求对账
            RiskFieldCheckInfoDO riskFieldCheckInfoDO = convertFields(payFinanceLossPoints, request.getRepoName());
            String onlineUrl;
            if (request.getRepoName().contains("oversea")) {
                onlineUrl = "https://auditadmin-sgp.corp.kuaishou.com/get/riskFieldCheckInfo";
            } else {
                onlineUrl = "https://auditadmin.corp.kuaishou.com/get/riskFieldCheckInfo";
            }

            CheckInfoResponse checkInfoResponse = getCheckInfo(riskFieldCheckInfoDO, onlineUrl);

            // 提取关键信息进行日志记录
            String requestSummary = "repoName=" + riskFieldCheckInfoDO.getApplication() + ", payFinanceLossPoints=" + payFinanceLossPoints;
            log.info("请求对账: request:{}", requestSummary);
            log.info("收到对账响应: {}, checkInfoCount: {}", ObjectMapperUtils.toJSON(checkInfoResponse), checkInfoResponse.getDataCount());

            // 获取所有checkId
            checkIdList.addAll(getCheckIds(checkInfoResponse));
            // checkIdList.addAll(getCheckNames(checkInfoResponse)); // 备用
            // checkIdList.addAll(getCheckInfos(checkInfoResponse)); // 备用
            checkIdList.stream().distinct().collect(Collectors.toList());

            log.info("拿到对账id集合: checkIdList:{}", ObjectMapperUtils.toJSON(checkIdList));
            i++;
        }
        // 根据checkId查询存量风险
        List<String> subRiskIds = getSubRiskIds(checkIdList.stream().distinct().collect(Collectors.toList()));
        List<FundsSubRiskDO> subRisks = querySubRiskById(subRiskIds);
        log.info("id集合 & 风险点: subRiskIds:{},subRisks:{}",
                ObjectMapperUtils.toJSON(subRiskIds), ObjectMapperUtils.toJSON(subRisks));

        return kspayRiskDetailConvert.buildRelateSubRiskDetail(subRisks);
    }

    private static class RiskTableFieldsBO {
        private String table;
        private String field;

        public String getTable() {
            return table;
        }

        public void setTable(String table) {
            this.table = table;
        }

        public String getField() {
            return field;
        }

        public void setField(String field) {
            this.field = field;
        }
    }

    // 简易接口（只会返回subRiskName）
    @Override
    public String querySimpleRelateHistoryRiskEvent(KspayRelateRiskEventRequest request) {
        List<String> subRiskNames = new ArrayList<>();
        List<KspayRiskDetailParam> kspayRiskDetailParams = queryRelateHistoryRiskEvent(request);
        int totalRiskEvents = kspayRiskDetailParams.size();
        log.info("共找到 {} 个资损风险场景.", totalRiskEvents);
        if (CollectionUtils.isEmpty(kspayRiskDetailParams)) {
            log.info("没有找到相关资损风险场景");
            return "";
        }
        int processedSubRisks = 0;
        for (KspayRiskDetailParam kspayRiskDetailParam : kspayRiskDetailParams) {
            if (kspayRiskDetailParam.getSubRiskName().equals("")) {
                continue;
            }
            subRiskNames.add(kspayRiskDetailParam.getSubRiskName());
            processedSubRisks++;
        }
        log.info("处理了{}个唯一的子风险", processedSubRisks);
        HashSet<String> simpleNotRepeatSet = new HashSet<>(subRiskNames);
        return String.join(",\n", simpleNotRepeatSet);
    }


    /***
     *
     * 核对名称，核对链接   queryRiskByRepoAndBranch   不用在这实现
     *
     *
     * 核对覆盖状态
     * 修改状态
     * 添加核对（埋链接）
     *
     * 风险点 ：风险点保鲜链接   kspaySimpleRelateHistoryRiskEvent
     * 添加场景（埋链接）
     *
     *
     * 根据应用和分支信息获取
     * kspay-core
     * feature_20240326_compensateFix
     *
     *
     */


    // 后面queryRelateHistoryRiskEvent接口废弃, 可以统一迁到这个接口, 前端页面调用也要改
    @Override
    public KspayAuditRiskEventResponse getKspayAuditRiskEvent(KspayRelateRiskEventRequest request) {
        List<MethodCovInfo> methodCovInfos =
                kspayFundsRiskFeatureViewService.kspayQueryBranchAccuracyFMethodsData(request.getRepoName(),
                        request.getBranchName());
        List<String> checkIdList = new ArrayList<>();
        List<CheckInfo> auditInfos = new ArrayList<>();
        KspayBranchRequest kspayBranchRequest = KspayBranchRequest.newBuilder()
                .setBranchName(request.getBranchName())
                .setRepoName(request.getRepoName())
                .build();
        // 如果查询精准没有返回，则取数据库中的数据，后续逻辑保持不变
        if (CollectionUtils.isEmpty(methodCovInfos)) {
            log.info("精准返回方法覆盖信息为空, 降级取db数据");
            List<KspayRiskFeatureDetailSimple> kspayRiskFeatureDetailSimples =
                    kspayRiskFeatureBizService.queryRiskByRepoAndBranch(kspayBranchRequest);

            List<PayFinanceLossPoint> dbPayFinanceLossPoints = new ArrayList<>();
            for (KspayRiskFeatureDetailSimple kspayRiskFeatureDetailSimple : kspayRiskFeatureDetailSimples) {
                String riskTableFields = kspayRiskFeatureDetailSimple.getRiskTableFields();
                if (StringUtils.isNotBlank(riskTableFields)) {
                    try {
                        List<RiskTableFieldsBO> riskTableFieldsList = GsonUtils.fromJSON(riskTableFields, ArrayList.class, RiskTableFieldsBO.class);
                        int processedItemsCount = 0;
                        for (RiskTableFieldsBO riskTableFieldsItem : riskTableFieldsList) {
                            dbPayFinanceLossPoints.add(PayFinanceLossPoint.newBuilder()
                                    .setField(riskTableFieldsItem.getField()).setTable(riskTableFieldsItem.getTable()).build());
                            processedItemsCount++;
                        }
                        log.info("已处理 {}项, 结果得到的dbPayFinanceLossPoints列表大小为 {}", processedItemsCount, dbPayFinanceLossPoints.size());
                    } catch (Exception e) {
                        log.error("An IO error occurred while parsing riskTableFields", e);
                    }
                }
            }
            if (!dbPayFinanceLossPoints.isEmpty()) {
                // 使用protobuf的构建器模式创建MethodCovInfo对象
                List<MethodCovInfo> simulatedMethodCovInfos = dbPayFinanceLossPoints.stream()
                        .map(payFinanceLossPoint -> {
                            MethodCovInfo.Builder builder = MethodCovInfo.newBuilder();
                            builder.addPayFinanceLossPoint(payFinanceLossPoint);
                            return builder.build();
                        })
                        .collect(Collectors.toList());

                methodCovInfos = simulatedMethodCovInfos;
            } else {
                log.warn("无法从数据库中获取有效的PayFinanceLossPoint数据，无法继续处理");
            }
        }
        log.info("资损方法覆盖信息: methodCovInfos:{}", ObjectMapperUtils.toJSON(methodCovInfos));
        int i = 1;
        for (MethodCovInfo methodCovInfo : methodCovInfos) {
            List<PayFinanceLossPoint> payFinanceLossPoints = methodCovInfo.getPayFinanceLossPointList();
            if (CollectionUtils.isEmpty(payFinanceLossPoints)) {
                log.warn("资损风险字段为空:{}", ObjectMapperUtils.toJSON(payFinanceLossPoints));
                continue;
            }
            // 请求对账
            RiskFieldCheckInfoDO riskFieldCheckInfoDO = convertFields(payFinanceLossPoints, request.getRepoName());
            String onlineUrl;
            if (request.getRepoName().contains("oversea")) {
                onlineUrl = "https://auditadmin-sgp.corp.kuaishou.com/get/riskFieldCheckInfo";
            } else {
                onlineUrl = "https://auditadmin.corp.kuaishou.com/get/riskFieldCheckInfo";
            }

            CheckInfoResponse checkInfoResponse = getCheckInfo(riskFieldCheckInfoDO, onlineUrl);

            log.info("请求对账: request:{}, checkInfoResponse:{}",
                    ObjectMapperUtils.toJSON(riskFieldCheckInfoDO), ObjectMapperUtils.toJSON(checkInfoResponse));
            // 获取所有checkId
            checkIdList.addAll(getCheckIds(checkInfoResponse));
            //******* 获取 对账Id和对账名称
            auditInfos.addAll(getCheckInfos(checkInfoResponse));

            checkIdList.stream().distinct().collect(Collectors.toList());
            log.info("拿到对账id集合: checkIdList:{}", ObjectMapperUtils.toJSON(checkIdList));
            i++;
        }
        // 根据checkId查询存量风险
        List<String> subRiskIds = getSubRiskIds(checkIdList.stream().distinct().collect(Collectors.toList()));
        List<FundsSubRiskDO> subRisks = querySubRiskById(subRiskIds);
        log.info("id集合 & 风险点: subRiskIds:{},subRisks:{}",
                ObjectMapperUtils.toJSON(subRiskIds), ObjectMapperUtils.toJSON(subRisks));

        //*** 获取所有的风险点名称 + 链接Id信息
        List<RiskEventList> subRiskNames = new ArrayList<>();
        List<KspayRiskDetailParam> kspayRiskDetailParams = kspayRiskDetailConvert.buildRelateSubRiskDetail(subRisks);
        if (CollectionUtils.isEmpty(kspayRiskDetailParams)) {
            log.info("没有找到任何存量风险");
        }
        log.info("所有存量风险: kspayRiskDetailParams:{}", ObjectMapperUtils.toJSON(kspayRiskDetailParams));
        for (KspayRiskDetailParam kspayRiskDetailParam : kspayRiskDetailParams) {
            if (kspayRiskDetailParam.getSubRiskName().equals("")) {
                continue;
            }
            subRiskNames.add(RiskEventList.newBuilder()
                    .setRiskName(kspayRiskDetailParam.getSubRiskName())
                    .setSubRiskIdLink(kspayRiskDetailParam.getSubRiskId())
                    .build());
        }


        KspayAuditRiskEventResponse response = KspayAuditRiskEventResponse.newBuilder()
                .setResult(1)
                .addAllCheckList(auditInfos.stream().distinct().collect(Collectors.toList()))
                .addAllRiskEventList(subRiskNames)
                .build();
        return response;
    }


    public KspayCheckInfoResourceServiceImpl.RiskFieldCheckInfoDO convertFields(List<PayFinanceLossPoint> payFinanceLossPoints,
                                                                                String application) {
        KspayCheckInfoResourceServiceImpl.RiskFieldCheckInfoDO checkInfoDO = new KspayCheckInfoResourceServiceImpl.RiskFieldCheckInfoDO();
        List<KspayCheckInfoResourceServiceImpl.RiskFieldCheckInfoTableDO> tables = new ArrayList<>(payFinanceLossPoints.size());
        for (PayFinanceLossPoint financeLossPoint : payFinanceLossPoints) {
            KspayCheckInfoResourceServiceImpl.RiskFieldCheckInfoTableDO checkInfoTableDO =
                    new KspayCheckInfoResourceServiceImpl.RiskFieldCheckInfoTableDO();
            checkInfoTableDO.setTableName(financeLossPoint.getTable());
            checkInfoTableDO.setFields(Arrays.asList(financeLossPoint.getField().split(",")));  // todo 为空
            tables.add(checkInfoTableDO);
        }
        checkInfoDO.setTables(tables);
        checkInfoDO.setApplication(application);
//        List<CheckInfoTables> checkInfoTables = new ArrayList<>();  // proto模式
//        for (PayFinanceLossPoint payFinanceLossPoint : payFinanceLossPoints) {
//            List<String> fields = Arrays.asList(payFinanceLossPoint.getField().split(","));
//            CheckInfoTables checkInfoTable = CheckInfoTables.newBuilder()
//                    .addAllFieldsList(fields)
//                    .setTableName(payFinanceLossPoint.getTable())
//                    .build();
//            checkInfoTables.add(checkInfoTable);
//        }
        return checkInfoDO;
    }


    public static CheckInfoResponse getCheckInfo(RiskFieldCheckInfoDO riskFieldCheckInfoDO, String onlineUrl) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        try {
            log.info("[KspayCheckInfoResourceServiceImpl] getCheckInfo_request : {}, onlineUrl:{}", toJSON(riskFieldCheckInfoDO), onlineUrl);
            Response response = HTTP_UTILS.post(onlineUrl, headers, toJSON(riskFieldCheckInfoDO));
            if (!response.isSuccessful()) {
                log.error("[KspayCheckInfoResourceServiceImpl] getCheckInfo failed with status code: {}", response.code());
                throw new BizException(CHECK_INFO_ERROR);
            }
            if (response.body() == null) {
                log.error("[KspayCheckInfoResourceServiceImpl] getCheckInfo received empty response body");
                throw new BizException(CHECK_INFO_NULL);
            }
            String res = response.body().string();
            CheckInfoResponse checkInfoResponse = ObjectMapperUtils.fromJSON(res, CheckInfoResponse.class);
            log.info("[KspayCheckInfoResourceServiceImpl] getCheckCode: {}, count: {}",
                    checkInfoResponse.getCode(), checkInfoResponse.getDataCount());
            return checkInfoResponse;
        } catch (Exception e) {
            log.error("[KspayCheckInfoResourceServiceImpl] getCheckInfo error: ", e);
            return null;
        }
    }

    public String extractKeyFieldsFromFundsSubRiskDO(FundsSubRiskDO fundsSubRiskDO) {
        if (fundsSubRiskDO == null) {
            return "未查询到数据";
        }
        return String.format("riskEventId: %s, subRiskName: %s",
                fundsSubRiskDO.getRiskEventId(),
                fundsSubRiskDO.getSubRiskName());
    }

    public List<FundsSubRiskDO> querySubRiskById(List<String> subRiskIds) {
        List<FundsSubRiskDO> fundsSubRiskDOS = new ArrayList<>();
        for (String subRiskId : subRiskIds) {
            FundsSubRiskDO fundsSubRiskDO = fundsSubRiskDAO.queryBySubRiskId(subRiskId);
            if (fundsSubRiskDO == null) {
                log.info("未查询到子风险数据, 不抛异常继续查询: {}", extractKeyFieldsFromFundsSubRiskDO(fundsSubRiskDO));
                continue;
            }
            fundsSubRiskDOS.add(fundsSubRiskDO);
        }
        // 输出查询到的所有子风险的关键信息
        StringBuilder logMessage = new StringBuilder("子风险数据: ");
        for (FundsSubRiskDO fund : fundsSubRiskDOS) {
            logMessage.append(extractKeyFieldsFromFundsSubRiskDO(fund)).append(" ");
        }
        log.info(logMessage.toString());
        return fundsSubRiskDOS;
    }

    public String extractRiskEventAndSubRiskName(FundsSubRiskDO fundsSubRiskDO) {
        return String.format("riskEventId: %s, subRiskName: %s",
                fundsSubRiskDO.getRiskEventId(),
                fundsSubRiskDO.getSubRiskName());
    }
//    // 之前的实现，不用了
//    public List<String> getCheckIds(CheckInfoResponse checkInfoResponse) {
//        List<String> checkIdList = new ArrayList<>();
////        Map<String, List<Integer>> fields = new HashMap<>();
//        for (TableData tableData : checkInfoResponse.getDataList()) {
////            String tableName = tableData.getTableName();
//            for (FieldCheckInfoList fieldCheckInfoList : tableData.getFieldCheckInfoListList()) {
//                if (CollectionUtils.isNotEmpty(fieldCheckInfoList.getCheckIdListList())) {
//                    checkIdList.addAll(fieldCheckInfoList.getCheckIdListList());
//                }
//            }
//        }
//        return checkIdList;
//    }

    public List<CheckInfo> getCheckInfos(CheckInfoResponse checkInfoResponse) {
        List<CheckInfo> checkInfoList = new ArrayList<>();
        for (TableData tableData : checkInfoResponse.getDataList()) {
            for (FieldCheckInfoList fieldCheckInfoList : tableData.getFieldCheckInfoListList()) {
                if (CollectionUtils.isNotEmpty(fieldCheckInfoList.getCheckListList())) {
                    checkInfoList.addAll(fieldCheckInfoList.getCheckListList());
                }
            }
        }
        return checkInfoList;
    }

    public List<String> getCheckIds(CheckInfoResponse checkInfoResponse) {
        List<String> checkIdList = new ArrayList<>();
        for (TableData tableData : checkInfoResponse.getDataList()) {
            for (FieldCheckInfoList fieldCheckInfoList : tableData.getFieldCheckInfoListList()) {
                if (CollectionUtils.isNotEmpty(fieldCheckInfoList.getCheckListList())) {
                    for (CheckInfo checkInfo : fieldCheckInfoList.getCheckListList()) {
                        checkIdList.add(checkInfo.getCheckId());
                    }
                }
            }
        }
        return checkIdList;
    }

    public List<String> getCheckNames(CheckInfoResponse checkInfoResponse) {
        List<String> checkIdList = new ArrayList<>();
        for (TableData tableData : checkInfoResponse.getDataList()) {
            for (FieldCheckInfoList fieldCheckInfoList : tableData.getFieldCheckInfoListList()) {
                if (CollectionUtils.isNotEmpty(fieldCheckInfoList.getCheckListList())) {
                    for (CheckInfo checkInfo : fieldCheckInfoList.getCheckListList()) {
                        checkIdList.add(checkInfo.getCheckName());
                    }
                }
            }
        }
        return checkIdList;
    }

    public List<String> getSubRiskIds(List<String> checkIdList) {
        List<String> subRiskIds = new ArrayList<>();
        for (String checkId : checkIdList) {
            List<FundsRiskDefenseStyleDO> fundsRiskDefenseStyleDOS = fundsRiskDefenseStyleDAO.queryAllDefenseInfo(checkId);
            if (fundsRiskDefenseStyleDOS == null) {
                log.info("无资损防控数据: fundsRiskDefenseStyleDOS:{}", toJSON(fundsRiskDefenseStyleDOS));
            }
            for (FundsRiskDefenseStyleDO fundsRiskDefenseStyleDO : fundsRiskDefenseStyleDOS) {
                subRiskIds.add(fundsRiskDefenseStyleDO.getSubRiskId());
            }
        }
        log.info("资损防控subRiskIds: subRiskIds:{}", ObjectMapperUtils.toJSON(subRiskIds));
        return subRiskIds;
    }


    public static class RiskFieldCheckInfoDO {
        private List<RiskFieldCheckInfoTableDO> tables;
        private String application;

        public List<RiskFieldCheckInfoTableDO> getTables() {
            return tables;
        }

        public void setTables(List<RiskFieldCheckInfoTableDO> tables) {
            this.tables = tables;
        }

        public String getApplication() {
            return application;
        }

        public void setApplication(String application) {
            this.application = application;
        }
    }

    public static class RiskFieldCheckInfoTableDO {
        private String tableName;
        private List<String> fields;
        private boolean isSerializing = false;

        public String getTableName() {
            return tableName;
        }

        public void setTableName(String tableName) {
            this.tableName = tableName;
        }

        public List<String> getFields() {
            if (isSerializing) {
                return Collections.emptyList(); // 或者返回其他默认值
            }
            isSerializing = true;
            try {
                return fields != null ? fields : Collections.emptyList();
            } finally {
                isSerializing = false;
            }
        }

        public void setFields(List<String> fields) {
            this.fields = fields;
        }
    }


    public static class RiskCheckInfoDO {
        private List<RiskCheckInfoTableDO> tables;
        private String application;

        public List<RiskCheckInfoTableDO> getTables() {
            return tables;
        }

        public void setTables(List<RiskCheckInfoTableDO> tables) {
            this.tables = tables;
        }

        public String getApplication() {
            return application;
        }

        public void setApplication(String application) {
            this.application = application;
        }
    }

    public class RiskCheckInfoTableDO {
        private String tableName;
        private List<CheckInfo> checkInfos;

        public String getTableName() {
            return tableName;
        }

        public void setTableName(String tableName) {
            this.tableName = tableName;
        }

        // 注意这里修正了字段名称
        public List<CheckInfo> getFields() {
            return checkInfos;
        }

        // 也相应地修正了这里
        public void setFields(List<CheckInfo> checkInfos) {
            this.checkInfos = checkInfos;
        }
    }

}
