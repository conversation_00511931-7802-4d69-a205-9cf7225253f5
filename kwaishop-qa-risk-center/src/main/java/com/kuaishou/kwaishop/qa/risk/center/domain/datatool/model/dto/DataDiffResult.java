package com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.dto;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-16
 */

@Data
@Builder
public class DataDiffResult {

    /**
     * 用例执行id
     */
    private Long id;

    /**
     * 执行状态
     */
    private Integer status;

    /**
     * 校验是否通过
     */
    private Boolean isDiff;

    /**
     * diff关联key
     */
    private String joinKey;
    /**
     * 对比字段-源字段
     */
    private String sourceKey; // 可以是一个公式

    /**
     * 对比字段-源字段值
     */
    private String sourceValue; // 具体值

    /**
     * 对比字段-目标字段
     */
    private String targetKey;  // 可以是一个公式

    /**
     * 对比字段-目标字段值
     */
    private String targetValue; // 具体值

    /**
     * 校验表达式
     */
    private String compareRule; // = >= <=   误差5% 10% 3%

}
