package com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.API_ERROR;


import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.kuaishou.dp.auth.dsc.client.DscAccessTokenProvider;
import com.kuaishou.dp.one.service.common.model.oneapi.DataSource;
import com.kuaishou.dp.one.service.common.model.oneapi.DataSourceType;
import com.kuaishou.dp.one.service.common.model.oneapi.Query;
import com.kuaishou.dp.one.service.common.model.oneapi.QueryAccessControl;
import com.kuaishou.dp.one.service.common.model.oneapi.QueryContext;
import com.kuaishou.dp.one.service.common.model.oneapi.QueryPriority;
import com.kuaishou.dp.one.service.rpc.client.hash.base.ClientErrorCode;
import com.kuaishou.dp.one.service.rpc.client.oneapi.OneApiClient;
import com.kuaishou.dp.one.service.rpc.client.oneapi.components.OneApiRequest;
import com.kuaishou.dp.one.service.rpc.client.oneapi.components.ResponseSetting;
import com.kuaishou.dp.one.service.rpc.client.sql.model.QueryResult;
import com.kuaishou.dp.one.service.rpc.client.sql.wrapper.StreamCloseableIterator;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.constants.LogQuery;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.model.LogQueryResponse;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.model.bo.EvaluateExcelModel;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.EvaluateService;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.ExportDataBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.ExportParam;
import com.kuaishou.kwaishop.qa.risk.center.fetch.fault.MaterialManageFetchService;
import com.kuaishou.kwaishop.qa.risk.center.utils.http.HttpUtils;
import com.kuaishou.protobuf.dp.one.service.Dialect;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;


@Service
@Lazy
@Slf4j
public class EvaluateServiceImpl implements EvaluateService {

    private static final HttpUtils HTTP_UTILS = new HttpUtils();

    public static final Long SUBJECTID = 88888888L;

    public static final String QA_RISK_CENTER_BIZ_TYPE = "kwaishop_qa_risk_center"; //lego申请

    public static final int EXPIRE_MILLI_SECS = 1000000;

    public static final int GROUPID = 1520;

    private OneApiClient client = new OneApiClient();


    @Autowired
    private MaterialManageFetchService materialManageFetchService;

    @Override
    public void exportEvaluateDataLogistic(long startTime, long endTime, int pageIndex) {

        Map<String, String> value = new HashMap<>();
        value.put("topicName", "bjxy-merchant1_kwaishop_cs_intelligence_klog_topic");
        value.put("queryStr", LogQuery.LOG_MMUAILOGISTICMODEL_STR);
        value.put("startTimeInMilis", String.valueOf(startTime));
        value.put("endTimeInMilis", String.valueOf(endTime));
        value.put("pageIndex", String.valueOf(pageIndex));
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", LogQuery.TOKEN);
        log.info("exportEvaluateData data {}", ObjectMapperUtils.toJSON(value));
        try {
            Response response = HTTP_UTILS.post(LogQuery.URL, headers, value);
            if (!response.isSuccessful()) {
                throw new BizException(API_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(API_ERROR);
            }
            String result = response.body().string();
            LogQueryResponse queryResponse = ObjectMapperUtils.fromJSON(result, LogQueryResponse.class);
            log.info("exportEvaluateData queryResponse:{}", ObjectMapperUtils.toJSON(queryResponse));
            List<EvaluateExcelModel> evaluateExcelModels = new ArrayList<>();
            List<EvaluateExcelModel> evaluateExcelModelsTool = new ArrayList<>();
            if (CollectionUtils.isEmpty(queryResponse.getData().getLogList())) {
                return;
            }
            queryResponse.getData().getLogList().forEach(e -> {
                Optional<LogQueryResponse.Field> msg = e.getLogFieldList().stream()
                        .filter(field -> "msg".equals(field.getField())).findFirst();
                Optional<LogQueryResponse.Field> traceid = e.getLogFieldList().stream()
                        .filter(field -> "traceid".equals(field.getField())).findFirst();
                String[] jsonArray = parseStr(msg.get().getValue());
                String requestStr = jsonArray[0];
                String responseStr = jsonArray[1];
                JsonObject answer = getAnswer(responseStr);
                String questionContext = getSubstringBetween(requestStr, "对话输入\\n", "\\n## 历史执行记录");
                if (answer != null && answer.get("answer") != null
                        && StringUtils.isNotBlank(answer.get("answer").getAsString())) {
                    evaluateExcelModels.add(EvaluateExcelModel.builder()
                            .traceid(traceid.get().getValue())
                            .questionContext(questionContext)
                            .prompt(getPrompt(requestStr))
                            .answer(answer.get("answer").getAsString())
                            .answerOrigin(new Gson().toJson(answer))
                            .question(getQuestion(questionContext))
                            .scene("MmuAiLogisticModel")
                            .build());
                }
                JsonArray tools = getTools(responseStr);
                if (tools != null && tools.size() > 0) {
                    evaluateExcelModelsTool.add(EvaluateExcelModel.builder()
                            .traceid(traceid.get().getValue())
                            .questionContext(questionContext)
                            .prompt(getPrompt(requestStr))
                            .tools(new Gson().toJson(tools))
                            .question(getQuestion(questionContext))
                            .scene("MmuAiLogisticModel")
                            .build());
                }

            });
            upLoadEvaluateData(evaluateExcelModels, "answer");
            upLoadEvaluateData(evaluateExcelModelsTool, "tool");
        } catch (Exception ex) {
            log.info("exportEvaluateData error ", ex);
        }

    }

    @Override
    public void exportEvaluateDataPlan(long startTime, long endTime, int pageIndex) {
        Map<String, String> value = new HashMap<>();
        value.put("topicName", "bjxy-merchant1_kwaishop_cs_intelligence_klog_topic");
        value.put("queryStr", LogQuery.LOG_MMUAIPLANMODEL_STR);
        value.put("startTimeInMilis", String.valueOf(startTime));
        value.put("endTimeInMilis", String.valueOf(endTime));
        value.put("pageIndex", String.valueOf(pageIndex));
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", LogQuery.TOKEN);
        log.info("exportEvaluateData data {}", ObjectMapperUtils.toJSON(value));
        try {
            Response response = HTTP_UTILS.post(LogQuery.URL, headers, value);
            if (!response.isSuccessful()) {
                throw new BizException(API_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(API_ERROR);
            }
            String result = response.body().string();
            LogQueryResponse queryResponse = ObjectMapperUtils.fromJSON(result, LogQueryResponse.class);
            log.info("exportEvaluateDataPlan queryResponse:{}", ObjectMapperUtils.toJSON(queryResponse));
            List<EvaluateExcelModel> evaluateExcelModels = new ArrayList<>();
            if (CollectionUtils.isEmpty(queryResponse.getData().getLogList())) {
                return;
            }
            queryResponse.getData().getLogList().forEach(e -> {
                Optional<LogQueryResponse.Field> msg = e.getLogFieldList().stream()
                        .filter(field -> "msg".equals(field.getField())).findFirst();
                Optional<LogQueryResponse.Field> traceid = e.getLogFieldList().stream()
                        .filter(field -> "traceid".equals(field.getField())).findFirst();
                String[] jsonArray = parseStr(msg.get().getValue());
                String requestStr = jsonArray[0];
                String responseStr = jsonArray[1];
                String questionContext = getSubstringBetween(requestStr, "对话输入\\n", "\\n# 输出");
                JsonObject answer = getAnswer(responseStr);
                evaluateExcelModels.add(EvaluateExcelModel.builder()
                        .traceid(traceid.get().getValue())
                        .questionContext(questionContext)
                        .prompt(getPrompt(requestStr))
                        .answerOrigin(new Gson().toJson(answer))
                        .answer(answer.get("intent").getAsString())
                        .question(getQuestion(questionContext))
                        .scene("MmuAiPlanModel")
                        .build());
            });
            upLoadEvaluateData(evaluateExcelModels, "plan");
        } catch (Exception ex) {
            log.info("exportEvaluateDataPlan error ", ex);
        }
    }


    public void upLoadEvaluateData(List<EvaluateExcelModel> evaluateExcelModels, String sheetName) {
        if (CollectionUtils.isEmpty(evaluateExcelModels)) {
            return;
        }
        ExportDataBO exportDataBO = getExportDataBytes(evaluateExcelModels, sheetName);
        ExportParam exportParam = ExportParam.builder().fileName("评测集.xls").subjectId(SUBJECTID).build();
        upLoadFile(exportDataBO, exportParam);
    }

    @Override
    public void upLoadFile(ExportDataBO data, ExportParam exportParam) {
        if (data.getCode() != BaseResultCode.SUCCESS) {
            return;
        }
        try {
            String blobKey =
                    materialManageFetchService.upLoadMaterialData(data.getBytes(), QA_RISK_CENTER_BIZ_TYPE,
                            "STAFF", exportParam.getSubjectId(),
                            exportParam.getFileName());
            log.info("upLoadFile blobKey={}", blobKey);
        } catch (Exception e) {
            log.error("upLoadFile Exception", e);
        }

    }

    @Override
    public void selectHive(long pDate, long limit, long offset) {
        try {
            String dsl = "SELECT (message_history) FROM ks_origin_kwaishop_cs_dw_log.kwaishop_cs_multi_recall "
                    + "WHERE (p_date) = '20250720' limit 10 offset 0";
            OneApiRequest request = buildRequest(DataSourceType.HIVE, Dialect.NATIVE, dsl);
            try (StreamCloseableIterator<QueryResult> streamCloseableIterator = client
                    .submitAsStream(request)) {
                while (streamCloseableIterator.hasNext()) {
                    QueryResult queryResult = streamCloseableIterator.next();
                    if (queryResult.getResponseCode() == ClientErrorCode.OK) {
                        System.out.println(queryResult); // 正确的返回结果
                    } else {
                        System.out.println("error msg: " + queryResult.getErrMessage()); //查询异常
                    }
                }
            }
        } catch (Exception e) {
            log.info("selectHive error", e);
        }

    }

    String[] parseStr(String msg) {
        // 去掉前面的描述部分，只保留JSON字符串
        String jsonString = msg.substring(msg.indexOf("{"));
        String[] jsonArray = jsonString.split(", response:");
        return jsonArray;
    }

    public static String getSubstringBetween(String input, String startStr, String endStr) {
        int startIndex = input.indexOf(startStr);
        if (startIndex == -1) {
            return ""; // 没有找到起始字符串
        }

        int endIndex = input.indexOf(endStr, startIndex + startStr.length());
        if (endIndex == -1) {
            return ""; // 没有找到结束字符串
        }

        return input.substring(startIndex + startStr.length(), endIndex);
    }

    public String getPrompt(String requestStr) {
        JsonObject jsonObject = new Gson().fromJson(requestStr, JsonObject.class);
        return jsonObject.get("messages").getAsJsonArray().get(0).getAsJsonObject().get("content").getAsString();
    }

    public JsonObject getAnswer(String responseStr) {
        try {
            JsonObject jsonObject = new Gson().fromJson(responseStr, JsonObject.class);
            String answerStr = jsonObject.get("choices").getAsJsonArray().get(0)
                    .getAsJsonObject().get("messageV2").getAsJsonObject().get("content").getAsString();
            return new Gson().fromJson(answerStr, JsonObject.class);
        } catch (Exception ex) {
            log.info("getAnswer error", ex);
            return null;
        }

    }

    public String getAnswerStr(String responseStr) {
        JsonObject jsonObject = new Gson().fromJson(responseStr, JsonObject.class);
        return jsonObject.get("choices").getAsJsonArray().get(0)
                .getAsJsonObject().get("messageV2").getAsJsonObject().get("content").getAsString();
    }

    public JsonArray getTools(String responseStr) {
        try {
            JsonObject jsonObject = new Gson().fromJson(responseStr, JsonObject.class);
            return jsonObject.get("choices").getAsJsonArray().get(0)
                    .getAsJsonObject().get("messageV2").getAsJsonObject().get("toolCalls").getAsJsonArray();
        } catch (Exception ex) {
            log.info("getTools error", ex);
            return null;
        }

    }

    public String getQuestion(String questionContext) {
        String[] array = questionContext.trim().split("\\\\n");
        if (array.length == 0) {
            return "";
        }
        return array[array.length - 1];
    }

    public ExportDataBO getExportDataBytes(List<EvaluateExcelModel> evaluateExcelModels,
                                           List<EvaluateExcelModel> evaluateExcelModelsTool) {
        ExportDataBO res = ExportDataBO.builder().build();
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ExcelWriter excelWriter = EasyExcel.write(outputStream, EvaluateExcelModel.class).build();

            WriteSheet writerSheet = EasyExcel.writerSheet("answer").build();
            excelWriter.write(evaluateExcelModels, writerSheet);
            writerSheet = EasyExcel.writerSheet("tools").build();
            excelWriter.write(evaluateExcelModelsTool, writerSheet);
            excelWriter.finish();
            res.setBytes(outputStream.toByteArray());
            res.setCode(BaseResultCode.SUCCESS);
            return res;
        } catch (Exception ex) {
            log.error("getExportDataBytes Exception", ex);
            res.setCode(BaseResultCode.SERVER_ERROR);
            return res;
        }

    }

    public ExportDataBO getExportDataBytes(List<EvaluateExcelModel> evaluateExcelModels, String sheetName) {
        ExportDataBO res = ExportDataBO.builder().build();
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ExcelWriter excelWriter = EasyExcel.write(outputStream, EvaluateExcelModel.class).build();

            WriteSheet writerSheet = EasyExcel.writerSheet(sheetName).build();
            excelWriter.write(evaluateExcelModels, writerSheet);
            excelWriter.finish();
            res.setBytes(outputStream.toByteArray());
            res.setCode(BaseResultCode.SUCCESS);
            return res;
        } catch (Exception ex) {
            log.error("getExportDataBytes Exception", ex);
            res.setCode(BaseResultCode.SERVER_ERROR);
            return res;
        }

    }

    private OneApiRequest buildRequest(DataSourceType dataSourceType, Dialect dialect, String payload) {
        String hiveServerToken = DscAccessTokenProvider.getToken("kwaishop_shop_test/<EMAIL>",
                "868d1fb2d1b04abfb7e146bb8f40ba66", EXPIRE_MILLI_SECS);
        QueryAccessControl queryAccessControl = QueryAccessControl.newBuilder()
                .setToken("7efa8f00a3eb402e89d408e981941323") //开发者token
                .setAuthcPrincipalOptional("kwaishop_shop_test/<EMAIL>") //HiveServer2 认证principle
                .setAuthcTokenOptional(hiveServerToken) //必填参数：HiveServer2 认证 hiveServerToken, hiveServerToken具体生成方式如下
                .setUser("litianning") //必填参数：HiveServer2 鉴权 owner
                .setGroupIdOptional(GROUPID) //HiveServer2 鉴权groupId
                .setAuthzTypeOptional("PROJECT") //HiveServer2 鉴权类型
                .build();
        QueryContext queryContext = QueryContext.newBuilder()
                .setQueryPriority(QueryPriority.P2)
                .build();
        DataSource dataSource = DataSource.newBuilder()
                .setDataSourceType(dataSourceType)
                .build();

        Query query;
        if (dialect == Dialect.NATIVE) {
            query = Query.newBuilder().setNativeDsl(payload).build();
        } else {
            query = Query.newBuilder().setUnifiedDsl(payload).build();
        }
        ResponseSetting responseSetting = ResponseSetting.newBuilder()
                .setPageSizeOptional(-1)
                .setOrmClassOptional(QueryResult.class)
                .build();

        OneApiRequest request = OneApiRequest.newBuilder()
                .setQueryAccessControl(queryAccessControl)
                .setQueryContext(queryContext)
                .setDataSource(dataSource)
                .setQuery(query)
                .setResponseSetting(responseSetting)
                .build();
        return request;
    }


}
