package com.kuaishou.kwaishop.qa.risk.center.domain.combine.biz.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.OPERATOR_EMPTY_ERROR;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.biz.BaseEnumBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.enums.DateTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultCaseCallTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultCaseRelationshipTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultCaseSourceEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultDataSyncStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultPlanConformTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultPlanEnvEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultPlanProblemTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultPlanStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.enums.ProblemDetailTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.enums.ProblemLevelTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.enums.ProblemMissingTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.enums.CoverTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.enums.DbTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.enums.EffectiveEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.enums.LevelEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.enums.RiskTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.enums.VerifyTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfoDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfoRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-02
 */
@Service
@Slf4j
@Lazy
public class BaseEnumBizServiceImpl implements BaseEnumBizService {

    @Override
    public EnumInfoDTO getEnumInfo(EnumInfoRequest request) {
        if (StringUtils.isBlank(request.getOperator())) {
            throw new BizException(OPERATOR_EMPTY_ERROR);
        }
        return buildEnumInfoDTO();
    }

    private EnumInfoDTO buildEnumInfoDTO() {
        return EnumInfoDTO.newBuilder()
                .addAllEffectiveType(EffectiveEnum.buildEnumInfo())
                .addAllLevelType(LevelEnum.buildEnumInfo())
                .addAllRiskType(RiskTypeEnum.buildEnumInfo())
                .addAllVerifyType(VerifyTypeEnum.buildEnumInfo())
                .addAllCoverType(CoverTypeEnum.buildEnumInfo())
                .addAllDbType(DbTypeEnum.buildEnumInfo())
                .addAllEntityType(EntityTypeEnum.buildEnumInfo())
                .addAllDateType(DateTypeEnum.buildEnumInfo())
                .addAllProblemType(ProblemDetailTypeEnum.buildEnumInfo())
                .addAllProblemMissingType(ProblemMissingTypeEnum.buildEnumInfo())
                .addAllAccidentLevel(ProblemLevelTypeEnum.buildEnumInfo(ProblemDetailTypeEnum.ACCIDENT.getCode()))
                .addAllOnlineProblemLevel(ProblemLevelTypeEnum.buildEnumInfo(ProblemDetailTypeEnum.ONLINE_PROBLEM.getCode()))
                .addAllFaultCallType(FaultCaseCallTypeEnum.buildEnumInfo())
                .addAllFaultRelationshipType(FaultCaseRelationshipTypeEnum.buildEnumInfo())
                .addAllFaultCaseSourceType(FaultCaseSourceEnum.buildEnumInfo())
                .addAllFaultSyncStatus(FaultDataSyncStatusEnum.buildEnumInfo())
                .addAllFaultConformType(FaultPlanConformTypeEnum.buildEnumInfo())
                .addAllFaultProblemType(FaultPlanProblemTypeEnum.buildEnumInfo())
                .addAllFaultPlanStatus(FaultPlanStatusEnum.buildEnumInfo())
                .addAllFaultPlanEnv(FaultPlanEnvEnum.buildEnumInfo())
                .build();
    }
}
