package com.kuaishou.kwaishop.qa.risk.center.domain.customer.utils;

import static com.kuaishou.kwaishop.qa.risk.center.domain.customer.constants.Numbers.APP_ID;


import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.protobuf.ByteString;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.im.ImBasic.User;
import com.kuaishou.im.ImMessage;
import com.kuaishou.im.MessageProto;
import com.kuaishou.kwaishop.accesscontrol.rich.client.AccessControlCsOwnerIdQueryRichClient;
import com.kuaishou.kwaishop.accesscontrol.rich.client.AccessControlFlowFacade;
import com.kuaishou.kwaishop.accesscontrol.rich.client.dto.CsOwnerIdQueryRespDTO;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.config.KlinkConfig;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.model.KlinkLoginExtBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.CommodityDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.MsgSendC2BRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.OrderDTO;
import com.kuaishou.protobuf.immessage.KwaiMessageProto;
import com.kuaishou.protobuf.plateco.merchant.cs.message.MessageExtraDTO;
import com.kuaishou.zt.accesscontrol.AuthBizInfo;
import com.kuaishou.zt.accesscontrol.BizParam;
import com.kwai.link.KlinkPressureTestClient;
import com.kwai.link.data.LoginListener;
import com.kwai.link.data.PacketData;
import com.kwai.link.data.ResponseListener;
import com.kwai.link.session.LinkError;
import com.kwai.link.session.Session;

import lombok.extern.slf4j.Slf4j;

@Lazy
@Service
@Slf4j
public class CsSendUtil {


    public long uid2oid(long kwaiId) {
        try {
            log.info("uid:" + kwaiId);
            AccessControlCsOwnerIdQueryRichClient client = AccessControlFlowFacade.builder()
                    .setAuthBizInfo(AuthBizInfo.newBuilder()
                            .setBizParam(BizParam.newBuilder()
                                    .setKpn("ESHOP")
                                    .setSubBiz("seller")
                                    .build())
                            .build())
                    .buildCsOwnerIdQueryRichClient();
            CsOwnerIdQueryRespDTO csOwnerIdQueryRespDTO = client.queryOrgCsOwnerIdBySellerId(kwaiId);
            return csOwnerIdQueryRespDTO.getOwnerId();
        } catch (Exception ex) {
            return 0L;
        }
    }

    public void buyerSend(String runIp, int runPort, String tokenValue, String ssecurity, String runTid, MsgSendC2BRequest request) {

        String userId = request.getSenderId();
        String text = request.getText();
        OrderDTO order = request.getOrder();
        CommodityDTO commodity = request.getCommodity();
        int contentType = request.getContentType();
        Session session = KlinkPressureTestClient.newSessionTCP(runIp, runPort, userId, tokenValue, ssecurity);
        if (KlinkPressureTestClient.isRegistered(session)) {
            log.info("isRegistered is True, shutdown!");
            session.shutdown(LinkError.SHUTDOWN);
        }
        log.info("session.login start|ip:" + runIp + "|port:" + runPort + "|buyer:" + userId + "|targer:" + request.getTargetId());
        session.login(new LoginListener() {
            @Override
            public void onSuccess() {
                log.info("buyerSend start");
                User sender = User.newBuilder().setAppId(APP_ID).setUid(Long.parseLong(userId)).build();
                User targetUser = User.newBuilder().setAppId(APP_ID).setUid(Long.parseLong(runTid)).build();
                log.info("sender:" + sender + "｜taraget:" + targetUser);
                try {
                    if (contentType == 1) {
                        sendOrder(sender, targetUser, session, order);
                    } else if (contentType == 2) {
                        sendCommodity(sender, targetUser, session, commodity);
                    } else {
                        sendMes(sender, targetUser, text, session);
                    }
                } catch (Exception e) {
                    log.info(e.getMessage());
                }
            }

            @Override
            public void onFailed(int i) {
                log.info("commodity onFailed, shutdown｜" + i);
                session.shutdown(LinkError.SHUTDOWN);
            }
        });
    }

    public void serviceMarketbuyerSend(String runIp, int runPort, String tokenValue, String ssecurity,
                                       String userId, String text, String runTid) {
        Session session = KlinkPressureTestClient.newSessionTCP(runIp, runPort, userId, tokenValue, ssecurity);
        if (KlinkPressureTestClient.isRegistered(session)) {
            log.info("isRegistered is True, shutdown!");
            session.shutdown(LinkError.SHUTDOWN);
        }
        session.login(new LoginListener() {
            @Override
            public void onSuccess() {
                User sender = User.newBuilder().setAppId(APP_ID).setUid(Long.parseLong(userId)).build();
                User targetUser = User.newBuilder().setAppId(APP_ID).setUid(Long.parseLong(runTid)).build();
                try {
                    serviceMarketSendMes(sender, targetUser, text, session);
                } catch (Exception e) {
                    session.shutdown(LinkError.SHUTDOWN);
                    log.info(e.getMessage());
                }
            }

            @Override
            public void onFailed(int i) {
                log.info("commodity onFailed, shutdown!!!" + i);
                session.shutdown(LinkError.SHUTDOWN);
            }
        });
    }


    public void distributeSend(String runIp, int runPort, String tokenValue, String ssecurity,
                               String userId, String text, String runTid) {
        Session session = KlinkPressureTestClient.newSessionTCP(runIp, runPort, userId, tokenValue, ssecurity);
        if (KlinkPressureTestClient.isRegistered(session)) {
            log.info("isRegistered is True, shutdown!");
            session.shutdown(LinkError.SHUTDOWN);
        }
        session.login(new LoginListener() {
            @Override
            public void onSuccess() {
                User sender = User.newBuilder().setAppId(APP_ID).setUid(Long.parseLong(userId)).build();
                User targetUser = User.newBuilder().setAppId(APP_ID).setUid(Long.parseLong(runTid)).build();
                distributeSendMes(sender, targetUser, text, session);
            }

            @Override
            public void onFailed(int i) {
                log.info("commodity onFailed, shutdown!!!" + i);
                session.shutdown(LinkError.SHUTDOWN);
            }
        });
    }

    private void distributeSendMes(User sender, User reciver, String text, Session session) {

        ByteString content = MessageProto.Text.newBuilder().setText(text).build().toByteString();

        long clientSeqId = System.currentTimeMillis();
        ImMessage.Message message = ImMessage.Message.newBuilder()
                .setClientSeqId(clientSeqId)
                .setFromUser(sender)
                .setContentType(MessageProto.ImcMessageType.TEXT_VALUE)
                .setContent(content)
                .setNotCountUnread(false).setNotAutoCreateSession(false)
                .setToUser(reciver)
                .setTitle(text)
                .build();

        session.send("Message.Send", "MERCHANT_DISTRIBUTE_COOPERATE", message.toByteArray(), new ResponseListener() {
            @Override
            public void onResponseReceived(PacketData packetData) {
                log.info("onResponseReceived, shutdown" + ObjectMapperUtils.toJSON(packetData));
                session.shutdown(LinkError.SHUTDOWN);
            }

            @Override
            public void onResponseFailed(int errorCode, PacketData packetData) {
                log.info("onResponseFailed, shutdown!!!" + errorCode + "|" + ObjectMapperUtils.toJSON(packetData));
                session.shutdown(LinkError.SHUTDOWN);
            }
        });
    }

    private void serviceMarketSendMes(User sender, User reciver, String text, Session session) {

        ByteString content = MessageProto.Text.newBuilder().setText(text).build().toByteString();

        long clientSeqId = System.currentTimeMillis();
        ImMessage.Message message = ImMessage.Message.newBuilder()
                .setClientSeqId(clientSeqId)
                .setFromUser(sender)
                .setContentType(MessageProto.ImcMessageType.TEXT_VALUE)
                .setContent(content)
                .setNotCountUnread(false).setNotAutoCreateSession(false)
                .setToUser(reciver)
                .setTitle(text)
                .build();

        session.send("Message.Send", "KWAISHOP_SERVICE_MARKET", message.toByteArray(), new ResponseListener() {
            @Override
            public void onResponseReceived(PacketData packetData) {
                log.info("onResponseReceived, shutdown!!!" + ObjectMapperUtils.toJSON(packetData));
                session.shutdown(LinkError.SHUTDOWN);
            }

            @Override
            public void onResponseFailed(int errorCode, PacketData packetData) {
                log.info("onResponseFailed, shutdown!!!" + errorCode + "|" + ObjectMapperUtils.toJSON(packetData));
                session.shutdown(LinkError.SHUTDOWN);
            }
        });
    }

    public void buyerSend(String senderId, String targetId, String text, KlinkConfig klinkConfig, KlinkLoginExtBO extBO) {
        String runIp = klinkConfig.getIp();
        int runPort = klinkConfig.getPort();
        String tokenValue = extBO.getApist();
        String ssecurity = extBO.getSsecurity();
        Session session = KlinkPressureTestClient.newSessionTCP(runIp, runPort, senderId, tokenValue, ssecurity);
        if (KlinkPressureTestClient.isRegistered(session)) {
            log.info("isRegistered is True, shutdown!");
            session.shutdown(LinkError.SHUTDOWN);
        }
        log.info("session.login start|ip:" + runIp + "|port:" + runPort + "|buyer:" + senderId + "|targer:" + targetId);
        session.login(new LoginListener() {
            @Override
            public void onSuccess() {
                log.info("buyerSend start");
                User sender = User.newBuilder().setAppId(APP_ID).setUid(Long.parseLong(senderId)).build();
                User targetUser = User.newBuilder().setAppId(APP_ID).setUid(Long.parseLong(targetId)).build();
                log.info("sender:" + sender + "｜taraget:" + targetUser);
                try {
                    sendMes(sender, targetUser, text, session);
                } catch (Exception e) {
                    log.info(e.getMessage());
                }
            }

            @Override
            public void onFailed(int i) {
                log.info("C2B login onFailed, shutdown｜" + i);
                session.shutdown(LinkError.SHUTDOWN);
            }
        });
    }

    @SuppressWarnings("checkstyle:ParameterNumber")
    public void merchantSend(String runIp, int runPort, String tokenValue, String ssecurity,
                             String userId, String text, String runTid, boolean isStaff) {
        Session session = KlinkPressureTestClient.newSessionTCP(runIp, runPort, runTid, tokenValue, ssecurity);
        if (KlinkPressureTestClient.isRegistered(session)) {
            log.info("isRegistered is True, shutdown!");
            session.shutdown(LinkError.SHUTDOWN);
        }
        session.login(new LoginListener() {
            @Override
            public void onSuccess() {

                User sender = User.newBuilder().setAppId(APP_ID).setUid(Long.parseLong(runTid)).build();
                User targetUser = User.newBuilder().setAppId(APP_ID).setUid(Long.parseLong(userId)).build();
                log.info("merchantSend start");
                merchantSendMes(sender, targetUser, text, session, isStaff);
                log.info("merchantSend success");
            }

            @Override
            public void onFailed(int i) {
                log.info("merchantSend onFailed, shutdown!" + i);
                session.shutdown(LinkError.SHUTDOWN);
            }
        });
    }

    @SuppressWarnings("checkstyle:ParameterNumber")
    public void serviceMarketMerchantSend(String runIp, int runPort, String tokenValue, String ssecurity,
                                          String userId, String text, String runTid, boolean isStaff) {
        Session session = KlinkPressureTestClient.newSessionTCP(runIp, runPort, userId, tokenValue, ssecurity);
        if (KlinkPressureTestClient.isRegistered(session)) {
            log.info("isRegistered is True, shutdown!");
            session.shutdown(LinkError.SHUTDOWN);
        }
        session.login(new LoginListener() {
            @Override
            public void onSuccess() {

                User sender = User.newBuilder().setAppId(APP_ID).setUid(Long.parseLong(userId)).build();
                User targetUser = User.newBuilder().setAppId(APP_ID).setUid(Long.parseLong(runTid)).build();
                try {
                    serviceMarketSendMes(sender, targetUser, text, session, isStaff);
                } catch (Exception e) {
                    session.shutdown(LinkError.SHUTDOWN);
                    log.info(e.getMessage());
                }
            }

            @Override
            public void onFailed(int i) {
                log.info("commodity onFailed, shutdown!!!" + i);
                session.shutdown(LinkError.SHUTDOWN);
            }
        });
    }


    private void sendCommodity(User sender, User reciver, Session session, CommodityDTO commodityParam) {
        log.info("send commodity");
        KwaiMessageProto.Commodity commodity = KwaiMessageProto.Commodity.newBuilder().setItemId(commodityParam.getItemId())
                .setItemTitle(commodityParam.getItemTitle()).build();
        ByteString content = commodity.toByteString();
        long clientSeqId = System.currentTimeMillis();
        ImMessage.Message message = ImMessage.Message.newBuilder()
                .setClientSeqId(clientSeqId)
                .setFromUser(sender)
                .setContentType(KwaiMessageProto.MessageType.COMMODITY_VALUE)
                .setContent(content)
                .setNotCountUnread(false).setNotAutoCreateSession(false)
                .setToUser(reciver)
                .setTitle("商品")
                .setRealFromUser(sender)
                .build();
        session.send("Message.Send", "MERCHANT", message.toByteArray(), new ResponseListener() {
            @Override
            public void onResponseReceived(PacketData packetData) {
                log.info("onResponseReceived, shutdown!!!");
                session.shutdown(LinkError.SHUTDOWN);
            }

            @Override
            public void onResponseFailed(int errorCode, PacketData packetData) {
                log.info("onResponseFailed, shutdown!!!");
                session.shutdown(LinkError.SHUTDOWN);
            }
        });
    }

    private void sendMes(User sender, User reciver, String text, Session session) {
        log.info("send text:" + text);
        ByteString content = MessageProto.Text.newBuilder().setText(text).build().toByteString();

        long clientSeqId = System.currentTimeMillis();
        ImMessage.Message message = ImMessage.Message.newBuilder()
                .setClientSeqId(clientSeqId)
                .setFromUser(sender)
                .setContentType(MessageProto.ImcMessageType.TEXT_VALUE)
                .setContent(content)
                .setNotCountUnread(false).setNotAutoCreateSession(false)
                .setToUser(reciver)
                .setTitle(text)
                .build();

        log.info("message:" + message.toString());
        session.send("Message.Send", "MERCHANT", message.toByteArray(), new ResponseListener() {
            @Override
            public void onResponseReceived(PacketData packetData) {
                log.info("onResponseReceived, shutdown!" + ObjectMapperUtils.toJSON(packetData));
                session.shutdown(LinkError.SHUTDOWN);
            }

            @Override
            public void onResponseFailed(int errorCode, PacketData packetData) {
                log.info("onResponseFailed, shutdown!" + errorCode + "|" + ObjectMapperUtils.toJSON(packetData));
                session.shutdown(LinkError.SHUTDOWN);
            }
        });
    }

    private void sendOrder(User sender, User reciver, Session session, OrderDTO orderParam) {
        log.info("send order");
        KwaiMessageProto.Order order = KwaiMessageProto.Order.newBuilder().setOrderId(orderParam.getOrderId())
                .setItemTitle(orderParam.getItemTitle()).build();
        ByteString content = order.toByteString();
        long clientSeqId = System.currentTimeMillis();
        ImMessage.Message message = ImMessage.Message.newBuilder()
                .setClientSeqId(clientSeqId)
                .setFromUser(sender)
                .setContentType(KwaiMessageProto.MessageType.ORDER_VALUE)
                .setContent(content)
                .setNotCountUnread(false).setNotAutoCreateSession(false)
                .setToUser(reciver)
                .setTitle("订单")
                .setRealFromUser(sender)
                .build();
        session.send("Message.Send", "MERCHANT", message.toByteArray(), new ResponseListener() {
            @Override
            public void onResponseReceived(PacketData packetData) {
                session.shutdown(LinkError.SHUTDOWN);
                log.info("onResponseReceived, shutdown!!!");
            }

            @Override
            public void onResponseFailed(int errorCode, PacketData packetData) {
                session.shutdown(LinkError.SHUTDOWN);
                log.info("onResponseFailed, shutdown!!!");
            }
        });
    }


    private void merchantSendMes(User sender, User reciver, String text, Session session, boolean isStaff) {
        ByteString content = MessageProto.Text.newBuilder().setText(text).build().toByteString();
        ByteString extra;
        if (isStaff) {
            extra = MessageExtraDTO.newBuilder().setRealFromRole(3).setDevice(1).build().toByteString();
        } else {
            extra = MessageExtraDTO.newBuilder().setRealFromRole(1).setDevice(1).build().toByteString();
        }
        ByteString extraInfo = com.kuaishou.protobuf.message.MessageProto.ExtraInfo
                .newBuilder().putBizExtras("MERCHANT", extra).build().toByteString();

        long clientSeqId = System.currentTimeMillis();
        ImMessage.Message message = ImMessage.Message.newBuilder()
                .setClientSeqId(clientSeqId)
                .setFromUser(sender)
                .setContentType(MessageProto.ImcMessageType.TEXT_VALUE)
                .setContent(content)
                .setNotCountUnread(false).setNotAutoCreateSession(false)
                .setToUser(reciver)
                .setTitle(text)
                .setExtra(extraInfo)
                .build();

        log.info("message:" + message.toString());
        session.send("Message.Send", "MERCHANT", message.toByteArray(), new ResponseListener() {
            @Override
            public void onResponseReceived(PacketData packetData) {
                log.info("onResponseReceived, success!!!");
                session.shutdown(LinkError.SHUTDOWN);
            }

            @Override
            public void onResponseFailed(int errorCode, PacketData packetData) {
                log.info("onResponseFailed, shutdown!!!");
                session.shutdown(LinkError.SHUTDOWN);
            }
        });
    }

    private void serviceMarketSendMes(User sender, User reciver, String text, Session session, boolean isStaff) {

        ByteString content = MessageProto.Text.newBuilder().setText(text).build().toByteString();
        ByteString extra;
        if (isStaff) {
            extra = MessageExtraDTO.newBuilder().setRealFromRole(3).setDevice(1).build().toByteString();
        } else {
            extra = MessageExtraDTO.newBuilder().setRealFromRole(1).setDevice(1).build().toByteString();
        }
        ByteString extraInfo = com.kuaishou.protobuf.message.MessageProto.ExtraInfo
                .newBuilder().putBizExtras("MERCHANT", extra).build().toByteString();

        long clientSeqId = System.currentTimeMillis();
        ImMessage.Message message = ImMessage.Message.newBuilder()
                .setClientSeqId(clientSeqId)
                .setFromUser(sender)
                .setContentType(MessageProto.ImcMessageType.TEXT_VALUE)
                .setContent(content)
                .setNotCountUnread(false).setNotAutoCreateSession(false)
                .setToUser(reciver)
                .setTitle(text)
                .setExtra(extraInfo)
                .build();

        log.info("message:" + message.toString());
        session.send("Message.Send", "KWAISHOP_SERVICE_MARKET", message.toByteArray(), new ResponseListener() {
            @Override
            public void onResponseReceived(PacketData packetData) {
                log.info("onResponseReceived, shutdown!!!" + ObjectMapperUtils.toJSON(packetData));
                session.shutdown(LinkError.SHUTDOWN);
            }

            @Override
            public void onResponseFailed(int errorCode, PacketData packetData) {
                log.info("onResponseFailed, shutdown!!!" + ObjectMapperUtils.toJSON(packetData));
                session.shutdown(LinkError.SHUTDOWN);
            }
        });
    }
}

