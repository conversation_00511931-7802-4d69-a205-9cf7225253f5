package com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.cs.biz.sdk.client.KwaishopCsBizSessionClient;
import com.kuaishou.kwaishop.cs.biz.session.protobuf.CloseSessionRequestDTO;
import com.kuaishou.kwaishop.cs.biz.session.protobuf.CloseSessionResponseDTO;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.MsgService;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.SessionService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.CloseSessionRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.CloseSessionResponse;

import lombok.extern.slf4j.Slf4j;

@Service
@Lazy
@Slf4j
public class SessionServiceImpl implements SessionService {

    @Autowired
    private MsgService msgService;

    @Override
    public CloseSessionResponse closeSession(CloseSessionRequest request) {
        CloseSessionResponse.Builder res = CloseSessionResponse.newBuilder();
        try {
            if (StringUtils.isNotBlank(request.getBuyerId()) && StringUtils.isNotBlank(request.getOid())) {
                long buyerId = Long.parseLong(request.getBuyerId());
                long oid = Long.parseLong(request.getOid());
                CloseSessionRequestDTO closeSessionRequestDTO = CloseSessionRequestDTO.newBuilder()
                        .setBuyerId(buyerId).setLoginUserId(oid).build();
                CloseSessionResponseDTO closeSessionResponseDTO = KwaishopCsBizSessionClient.closeSession(closeSessionRequestDTO);
                return res.setErrorMsg(closeSessionResponseDTO.getErrorMsg()).setResult(closeSessionResponseDTO.getResult()).build();
            } else {
                return res.setErrorMsg("买家id和卖家id不能未空|" + request.getBuyerId() + "｜" + request.getOid()).build();
            }
        } catch (Exception ex) {
            log.info("异常｜" + ex);
            return res.setErrorMsg(ex.getMessage()).build();
        }
    }

}
