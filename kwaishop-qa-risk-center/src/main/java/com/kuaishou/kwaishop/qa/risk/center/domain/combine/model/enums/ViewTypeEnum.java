package com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-21
 */
public enum ViewTypeEnum {

    MEMBER_VIEW(1, "成员视角"),
    TEAM_VIEW(2, "团队视角"),
    ALL_VIEW(3, "全员视角"),
    ;

    private final Integer code;
    private final String desc;

    ViewTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ViewTypeEnum of(Integer code) {
        for (ViewTypeEnum viewTypeEnum: values()) {
            if (viewTypeEnum.getCode().equals(code)) {
                return viewTypeEnum;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo() {
        List<EnumInfo> res = new ArrayList<>();
        for (ViewTypeEnum typeEnum: values()) {
            EnumInfo enumInfo = EnumInfo.newBuilder()
                    .setValue(typeEnum.getCode())
                    .setDesc(typeEnum.getDesc())
                    .build();
            res.add(enumInfo);
        }
        return res;
    }
}
