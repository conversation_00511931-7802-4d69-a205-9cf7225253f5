package com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-18
 */
public enum DateTypeEnum {

    DAY(1, "日维度"),
    WEEK(2, "周维度"),
    MONTH(3, "月维度"),
    ;

    private final Integer code;
    private final String desc;

    DateTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static DateTypeEnum of(Integer code) {
        for (DateTypeEnum typeEnum: values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo() {
        List<EnumInfo> res = new ArrayList<>();
        for (DateTypeEnum typeEnum: values()) {
            EnumInfo enumInfo = EnumInfo.newBuilder()
                    .setValue(typeEnum.getCode())
                    .setDesc(typeEnum.getDesc())
                    .build();
            res.add(enumInfo);
        }
        return res;
    }
}
