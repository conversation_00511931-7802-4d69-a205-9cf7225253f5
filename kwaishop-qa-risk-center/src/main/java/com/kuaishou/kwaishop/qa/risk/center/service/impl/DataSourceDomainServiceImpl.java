package com.kuaishou.kwaishop.qa.risk.center.service.impl;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataQueryDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.biz.DataSourceBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CreateDataSourceRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CreateDataSourceResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CreateQueryExeRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CreateQueryExeResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DeleteDataSourceRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DeleteDataSourceResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.KrpcDataSourceDomainServiceGrpc.DataSourceDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataSourceByIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataSourceByIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataSourceLikeNameRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataSourceLikeNameResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataSourceRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataSourceResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryPageDataSourceRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryPageDataSourceResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.UpdateDataSourceRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.UpdateDataSourceResponse;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-13
 */

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class DataSourceDomainServiceImpl extends DataSourceDomainServiceImplBaseV2 {

    @Autowired
    private DataSourceBizService dataSourceBizService;


    @Override
    public CreateDataSourceResponse createDataSource(CreateDataSourceRequest request) {
        log.info("[DataSourceDomainServiceImpl] createDataSource request: {}", ProtobufUtil.protoToJsonString(request));
        return dataSourceBizService.createDataSource(request);
    }

    @Override
    public UpdateDataSourceResponse updateDataSource(UpdateDataSourceRequest request) {
        log.info("[DataSourceDomainServiceImpl] updateDataSource request: {}", ProtobufUtil.protoToJsonString(request));
        return dataSourceBizService.updateDataSource(request);
    }

    @Override
    public QueryPageDataSourceResponse queryPageDataSource(QueryPageDataSourceRequest request) {
        return dataSourceBizService.queryPageDataSource(request);
    }

    @Override
    public QueryDataSourceResponse queryDataSource(QueryDataSourceRequest request) {
        return dataSourceBizService.queryDataSource(request);
    }

    @Override
    public DeleteDataSourceResponse deleteDataSource(DeleteDataSourceRequest request) {
        return dataSourceBizService.deleteDataSource(request);
    }

    @Override
    public QueryDataSourceByIdResponse queryDataSourceById(QueryDataSourceByIdRequest request) {
        return dataSourceBizService.queryDataSourceById(request);
    }

    @Override
    public QueryDataSourceLikeNameResponse queryDataSourceLikeName(QueryDataSourceLikeNameRequest request) {
        return dataSourceBizService.queryDataSourceLikeName(request);
    }

    @Override
    public CreateQueryExeResponse createQueryExe(CreateQueryExeRequest request) {
        DataQueryDO dataQueryDO = dataSourceBizService.createQueryExe(request);
        try {
            return CreateQueryExeResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setDataQueryExeId(dataQueryDO.getId())
                    .build();
        } catch (Exception e) {
            return CreateQueryExeResponse.newBuilder().setResult(0)
                    .setErrorMsg(e.getMessage())
                    .setDataQueryExeId(0)
                    .build();
        }


    }
}
