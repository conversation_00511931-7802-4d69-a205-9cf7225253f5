package com.kuaishou.kwaishop.qa.risk.center.domain.openapi.service;

import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskStringConfigKey.tokenUrl;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskStringConfigKey.userInfoUrl;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.ApplyHandler;
import com.kuaishou.kwaishop.qa.risk.center.utils.http.HttpUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.http.KessHttpUtils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2025/2/18 14:24
 * @注释 获取员工信息
 */
@Slf4j
@Service
public class EmployeeRoleService {


    @Autowired
    private ApplyHandler applyHandler;

    private static final String APP_KEY = "386925f6-03a4-44d1-93a9-c0402f53960d";

    private static final String SECRET_KEY = "36fd168efffeda8e719fde99b95a7473";


    public String getEmployee(String user) {
        if (!isValid(user)) {
            return null;
        }
        try {
            //获取授权token
            String accessToken = getAccessToken();
            user = user.trim();
            String url = userInfoUrl.get().replace("{user}", user);
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("Authorization", MessageFormat.format("Bearer {0}", accessToken));
            headerMap.put("Content-Type", "application/json");

            String response = KessHttpUtils.get(url, "", headerMap);

            Map<String, Object> responseMap = ObjectMapperUtils.fromJson(response);
            log.info("userInfo response: {}", ObjectMapperUtils.toJSON(responseMap));
            return ObjectMapperUtils.toJSON(responseMap);
        } catch (IOException e) {
            log.error("getEmployee error");
        }

        return "";
    }

    public boolean isValid(String str) {
        String regex = "^(?:[a-zA-Z]+[0-9]*|[a-zA-Z]+)$";
        Pattern pattern = Pattern.compile(regex);
        return pattern.matcher(str).matches();
    }


    public String getAccessToken() throws IOException {
        Map<String, String> params = new HashMap<>();
        params.put("appKey", APP_KEY);
        params.put("secretKey", SECRET_KEY);
        HttpUtils httpUtils = new HttpUtils();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        try {
            Response response = httpUtils.post(tokenUrl.get(), headers, params);
            log.info("getAccessToken response: {}", response);
            assert response.body() != null;
            String responseString = response.body().string();
            Map<String, Object> responseMap = ObjectMapperUtils.fromJson(responseString);
            if ((int) responseMap.get("code") != 0) {
                return "";
            }
            Map<String, Object> resultMap = (Map<String, Object>) responseMap.get("result");
            return (String) resultMap.get("accessToken");
        } catch (IOException ex) {
            throw new IOException("更新token失败");
        }
    }


}
