package com.kuaishou.kwaishop.qa.risk.center.domain.account.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.AUTH_GET_ERROR;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.gson.JsonObject;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.UserAuthDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAuthDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.UserAuthConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAuthBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserAuthService;
import com.kuaishou.kwaishop.qa.risk.center.utils.JsonUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.kess.KessUtil;
import com.kuaishou.kwaishop.qa.risk.center.utils.kess.dto.KessRun;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-22
 */
@Lazy
@Service
@Slf4j
public class UserAuthServiceImpl implements UserAuthService {

    @Autowired
    private UserAuthDAO userAuthDAO;

    @Autowired
    private UserAuthConvert userAuthConvert;

    private static final String KWAISHOP_DISTRIBUTE_OPERATION_SERVICE = "kwaishop-distribute-operation-service";

    private static final String DISTRIBUTE_QUERY_ROLE_INFO = "DistributeQueryRoleInfo";

    public static final String KWAISHOP_APOLLO_PINOCCHIO_CENTER = "kwaishop-apollo-pinocchio-center";

    public static final String GET_ALL_SCORE_FOR_BUSINESS = "GetAllScoreForBusiness";

    /**
     * 获取所有权限的入口！！！
     * @param userId
     * @return
     */
    @Override
    public UserAuthDO queryAllUserAuth(Long userId) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("id", userId);
        jsonObject.addProperty("type_code", 0);
        jsonObject.addProperty("operator", "qa-risk-user-account");
        KessRun kessrun = KessRun.builder()
                .jsonData(JsonUtils.jsonObjectToMap(jsonObject))
                .rpcName(KWAISHOP_DISTRIBUTE_OPERATION_SERVICE)
                .rpcMethod(DISTRIBUTE_QUERY_ROLE_INFO)
                .build();

        try {
            String run = KessUtil.run(kessrun);
            log.info("查询用户权限 = {}", run);
            JsonObject authResults = JsonUtils.stringToJson(run, JsonObject.class);
            if (!authResults.getAsJsonObject("base_resp_info").get("resp_code_desc").getAsString().equals("SUCCESS")) {
                throw new BizException(AUTH_GET_ERROR);
            }
            UserAuthDO userAuthDO = userAuthConvert.parseUserAuth(authResults.get("data").getAsString());
            userAuthDO.setUserId(userId);
            // 获取双分
            queryScoreByUserId(String.valueOf(userId), userAuthDO);
            // 获取保证金
            queryDepositByUserId(String.valueOf(userId), userAuthDO);
            // 获取店铺星级
            getShopStarInfo(userId, userAuthDO);
            // 获取商家信息，主要是用户name
            getSellerDetailInfo(String.valueOf(userId), userAuthDO);
            return userAuthDO;
        } catch (IOException e) {
            log.error("获取乐高服务错误, {}", e.getMessage());
            throw new BizException(SERVER_ERROR);
        }
    }

    /**
     * 获取用户信息info
     * @param sellerId
     * @return
     */
    public void getSellerDetailInfo(String sellerId, UserAuthDO userAuthDO) {
        Map<String, Object> para = new HashMap<>();
        para.put("seller_id", sellerId);

        KessRun kessRun = KessRun.builder()
                .jsonData(para)
                .rpcMethod("GetSellerDetailInfo")
                .rpcName("kwaishop-shop-center")
                .build();

        try {
            String response = KessUtil.run(kessRun);
            log.info("获取商家信息, {}", response);
            Map<String, Object> shopInfo = ObjectMapperUtils.fromJSON(response, Map.class);
            if (shopInfo.containsKey("seller_detail_info")) {
                Map<String, Object> detailInfo = (Map<String, Object>) shopInfo.get("seller_detail_info");
                Map<String, Object> darenInfo = (Map<String, Object>) detailInfo.get("daren_info");
                //赋值name
                userAuthDO.setSellerName(darenInfo.get("name").toString());
            }
        } catch (Exception e) {
            log.error("获取商家信息失败，{}", e.getMessage());
            throw new BizException(SERVER_ERROR);
        }
    }

    @Override
    public String queryScoreByUserId(String userId, UserAuthDO userAuthDO) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("seller_id", userId);

        KessRun kessRun = KessRun.builder()
                .jsonData(JsonUtils.jsonObjectToMap(jsonObject))
                .rpcName(KWAISHOP_APOLLO_PINOCCHIO_CENTER)
                .rpcMethod(GET_ALL_SCORE_FOR_BUSINESS)
                .build();
        try {
            String run = KessUtil.run(kessRun);
            log.info("获取双分数据request = {}, response = {}", userId, run);
            JsonObject data = JsonUtils.stringToJson(run, JsonObject.class).getAsJsonObject("data");
            JsonObject shopScoreInfo = data.getAsJsonObject("shop_score_info");

            String shopScore = shopScoreInfo.getAsJsonObject("score_summary").get("score").getAsString();
            String promoterScore = data.getAsJsonObject("master_score_info").getAsJsonObject("score_summary").get("score").getAsString();

            userAuthDO.setPromoterScore(promoterScore);
            userAuthDO.setShopScore(shopScore);
        } catch (Exception e) {
            log.error("获取双分失败，{}", e.getMessage());
            throw new BizException(SERVER_ERROR);
        }
        return "0";
    }

    @Override
    public void queryDepositByUserId(String userId, UserAuthDO userAuthDO) {

        String depositRes = getDepositInfo(Long.valueOf(userId));
        Map<String, Object> depositInfo = ObjectMapperUtils.fromJSON(depositRes, Map.class);
        if (depositInfo.containsKey("if_deposit_balance_enough") && depositInfo.containsKey("if_frozen")) {
            if (depositInfo.get("if_deposit_balance_enough").equals(true) && depositInfo.get("if_frozen").equals(false)) {
                userAuthDO.setDeposit(1);
            } else {
                userAuthDO.setDeposit(0);
            }
        }
    }

    public String getDepositInfo(Long sellerId) {

        Map<String, Object> para = new HashMap<>();
        para.put("user_id", sellerId);
        para.put("security_deposit_type", 1);

        KessRun kessRun = KessRun.builder().jsonData(para).rpcMethod("GetDepositInfo")
                .rpcName("grpc_kwaishopFundsAccountService").build();
        String s = "";
        try {
            s = KessUtil.run(kessRun);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return s;
    }

    public void getShopStarInfo(Long sellerId, UserAuthDO userAuthDO) {

        Map<String, Object> para = new HashMap<>();
        para.put("user_id", sellerId);
        para.put("token", "");

        KessRun kessRun = KessRun.builder().jsonData(para).rpcMethod("GetShopStarInfoForSeller")
                .rpcName("kwaishop-apollo-pinocchio-center").build();
        String s = "";
        try {
            s = KessUtil.run(kessRun);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        String shopStarRes = s;
        Map<String, Object> shopStarInfo = ObjectMapperUtils.fromJSON(shopStarRes, Map.class);
        if (shopStarInfo.containsKey("stars")) {
            userAuthDO.setShopRating(shopStarInfo.get("stars").toString());
        }
    }

    @Override
    public String queryBail(Long userId) {
        return null;
    }

    @Override
    public void openAuth() {

    }

    @Override
    public void insertAuthInfo(Long userId) {
        // 先去查权限表里有没有数据
        List<UserAuthDO> userAuthDOLists = queryUserAuthRecords(UserAuthBO.builder()
                .userId(userId)
                .build());
        if (!userAuthDOLists.isEmpty()) {
            // 如果
            return;
        }
        // 先根据userId去查询
        UserAuthDO userAuthDO = queryAllUserAuth(userId);
        // 查到后插入表中
        userAuthDAO.insert(userAuthDO);
    }

    @Override
    public int updateAuthInfo(Long userId) {
        List<UserAuthDO> userAuthDOLists = queryUserAuthRecords(UserAuthBO.builder()
                .userId(userId)
                .build());
        if (userAuthDOLists.isEmpty()) {
            // 不存在数据则进行插入
            userAuthDAO.insert(queryAllUserAuth(userId));
            return 1;
        }
        // 先去根据权限info
        UserAuthDO userAuthDO = queryAllUserAuth(userId);
        userAuthDO.setId(userAuthDOLists.get(0).getId());
        return userAuthDAO.updateSelectiveById(userAuthDO);
    }

    @Override
    public List<UserAuthDO> queryUserAuthRecords(UserAuthBO userAuthBO) {
        return userAuthDAO.queryList(userAuthBO);
    }

    @Override
    public int update(UserAuthDO userAuthDO) {
        return userAuthDAO.updateSelectiveById(userAuthDO);
    }
}
