package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.detail.KspayDetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsRiskFeatureViewBranchDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-01
 */
public interface FundsRiskFeatureViewBranchDAO extends KspayDetailDAO<FundsRiskFeatureViewBranchDO, CombineQueryParam> {
    long insert(FundsRiskFeatureViewBranchDO fundsRiskFeatureViewBranchDO);
    int updateSelectiveById(FundsRiskFeatureViewBranchDO fundsRiskFeatureViewBranchDO);

}
