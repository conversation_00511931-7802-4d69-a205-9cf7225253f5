package com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz.impl;

import static com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.KspayQaRiskMapConfig.BIZ_SECOND_DEPARTMENT_MAP;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import com.github.phantomthief.tuple.ThreeTuple;
import com.kuaishou.infra.boot.jdbc.datasource.TransactionalDataSourceRouting;
import com.kuaishou.intown.json.JSON;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.utils.SnowflakeIdWorker;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.GitToDepartmentMappingDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.GitToDepartmentMappingDAONew;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FirstDepartmentMappingDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.GitToDepartmentMappingDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.GitToDepartmentMappingParamDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.GitToDepartmentMappingQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.SecondDepartmentMappingDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz.KspayGitToDepartmentMappingService;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.ral.client.RiskPlatformApiClient;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.FirstDepartmentInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.GitToDepartmentMappingParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.GitToMappingInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryGitToMappingRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryGitToMappingResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.SecondDepartmentInfo;
import com.kuaishou.kwaishop.qa.risk.center.utils.JsonUtils;

import lombok.extern.slf4j.Slf4j;

@Service
@Lazy
@Slf4j
public class KspayGitToDepartmentMappingServiceImpl implements KspayGitToDepartmentMappingService {
    @Autowired
    private GitToDepartmentMappingDAO gitToDepartmentMappingDAO;
    @Autowired
    private GitToDepartmentMappingDAONew gitToDepartmentMappingDAONew;
    private final RiskPlatformApiClient riskPlatformApiClient;
    private final NamedParameterJdbcTemplate jdbcTemplate;
    private int defaultPageSize = 1000;
    public KspayGitToDepartmentMappingServiceImpl(RiskPlatformApiClient riskPlatformApiClient, NamedParameterJdbcTemplate jdbcTemplate) {
        this.riskPlatformApiClient = riskPlatformApiClient;
        this.jdbcTemplate = jdbcTemplate;
    }

    @Override
    public void insertOrUpdateGitToDepartmentMapping(GitToDepartmentMappingParam gitToDepartmentMappingParam) {
        try {
            SnowflakeIdWorker idWorker = new SnowflakeIdWorker(0, 0);
            Long id = idWorker.nextId();
            GitToDepartmentMappingParamDO paramDO = convertToDO(id, gitToDepartmentMappingParam);
            log.info("开始处理 GitToDepartmentMapping 记录，paramDO: {}", JsonUtils.toJsonString(paramDO));

            GitToDepartmentMappingParamDO existingMapping =
                    gitToDepartmentMappingDAO.queryMappingByProjectId(paramDO.getGitProjectId());

            if (existingMapping == null) {
                gitToDepartmentMappingDAO.insertMapping(paramDO);
            } else {
                gitToDepartmentMappingDAO.updateMapping(paramDO);
            }
        } catch (Exception e) {
            log.error("处理 GitToDepartmentMapping 记录时发生异常", e);
            throw new BizException(SERVER_ERROR, "处理 GitToDepartmentMapping 记录时发生异常");
        }
    }

    public QueryGitToMappingResponse queryGitToMapping(QueryGitToMappingRequest request) {
        // 调用 Platform-API 获取响应
        log.info("[KspayGitDepartmentMappingServiceImpl] Calling getGitToDepartmentMapping with request: {}", JSON.toJSONString(request));
        QueryGitToMappingResponse queryGitToMappingResponse = riskPlatformApiClient.getGitToDepartmentMapping(request);

        if (queryGitToMappingResponse == null) {
            return QueryGitToMappingResponse.newBuilder()
                    .setResult(-1)
                    .setErrMsg("Internal Server Error")
                    .build();
        }

        return queryGitToMappingResponse;
    }

    private GitToDepartmentMappingParamDO convertToDO(Long id, GitToDepartmentMappingParam gitToDepartmentMappingParam) {
        GitToDepartmentMappingParamDO paramDO = new GitToDepartmentMappingParamDO();
        paramDO.setId(id);
        paramDO.setFirstDepartmentId(
                gitToDepartmentMappingParam.getFirstDepartmentId() != 0
                        ? gitToDepartmentMappingParam.getFirstDepartmentId() : null
        );
        paramDO.setSecondDepartmentId(
                gitToDepartmentMappingParam.getSecondDepartmentId() != 0
                        ? gitToDepartmentMappingParam.getSecondDepartmentId() : null
        );
        paramDO.setThirdDepartmentId(
                gitToDepartmentMappingParam.getThirdDepartmentId() != 0
                        ? gitToDepartmentMappingParam.getThirdDepartmentId() : null
        );
        paramDO.setGitProjectId(gitToDepartmentMappingParam.getGitProjectId());
        paramDO.setFirstDepartmentName(
                !gitToDepartmentMappingParam.getFirstDepartmentName().isEmpty()
                        ? gitToDepartmentMappingParam.getFirstDepartmentName() : null
        );
        paramDO.setSecondDepartmentName(
                !gitToDepartmentMappingParam.getSecondDepartmentName().isEmpty()
                        ? gitToDepartmentMappingParam.getSecondDepartmentName() : null
        );
        paramDO.setThirdDepartmentName(
                !gitToDepartmentMappingParam.getThirdDepartmentName().isEmpty()
                        ? gitToDepartmentMappingParam.getThirdDepartmentName() : null
        );
        paramDO.setGitName(
                !gitToDepartmentMappingParam.getGitName().isEmpty()
                        ? gitToDepartmentMappingParam.getGitName() : null
        );
        paramDO.setGitDescription(
                !gitToDepartmentMappingParam.getGitDescription().isEmpty()
                        ? gitToDepartmentMappingParam.getGitDescription() : null
        );
        paramDO.setCreator(
                !gitToDepartmentMappingParam.getCreator().isEmpty()
                        ? gitToDepartmentMappingParam.getCreator() : null
        );
        paramDO.setUpdater(
                !gitToDepartmentMappingParam.getUpdater().isEmpty()
                        ? gitToDepartmentMappingParam.getUpdater() : null
        );
        paramDO.setCreateTime(
                gitToDepartmentMappingParam.getCreateTime() != 0
                        ? gitToDepartmentMappingParam.getCreateTime() : null
        );
        paramDO.setUpdateTime(
                gitToDepartmentMappingParam.getUpdateTime() != 0
                        ? gitToDepartmentMappingParam.getUpdateTime() : null
        );

        return paramDO;
    }


    @Override
    @TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
    public ThreeTuple<List<GitToMappingInfo>,
            List<FirstDepartmentInfo>,
            List<SecondDepartmentInfo>> queryGitToMappings(QueryGitToMappingRequest request) {
        GitToDepartmentMappingQueryCondition condition = buildQueryCondition(request);
        log.info("[GitToDepartmentMappingServiceImpl] gitToDepartmentMappingQueryCondition: {}", JSON.toJSONString(condition));

        List<GitToMappingInfo> gitToMappingInfoList = queryGitToMappingInfo(condition);
        List<FirstDepartmentInfo> firstDepartmentInfoList = queryFirstDepartmentInfo(condition);
        List<SecondDepartmentInfo> secondDepartmentInfoList = querySecondDepartmentInfo(condition);
        return new ThreeTuple<>(gitToMappingInfoList, firstDepartmentInfoList, secondDepartmentInfoList);
    }


    @TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
    public List<GitToMappingInfo> queryGitToMappingInfo(GitToDepartmentMappingQueryCondition condition) {
        List<GitToDepartmentMappingDO> gitToDepartmentMappingDOList = gitToDepartmentMappingDAONew.queryGitToDepartmentMappingInfo(
                condition,
                (condition.getPageNo() - 1) * condition.getPageSize(),
                condition.getPageSize(),
                defaultPageSize,
                jdbcTemplate
        );
        log.info("[GitToDepartmentMappingServiceImpl] gitToDepartmentMappingDOList: {}", JSON.toJSONString(gitToDepartmentMappingDOList));

        // 获取配置
        Map<String, Map<String, Object>> bizSpaceMap = BIZ_SECOND_DEPARTMENT_MAP.get();

        // 过滤出 active 为 true 的部门
        List<GitToMappingInfo> filteredGitToMappingInfoList = gitToDepartmentMappingDOList.stream()
                .filter(mapping -> {
                    Map<String, Object> departmentConfig = bizSpaceMap.get(String.valueOf(mapping.getSecondDepartmentId()));
                    if (departmentConfig != null) {
                        Object isActive = departmentConfig.get("active");
                        return isActive != null && Boolean.TRUE.equals(isActive);
                    }
                    return false;
                })
                .map(this::convertToGitToMappingInfo)
                .collect(Collectors.toList());
        log.info("[GitToDepartmentMappingServiceImpl] filteredGitToMappingInfoList: {}", JSON.toJSONString(filteredGitToMappingInfoList));

        return filteredGitToMappingInfoList;
    }

    @TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
    public List<FirstDepartmentInfo> queryFirstDepartmentInfo(GitToDepartmentMappingQueryCondition condition) {
        List<FirstDepartmentMappingDO> firstDepartmentMappingDOList =
                gitToDepartmentMappingDAONew.queryDistinctFirstDepartmentInfo(condition, jdbcTemplate);
        log.info("[GitToDepartmentMappingServiceImpl] FirstDepartmentMappingDOList: {}", JSON.toJSONString(firstDepartmentMappingDOList));

        List<FirstDepartmentInfo> firstDepartmentInfos;
        try {
            firstDepartmentInfos = firstDepartmentMappingDOList.stream()
                    .map(this::convertToFirstDepartmentInfo)
                    .collect(Collectors.toList());
            log.info("[GitToDepartmentMappingServiceImpl] firstDepartmentInfos: {}", JSON.toJSONString(firstDepartmentInfos));
        } catch (Exception e) {
            log.error("[GitToDepartmentMappingServiceImpl] Error converting firstDepartmentMappingDOList to firstDepartmentInfos: {}",
                    JSON.toJSONString(firstDepartmentMappingDOList), e);
            return null;
        }
        return firstDepartmentInfos;
    }

    @TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
    public List<SecondDepartmentInfo> querySecondDepartmentInfo(GitToDepartmentMappingQueryCondition condition) {
        List<SecondDepartmentMappingDO> secondDepartmentMappingDOList =
                gitToDepartmentMappingDAONew.queryDistinctSecondDepartmentInfo(condition, jdbcTemplate);
        log.info("[GitToDepartmentMappingServiceImpl] SecondDepartmentMappingDOList: {}", JSON.toJSONString(secondDepartmentMappingDOList));

        // 获取配置
        Map<String, Map<String, Object>> bizSpaceMap = BIZ_SECOND_DEPARTMENT_MAP.get();

        // 过滤出 active 为 true 的部门
        List<SecondDepartmentInfo> filteredSecondDepartmentInfoList = secondDepartmentMappingDOList.stream()
                .filter(mapping -> {
                    Map<String, Object> departmentConfig = bizSpaceMap.get(String.valueOf(mapping.getSecondDepartmentId()));
                    if (departmentConfig != null) {
                        Object isActive = departmentConfig.get("active");
                        return isActive != null && Boolean.TRUE.equals(isActive);
                    }
                    return false;
                })
                .map(this::convertToSecondDepartmentInfo)
                .collect(Collectors.toList());
        log.info("[GitToDepartmentMappingServiceImpl] filteredSecondDepartmentInfoList: {}",
                JSON.toJSONString(filteredSecondDepartmentInfoList));

        return filteredSecondDepartmentInfoList;
    }

    private GitToMappingInfo convertToGitToMappingInfo(GitToDepartmentMappingDO doObj) {
        if (doObj == null) {
            throw new IllegalArgumentException("doObj cannot be null");
        }

        log.debug("Converting GitToDepartmentMappingDO to GitToMappingInfo: {}", doObj);

        GitToMappingInfo info = GitToMappingInfo.newBuilder()
                .setGitProjectId(Optional.ofNullable(doObj.getGitProjectId()).orElse(0L))
                .setGitName(Optional.ofNullable(doObj.getGitName()).orElse(""))
                .setGitDescription(Optional.ofNullable(doObj.getGitDescription()).orElse(""))
                .setFirstDepartmentId(Optional.ofNullable(doObj.getFirstDepartmentId()).orElse(0L))
                .setSecondDepartmentId(Optional.ofNullable(doObj.getSecondDepartmentId()).orElse(0L))
                .setThirdDepartmentId(Optional.ofNullable(doObj.getThirdDepartmentId()).orElse(0L))
                .setFirstDepartmentName(Optional.ofNullable(doObj.getFirstDepartmentName()).orElse(""))
                .setSecondDepartmentName(Optional.ofNullable(doObj.getSecondDepartmentName()).orElse(""))
                .setThirdDepartmentName(Optional.ofNullable(doObj.getThirdDepartmentName()).orElse(""))
                .build();
        return info;
    }

    private FirstDepartmentInfo convertToFirstDepartmentInfo(FirstDepartmentMappingDO doObj) {
        if (doObj == null) {
            throw new IllegalArgumentException("doObj cannot be null");
        }

        log.debug("Converting FirstDepartmentMappingDO to FirstDepartmentInfo: {}", doObj);

        FirstDepartmentInfo info = FirstDepartmentInfo.newBuilder()
                .setFirstDepartmentId(Optional.ofNullable(doObj.getFirstDepartmentId()).orElse(0L))
                .setFirstDepartmentName(Optional.ofNullable(doObj.getFirstDepartmentName()).orElse(""))
                .build();
        return info;
    }

    private SecondDepartmentInfo convertToSecondDepartmentInfo(SecondDepartmentMappingDO doObj) {
        if (doObj == null) {
            throw new IllegalArgumentException("doObj cannot be null");
        }

        log.debug("Converting SecondDepartmentMappingDO to SecondDepartmentInfo: {}", doObj);

        SecondDepartmentInfo info = SecondDepartmentInfo.newBuilder()
                .setSecondDepartmentId(Optional.ofNullable(doObj.getSecondDepartmentId()).orElse(0L))
                .setSecondDepartmentName(Optional.ofNullable(doObj.getSecondDepartmentName()).orElse(""))
                .build();
        return info;
    }

    private GitToDepartmentMappingQueryCondition buildQueryCondition(QueryGitToMappingRequest request) {
        // 构建查询条件
        GitToDepartmentMappingQueryCondition condition = new GitToDepartmentMappingQueryCondition();
        condition.setPageNo(request.getPageNo());
        condition.setPageSize(request.getPageSize());
        condition.setFirstDepartmentId(request.getFirstDepartmentId());
        condition.setSecondDepartmentId(request.getSecondDepartmentId());
        condition.setThirdDepartmentId(request.getThirdDepartmentId());
        condition.setGitProjectId(request.getGitProjectId());
        condition.setFirstDepartmentName(request.getFirstDepartmentName());
        condition.setSecondDepartmentName(request.getSecondDepartmentName());
        condition.setThirdDepartmentName(request.getThirdDepartmentName());
        condition.setGitName(request.getGitName());
        return condition;
    }

    @Override
    @TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
    public List<String> querySecondDepartmentName(QueryGitToMappingRequest request) {
        GitToDepartmentMappingQueryCondition condition = buildQueryCondition(request);
        List<String> secondDepartmentNameList = gitToDepartmentMappingDAONew.querySecondDepartmentName(condition, jdbcTemplate);
        log.info("[GitToDepartmentMappingServiceImpl] secondDepartmentNameList: {}", JSON.toJSONString(secondDepartmentNameList));
        // 过滤掉 null 值
        List<String> filteredSecondDepartmentNames = secondDepartmentNameList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return filteredSecondDepartmentNames;
    }
}