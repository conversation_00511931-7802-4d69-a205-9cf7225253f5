package com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz;

import java.util.List;

import com.github.phantomthief.tuple.ThreeTuple;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.FirstDepartmentInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.GitToDepartmentMappingParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.GitToMappingInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryGitToMappingRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.SecondDepartmentInfo;
public interface KspayGitToDepartmentMappingService {
    void insertOrUpdateGitToDepartmentMapping(GitToDepartmentMappingParam gitToDepartmentMappingParam);

    List<String> querySecondDepartmentName(QueryGitToMappingRequest request);

    ThreeTuple<List<GitToMappingInfo>, List<FirstDepartmentInfo>, List<SecondDepartmentInfo>> queryGitToMappings(QueryGitToMappingRequest request);


}
