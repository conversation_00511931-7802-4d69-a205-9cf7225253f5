package com.kuaishou.kwaishop.qa.risk.center.domain.combine.biz;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseCombineDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseCombineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseLevelCombineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseProblemCombineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCombineProblemInfoDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultDetailsDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultDetailsGroupByMemberRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.EntityDataDTO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-13
 */
public interface FaultCaseCombineBizService {

    FaultCaseCombineDTO getFaultCaseCombine(FaultCaseCombineRequest request);

    List<FaultCombineProblemInfoDTO> getCaseProblemCombine(FaultCaseProblemCombineRequest request);

    List<EntityDataDTO> getCaseLevelCombine(FaultCaseLevelCombineRequest request);

    List<FaultDetailsDTO> getFaultDetailsGroupByMember(FaultDetailsGroupByMemberRequest request);
}
