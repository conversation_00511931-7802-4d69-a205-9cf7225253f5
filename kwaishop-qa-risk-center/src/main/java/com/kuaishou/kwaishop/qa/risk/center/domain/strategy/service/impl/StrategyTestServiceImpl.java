package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.SQL_ERROR;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.gson.JsonArray;
import com.kuaishou.intown.json.JSONObject;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.dao.TaskDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.DiffResult;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.PolicyTestResponse;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecordListStatus;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTestDataPool;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.DataService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.DiffService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.PolicyRpcService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.RecordService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.ReportService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.StrategyTestService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.StrategyTrafficRecordService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.SimpleQueryRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StartRecordingRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StrategyTestRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestAiOpenAiRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestAiOpenAiResponse;
import com.kuaishou.kwaishop.qa.risk.center.utils.http.HttpUtilsNew;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-20
 */
@Service
@Slf4j
@Lazy
public class StrategyTestServiceImpl implements StrategyTestService {

    @Autowired
    private DataService dataService;

    @Autowired
    private PolicyRpcService policyRpcService;

    @Autowired
    private DiffService diffService;

    @Autowired
    private ReportService reportService;

    @Autowired
    private RecordService recordService;

    @Autowired
    private TaskDAOImpl taskDAO;

    @Autowired
    private StrategyTrafficRecordService strategyTrafficRecordService;

    private static final String S_YILIAO_QUERY_SUOSUO = "S_yiliao_query_sousuo";

    private static final String USER_ID = "3690600021";

    /**
     * 回放的具体逻辑在这里
     * @param request
     */
    @Override
    public void replayRequest(ReplayRequest request) {
        //首先取数
        List<StrategyTestDataPool> dataPoolParams = dataService.getDataPoolById(request);
        dataPoolParams.forEach(dataPoolParam -> {
            log.info("dataPoolParam = {}", dataPoolParam);
        });
        List<DiffResult> diffResults = new ArrayList<>();
        dataPoolParams.forEach(strategyTestDataPool -> {
            PolicyTestResponse policyTestResponse = policyRpcService.policyTree(strategyTestDataPool, request.getLaneId());
            // 对新的diff结果进行录入，把结果重新放到
            diffService.calculateDiffToDataPool(strategyTestDataPool, policyTestResponse, request.getLaneId());
        });

        strategyTrafficRecordService.updateDiffInfo(dataPoolParams);
        if (!request.getTaskId().isEmpty()) {
            taskDAO.updateStatus(request.getTaskId(), RecordListStatus.REPLAY_COMPLETED.getCode());
        }

        // 每次回放后，计算一下总的diff率。
        diffService.changeDiffConclusion(request);

        // 每次把结论更新下
    }

    @Override
    public String simpleQuery(SimpleQueryRequest request) {
        if (!request.getCaseId().isEmpty()) {
            return strategyTrafficRecordService.queryTrafficData(request.getCaseId());
        } else {
            return taskDAO.queryTaskSql(request.getTaskId());
        }
    }

    @Override
    public TestAiOpenAiResponse testAiOpenAi1(TestAiOpenAiRequest request) {
        // --- 业务代码 ---

        // 1. 准备业务参数
        final String apiUrl = "http://langbridge-test.internal/index/api/model/v1/chat/completions";
        final String authToken = "c590b19ac7c544d8b4989f53bd3fa95d";
        String jsonPayload = request.getJsonString();

        // 2. 准备请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + authToken);
        headers.put("Content-Type", "application/json"); // 虽然工具类会设置，但在这里明确指定更清晰

        try {
            // 3. 调用 HttpUtils 发送请求
            // 业务层只关心：URL是什么，请求体是什么，请求头是什么
            // 无需关心如何创建Client、如何设置超时等底层细节
            String responseBody = HttpUtilsNew.postJson(apiUrl, jsonPayload, headers);
            log.info("testAiOpenAi1 = {}", responseBody);
            return TestAiOpenAiResponse.newBuilder()
                    .setJsonString(responseBody)
                    .build();
        } catch (IOException e) {
            // 5. 处理所有可能的网络或请求失败异常
            // HttpUtils 抛出的异常会在这里被统一捕获
            System.err.println("调用外部API失败: " + e.getMessage());
            e.printStackTrace(); // 在生产环境中建议使用日志框架 logger.error("...", e);

            return null;
        }

        // 6. 返回最终的业务响应对象
    }

    /**
     * 之前的逻辑，已不用
     * @param request
     */
    @Override
    public void startTest(StrategyTestRequest request) {
        List<StrategyTestDataPool> dataPoolParams = dataService.getDataPoolByRpc();
        dataPoolParams.forEach(dataPoolParam -> {
            log.info("dataPoolParam = {}", dataPoolParam);
        });

        List<DiffResult> diffResults = new ArrayList<>();
        dataPoolParams.forEach(strategyTestDataPool -> {
            PolicyTestResponse policyTestResponse = policyRpcService.policyTree(strategyTestDataPool, "");
            // 对结果进行diff
            DiffResult diffResult = diffService.calculateDiff(strategyTestDataPool, policyTestResponse);
            diffResults.add(diffResult);
        });
        double diffRate = ((double) diffResults.size() / dataPoolParams.size());

        // 生成报告，发送kim群消息
        reportService.report(diffResults, String.format("%.2f%%", diffRate * 100));
    }

    /**
     * 录制逻辑
     * @param request
     */
    @Override
    public void startRecording(StartRecordingRequest request) {

        String taskId = generateNumericTaskId(15);
        JSONObject sqlTemplate = new JSONObject();

        // 简单校验是否是一个合法的sql
        if (!request.getSql().isEmpty() && !validate(request.getSql())) {
            throw new BizException(SQL_ERROR);
        }

        sqlTemplate.put("tableType", "HIVE");
        String sql = request.getSql().isEmpty()
                ? generateSQLQuery(request.getPDate(), request.getSceneNameList(), request.getRecordCount())
                : request.getSql();
        sqlTemplate.put("sql", sql);

//        new Thread(() -> recordService.doSqlSearch(RecordRequest.builder()
//                        .sqlTemplate(sqlTemplate)
//                        .taskId(taskId)
//                        .user(request.getUser())
//                        .pDate(request.getPDate())
//                        .scene(request.getSceneNameList().stream().map(String::valueOf).collect(Collectors.toList()))
//                        .expectLaneId(request.getExpectLaneId())
//                        .taskName(request.getTaskName())
//                        .taskDescription(request.getTaskDescription())
//                        .build()))
//                .start();

    }

    public List<String> getListStringByJson(JsonArray jsonElements) {
        List<String> list = new ArrayList<>();
        jsonElements.forEach(jsonObject -> {
            list.add(jsonObject.getAsString());
        });
        return list;
    }


    public String generateNumericTaskId(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("Length must be greater than 0");
        }

        // 获取当前时间戳的后几位
        long timestamp = System.currentTimeMillis();
        String timestampStr = Long.toString(timestamp);

        // 取时间戳的后6位（根据需要调整）
        String timePart = timestampStr.substring(timestampStr.length() - 6);

        // 生成剩余的随机数字
        int randomLength = length - timePart.length();
        StringBuilder randomPart = new StringBuilder(randomLength);
        for (int i = 0; i < randomLength; i++) {
            int randomDigit = (int) (Math.random() * 10);
            randomPart.append(randomDigit);
        }

        // 组合时间戳部分和随机数字部分
        return timePart + randomPart.toString();
    }


    private static final String BASE_QUERY = "SELECT feature_context, scene_key, trace_id, strategy_context, action_context, create_time "
            +
            "FROM ( "
            +
            "SELECT feature_context, scene_key, trace_id, strategy_context, action_context, create_time "
            +
            "FROM ks_origin_ecom_log.kwaishop_apollo_strategy_policy_tree_execute_record "
            +
            "WHERE p_date = '%s' AND rule_expression_result = true AND scene_key = '%s' "
            +
            "ORDER BY create_time DESC "
            +
            "LIMIT %d "
            +
            ") AS subquery";

    private static final String BASE_QUERY_SELECTION =
            "SELECT *, '%s' AS strategy_node "
                    +
                    "FROM ks_origin_ecom_log.kwaishop_apollo_strategy_policy_tree_execute_record "
                    +
                    "WHERE p_date = '%s' "
                    +
                    "AND scene_key = '%s' "
                    +
                    "AND rule_expression_result = true "
                    +
                    "AND get_json_object(rule_execute_remark, '$.STRATEGY_NODE') LIKE '%%%s_%%\":true%%' "
                    +
                    "LIMIT 1";


    public String generateSQLQuery(String pDate, List<String> sceneKeys, int limit) {
        int sqlLimit = (limit == 0) ? 10 : limit;

        return sceneKeys.stream()
                    .map(sceneKey -> buildSceneKeyQuery(pDate, sceneKey, sqlLimit))
                    .collect(Collectors.joining(" UNION ALL ", "", " ORDER BY create_time DESC"));

    }

    private String buildSceneKeyQuery(String pDate, String sceneKey, int limit) {
        return String.format(BASE_QUERY, pDate, sceneKey, limit);
    }

    private String buildStrategyQuery(String pDate, String sceneKey, String strategyId) {
        return String.format(BASE_QUERY_SELECTION, strategyId, pDate, sceneKey, strategyId);
    }

    public String generateSQLQuerySelection(String pDate, String sceneKey, List<String> strategyIds) {
        return strategyIds.stream()
                .map(strategyId -> buildStrategyQuery(pDate, sceneKey, strategyId))
                .collect(Collectors.joining(" UNION ALL ", "", ""));
    }

    public boolean validate(String sql) {
        // Basic checks for a simple SELECT statement
        String trimmedSQL = sql.trim().toLowerCase();

        // Check if it starts with "select" and contains "from"
        if (!trimmedSQL.startsWith("select")) {
            return false;
        }
        if (!trimmedSQL.contains("from")) {
            return false;
        }

        // Check for basic SQL structure
        String[] parts = trimmedSQL.split("\\s+");
        if (parts.length < 4) { // At least: SELECT ... FROM ...
            return false;
        }

        // Additional checks can be added here
        // For example, check for semicolon at the end
        if (!trimmedSQL.endsWith(";")) {
            return false;
        }

        return true;
    }


}


