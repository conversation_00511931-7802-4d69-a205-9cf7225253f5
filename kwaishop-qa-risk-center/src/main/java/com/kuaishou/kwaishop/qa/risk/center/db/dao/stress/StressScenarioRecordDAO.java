package com.kuaishou.kwaishop.qa.risk.center.db.dao.stress;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressScenarioRecordBO;

public interface StressScenarioRecordDAO {
    PageBO<StressScenarioRecordDO> queryPageList(QueryStressScenarioRecordBO bo);
    long insert(StressScenarioRecordDO scenarioRecordDO);
    StressScenarioRecordDO getById(Long id);
}
