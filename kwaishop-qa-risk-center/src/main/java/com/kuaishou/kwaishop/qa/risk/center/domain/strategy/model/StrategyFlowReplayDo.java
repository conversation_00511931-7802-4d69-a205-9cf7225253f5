package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-17
 * 回放任务主表
 */
@Data
@NoArgsConstructor
@SuperBuilder
@AllArgsConstructor
@TableName("strategy_flow_replay")
public class StrategyFlowReplayDo {
    @TableId
    private Long id;  // 主键id
    private String name;  // 任务名称
    private Long flowRecordId;  // 录制任务id
    private String description;  // 任务描述
    private Integer total;  // 回放总条数
    private Integer diffResult;  // diff结论，有无diff
    private String diffDetailResult;  // diff详细结论
    private Integer replayQps;  // 回放qps
    private String replayConfig;  // 回放配置
    private Integer replayStatus;  // 回放状态，1回放中，2回放完成
    private Long replayStartTime;  // 回放开始时间
    private Long replayEndTime;  // 回放结束时间
    private String createUser;  // 更新人
    private String updateUser;  // 创建人
    private Long createTime;  // 创建时间
    private Long updateTime;  // 更新时间
    private String extra;  // 扩展字段
    private Integer deleted;  // 是否删除 0:否（默认）1：是

}
