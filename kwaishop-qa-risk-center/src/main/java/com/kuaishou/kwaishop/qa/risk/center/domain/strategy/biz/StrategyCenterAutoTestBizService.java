package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.biz;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.PageReplayDetailDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.QueryTrafficRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.SimpleQueryRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StartRecordingRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StrategyTestRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestAiOpenAiRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestAiOpenAiResponse;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-20
 */
public interface StrategyCenterAutoTestBizService {
    void startTest(StrategyTestRequest request);

    void startRecording(StartRecordingRequest request);

    void queryTraffic(QueryTrafficRequest request);

    /**
     * 回放详情，查最后的所有的结果
     * @param request
     * @return
     */
    PageReplayDetailDTO replayDetail(ReplayDetailRequest request);

    void replayRequest(ReplayRequest request);

    String simpleQuery(SimpleQueryRequest request);

    TestAiOpenAiResponse testAiOpenAi1(TestAiOpenAiRequest request);

//    PageRecordTaskDetailDTO queryRecordTask(QueryPageRecordTaskRequest request);
}
