package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-13
 * 回放主表BO
 */
@Data
public class StrategyFlowReplayBo {

    private Long id;

    private String name;

    private Long flowRecordId;

    private String description;

    private Integer total;

    private Integer diffResult;

    private String diffResultDesc;

    private String diffDetailResult;

    private Integer replayQps;

    private String sceneKey;

    private String whiteFeatureKey;

    private String blackFeatureKey;

    private ReplayConfig replayConfigOne;

    private ReplayConfig replayConfigTwo;

    /**
     * @see com.kuaishou.kwaishop.qa.risk.center.common.Enum.ReplayStatus
     */
    private Integer replayStatus;

    private String replayStatusDesc;

    private Long replayStartTime;

    private Long replayEndTime;

    private String createUser;

    private String updateUser;

    private Long createTime;

    private Long updateTime;
}
