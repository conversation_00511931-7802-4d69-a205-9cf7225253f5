package com.kuaishou.kwaishop.qa.risk.center.domain.problem.service.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.CREATE_PROBLEM_DETAIL_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.PROBLEM_DELETE_AUTH_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.PROBLEM_DETAIL_NOT_FOUND_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.PROBLEM_TYPE_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.UPDATE_PROBLEM_DETAIL_ERROR;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.problem.ProblemDetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.problem.ProblemDetailDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.auth.service.AuthService;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.convert.ProblemDetailConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.bo.ProblemDetailBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.service.ProblemDetailService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-06
 */
@Slf4j
@Service
@Lazy
public class ProblemDetailServiceImpl implements ProblemDetailService {

    @Autowired
    private ProblemDetailDAO problemDetailDAO;

    @Autowired
    private AuthService authService;

    @Autowired
    private ProblemDetailConvert problemDetailConvert;

    @Override
    public ProblemDetailDO createProblem(ProblemDetailBO problemDetailBO) {
        if (problemDetailBO == null) {
            throw new BizException(CREATE_PROBLEM_DETAIL_ERROR);
        }
        authService.checkAuth(problemDetailBO.getCenterId(), problemDetailBO.getTeamId());
        ProblemDetailDO createDO = problemDetailConvert.buildCreateDO(problemDetailBO);
        problemDetailDAO.insert(createDO);
        return createDO;
    }

    @Override
    public void updateProblem(ProblemDetailBO problemDetailBO) {
        if (problemDetailBO == null) {
            throw new BizException(UPDATE_PROBLEM_DETAIL_ERROR);
        }
        authService.checkAuth(problemDetailBO.getCenterId(), problemDetailBO.getTeamId());
        ProblemDetailDO existDO = problemDetailDAO.queryById(problemDetailBO.getId());
        if (existDO == null) {
            throw new BizException(PROBLEM_DETAIL_NOT_FOUND_ERROR);
        }
        if (!existDO.getDetailType().equals(problemDetailBO.getDetailType())) {
            log.error("[ProblemAbstractService] updateProblem error, problemDetailBO: {}, existDO: {}",
                    toJSON(problemDetailBO), toJSON(existDO));
            throw new BizException(PROBLEM_TYPE_ERROR);
        }
        problemDetailDAO.updateSelectiveById(problemDetailConvert.buildUpdateDO(existDO, problemDetailBO));
    }

    @Override
    public void deleteProblem(Long id, String operator) {
        ProblemDetailDO existDO = problemDetailDAO.queryById(id);
        if (existDO == null) {
            throw new BizException(PROBLEM_DETAIL_NOT_FOUND_ERROR);
        }
        if (!existDO.getCreator().equals(operator)) {
            log.error("[ProblemAbstractService] deleteProblem error, existDO: {}, current operator: {}",
                    toJSON(existDO), operator);
            throw new BizException(PROBLEM_DELETE_AUTH_ERROR);
        }
        problemDetailDAO.logicDeleted(id, operator);
    }

    @Override
    public List<ProblemDetailDO> queryProblemList(ProblemDetailBO problemDetailBO) {
        return problemDetailDAO.queryProblemDetailList(problemDetailBO);
    }

    @Override
    public PageBO<ProblemDetailDO> queryPageProblemList(ProblemDetailBO problemDetailBO) {
        return problemDetailDAO.queryPageProblemDetailList(problemDetailBO);
    }

}
