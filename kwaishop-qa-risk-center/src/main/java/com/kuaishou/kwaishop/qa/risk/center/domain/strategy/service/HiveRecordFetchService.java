package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service;

import java.util.List;
import java.util.Map;

import com.kuaishou.kwaishop.qa.risk.center.config.constants.QueryTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.impl.SqlJobHandler;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-14
 */
public interface HiveRecordFetchService {

    List<Map<String, Object>> fetchDpData(Integer queryType, String querySql, Long dataSourceId, String user, String taskId);

    /**
     * 同步查询hive数据
     * @param queryType
     * @param querySql
     * @param user
     * @return
     */
    List<Map<String, Object>> fetchIdpDataSync(QueryTypeEnum queryType, String querySql, String user);

    /**
     * 异步查询hive数据
     * @param queryType
     * @param querySql
     * @param user
     * @return
     */
    SqlJobHandler fetchIdpDataAsync(QueryTypeEnum queryType, String querySql, String user);
}
