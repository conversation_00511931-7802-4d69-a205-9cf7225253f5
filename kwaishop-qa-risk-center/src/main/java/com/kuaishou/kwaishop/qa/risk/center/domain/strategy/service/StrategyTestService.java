package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.SimpleQueryRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StartRecordingRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StrategyTestRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestAiOpenAiRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestAiOpenAiResponse;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-20
 */
public interface StrategyTestService {
    void startTest(StrategyTestRequest request);

    void startRecording(StartRecordingRequest request);

    void replayRequest(ReplayRequest request);

    String simpleQuery(SimpleQueryRequest request);

    TestAiOpenAiResponse testAiOpenAi1(TestAiOpenAiRequest request);

}
