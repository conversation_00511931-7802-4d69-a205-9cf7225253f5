package com.kuaishou.kwaishop.qa.risk.center.domain.account.convert;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagMetaDO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.TagBaseInfoDTO;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/15 14:37
 * @注释
 */
public interface TagMetaConvert {

    List<TagBaseInfoDTO> buildTagListInfoDTO(List<TagMetaDO> tagMetaDO);


    TagMetaDO buildTagInfoDO(TagBaseInfoDTO tagBaseInfoDTO);

    List<TagMetaDO> buildTagInfoDOs(List<TagBaseInfoDTO> tagBaseInfoDTOS);


}
