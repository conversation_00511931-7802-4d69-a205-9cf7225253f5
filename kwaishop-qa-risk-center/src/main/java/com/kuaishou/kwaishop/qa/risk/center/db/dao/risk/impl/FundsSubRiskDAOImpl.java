package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import java.util.Collection;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.KspayPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.KspayBaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.FundsSubRiskDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsSubRiskDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.FundsSubRiskMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.FundsSubRiskQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;

/**
 * <AUTHOR>
 * Created on 2022-12-26
 */
@Repository
public class FundsSubRiskDAOImpl extends KspayBaseDAOImpl<FundsSubRiskDO, FundsSubRiskQueryCondition> implements FundsSubRiskDAO {
    @Autowired
    private FundsSubRiskMapper fundsSubRiskMapper;

    @Override
    protected void fillQueryCondition(FundsSubRiskQueryCondition condition, QueryWrapper<FundsSubRiskDO> queryWrapper) {

        if (StringUtils.isNotBlank(condition.getSubRiskName())) {
            queryWrapper.and(q -> q.eq("sub_risk_name", condition.getSubRiskName()));
        }
        if (StringUtils.isNotBlank(condition.getRiskType())) {
            queryWrapper.and(q -> q.eq("risk_type", condition.getRiskType()));
        }
        if (!condition.getLevel().equals(0L)) {
            queryWrapper.and(q -> q.eq("level", condition.getLevel()));
        }
        if (StringUtils.isNotBlank(condition.getCreator())) {
            queryWrapper.and(q -> q.eq("creator", condition.getCreator()));
        }

    }
    protected void fillLikeQueryCondition(FundsSubRiskQueryCondition condition, QueryWrapper<FundsSubRiskDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getSubRiskName())) {
            queryWrapper.and(q -> q.like("sub_risk_name", condition.getSubRiskName()));
        }
        if (StringUtils.isNotBlank(condition.getRiskType())) {
            queryWrapper.and(q -> q.like("risk_type", condition.getRiskType()));
        }
        if (!condition.getLevel().equals(0L)) {
            queryWrapper.and(q -> q.eq("level", condition.getLevel()));
        }
        if (StringUtils.isNotBlank(condition.getCreator())) {
            queryWrapper.and(q -> q.like("creator", condition.getCreator()));
        }
        if (StringUtils.isNotBlank(condition.getDepartment())) {
//            queryWrapper.and(q -> q.apply("JSON_EXTRACT(extra_info, '$[0].department') = {0}", condition.getDepartment()));
            queryWrapper.apply("JSON_EXTRACT(extra_info, '$.department') = {0}", condition.getDepartment());
        }
        if (StringUtils.isNotBlank(condition.getBusinessDomain())) {
            queryWrapper.apply("JSON_EXTRACT(extra_info, '$[0].businessDomain') = {0}", condition.getBusinessDomain());
        }
    }
    @Override
    protected BaseMapper<FundsSubRiskDO> getMapper() {
        return fundsSubRiskMapper;
    }

    @Override
    public List<FundsSubRiskDO> queryByCenterIds(Collection<Long> centerIds, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsSubRiskDO> queryByTeamIds(Long centerId, Collection<Long> teamIds, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsSubRiskDO> queryByCenterId(Long centerId, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsSubRiskDO> queryByTeamId(Long centerId, Long teamId, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsSubRiskDO> queryByCenterIdsTeamIds(Collection<Long> centerIds, Collection<Long> teamIds,
            CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public KspayPageBO<FundsSubRiskDO> queryPageRiskDetailList(FundsSubRiskQueryCondition fundsSubRiskQueryCondition) {

        return queryPageList(fundsSubRiskQueryCondition);
    }

    @Override
    public List<FundsSubRiskDO> queryFundsSubRisk(FundsSubRiskQueryCondition fundsSubRiskQueryCondition) {
        return queryList(fundsSubRiskQueryCondition);
    }

    @Override
    public FundsSubRiskDO queryBySubRiskId(String subRiskId) {
        return  queryById(Long.parseLong(subRiskId));
    }

    @Override
    public List<FundsSubRiskDO> getAllList() {
        return fundsSubRiskMapper.getAllList();
    }
}
