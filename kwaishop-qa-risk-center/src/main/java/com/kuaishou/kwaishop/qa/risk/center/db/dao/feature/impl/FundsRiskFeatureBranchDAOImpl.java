package com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.TransactionalDataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.FundsRiskFeatureBranchDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.KspayBaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureBranchDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.feature.FundsRiskFeatureBranchMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundRiskFeatureDetailQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.bo.KatCaseRecordBO;
//import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetail;

import lombok.extern.slf4j.Slf4j;

@Repository
@Service
@Lazy
@Slf4j
public class FundsRiskFeatureBranchDAOImpl extends KspayBaseDAOImpl<FundsRiskFeatureBranchDO,
        FundRiskFeatureDetailQueryCondition> implements FundsRiskFeatureBranchDAO {

    @Autowired
    private FundsRiskFeatureBranchMapper fundsRiskFeatureBranchMapper;
    @Override
    public List<FundsRiskFeatureBranchDO> queryFundRiskFeatureBranch(FundRiskFeatureDetailQueryCondition fundRiskFeatureDetailQueryCondition) {
        return queryList(fundRiskFeatureDetailQueryCondition);
    }

    @Override
    public FundsRiskFeatureBranchDO queryByFeatureViewId(int featureViewId) {
        FundRiskFeatureDetailQueryCondition queryCondition = FundRiskFeatureDetailQueryCondition.builder()
                .featureViewId((long) featureViewId)
                .build();
        return queryOne(queryCondition);
    }

    @Override
    public List<FundsRiskFeatureBranchDO> queryFundRiskFeatureBranchDataNoNull() {
        return fundsRiskFeatureBranchMapper.queryFundRiskFeatureBranchDataNoNull();
    }

    @Override
    public List<FundsRiskFeatureBranchDO> queryFundRiskFeatureBranchDataNoRisk() {
        return fundsRiskFeatureBranchMapper.queryFundRiskFeatureBranchDataNoRisk();
    }

    @Override
    @TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
    public List<FundsRiskFeatureBranchDO> queryFundRiskByRepoAndBranch(FundRiskFeatureDetailQueryCondition fundRiskFeatureDetailQueryCondition) {
        List<FundsRiskFeatureBranchDO> response = fundsRiskFeatureBranchMapper.queryFundRiskByRepoAndBranch(
                fundRiskFeatureDetailQueryCondition.getRepoName(),
                fundRiskFeatureDetailQueryCondition.getBranchName());
        return response;
    }

    @Override
    public FundsRiskFeatureBranchDO queryFundRiskByRepoAndBranchAndViewId(FundRiskFeatureDetailQueryCondition fundRiskFeatureDetailQueryCondition) {
        return fundsRiskFeatureBranchMapper.queryFundRiskByRepoAndBranchAndViewId(fundRiskFeatureDetailQueryCondition.getRepoName(),
                fundRiskFeatureDetailQueryCondition.getBranchName(), fundRiskFeatureDetailQueryCondition.getFeatureViewId());
    }

    @Override
    public FundsRiskFeatureBranchDO queryByMainId(String mainId) {
        return queryById(Long.parseLong(mainId.trim()));
    }

    @Override
    public int updateRiskStatusByMainId(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO) {
        return fundsRiskFeatureBranchMapper.updateBranchRiskStatusById(fundsRiskFeatureBranchDO.getUpdater(), System.currentTimeMillis(),
                fundsRiskFeatureBranchDO.getRiskStatus(), fundsRiskFeatureBranchDO.getUpdater(),
                fundsRiskFeatureBranchDO.getAuditCoverUpdater(), fundsRiskFeatureBranchDO.getId().toString());  // 支持传入riskStatus
    }

    @Override
    public int updateAuditCoverByMainId(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO) {
        return fundsRiskFeatureBranchMapper.updateBranchAuditCoverById(fundsRiskFeatureBranchDO.getUpdater(), System.currentTimeMillis(),
                fundsRiskFeatureBranchDO.getAuditCover(), fundsRiskFeatureBranchDO.getRiskStatusUpdater(),
                fundsRiskFeatureBranchDO.getUpdater(), fundsRiskFeatureBranchDO.getId().toString());  // 支持传入auditCover
    }

    @Override
    public int updateAllStatusByMainId(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO) {
        return fundsRiskFeatureBranchMapper.updateAllStatusByMainId(fundsRiskFeatureBranchDO.getUpdater(), System.currentTimeMillis(),
                fundsRiskFeatureBranchDO.getRiskStatus(), fundsRiskFeatureBranchDO.getAuditCover(), fundsRiskFeatureBranchDO.getUpdater(),
                fundsRiskFeatureBranchDO.getUpdater(), fundsRiskFeatureBranchDO.getId().toString());  // 支持传入riskStatus & auditCover
    }

    @Override
    public int updateAllStatusByFeatureId(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO) {
        return fundsRiskFeatureBranchMapper.updateAllStatusByFeatureId(fundsRiskFeatureBranchDO.getUpdater(), System.currentTimeMillis(),
                fundsRiskFeatureBranchDO.getRiskStatus(), fundsRiskFeatureBranchDO.getAuditCover(), fundsRiskFeatureBranchDO.getUpdater(),
                fundsRiskFeatureBranchDO.getUpdater(), fundsRiskFeatureBranchDO.getFeatureViewId());
    }

    @Override
    public int updateByFeatureViewId(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO) {
        // 支持传入riskStatus 和 auditCover
        return fundsRiskFeatureBranchMapper.updateFeatureViewBranchByFeatureViewId(fundsRiskFeatureBranchDO.getUpdater(),
                System.currentTimeMillis(), fundsRiskFeatureBranchDO.getRiskStatus(), fundsRiskFeatureBranchDO.getAuditCover(),
                fundsRiskFeatureBranchDO.getUpdater(), fundsRiskFeatureBranchDO.getUpdater(),
                fundsRiskFeatureBranchDO.getFeatureViewId());
    }

    @Override
    public void updateExtraDataByFeatureId(Long id, String extraData) {
        fundsRiskFeatureBranchMapper.updateExtraDataByFeatureId(id, extraData);
    }

    @Override
    public List<FundsRiskFeatureBranchDO> getKspayRiskStatistic() {
        return fundsRiskFeatureBranchMapper.getKspayRiskStatistic();
    }

    @Override
    public List<FundsRiskFeatureBranchDO> getKspayRiskStatisticWithTime(Long startTime, Long endTime) {
        return fundsRiskFeatureBranchMapper.getKspayRiskStatisticWithTime(startTime, endTime);
    }

    @Override
    public int updateRuleIsValidByMixed(String repoName, String branchName, Long featureViewId, String ruleIsValid) {
        return fundsRiskFeatureBranchMapper.updateRuleIsValidByMixed(repoName, branchName, featureViewId, ruleIsValid);
    }

    @Override
    public void updateKatCaseRecord(List<KatCaseRecordBO> records, String branchName, String repoName) {
        fundsRiskFeatureBranchMapper.updateKatCaseRecord(records, branchName, repoName);
    }

    @Override
    public String queryRuleIsValid(String repoName, String branchName, Long featureViewId) {
        return fundsRiskFeatureBranchMapper.queryRuleIsValid(repoName, branchName, featureViewId);
    }
//    @Override
//    public FundsRiskFeatureBranchDO queryByFeatureId(String featureId) {
//        return  queryById(Long.parseLong(featureId));
//    }

    @Override
    protected void fillLikeQueryCondition(FundRiskFeatureDetailQueryCondition condition, QueryWrapper<FundsRiskFeatureBranchDO> queryWrapper) {

    }

    @Override
    protected void fillQueryCondition(FundRiskFeatureDetailQueryCondition condition, QueryWrapper<FundsRiskFeatureBranchDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getFeatureViewId().toString())) {
            queryWrapper.and(q -> q.eq("feature_view_id", condition.getFeatureViewId()));
        }
        if (StringUtils.isNotBlank(condition.getFeatureName())) {
            queryWrapper.and(q -> q.eq("feature_name", condition.getFeatureName()));
        }
//        if (condition.getFundRiskMethodAmount() != null) {
//            log.info("testsetst:{}", condition.getFundRiskMethodAmount());
//            // 确认 FundRiskMethodAmount 的值
//            if (condition.getFundRiskMethodAmount() == 1) {
//                // 查询 fund_risk_method_amount > 0 的数据
//                queryWrapper.and(q -> q.gt("fund_risk_method_amount", 0));
//            } else if (condition.getFundRiskMethodAmount() == 2) {
//                log.info("testsetst111:{}", condition.getFundRiskMethodAmount());
//                // 如果 FundRiskMethodAmount 是 0 或 null，查询 fund_risk_method_amount = 0 或为 null 的数据
//                queryWrapper.and(q -> q.eq("fund_risk_method_amount", 0).or().isNull("fund_risk_method_amount"));
//            }
//        }
    }


    @Override
    protected BaseMapper<FundsRiskFeatureBranchDO> getMapper() {
        return fundsRiskFeatureBranchMapper;
    }
}
