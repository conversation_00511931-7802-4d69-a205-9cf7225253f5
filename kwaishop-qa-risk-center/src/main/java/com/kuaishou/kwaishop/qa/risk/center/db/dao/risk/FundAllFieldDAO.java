package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundAllFieldDO;

public interface FundAllFieldDAO {

    // 插入一条字段记录
    void insertField(FundAllFieldDO fundAllFieldDO);

    // 查询单个字段是否存在
    List<FundAllFieldDO> getSimpleFieldByUnique(String bizDef, String sourceName, String tableName, String columnName);

    // 更新某个字段
    int updateFieldByUnique(String fieldHash, FundAllFieldDO fundAllFieldDO);

    // 查询所有的字段
    List<FundAllFieldDO> getAllFields();
}
