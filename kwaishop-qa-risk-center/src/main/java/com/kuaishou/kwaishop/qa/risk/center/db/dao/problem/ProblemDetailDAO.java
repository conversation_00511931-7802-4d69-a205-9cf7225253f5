package com.kuaishou.kwaishop.qa.risk.center.db.dao.problem;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.detail.DetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.problem.ProblemDetailDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.ProblemQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.bo.ProblemDetailBO;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-02
 */
public interface ProblemDetailDAO extends DetailDAO<ProblemDetailDO, ProblemQueryParam> {

    long insert(ProblemDetailDO problemDetailDO);

    ProblemDetailDO queryById(Long id);

    int updateSelectiveById(ProblemDetailDO riskDetailDO);

    int logicDeleted(Long id, String operator);

    List<ProblemDetailDO> queryProblemDetailList(ProblemDetailBO problemDetailBO);

    PageBO<ProblemDetailDO> queryPageProblemDetailList(ProblemDetailBO problemDetailBO);
}
