package com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.KspayPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.FundRiskIntelligenceFindDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.KspayBaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundRiskIntelligenceFindDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.feature.FundsRiskIntelligenceFindMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundRiskIntelligenceQueryCondition;

import lombok.extern.slf4j.Slf4j;

@Repository
@Slf4j
public class FundRiskIntelligenceFindDAOImpl extends KspayBaseDAOImpl<FundRiskIntelligenceFindDO,
        FundRiskIntelligenceQueryCondition> implements FundRiskIntelligenceFindDAO {

    @Autowired
    private FundsRiskIntelligenceFindMapper fundsRiskIntelligenceFindMapper;

    @Override
    public KspayPageBO<FundRiskIntelligenceFindDO> queryPageRiskIntelligenceFindList(
            FundRiskIntelligenceQueryCondition fundRiskIntelligenceQueryCondition) {
        log.info("fundRiskIntelligenceQueryCondition: {}", toJSON(fundRiskIntelligenceQueryCondition));
        return queryPageList(fundRiskIntelligenceQueryCondition);
    }

    @Override
    public FundRiskIntelligenceFindDO queryRiskFieldsByMainId(String id) {
        return fundsRiskIntelligenceFindMapper.queryRiskFieldsByMainId(id);
    }

    @Override
    public int updateRiskLevelByMainId(FundRiskIntelligenceFindDO fundRiskIntelligenceFindDO) {
        return fundsRiskIntelligenceFindMapper.updateRiskLevelByMainId(fundRiskIntelligenceFindDO.getUpdater(),
                fundRiskIntelligenceFindDO.getUpdateTime(), fundRiskIntelligenceFindDO.getRiskLevel(),
                fundRiskIntelligenceFindDO.getId());
    }

    @Override
    protected void fillLikeQueryCondition(FundRiskIntelligenceQueryCondition condition, QueryWrapper<FundRiskIntelligenceFindDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getBizDef())) {
            queryWrapper.and(q -> q.like("biz_def", condition.getBizDef()));
        }
        if (StringUtils.isNotBlank(condition.getSourceName())) {
            queryWrapper.and(q -> q.like("source_name", condition.getSourceName()));
        }
        if (StringUtils.isNotBlank(condition.getTableName())) {
            queryWrapper.and(q -> q.like("table_name", condition.getTableName()));
        }
        if (StringUtils.isNotBlank(condition.getColumnName())) {
            queryWrapper.and(q -> q.like("column_name", condition.getColumnName()));
        }
        if (StringUtils.isNotBlank(condition.getSymbol())) {
            queryWrapper.and(q -> q.like("symbol", condition.getSymbol()));
        }
        if (StringUtils.isNotBlank(condition.getRiskLevel())) {
            queryWrapper.and(q -> q.like("risk_level", condition.getRiskLevel()));
        }
        // 新增业务过滤条件
        queryWrapper.apply("JSON_EXTRACT(extra_info, '$.biz_code') = {0}", condition.getBizCode());
//        queryWrapper.and(q -> q.ne("status", 3));  // 过滤需求状态为删除 = 3的数据
    }

    @Override
    protected void fillQueryCondition(FundRiskIntelligenceQueryCondition condition, QueryWrapper<FundRiskIntelligenceFindDO> queryWrapper) {

    }

    @Override
    protected BaseMapper<FundRiskIntelligenceFindDO> getMapper() {
        return fundsRiskIntelligenceFindMapper;
    }
}
