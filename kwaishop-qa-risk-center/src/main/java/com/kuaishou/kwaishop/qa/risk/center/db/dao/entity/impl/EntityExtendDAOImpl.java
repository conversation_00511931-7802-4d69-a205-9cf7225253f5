package com.kuaishou.kwaishop.qa.risk.center.db.dao.entity.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kuaishou.infra.boot.jdbc.datasource.TransactionalDataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.entity.EntityExtendDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.entity.EntityMapper;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
@Service
@TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
public class EntityExtendDAOImpl extends ServiceImpl<EntityMapper, EntityDO> implements EntityExtendDAO {
}
