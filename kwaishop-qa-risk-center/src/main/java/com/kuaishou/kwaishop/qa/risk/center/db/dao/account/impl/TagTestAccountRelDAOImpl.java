package com.kuaishou.kwaishop.qa.risk.center.db.dao.account.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.TagTestAccountRelDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagTestAccountRelDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.account.TagTestAccountRelMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.account.TestAccountQueryCondition;

@Repository
public class TagTestAccountRelDAOImpl extends BaseDAOImpl<TagTestAccountRelDO, TestAccountQueryCondition> implements TagTestAccountRelDAO {

    @Autowired
    private TagTestAccountRelMapper tagTestAccountRelMapper;

    @Override
    public List<TagTestAccountRelDO> getTestTagInIds(List<Long> accountIds, List<Integer> authStatusList) {
        return tagTestAccountRelMapper.getTagListByIds(accountIds, authStatusList);
    }

    @Override
    public List<TagTestAccountRelDO> getTestTagInAccounts(List<Long> accountIds, List<Integer> authStatusList) {
        if (accountIds.size() == 0) {
            return new ArrayList<>();
        }
        return tagTestAccountRelMapper.getTagListBytestAccountIds(accountIds, authStatusList);
    }

    @Override
    public List<TagTestAccountRelDO> getTestTagInAccount(Long accountId, List<Integer> authStatusList) {
        List<Long> list = new ArrayList<>();
        list.add(accountId);
        return tagTestAccountRelMapper.getTagListBytestAccountIds(list, authStatusList);
    }

    @Override
    public void deleteTagFromAccount(Long accountId, String tagCode) {
        tagTestAccountRelMapper.deleteFromAccount(accountId, tagCode, 1, System.currentTimeMillis());
    }

    @Override
    public List<TagTestAccountRelDO> getTagListByTestAccountIds(List<Integer> tagAuthStatusList) {
        return tagTestAccountRelMapper.getTagListByTestAccountIds(tagAuthStatusList);
    }

    @Override
    public List<TagTestAccountRelDO> getTestAccountIdsByTag(String tagCode) {
        return tagTestAccountRelMapper.getTestAccountIdsByTag(tagCode);
    }

    @Override
    protected void fillQueryCondition(TestAccountQueryCondition condition,
                                      QueryWrapper<TagTestAccountRelDO> queryWrapper) {

    }

    @Override
    protected BaseMapper<TagTestAccountRelDO> getMapper() {
        return tagTestAccountRelMapper;
    }


    public long insert(TagTestAccountRelDO tagTestAccountRelDO) {
        tagTestAccountRelMapper.insert(tagTestAccountRelDO);
        return 0;
    }
}
