package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.KspayBaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.ConfigRiskDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.ConfigRiskRuleDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.ConfigRiskRuleMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.ConfigRuleQueryCondition;

@Repository
public class ConfigRiskDAOImpl extends KspayBaseDAOImpl<ConfigRiskRuleDO,
        ConfigRuleQueryCondition> implements ConfigRiskDAO {

    @Autowired
    private ConfigRiskRuleMapper configRiskRuleMapper;
    @Override
    public List<ConfigRiskRuleDO> getConfigRiskRule(ConfigRuleQueryCondition configRuleQueryCondition) {
        return queryList(configRuleQueryCondition);
    }

    @Override
    protected void fillLikeQueryCondition(ConfigRuleQueryCondition condition, QueryWrapper<ConfigRiskRuleDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getRuleName())) {
            queryWrapper.and(q -> q.like("rule_name", condition.getRuleName()));
        }
        if (condition.getId() != null) {
            queryWrapper.and(q -> q.like("id", condition.getId()));
        }
    }

    @Override
    protected void fillQueryCondition(ConfigRuleQueryCondition condition, QueryWrapper<ConfigRiskRuleDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getRuleName())) {
            queryWrapper.and(q -> q.eq("rule_name", condition.getRuleName()));
        }
        if (condition.getId() != null) {
            queryWrapper.and(q -> q.eq("id", condition.getId()));
        }
    }

    @Override
    protected BaseMapper<ConfigRiskRuleDO> getMapper() {
        return configRiskRuleMapper;
    }
}
