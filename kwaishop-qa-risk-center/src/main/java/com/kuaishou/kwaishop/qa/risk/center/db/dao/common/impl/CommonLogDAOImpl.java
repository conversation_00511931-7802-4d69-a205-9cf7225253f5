package com.kuaishou.kwaishop.qa.risk.center.db.dao.common.impl;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.common.CommonLogDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.common.CommonLogDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.common.CommonLogMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.common.CommonLogQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.common.model.bo.LogDataBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.common.model.enums.LogDateTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.common.model.enums.LogStatisticsTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.common.model.enums.LogTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-24
 */
@Repository
public class CommonLogDAOImpl extends BaseDAOImpl<CommonLogDO, CommonLogQueryCondition> implements CommonLogDAO {

    @Autowired
    private CommonLogMapper commonLogMapper;

    @Override
    protected void fillQueryCondition(CommonLogQueryCondition condition, QueryWrapper<CommonLogDO> queryWrapper) {
        if (condition.getDateType() != null && LogDateTypeEnum.of(condition.getDateType()) != null) {
            queryWrapper.and(q -> q.eq("date_type", condition.getDateType()));
        }
        if (condition.getStatisticsType() != null && LogStatisticsTypeEnum.of(condition.getStatisticsType()) != null) {
            queryWrapper.and(q -> q.eq("statistics_type", condition.getStatisticsType()));
        }
        if (condition.getLogType() != null && LogTypeEnum.of(condition.getLogType()) != null) {
            queryWrapper.and(q -> q.eq("log_type", condition.getLogType()));
        }
        if (StringUtils.isNotBlank(condition.getDt())) {
            queryWrapper.and(q -> q.eq("dt", condition.getDt()));
        }
        if (StringUtils.isNotBlank(condition.getName())) {
            queryWrapper.and(q -> q.eq("name", condition.getName()));
        }
        if (StringUtils.isNotBlank(condition.getDtStart())) {
            queryWrapper.and(q -> q.ge("dt", condition.getDtStart()));
        }
        if (StringUtils.isNotBlank(condition.getDtEnd())) {
            queryWrapper.and(q -> q.le("dt", condition.getDtEnd()));
        }
    }

    @Override
    protected BaseMapper<CommonLogDO> getMapper() {
        return commonLogMapper;
    }


    @Override
    public List<CommonLogDO> queryLogList(LogDataBO logDataBO) {
        CommonLogQueryCondition condition = CommonLogQueryCondition.builder()
                .dateType(logDataBO.getDateType())
                .statisticsType(logDataBO.getStatisticsType())
                .logType(logDataBO.getLogType())
                .dt(logDataBO.getDt())
                .name(logDataBO.getName())
                .dtStart(logDataBO.getDtStart())
                .dtEnd(logDataBO.getDtEnd())
                .readMaster(logDataBO.getReadMaster())
                .build();
        return queryList(condition);
    }

    @Override
    public Map<String, CommonLogDO> queryLogDtMap(LogDataBO logDataBO) {
        List<CommonLogDO> dataList = queryLogList(logDataBO);
        return CollectionUtils.isEmpty(dataList) ? Maps.newHashMap() : dataList.stream()
                .collect(Maps::newHashMap, (m, v) -> m.put(v.getDt(), v), Map::putAll);
    }

    @Override
    public long batchInsertOrUpdate(List<CommonLogDO> commonLogDOS) {
        if (CollectionUtils.isEmpty(commonLogDOS)) {
            return 0L;
        }
        return commonLogMapper.batchInsertOrUpdate(commonLogDOS);
    }

    @Override
    public long insertOrUpdate(CommonLogDO commonLogDO) {
        if (commonLogDO == null) {
            return 0L;
        }
        return commonLogMapper.insertOrUpdate(commonLogDO);
    }
}
