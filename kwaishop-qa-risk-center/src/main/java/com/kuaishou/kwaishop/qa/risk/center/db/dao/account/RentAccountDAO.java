package com.kuaishou.kwaishop.qa.risk.center.db.dao.account;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.BaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.RentAccountBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-25
 */
public interface RentAccountDAO extends BaseDAO<RentAccountDO> {

    List<RentAccountDO> queryList(RentAccountBO rentAccountBo);

    void rentAccount(RentAccountBO rentAccountBo);

    void returnAccount(RentAccountBO rentAccountBo);

    PageBO<RentAccountDO> queryPageList(RentAccountBO queryBO);

    void extendRent(RentAccountBO rentAccountBO);

    List<RentAccountDO> queryListByOperator(String borrower);
}
