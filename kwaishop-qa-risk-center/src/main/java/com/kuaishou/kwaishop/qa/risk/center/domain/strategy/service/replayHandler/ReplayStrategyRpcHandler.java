package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.replayHandler;

import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskIntegerConfigKey.flowRecordIdDataCacheExecutorSize;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.AbortPolicy;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.phantomthief.scope.Scope;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Maps;
import com.kuaishou.framework.concurrent.BatchAsyncCacheLoader;
import com.kuaishou.framework.concurrent.DynamicThreadExecutor;
import com.kuaishou.framework.concurrent.ExecutorsEx;
import com.kuaishou.framework.scope.TraceContextUtil;
import com.kuaishou.infra.framework.common.util.KsCacheBuilder;
import com.kuaishou.kconf.common.json.JsonMapperUtils;
import com.kuaishou.krpc.config.ReferenceConfig;
import com.kuaishou.krpc.config.annotation.KrpcReference;
import com.kuaishou.krpc.metadata.KrpcCallOptions;
import com.kuaishou.kwaishop.apollo.policy.center.protobuf.policy.KrpcKwaishopApolloPolicyServiceGrpc.IKwaishopApolloPolicyService;
import com.kuaishou.kwaishop.apollo.policy.center.protobuf.policy.PolicyForTrafficReplayRequest;
import com.kuaishou.kwaishop.apollo.policy.center.protobuf.policy.PolicyForTrafficReplayResponse;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.IpPortDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.RecordService;
import com.kuaishou.protobuf.trace.TraceContext;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-17
 */
@Service
@Slf4j
public class ReplayStrategyRpcHandler {

    @KrpcReference(serviceName = "kwaishop-apollo-strategy-center")
    private IKwaishopApolloPolicyService kwaishopApolloPolicyService;

    @Autowired
    private RecordService recordService;

    private static final long MAX_NUMBER_COUNT = 3000;

    private DynamicThreadExecutor flowReplayIdDataCacheExecutor =
            DynamicThreadExecutor.dynamic(flowRecordIdDataCacheExecutorSize::get, n -> {
                ThreadPoolExecutor threadPoolExecutor =
                        ExecutorsEx.newRejectingThreadPool(n, n, "flowReplayIdDataCacheExecutor", new AbortPolicy());
                threadPoolExecutor.allowCoreThreadTimeOut(true);
                return threadPoolExecutor;
            });

    private final LoadingCache<String, IKwaishopApolloPolicyService> flowReplayIpPortCache =
            KsCacheBuilder.newBuilder().maximumSize(MAX_NUMBER_COUNT).refreshAfterWrite(60, TimeUnit.SECONDS)
                    .expireAfterAccess(1, TimeUnit.DAYS).concurrencyLevel(10)
                    .build(new BatchAsyncCacheLoader<String, IKwaishopApolloPolicyService>(1, Duration.ofSeconds(3),
                            flowReplayIdDataCacheExecutor) {
                        @Override
                        public IKwaishopApolloPolicyService load(String ipPort) throws Exception {
                            String[] split = ipPort.split(":");

                            return ReferenceConfig.<IKwaishopApolloPolicyService> newBuilder().serviceNames(
                                            Collections.singletonList(
                                                    "kwaishop-apollo-strategy-center")) // 请求的服务名称，与Kess上注册的名称完全一致
                                    .interfaceName(IKwaishopApolloPolicyService.class.getName()).protocol("grpc")
                                    .directUrl("protocol://" + split[0] + ":" + split[1]) // 指定接口
                                    .build().refer();
                        }
                    });


    public PolicyForTrafficReplayResponse policyByLand(String sceneKey, String paramMap, String landId,
            String sceneType) {
        //         在这个 scope 内里调用业务逻辑就发到泳道上了
        //         比如发起的 grpc 请求
        final PolicyForTrafficReplayResponse[] response = {null};
        Scope.runWithExistScope(new Scope(), () -> {
            TraceContext traceContext = TraceContext.newBuilder().setLaneId(landId).build();
            TraceContextUtil.initTraceContext(traceContext);
            Map<String, Object> originalLogDto = Maps.newHashMap();
            originalLogDto.put("originalRequestFeatureContext", paramMap);
            originalLogDto.put("sceneType", sceneType);
            originalLogDto.put("listType", "ONLINE");
            PolicyForTrafficReplayRequest request = PolicyForTrafficReplayRequest.newBuilder().setSceneKey(sceneKey)
                    .setOriginalLogDto(JsonMapperUtils.toJson(originalLogDto)).build();
            log.info("ReplayStrategyRpcHandler.policyByLand.request:{}", JsonMapperUtils.toJson(request));
            response[0] = kwaishopApolloPolicyService.policyForTrafficReplay(request);
            log.info("ReplayStrategyRpcHandler.policyByLand.response:{}", JsonMapperUtils.toJson(response[0]));
        });
        return response[0];
    }

    public PolicyForTrafficReplayResponse policyByGroup(String sceneKey, String paramMap, String group,
            String sceneType) {
        //         在这个 scope 内里调用业务逻辑就发到泳道上了
        //         比如发起的 grpc 请求
        KrpcCallOptions krpcCallOptions;
        if ("Default".equalsIgnoreCase(group)) {
            krpcCallOptions = KrpcCallOptions.newBuilder().build();
        } else {
            krpcCallOptions = KrpcCallOptions.newBuilder().group(group).build();
        }
        Map<String, Object> originalLogDto = Maps.newHashMap();
        originalLogDto.put("originalRequestFeatureContext", paramMap);
        originalLogDto.put("sceneType", sceneType);
        originalLogDto.put("listType", "ONLINE");
        PolicyForTrafficReplayRequest request = PolicyForTrafficReplayRequest.newBuilder().setSceneKey(sceneKey)
                .setOriginalLogDto(JsonMapperUtils.toJson(originalLogDto)).build();
        log.info("ReplayStrategyRpcHandler.policyByGroup.request:{}", JsonMapperUtils.toJson(request));
        PolicyForTrafficReplayResponse response =
                kwaishopApolloPolicyService.policyForTrafficReplay(request, krpcCallOptions);
        log.info("ReplayStrategyRpcHandler.policyByGroup.response:{}", JsonMapperUtils.toJson(response));
        return response;
    }

    @SneakyThrows
    public PolicyForTrafficReplayResponse policyByIpPort(String sceneKey, String paramMap,
            List<IpPortDto> ipPortDtoList, String sceneType) {
        int randomIndex = ThreadLocalRandom.current().nextInt(0, ipPortDtoList.size());
        IKwaishopApolloPolicyService apiClient = flowReplayIpPortCache.get(
                ipPortDtoList.get(randomIndex).getIp().concat(":")
                        .concat(String.valueOf(ipPortDtoList.get(randomIndex).getPort())));

        Map<String, Object> originalLogDto = Maps.newHashMap();
        originalLogDto.put("originalRequestFeatureContext", paramMap);
        originalLogDto.put("sceneType", sceneType);
        originalLogDto.put("listType", "ONLINE");
        PolicyForTrafficReplayRequest request = PolicyForTrafficReplayRequest.newBuilder().setSceneKey(sceneKey)
                .setOriginalLogDto(JsonMapperUtils.toJson(originalLogDto)).build();
        PolicyForTrafficReplayResponse response = apiClient.policyForTrafficReplay(request);
        return response;
    }

}
