package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.FundsMeasureDynamicConfigDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsMeasureDynamicConfigDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.FundsMeasureDynamicConfigMapper;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024/3/12 21:45
 */
@Component
public class FundsMeasureDynamicConfigDAOImpl implements FundsMeasureDynamicConfigDAO {
    @Autowired
    private FundsMeasureDynamicConfigMapper fundsMeasureDynamicConfigMapper;
    @Override
    public List<FundsMeasureDynamicConfigDO> getConfigList(String moduleName) {
        return fundsMeasureDynamicConfigMapper.getConfigList(moduleName);
    }
}
