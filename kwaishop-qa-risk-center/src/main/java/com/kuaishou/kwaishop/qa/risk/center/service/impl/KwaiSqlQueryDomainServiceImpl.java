package com.kuaishou.kwaishop.qa.risk.center.service.impl;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.biz.KwaiSqlQueryBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Dm2GetByServiceCodeRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Dm2GetByServiceCodeResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Dm2QueryDebugListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.Dm2QueryDebugListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DmGetByQueryIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DmGetByQueryIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DmQueryRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DmQueryResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.GetColumnListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.GetColumnListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.GetMetaDatabaseListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.GetMetaDatabaseListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.GetTableListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.GetTableListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.KrpcKwaiSqlQueryDomainServiceGrpc.KwaiSqlQueryDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.KwaiSqlQueryServiceRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.KwaiSqlQueryServiceResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryByQueryIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryByQueryIdResponse;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-18
 */

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class KwaiSqlQueryDomainServiceImpl extends KwaiSqlQueryDomainServiceImplBaseV2 {

    @Autowired
    private KwaiSqlQueryBizService kwaiSqlQueryBizService;

    public KwaiSqlQueryServiceResponse fetchDpData(KwaiSqlQueryServiceRequest request) {
        log.info("[KwaiSqlQueryDomainServiceImpl] fetchDpData request: {}", ProtobufUtil.protoToJsonString(request));
        return kwaiSqlQueryBizService.fetchDpData(request);

    }

    public KwaiSqlQueryServiceResponse fetchSqlColumn(KwaiSqlQueryServiceRequest request) {
        log.info("[KwaiSqlQueryDomainServiceImpl] fetchDpData request: {}", ProtobufUtil.protoToJsonString(request));
        return kwaiSqlQueryBizService.fetchSqlColumn(request);

    }

    @Override
    public GetMetaDatabaseListResponse getMetaDatabaseList(GetMetaDatabaseListRequest request) {
        log.info("[KwaiSqlQueryDomainServiceImpl] getMetaDatabaseList request: {}",
                ProtobufUtil.protoToJsonString(request));
        return kwaiSqlQueryBizService.getMetaDatabaseList(request);
    }

    @Override
    public GetTableListResponse getTableList(GetTableListRequest request) {
        log.info("[KwaiSqlQueryDomainServiceImpl] getTableList request: {}", ProtobufUtil.protoToJsonString(request));
        return kwaiSqlQueryBizService.getTableList(request);
    }

    @Override
    public GetColumnListResponse getColumnList(GetColumnListRequest request) {
        log.info("[KwaiSqlQueryDomainServiceImpl] getColumnList request: {}", ProtobufUtil.protoToJsonString(request));
        return kwaiSqlQueryBizService.getColumnList(request);
    }

    @Override
    public DmGetByQueryIdResponse dmGetByQueryId(DmGetByQueryIdRequest request) {
        log.info("[KwaiSqlQueryDomainServiceImpl] dmGetByQueryId request: {}", ProtobufUtil.protoToJsonString(request));
        return kwaiSqlQueryBizService.dmGetByQueryId(request);
    }

    public DmQueryResponse dmQuery(DmQueryRequest request) {
        log.info("[KwaiSqlQueryDomainServiceImpl] dmQuery request: {}", ProtobufUtil.protoToJsonString(request));
        return kwaiSqlQueryBizService.dmQuery(request);
    }


    @Override
    public QueryByQueryIdResponse queryByQueryId(QueryByQueryIdRequest request) {
        log.info("[KwaiSqlQueryDomainServiceImpl] queryByQueryId request: {}",
                ProtobufUtil.protoToJsonString(request));
        return kwaiSqlQueryBizService.dmQueryByQueryId(request);
    }

    @Override
    public Dm2GetByServiceCodeResponse dm2GetByServiceCode(Dm2GetByServiceCodeRequest request) {
        log.info("[KwaiSqlQueryDomainServiceImpl] dm2GetByServiceCode request: {}",
                ProtobufUtil.protoToJsonString(request));
        return kwaiSqlQueryBizService.dm2GetByServiceCode(request);
    }

    @Override
    public Dm2QueryDebugListResponse dm2QueryDebugList(Dm2QueryDebugListRequest request) {
        log.info("[KwaiSqlQueryDomainServiceImpl] dm2QueryDebugList request: {}",
                ProtobufUtil.protoToJsonString(request));
        return kwaiSqlQueryBizService.queryDebugList(request);
    }
}
