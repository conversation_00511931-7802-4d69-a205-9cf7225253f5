package com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.ApplyHandler.OLD_PROCESS_KEY;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.RentAccountBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.service.ApprovalService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.DPMApprovalParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.ApprovalBizCodeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.vo.ApprovalEntityDataVO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.RentAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.StartApprovalRequest;

import lombok.extern.slf4j.Slf4j;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2025/1/20 12:01
 * @注释
 */
@Slf4j
@Service
@Lazy
public class OldAccountApprovalServiceImpl implements ApprovalService {

    @Autowired
    private RentAccountBizService rentAccountBizService;

    @Override
    public ApprovalEntityDataVO approvalForm(StartApprovalRequest request, String code, DPMApprovalParam param) {
        ApprovalEntityDataVO vo = new ApprovalEntityDataVO();
        String jsonString = "[{\"fieldName\":\"申请说明\",\"fieldKey\":\"apply_explain\",\"fieldValue\":{\"desc\":\"账号租借权限申请\""
                + ",\"userId\":\"%s\",\"申请人\":\"%s\",\"租借时长\":\"%s\"}}]";
        String businessSummary = String.format(jsonString, request.getUserId(), request.getUserName(), request.getDuration());
        String businessId = "account_test" + System.currentTimeMillis();
        vo.setBusinessExplain(businessId);
        vo.setBusinessId(businessId);
        vo.setFormType(OLD_PROCESS_KEY);
        vo.setProcessKey(OLD_PROCESS_KEY);
        vo.setProcessDefinitionKey(OLD_PROCESS_KEY);
        param.setBusinessSummary(businessSummary);
        if (request.getUserId() > 0) {
            vo.setUserId(String.valueOf(request.getUserId()));
            param.setUserId(String.valueOf(request.getUserId()));
        }
        if (request.getDuration() > 0) {
            vo.setDuration(String.valueOf(request.getDuration()));
            param.setDuration(String.valueOf(request.getDuration()));
        }

        vo.setProcessVariables(param);
        vo.setUsername(request.getUserName());
        vo.setBusinessSummary(businessSummary);
        return vo;
    }

    @Override
    public void execute(Map<String, Object> variables) {
        try {
            log.info("执行账号租借操作");
            RentAccountRequest rentAccountRequest = RentAccountRequest.newBuilder()
                    .setUserId(Long.parseLong((String) variables.get("userId")))
                    .setBorrower((String) variables.get("userName"))
                    .setDuration(Integer.parseInt((String) variables.get("duration")))
                    .build();
            rentAccountBizService.rentAccount(rentAccountRequest);
        } catch (Exception e) {
            log.info("bpm处理账号租借失败:{}", ObjectMapperUtils.toJSON(variables));
            e.printStackTrace();
        }
    }


    @Override
    public String getCode() {
        return ApprovalBizCodeEnum.OLD_ACCOUNT.getCode();
    }
}
