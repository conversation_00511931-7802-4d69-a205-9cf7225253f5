package com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.Application;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Application.AppJoinSetDO;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Application.TianheApplicationDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-28
 */
@Mapper
@DataSourceRouting("kwaishopQaRiskCenter")
public interface TianheApplicationMapper extends BaseMapper<TianheApplicationDO> {

    @Select("SELECT "
            + "a.id AS app_id, a.app_key AS app_key, a.app_name As app_name, a.operators AS operators, a.creator AS creator,"
            + "s.id AS set_id, s.set_name AS set_name "
            + "FROM tianhe_risk_application a "
            + "JOIN tianhe_risk_sample_set s ON a.id = s.app_id "
            + "WHERE a.deleted = 0 AND s.deleted = 0 "
            + "ORDER BY a.id ASC, s.id ASC ")
    List<AppJoinSetDO> queryAllApplication();

    @Insert("INSERT INTO tianhe_risk_application(app_key,app_name,create_time,update_time,deleted,creator,operators,sample_sets,platform_source)"
            + "VALUES(#{appKey},#{appName},#{createTime},#{updateTime},#{deleted},#{creator},#{operators},#{sampleSets},#{platformSource})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertApplication(TianheApplicationDO applicationDO);
}
