package com.kuaishou.kwaishop.qa.risk.center.db.dao.errorcode.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.errorcode.BizAreaDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.errorcode.BizAreaDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.errorcode.BizAreaMapper;

@Repository
public class BizAreaDAOImpl implements BizAreaDAO {

    @Autowired
    private BizAreaMapper bizAreaMapper;

    @Override
    public void insert(BizAreaDO bizAreaDO) {
        long time = System.currentTimeMillis();
        bizAreaDO.setCreateTime(time);
        bizAreaDO.setUpdateTime(time);
        int num = bizAreaMapper.insert(bizAreaDO);
        if (num != 1) {
            throw new RuntimeException("insert bizAreaDO error");
        }

    }

    @Override
    public BizAreaDO getById(Long id) {
        BizAreaDO bizAreaDO = bizAreaMapper.selectById(id);
        return bizAreaDO;
    }

    @Override
    public void updateById(BizAreaDO bizAreaDO) {
        long time = System.currentTimeMillis();
        bizAreaDO.setUpdateTime(time);
        Wrapper<BizAreaDO> where = new LambdaQueryWrapper<BizAreaDO>()
                .eq(BizAreaDO::getId, bizAreaDO.getId());
        int num = bizAreaMapper.update(bizAreaDO, where);
        if (num != 1) {
            throw new RuntimeException("updateById bizAreaDO error");
        }
    }

    @Override
    public void deleteById(Long id) {
        if (id == null || id <= 0) {
            throw new RuntimeException("updateById bizAreaDO error for id is illegal");
        }
        int num = bizAreaMapper.deleteById(id);
        if (num != 1) {
            throw new RuntimeException("updateById bizAreaDO error");
        }
    }

    @Override
    public BizAreaDO queryByBizArea(String areaCode) {

        LambdaQueryWrapper<BizAreaDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(areaCode)) {
            queryWrapper
                    .eq(BizAreaDO::getAreaCode, areaCode);
        }

        return bizAreaMapper.selectOne(queryWrapper);
    }


    @Override
    public IPage<BizAreaDO> getBizAreaList(String creator, Long current, Long size) {

        IPage<BizAreaDO> page = new Page<>(current, size);
        LambdaQueryWrapper<BizAreaDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(creator)) {
            queryWrapper
                    .eq(BizAreaDO::getCreator, creator);
        }
        IPage<BizAreaDO> selectPage = bizAreaMapper.selectPage(page, queryWrapper);
        return selectPage;
    }
}
