package com.kuaishou.kwaishop.qa.risk.center.db.query.risk;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-01-05
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FundsSubRiskQueryCondition extends BaseQueryCondition {

    private String riskEventId;
    private String subRiskName;
    private String riskType;
    private Long level;
    private String department;
    private String businessDomain;

}
