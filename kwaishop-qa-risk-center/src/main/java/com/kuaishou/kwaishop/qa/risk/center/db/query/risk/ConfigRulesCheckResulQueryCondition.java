package com.kuaishou.kwaishop.qa.risk.center.db.query.risk;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ConfigRulesCheckResulQueryCondition extends BaseQueryCondition {
    private String reportName;
    private List<Integer> result;
    private List<Integer> type;
    private long startTime;
    private long endTime;
}
