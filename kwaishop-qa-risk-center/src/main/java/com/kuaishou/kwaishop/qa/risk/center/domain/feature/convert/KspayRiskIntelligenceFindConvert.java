package com.kuaishou.kwaishop.qa.risk.center.domain.feature.convert;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundRiskIntelligenceFindDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundRiskIntelligenceQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskIntelligenceFindListParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.PageKspayRiskIntelligenceFindListDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryPageRiskFieldsRequest;

public interface KspayRiskIntelligenceFindConvert {
    FundRiskIntelligenceQueryCondition buildRiskIntelligenceListQueryCondition(QueryPageRiskFieldsRequest request);
    KspayRiskIntelligenceFindListParam buildRiskIntelligenceParam(FundRiskIntelligenceFindDO fundRiskIntelligenceFindDO);

    PageKspayRiskIntelligenceFindListDTO convertToIntelligencePageDTO(List<KspayRiskIntelligenceFindListParam> kspayRiskIntelligenceFindListParams,
                                                                      Integer pageNo, Integer pageSize, Long total);

    FundRiskIntelligenceFindDO buildUpdateRiskLevelParam(FundRiskIntelligenceFindDO fundRiskIntelligenceFindDO,
                                                        KspayRiskIntelligenceFindListParam request, String riskLevel);
}
