package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;


public enum DiffConclusionStatus {

    UN_KNOWN(0, "未执行"),
    NO_DIFF(1, "无"),
    HAS_DIFF(2, "有");

    private final int code;
    private final String description;

    DiffConclusionStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return this.description;
    }

    public static DiffConclusionStatus fromCode(int code) {
        for (DiffConclusionStatus status : DiffConclusionStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }
}
