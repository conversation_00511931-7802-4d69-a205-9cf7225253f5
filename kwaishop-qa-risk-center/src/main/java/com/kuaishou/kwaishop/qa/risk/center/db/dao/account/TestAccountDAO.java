package com.kuaishou.kwaishop.qa.risk.center.db.dao.account;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.BaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TestAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TestAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAccountBO;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/15 15:46
 * @注释
 */
public interface TestAccountDAO extends BaseDAO<TestAccountDO> {
    List<TestAccountDO> queryList(UserAccountBO userAccountBO);

    List<TestAccountDO> queryByUid(Long uid);

    TestAccountDO queryByBid(Long bid);

    PageBO<TestAccountDO> queryPageList(TestAccountBO testAccountBO, List<Long> userIds);

    //根据用户user_id查数据
    List<TestAccountDO> queryUserAccountByUserId(Long userId);

    //查询某个用户租借的所有账号
    List<TestAccountDO> queryUserAccountByIds(List<Long> ids);

    void deleteTestAccount(Long uid);


}
