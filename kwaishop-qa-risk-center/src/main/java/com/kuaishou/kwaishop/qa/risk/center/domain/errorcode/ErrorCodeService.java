package com.kuaishou.kwaishop.qa.risk.center.domain.errorcode;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.errorcode.ErrorCodeDO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.AddErrorCodeRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.DeleteErrorCodeRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.ErrorCodeListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.ErrorCodeListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.ErrorInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.UpdateErrorCodeRequest;

public interface ErrorCodeService {

    void addErrorCode(AddErrorCodeRequest addErrorCodeRequest);

    void updateErrorCode(UpdateErrorCodeRequest updateRequest);

    void deleteErrorCode(DeleteErrorCodeRequest deleteRequest);

    ErrorCodeDO queryByErrorCode(ErrorInfoRequest errorInfoRequest);

    ErrorCodeListResponse getErrorCodeList(ErrorCodeListRequest listRequest);

}
