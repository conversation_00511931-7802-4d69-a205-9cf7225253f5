package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.redis;

import com.kuaishou.framework.jedis.JedisClusterConfig;

import kuaishou.common.BizDef;
import lombok.NonNull;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-21
 */
public enum ThemisQaRiskRedisConfig implements JedisClusterConfig {

    kwaishopThemisTools;

    @Override
    public String bizName() {
        return name();
    }

    @NonNull
    @Override
    public BizDef bizDef() {
        return BizDef.KWAISHOP_QA_RISK;
    }
}
