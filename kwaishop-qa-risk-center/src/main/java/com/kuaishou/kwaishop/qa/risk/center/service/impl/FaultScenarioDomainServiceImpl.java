package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.biz.FaultScenarioBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultMethodRecommendEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.service.AccuracyService;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.service.FaultScenarioService;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.service.FaultServiceInfoService;
import com.kuaishou.kwaishop.qa.risk.center.domain.openapi.service.RpcMonitorQueryService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.CalculateFaultTeamStatisticsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.CalculateFaultTeamStatisticsResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.CreateFaultScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.CreateFaultScenarioResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.DeleteFaultScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.DeleteFaultScenarioResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.ExportAccuracyDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.ExportAccuracyDataResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.GetScenarioWithOperatorRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.GetScenarioWithOperatorResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.ImportFaultScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.ImportFaultScenarioResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.KrpcFaultScenarioDomainServiceGrpc.FaultScenarioDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryAccuracyAnalyzeTaskRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryAccuracyAnalyzeTaskResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultChangeRecordListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultChangeRecordListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultScenarioMethodRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultScenarioMethodResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryPageScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryPageScenarioResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryRecommendSrcMethodRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryRecommendSrcMethodResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryScenarioListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryScenarioListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QuerySrcServiceCallCountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QuerySrcServiceCallCountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateFaultScenarioInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateFaultScenarioInfoResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateFaultScenarioStatusRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateFaultScenarioStatusResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateRecommendMethodTypeRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateRecommendMethodTypeResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class FaultScenarioDomainServiceImpl extends FaultScenarioDomainServiceImplBaseV2 {

    @Autowired
    private FaultScenarioBizService faultScenarioBizService;

    @Autowired
    private FaultScenarioService faultScenarioService;

    @Autowired
    private AccuracyService accuracyService;

    @Autowired
    private RpcMonitorQueryService rpcMonitorQueryService;

    @Autowired
    private FaultServiceInfoService faultServiceInfoService;

    @Override
    public ImportFaultScenarioResponse importFaultScenario(ImportFaultScenarioRequest request) {
        log.error("[FaultScenarioDomainServiceImpl] ImportFaultScenario , req: {} ", protoToJsonString(request));
        try {
            faultScenarioService.importFaultScenario(request);
            return ImportFaultScenarioResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .buildPartial();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] ImportFaultScenario error, req: {}, exception: ", protoToJsonString(request), e);
            return ImportFaultScenarioResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();

        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] ImportFaultScenario error, req: {}, exception: ", protoToJsonString(request), e);
            return ImportFaultScenarioResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();

        }

    }

    @Override
    public GetScenarioWithOperatorResponse getScenarioWithOperator(GetScenarioWithOperatorRequest request) {
        log.error("[FaultScenarioDomainServiceImpl] GetScenarioWithOperator , req: {} ", protoToJsonString(request));
        try {
            return GetScenarioWithOperatorResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(faultScenarioBizService.getScenarioByOperator(request.getOperator()))
                    .build();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] GetScenarioWithOperator error, req: {}, exception: ", protoToJsonString(request), e);

            return GetScenarioWithOperatorResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] GetScenarioWithOperator error, req: {}, exception: ", protoToJsonString(request), e);

            return GetScenarioWithOperatorResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryPageScenarioResponse queryPageFaultScenario(QueryPageScenarioRequest request) {
        log.error("[FaultScenarioDomainServiceImpl] queryPageFaultScenario , req: {} ", protoToJsonString(request));
        try {
            return QueryPageScenarioResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(faultScenarioBizService.queryPageFaultScenario(request))
                    .build();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] queryPageFaultScenario error, req: {}, exception: ", protoToJsonString(request), e);

            return QueryPageScenarioResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] queryPageFaultScenario error, req: {}, exception: ", protoToJsonString(request), e);

            return QueryPageScenarioResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    @Override
    public QueryScenarioListResponse queryFaultScenarioList(QueryScenarioListRequest request) {
        log.error("[FaultScenarioDomainServiceImpl] queryFaultScenarioPageList , req: {} ", protoToJsonString(request));

        try {

            return QueryScenarioListResponse
                    .newBuilder()
                    .addAllData(faultScenarioBizService.queryFaultScenarioList(request))
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] queryFaultScenarioPageList error, req: {}, exception: ", protoToJsonString(request), e);

            return QueryScenarioListResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] queryFaultScenarioPageList error, req: {}, exception: ", protoToJsonString(request), e);

            return QueryScenarioListResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    @Override
    public QueryAccuracyAnalyzeTaskResponse queryAccuracyAnalyzeTask(QueryAccuracyAnalyzeTaskRequest request) {
        log.error("[FaultScenarioDomainServiceImpl] queryAccuracyAnalyzeTask , req: {} ", protoToJsonString(request));
        try {
            return QueryAccuracyAnalyzeTaskResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(accuracyService.queryAccuracyAnalyzeTaskById(request.getId()))
                    .build();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] queryAccuracyAnalyzeTask error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryAccuracyAnalyzeTaskResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] queryAccuracyAnalyzeTask error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryAccuracyAnalyzeTaskResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }


    @Override
    public ExportAccuracyDataResponse exportAccuracyData(ExportAccuracyDataRequest request) {
        log.error("[FaultScenarioDomainServiceImpl] exportAccuracyData , req: {} ", protoToJsonString(request));
        try {
            accuracyService.exportAccuracyData();
            return ExportAccuracyDataResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .buildPartial();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] exportAccuracyData error, req: {}, exception: ", protoToJsonString(request), e);
            return ExportAccuracyDataResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();

        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] exportAccuracyData error, req: {}, exception: ", protoToJsonString(request), e);
            return ExportAccuracyDataResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }


    @Override
    public QueryFaultScenarioMethodResponse queryFaultScenarioMethod(QueryFaultScenarioMethodRequest request) {
        log.error("[FaultScenarioDomainServiceImpl] QueryFaultScenarioMethod , req: {} ", protoToJsonString(request));
        try {
            return QueryFaultScenarioMethodResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(faultScenarioBizService.queryFaultScenarioMetohd(request))
                    .buildPartial();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] QueryFaultScenarioMethod error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultScenarioMethodResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();

        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] QueryFaultScenarioMethod error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultScenarioMethodResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }


    @Override
    public UpdateFaultScenarioStatusResponse updateFaultScenarioStatus(UpdateFaultScenarioStatusRequest request) {
        log.error("[FaultScenarioDomainServiceImpl] updateFaultScenarioStatus , req: {} ", protoToJsonString(request));
        try {
            faultScenarioService.updateFaultScenarioStatus(request);
            return UpdateFaultScenarioStatusResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .buildPartial();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] updateFaultScenarioStatus error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateFaultScenarioStatusResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();

        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] updateFaultScenarioStatus error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateFaultScenarioStatusResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryFaultChangeRecordListResponse queryFaultChangeRecordList(QueryFaultChangeRecordListRequest request) {
        log.error("[FaultScenarioDomainServiceImpl] updateFaultScenarioStatus , req: {} ", protoToJsonString(request));
        try {
            return QueryFaultChangeRecordListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(faultScenarioService.queryFaultChangeRecordList(request))
                    .buildPartial();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] updateFaultScenarioStatus error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultChangeRecordListResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();

        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] updateFaultScenarioStatus error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultChangeRecordListResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    @Override
    public UpdateFaultScenarioInfoResponse updateFaultScenarioInfo(UpdateFaultScenarioInfoRequest request) {
        log.error("[FaultScenarioDomainServiceImpl] updateFaultScenarioInfo , req: {} ", protoToJsonString(request));
        try {
            faultScenarioService.updateFaultScenarioInfo(request);
            return UpdateFaultScenarioInfoResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .buildPartial();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] updateFaultScenarioInfo error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateFaultScenarioInfoResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();

        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] updateFaultScenarioInfo error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateFaultScenarioInfoResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    @Override
    public CreateFaultScenarioResponse createFaultScenario(CreateFaultScenarioRequest request) {
        log.error("[FaultScenarioDomainServiceImpl] createFaultScenario , req: {} ", protoToJsonString(request));
        try {
            faultScenarioService.createFaultScenario(request);
            return CreateFaultScenarioResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .buildPartial();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] createFaultScenario error, req: {}, exception: ", protoToJsonString(request), e);
            return CreateFaultScenarioResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();

        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] createFaultScenario error, req: {}, exception: ", protoToJsonString(request), e);
            return CreateFaultScenarioResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public DeleteFaultScenarioResponse deleteFaultScenario(DeleteFaultScenarioRequest request) {

        log.error("[FaultScenarioDomainServiceImpl] deleteFaultScenario , req: {} ", protoToJsonString(request));
        try {
            faultScenarioService.deleteFaultScenario(request);
            return DeleteFaultScenarioResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .buildPartial();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] deleteFaultScenario error, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteFaultScenarioResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();

        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] deleteFaultScenario error, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteFaultScenarioResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public CalculateFaultTeamStatisticsResponse calculateFaultTeamStatistics(CalculateFaultTeamStatisticsRequest request) {
        log.error("[FaultScenarioDomainServiceImpl] calculateFaultTeamStatistics , req: {} ", protoToJsonString(request));
        try {
            return CalculateFaultTeamStatisticsResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(faultScenarioBizService.calculateFaultTeamStatistics(request.getStartTime(), request.getEndTime()))
                    .buildPartial();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] calculateFaultTeamStatistics error, req: {}, exception: ", protoToJsonString(request), e);
            return CalculateFaultTeamStatisticsResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();

        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] calculateFaultTeamStatistics error, req: {}, exception: ", protoToJsonString(request), e);
            return CalculateFaultTeamStatisticsResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    @Override
    public QuerySrcServiceCallCountResponse querySrcServiceCallCount(QuerySrcServiceCallCountRequest request) {
        log.error("[FaultScenarioDomainServiceImpl] querySrcServiceCallCount , req: {} ", protoToJsonString(request));
        try {

            return QuerySrcServiceCallCountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(rpcMonitorQueryService.querySrcMethod(request.getSrcKsn(), request.getStartTime(), request.getEndTime()))
                    .buildPartial();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] querySrcServiceCallCount error, req: {}, exception: ", protoToJsonString(request), e);
            return QuerySrcServiceCallCountResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();

        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] querySrcServiceCallCount error, req: {}, exception: ", protoToJsonString(request), e);
            return QuerySrcServiceCallCountResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    @Override
    public QueryRecommendSrcMethodResponse queryRecommendSrcMethod(QueryRecommendSrcMethodRequest request) {
        log.error("[FaultScenarioDomainServiceImpl] queryRecommendSrcMethod , req: {} ", protoToJsonString(request));
        try {

            return QueryRecommendSrcMethodResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(faultServiceInfoService.queryRecommendSrcMethod(request))
                    .buildPartial();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] queryRecommendSrcMethod error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryRecommendSrcMethodResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();

        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] queryRecommendSrcMethod error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryRecommendSrcMethodResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateRecommendMethodTypeResponse updateRecommendMethodType(UpdateRecommendMethodTypeRequest request) {
        log.error("[FaultScenarioDomainServiceImpl] updateRecommendMethodType , req: {} ", protoToJsonString(request));
        try {
            faultServiceInfoService.updateType(request.getId(), FaultMethodRecommendEnum.of(request.getType()));
            return UpdateRecommendMethodTypeResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .buildPartial();
        } catch (BizException e) {
            log.error("[FaultScenarioDomainServiceImpl] updateRecommendMethodType error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateRecommendMethodTypeResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();

        } catch (Exception e) {
            log.error("[FaultScenarioDomainServiceImpl] updateRecommendMethodType error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateRecommendMethodTypeResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

}
