package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyRecordListDO;

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface StrategyRecordListMapper extends BaseMapper<StrategyRecordListDO> {

    @Insert("INSERT INTO strategy_record_list (task_id, task_name, task_description, expect_lane_id, "
            +
            "scene_name, record_partition, status, create_time, update_time, creator, record_sql) "
            +
            "VALUES (#{taskId} ,#{taskName}, #{taskDescription}, #{expectLaneId}, #{sceneName},"
            +
            " #{recordPartition}, #{status}, #{createTime}, #{updateTime}, #{creator}, #{recordSql})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(StrategyRecordListDO record);

    @Update("UPDATE strategy_record_list SET status = #{status}, update_time = #{updateTime} WHERE task_id = #{taskId}")
    int updateStatusV1(StrategyRecordListDO record);

    // 那个taskId其实就没作用了，只是为了更新状态
    @Update("UPDATE strategy_record_list SET status = #{status} WHERE id = #{taskId}")
    int updateStatus(String taskId, int status);

    @Select("SELECT record_sql FROM strategy_record_list WHERE id = #{taskId}")
    String queryTaskSql(String taskId);

    @Select("SELECT id FROM strategy_record_list WHERE task_id = #{taskId}")
    String queryIdByTaskId(String taskId);

    @Update("UPDATE strategy_record_list SET diff_value = #{diffValue} WHERE id = #{taskId}")
    void updateConclusion(String taskId, String diffValue);
}
