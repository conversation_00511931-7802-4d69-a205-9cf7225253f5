package com.kuaishou.kwaishop.qa.risk.center.db.dao.account;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.BaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagMetaDO;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/15 15:47
 * @注释
 */
public interface TagMetaDAO extends BaseDAO<TagMetaDO> {

    TagMetaDO queryTagMetaByCode(String code);

    TagMetaDO queryTagMetaByName(String name);

    List<TagMetaDO> selectAll();

    long insert(TagMetaDO tagMetaDO);

    void deleteById(TagMetaDO tagMetaDO);


}
