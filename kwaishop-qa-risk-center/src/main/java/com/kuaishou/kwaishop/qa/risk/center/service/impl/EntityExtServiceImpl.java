package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.biz.MemberExtService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.biz.TeamExtService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.KrpcEntityExtServiceGrpc.EntityExtServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.PageMemberInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.PageTeamInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryMemberPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryMemberPageListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryTeamListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryTeamListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryTeamNamesRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryTeamNamesResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryTeamPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryTeamPageListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.TeamInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.TeamInfoMap;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.UpdateMembersRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.UpdateMembersResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-27
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class EntityExtServiceImpl extends EntityExtServiceImplBaseV2 {

    @Autowired
    private TeamExtService teamExtService;

    @Autowired
    private MemberExtService memberExtService;


    @Override
    public QueryTeamListResponse queryTeamList(QueryTeamListRequest request) {
        log.info("[EntityExtServiceImpl] queryTeamList request: {}", protoToJsonString(request));
        try {
            List<TeamInfo> data = teamExtService.queryTeamList(request);
            return QueryTeamListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(data)
                    .build();
        } catch (BizException e) {
            log.error("[EntityExtServiceImpl] queryTeamList bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryTeamListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[EntityExtServiceImpl] queryTeamList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryTeamListResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryTeamPageListResponse queryTeamPageList(QueryTeamPageListRequest request) {
        log.info("[EntityExtServiceImpl] queryTeamPageList request: {}", protoToJsonString(request));
        try {
            PageTeamInfo data = teamExtService.queryTeamPageList(request);
            return QueryTeamPageListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(data)
                    .build();
        } catch (BizException e) {
            log.error("[EntityExtServiceImpl] queryTeamPageList bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryTeamPageListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[EntityExtServiceImpl] queryTeamPageList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryTeamPageListResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateMembersResponse updateMembers(UpdateMembersRequest request) {
        log.info("[EntityExtServiceImpl] updateMembers request: {}", protoToJsonString(request));
        try {
            memberExtService.updateMembers(request);
            return UpdateMembersResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[EntityExtServiceImpl] updateMembers bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateMembersResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[EntityExtServiceImpl] updateMembers error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateMembersResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryMemberPageListResponse queryMemberPageList(QueryMemberPageListRequest request) {
        log.info("[EntityExtServiceImpl] queryMemberPageList request: {}", protoToJsonString(request));
        try {
            PageMemberInfo res = memberExtService.queryMemberPageList(request);
            return QueryMemberPageListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(res)
                    .build();
        } catch (BizException e) {
            log.error("[EntityExtServiceImpl] queryMemberPageList bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryMemberPageListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[EntityExtServiceImpl] queryMemberPageList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryMemberPageListResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryTeamNamesResponse getTeamNames(QueryTeamNamesRequest request) {
        log.info("[EntityExtServiceImpl] queryTeamNames request: {}", protoToJsonString(request));
        try {
            Map<Long, String> longStringMap = teamExtService.queryTeamList();
            return buildQueryNamesResponse(longStringMap);
        } catch (BizException e) {
            log.error("[EntityExtServiceImpl] queryTeamNames bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryTeamNamesResponse.newBuilder()
                    .setResult(e.getCode())
                    .build();
        } catch (Exception e) {
            log.error("[EntityExtServiceImpl] queryTeamNames error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryTeamNamesResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    private QueryTeamNamesResponse buildQueryNamesResponse(Map<Long, String> longStringMap) {
        QueryTeamNamesResponse.Builder res = QueryTeamNamesResponse.newBuilder();
        res.setResult(BaseResultCode.SUCCESS_VALUE);
        longStringMap.forEach((k, v) -> {
            res.addTeamInfo(TeamInfoMap.newBuilder()
                    .setId(String.valueOf(k))
                    .setName(v)
                    .build());
        });
        return res.build();
    }
}
