package com.kuaishou.kwaishop.qa.risk.center.domain.account.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.utils.excel.ExcelUtils.checkAndGetExcelDataV2;

import java.util.Collections;
import java.util.List;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.UserAccountConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportAccountExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportTokenExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TokenImportExcelModel;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.AccountExcelImportService;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.BaseExcelModel;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.ValidResultBO;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.read.CustomValidator;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.read.KExcelCheckModel;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.read.KExcelCheckRsp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
@Slf4j
public abstract class AbstractExcelImportService implements AccountExcelImportService {

    @Autowired
    private UserAccountConvert userAccountConvert;

    @Override
    public List<UserAccountDO> getAccountDOList(ImportAccountExcelBO excelBO) {
        List<BaseExcelModel> excelModels = checkAndGetExcelDataV2(excelBO.getCdnUrl(), getScene(), getModel(), buildValidator());
        return buildAccountDO(excelModels, excelBO);
    }

    @Override
    public List<UserAccountDO> getAccountTokenDOList(ImportTokenExcelBO excelBO) {
        List<BaseExcelModel> excelModels = checkAndGetExcelDataV2(excelBO.getCdnUrl(), getScene(), new TokenImportExcelModel(), null);
        return buildAccountTokenDO(excelModels, excelBO);
    }

    protected abstract List<UserAccountDO> buildAccountDO(List<BaseExcelModel> excelModels, ImportAccountExcelBO excelBO);

    protected List<CustomValidator> buildValidator() {
        CustomValidator<? extends BaseExcelModel> customValidator = (CustomValidator<BaseExcelModel>) validateList -> {
            KExcelCheckRsp<BaseExcelModel>
                    excelCheckRsp = new KExcelCheckRsp<>();
            List<KExcelCheckModel<BaseExcelModel>> failList = Lists.newArrayList();
            List<KExcelCheckModel<BaseExcelModel>> successList = Lists.newArrayList();
            validateList.forEach(e -> {
                ValidResultBO resultBO = e.customValid();
                if (resultBO == null || resultBO.getResult()) {
                    successList.add(KExcelCheckModel.of(e, Boolean.TRUE, StringUtils.EMPTY));
                } else {
                    failList.add(KExcelCheckModel.of(e, Boolean.FALSE, resultBO.getFailMsg()));
                }
            });
            excelCheckRsp.setFailList(failList);
            excelCheckRsp.setSuccessList(successList);
            return excelCheckRsp;
        };
        return Collections.singletonList(customValidator);
    }

    protected abstract String getScene();

    protected abstract BaseExcelModel getModel();

}
