package com.kuaishou.kwaishop.qa.risk.center.domain.account.service;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.IMPORT_TYPE_NOT_RIGHT_ERROR;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserImportTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
@Service
@Lazy
@Slf4j
public class AccountExcelImportFactory {

    @Autowired
    private List<AccountExcelImportService> excelImportServices;

    private Map<Integer, AccountExcelImportService> excelServiceMap;

    @PostConstruct
    private void init() {
        excelServiceMap = new HashMap<>();
        excelImportServices.forEach(p -> excelServiceMap.put(p.getCode(), p));
    }

    public AccountExcelImportService getService(Integer code) {
        if (UserImportTypeEnum.of(code) != null) {
            if (excelServiceMap.containsKey(code)) {
                return excelServiceMap.get(code);
            }
        }
        throw new BizException(IMPORT_TYPE_NOT_RIGHT_ERROR);
    }
}
