package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import java.util.List;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-29
 */
@Data
@Builder
public class StrategyTestDataPool {

    private String featureContext; // 这个放请求参数

    private List<String> actionIds; //这里放命中的动作id

    private List<String> strategyIds; //这里放命中的策略id

    private String sceneKey; // 这个放场景key

    private String id; // 这个放id

    private Integer diffConclusion; // 这个放diff结果

    private List<String> actualActionIds; // 这个放实际的审核结果

    private List<String> actualStrategyIds; // 这个放预期的审核结果

    private Long replayTime;

    private String laneId;

    private String replayLaneId;
}
