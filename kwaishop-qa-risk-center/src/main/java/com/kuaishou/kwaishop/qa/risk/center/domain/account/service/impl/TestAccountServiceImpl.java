package com.kuaishou.kwaishop.qa.risk.center.domain.account.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_DATA_ERROR;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.TagTestAccountRelDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.TestAccountDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.impl.RentTestAccountRecordDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentTestAccountRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagTestAccountRelDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TestAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TestAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TestAccountRentBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.TestAccountService;

import lombok.extern.slf4j.Slf4j;

@Lazy
@Service
@Slf4j
public class TestAccountServiceImpl implements TestAccountService {

    @Autowired
    private TestAccountDAO testAccountDAO;

    @Autowired
    private TagTestAccountRelDAO tagTestAccountRelDAO;

    @Autowired
    private RentTestAccountRecordDAOImpl rentTestAccountRecordDAO;

    @Override
    public PageBO<RentTestAccountRecordDO> queryMyRentRecords(TestAccountBO queryBO) {
        if (queryBO == null) {
            throw new BizException(ACCOUNT_DATA_ERROR);
        }
        //查询自己所有租用账号
        PageBO<RentTestAccountRecordDO> pageBO = rentTestAccountRecordDAO.queryPageList(queryBO);
        //查询并组装 testaccount
        List<Long> list = pageBO.getData().stream().map(item -> item.getTestAccountId()
        ).collect(Collectors.toList());
        List<TestAccountDO> testAccountDOs = testAccountDAO.queryUserAccountByIds(list);
        //获取账号审批通过未过期的tag
        List<TagTestAccountRelDO> tagTestAccountRelList = tagTestAccountRelDAO.getTestTagInAccounts(list,
                Arrays.asList(2));
        //查询并组装 testaccount tag
        testAccountDOs.stream().forEach(item -> item.setTestAccountTags(
                        tagTestAccountRelList.stream().filter(n -> n.getTestAccountId().equals(item.getId()))
                                .collect(Collectors.toList())
                )
        );
        pageBO.getData().stream().forEach(item -> item.setTestAccount(
                testAccountDOs.stream().filter(x -> x.getId().equals(item.getTestAccountId())).findFirst()
                        .orElse(null))
        );
        return pageBO;
    }

    @Override
    public PageBO<TestAccountDO> queryMyTestAccountRecords(TestAccountBO queryBO) {
        if (queryBO == null) {
            throw new BizException(ACCOUNT_DATA_ERROR);
        }
        PageBO<TestAccountDO> pageBO = testAccountDAO.queryPageList(queryBO, null);
        List<Long> list = pageBO.getData().stream().map(item -> item.getId()).collect(Collectors.toList());
        //获取账号审批通过未过期的tag
        List<TagTestAccountRelDO> tagTestAccountRelList = tagTestAccountRelDAO.getTestTagInAccounts(list,
                Arrays.asList(2));
        //查询并组装 testaccount tag
        pageBO.getData().stream().forEach(item -> item.setTestAccountTags(
                        tagTestAccountRelList.stream().filter(n -> n.getTestAccountId().equals(item.getId()))
                                .collect(Collectors.toList())
                )
        );
        return pageBO;
    }

    @Override
    public PageBO<TestAccountDO> queryTestAccountPage(TestAccountBO queryBO) {
        if (queryBO == null) {
            throw new BizException(ACCOUNT_DATA_ERROR);
        }
        PageBO<TestAccountDO> pageBO = testAccountDAO.queryPageList(queryBO, null);
        return null;
    }

    @Override
    public List<RentTestAccountRecordDO> queryAllRentRecords(TestAccountRentBO testAccountRentBO) {
        return rentTestAccountRecordDAO.queryRentTestAccountList(testAccountRentBO);
    }

    @Override
    public List<TagTestAccountRelDO> queryAllTagsAccountsDetail() {
        return tagTestAccountRelDAO.getTagListByTestAccountIds(Arrays.asList(2));
    }

    @Override
    public PageBO<TestAccountDO> queryAllTestAccountRecords(TestAccountBO queryBO) {
        if (queryBO == null) {
            throw new BizException(ACCOUNT_DATA_ERROR);
        }
        PageBO<TestAccountDO> pageBO;
        //适配根据标签筛选账号
        if (queryBO.getTagValue() == null || queryBO.getTagValue().isEmpty()) {
            pageBO = testAccountDAO.queryPageList(queryBO, null);
            List<Long> list = pageBO.getData().stream().map(item -> item.getId()).collect(Collectors.toList());
            //获取账号审批通过未过期的tag
            List<TagTestAccountRelDO> tagTestAccountRelList = tagTestAccountRelDAO.getTestTagInAccounts(list,
                    Arrays.asList(2));
            //查询并组装 testaccount tags
            pageBO.getData().stream().forEach(item -> item.setTestAccountTags(
                            tagTestAccountRelList.stream().filter(n -> n.getTestAccountId().equals(item.getId()))
                                    .collect(Collectors.toList())
                    )
            );
        } else {
            //获取账号审批通过未过期的tag和account
            List<TagTestAccountRelDO> list = tagTestAccountRelDAO.getTestAccountIdsByTag(queryBO.getTagValue());
            List<Long> userIds = list.stream().map(TagTestAccountRelDO::getTestAccountId).collect(Collectors.toList());
            pageBO = testAccountDAO.queryPageList(queryBO, userIds);
            pageBO.getData().stream().forEach(item -> item.setTestAccountTags(
                    list.stream().filter(u -> u.getTestAccountId().equals(item.getKwaiId())).collect(Collectors.toList())
            ));
        }

        return pageBO;
    }
}
