package com.kuaishou.kwaishop.qa.risk.center.db.query.account;

import com.kuaishou.kwaishop.qa.risk.center.db.query.detail.DetailBaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TagTestAccountRelQueryCondition extends DetailBaseQueryCondition {
    /**
     * 用户id
     */
    private Long testAccountId;
}
