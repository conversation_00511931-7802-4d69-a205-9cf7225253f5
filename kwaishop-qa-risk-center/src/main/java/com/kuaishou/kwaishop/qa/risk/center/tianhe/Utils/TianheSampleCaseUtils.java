package com.kuaishou.kwaishop.qa.risk.center.tianhe.Utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.apache.http.HttpEntity;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.kuaishou.blobstore.common.utils.StringUtils;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ActionSetExtendData;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Sample.TianheExecuteSampleDO;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Sample.TianheSampleCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Enums.TianheSampleExecuteStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.Sample.TianheExecuteSampleMapper;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.Sample.TianheSampleCaseMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-30
 */
@Service
@Slf4j
public class TianheSampleCaseUtils {
    @Autowired
    private TianheSampleCaseMapper tianheSampleCaseMapper;
    @Autowired
    private TianheExecuteSampleMapper tianheExecuteSampleMapper;
    private static int uiAutomationCallCount = 0; // 用于UI自动化模版并发


    public boolean isValidUrl(String url) {
        String urlPattern = "^https?://.*$";
        return url.matches(urlPattern);
    }

    public String getUrl(String ext) {
        JsonElement jsonElement = JsonParser.parseString(ext);
        JsonObject jsonObject = jsonElement.getAsJsonObject();
        String prtUrl = jsonObject.has("prtUrl") ? jsonObject.get("prtUrl").getAsString() : null;
        String onlineUrl = jsonObject.has("onlineUrl") ? jsonObject.get("onlineUrl").getAsString() : null;

        JsonObject newJson = new JsonObject();
        newJson.addProperty("prtUrl", prtUrl);
        newJson.addProperty("onlineUrl", onlineUrl);

        return ObjectMapperUtils.toJSON(newJson);
    }

    /**
     *
     * @param ext 所有的ext信息
     * @return 用例list需要返回的ext
     */
    public String getExt(String ext) {
        JsonElement jsonElement = JsonParser.parseString(ext);
        JsonObject jsonObject = jsonElement.getAsJsonObject();
        String prtUrl = jsonObject.has("prtUrl") ? jsonObject.get("prtUrl").getAsString() : null;
        String onlineUrl = jsonObject.has("onlineUrl") ? jsonObject.get("onlineUrl").getAsString() : null;
        String accountUID = jsonObject.has("accountUID") ? jsonObject.get("accountUID").getAsString() : null;
        String password = jsonObject.has("password") ? jsonObject.get("password").getAsString() : null;
        String testLaneId = jsonObject.has("testLaneId") ? jsonObject.get("testLaneId").getAsString() : null;
        String targetLaneId = jsonObject.has("targetLaneId") ? jsonObject.get("targetLaneId").getAsString() : null;
        String changeOrderId = jsonObject.has("changeOrderId")
                ? jsonObject.get("changeOrderId").getAsString() : null;

        Map<String, Object> newJson = new HashMap<>();
        newJson.put("prtUrl", prtUrl);
        newJson.put("onlineUrl", onlineUrl);
        newJson.put("accountUID", accountUID);
        newJson.put("password", password);
        newJson.put("testLaneId", testLaneId);
        newJson.put("targetLaneId", targetLaneId);
        newJson.put("changeOrderId", changeOrderId);

        return ObjectMapperUtils.toJSON(newJson);
    }

    /**
     *
     * @param sampleExt 用例的所有ext信息
     * @param executeExt 执行记录的所有ext信息
     * @return 执行记录list需要返回的ext
     */
    public String getExecuteExt(String sampleExt, String executeExt) {
        JsonElement jsonElement1 = JsonParser.parseString(sampleExt);
        JsonElement jsonElement2 = JsonParser.parseString(executeExt);
        JsonObject jsonObject1 = jsonElement1.getAsJsonObject();
        JsonObject jsonObject2 = jsonElement2.getAsJsonObject();

        String prtUrl = jsonObject1.has("prtUrl") ? jsonObject1.get("prtUrl").getAsString() : null;
        String onlineUrl = jsonObject1.has("onlineUrl") ? jsonObject1.get("onlineUrl").getAsString() : null;
        String prtPhotoname = jsonObject2.has("prt_photoname") ? jsonObject2.get("prt_photoname").getAsString() : null;
        String onlinePhotoname = jsonObject2.has("online_photoname") ? jsonObject2.get("online_photoname").getAsString() : null;
        String targetPhotoname = jsonObject2.has("target_photoname") ? jsonObject2.get("target_photoname").getAsString() : null;
        String aiExecuteContent = jsonObject2.has("aiExecuteContent") ? jsonObject2.get("aiExecuteContent").getAsString() : null;

        Map<String, Object> newJson = new HashMap<>();
        newJson.put("prtUrl", prtUrl);
        newJson.put("onlineUrl", onlineUrl);
        newJson.put("prt_photoname", prtPhotoname);
        newJson.put("online_photoname", onlinePhotoname);
        newJson.put("target_photoname", targetPhotoname);
        newJson.put("aiExecuteContent", aiExecuteContent);

        return ObjectMapperUtils.toJSON(newJson);
    }

    /**
     *
     * @param executeExt 执行记录的ext信息
     * @return 执行记录的diffRate
     */
    public String getDiffRate(String executeExt) {
        JsonElement jsonElement = JsonParser.parseString(executeExt);
        JsonObject jsonObject = jsonElement.getAsJsonObject();

        return jsonObject.has("diff_rate") ? jsonObject.get("diff_rate").getAsString() : "none";
    }

    public boolean diffRateEqualsZero(double diffRate) {
        final double eps = 0.01;
        return Math.abs(diffRate) < eps;
    }

    /**
     * 执行单个用例
     * @param id 用例id
     * @param executeId 执行id
     * @param changeOrderId 变更单id
     * @return 插入记录的条数 >0 成功，<=0 失败
     */
    public int executeSingleSample(int id, String executeId, String changeOrderId) {
        log.info("executeSingleSample id {}, executeId {} , changeOrderId {}", id, executeId, changeOrderId);
        int insertCount = 0;
        try {
            // 执行记录对应用例
            TianheSampleCaseDO data = tianheSampleCaseMapper.selectById(id);
            JsonElement jsonElement = JsonParser.parseString(data.getExt());
            JsonObject jsonObject = jsonElement.getAsJsonObject();
            String sampleChangeOrderId = jsonObject.has("changeOrderId")
                    ? jsonObject.get("changeOrderId").getAsString() : null;
            if (data.getDeleted() == 1) { // 校验下用例是否被删除
                return insertCount;
            }
            TianheExecuteSampleDO newData = new TianheExecuteSampleDO();
            long timestamp = System.currentTimeMillis();

            newData.setChangeOrderId(changeOrderId);
            newData.setName(data.getName());
            newData.setStatus(0); // 0 初始状态
            newData.setCreateTime(timestamp);
            newData.setUpdateTime(timestamp);
            newData.setDeleted(0);
            newData.setCreator(data.getCreator());
            newData.setModifier(data.getModifier());
            newData.setOperators(data.getOperators());
            newData.setExecuteId(executeId);
            newData.setSampleId(data.getId());
            newData.setExecuteStatus(TianheSampleExecuteStatusEnum.CREATED.ordinal());
            // 处理ext里的存储
            Map<String, Object> map = new HashMap<>();
            String photoname = data.getId() + "_" + executeId + "_";
            String prePrtUrl = "https://cdnfile.corp.kuaishou.com/kc/files/a/tianheTestPaltform/prt_";
            String preOnlineUrl = "https://cdnfile.corp.kuaishou.com/kc/files/a/tianheTestPaltform/online_";
//            String preTargetUrl = "https://cdnfile.corp.kuaishou.com/kc/files/a/tianheTestPaltform/target_";

            int photoIdx = 0;

            // 解析sample 维度的ext
            String ext = data.getExt();

            Map<String, Object> extMap = ObjectMapperUtils.fromJson(ext);

            String prtUrl = Optional.ofNullable(extMap.get("prtUrl"))
                    .map(Object::toString)
                    .orElse(null);

            String onlineUrl = Optional.ofNullable(extMap.get("onlineUrl"))
                    .map(Object::toString)
                    .orElse(null);

            String testLaneId = Optional.ofNullable(extMap.get("testLaneId"))
                    .map(Object::toString)
                    .orElse(null);

            String targetLaneId = Optional.ofNullable(extMap.get("targetLaneId"))
                    .map(Object::toString)
                    .orElse(null);

            // actionSet里每个动作对应一条执行记录
            List<ActionSetExtendData> actionSetList = ObjectMapperUtils.fromJSON(data.getActionSet(), List.class, ActionSetExtendData.class);

            for (ActionSetExtendData actionType : actionSetList) {
                String prtPhotoname = prePrtUrl + photoname + photoIdx + ".png";
                String onlinePhotoname = preOnlineUrl + photoname + photoIdx + ".png";
                // 这几个字段存ext里
                // target_photoname 表示目标环境图片
                map.put("prt_photoname", prtPhotoname);
                map.put("online_photoname", onlinePhotoname);
                map.put("platform_source", data.getPlatformSource());
                map.put("app_key", data.getAppKey());
                map.put("page_code", data.getPageCode());
                map.put("testLaneId", testLaneId);
                map.put("targetLaneId", targetLaneId);
                map.put("aiExecutecontent", "");
                newData.setExt(ObjectMapperUtils.toJSON(map));

                newData.setActionType(Integer.valueOf(actionType.getActionType()));
                newData.setXpath(actionType.getXpath());
                // 暂时先这么处理
                // 边界场景，例如一个sample对应2个action，上游执行单用例时，某一个action执行失败上游也是成功的
                insertCount += tianheExecuteSampleMapper.insert(newData);
                photoIdx++;
            }
            if (StringUtils.isBlank(sampleChangeOrderId)) {
                // 调Jenkins任务 人工用例执行
                runJenkinsArtificialJob(prtUrl, onlineUrl, photoname, executeId, data.getId(),
                        testLaneId, targetLaneId);
            } else {
                runJenkinsAIJob(prtUrl, onlineUrl, photoname, executeId, data.getId(),
                        testLaneId);
            }

        } catch (Exception e) {
            log.error("executeSingleSample error ", e);
            insertCount = -1;
        }
        return insertCount;
    }

    public void selfCheck(long sampleId, String prtUrl, String testLaneId) {
        log.info("executeSingleSample id {}, prtUrl {} , changtestLaneIdeOrderId {}", sampleId, prtUrl, testLaneId);
        try {
            runJenkinsAISelfDetectionJob(prtUrl, sampleId, testLaneId);
        } catch (Exception e) {
            log.error("executeSingleSample error ", e);
        }
    }

    /**
     * 执行PCUI自动化Jenkins任务 2025 0701 废弃不用
     * @param prtUrl prt页面链接
     * @param onlineUrl 线上页面链接
     * @param photoname 图片名
     * @param executeId 执行id
     * @param sampleId 用例id
     */
    private void runJenkinsJob(String prtUrl, String onlineUrl, String photoname, String executeId, long sampleId) {
        log.error("runJenkinsJob request onlineUrl {}, executeId {}, sampleId {}",
                onlineUrl, executeId, sampleId);
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost;
            if (uiAutomationCallCount == 0) {
                httpPost = new HttpPost("https://ui-test.staging.kuaishou.com/job/tianhe_test_platform_pc_ui_test/buildWithParameters");
            } else {
                String url = String.format("https://ui-test.staging.kuaishou.com/job/tianhe_test_platform_pc_ui_test_%d/buildWithParameters", uiAutomationCallCount + 1);
                httpPost = new HttpPost(url);
            }
            uiAutomationCallCount = (uiAutomationCallCount + 1) % 5;

            String username = "admin";
            String password = "11814b606b7787935b2fe3c0fa95e2b393";
            httpPost.setHeader("Authorization", "Basic " + java.util.Base64.getEncoder().encodeToString((username + ":" + password).getBytes()));

            // 设置请求参数
            List<BasicNameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("case_path", "test_case/tianhe/test_tianhe.py::TestTianhe::test_tianhe"));
            params.add(new BasicNameValuePair("put", "true@@@15895880069@@@csr123456@@@" + prtUrl + "@@@" + onlineUrl + "@@@"
                    + photoname + "@@@" + executeId + "@@@" + sampleId));

            HttpEntity entity = new UrlEncodedFormEntity(params, "UTF-8");
            httpPost.setEntity(entity);

            // 执行请求
            CloseableHttpResponse response = httpClient.execute(httpPost);

            // 获取响应状态码
            int statusCode = response.getStatusLine().getStatusCode();

            httpClient.close();
        } catch (Exception e) {
            log.error("runJenkinsJob error ", e);
        }
    }

    /**
     * 人工用例执行，ui自动化模版进行了更换，所以新建一个方法处理
     * @param prtUrl
     * @param onlineUrl
     * @param photoname
     * @param executeId
     * @param sampleId
     */
    public void runJenkinsArtificialJob(String prtUrl, String onlineUrl, String photoname,
            String executeId, long sampleId, String testLaneId, String targetLaneId) {
        log.error("runJenkinsJob request onlineUrl {}, executeId {}, sampleId {}",
                onlineUrl, executeId, sampleId);
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost;
            httpPost = new HttpPost("https://ui-test.staging.kuaishou.com/job/tianhe_test_artificial/buildWithParameters");
            uiAutomationCallCount = (uiAutomationCallCount + 1) % 5;

            String username = "admin";
            String password = "11814b606b7787935b2fe3c0fa95e2b393";
            httpPost.setHeader("Authorization", "Basic " + java.util.Base64.getEncoder().encodeToString((username + ":" + password).getBytes()));

            // 设置请求参数
            List<BasicNameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("case_path", "test_case/ai/test_tianhe_ai"
                    + ".py::TestTianhe::test_tianhe_artificial_sample"));
            // 把url里面的&替换成@@@@， 不然jenkins模版执行的时候会把&当作分割符号，导致执行错误
            params.add(new BasicNameValuePair("put",
                    "true@@@15895880069@@@csr123456@@@" + prtUrl.replace("&", "@@@@")
                            + "@@@" + onlineUrl.replace("&", "@@@@") + "@@@"
                            + photoname + "@@@" + executeId + "@@@" + sampleId + "@@@" + testLaneId
                            + "@@@" + targetLaneId));

            HttpEntity entity = new UrlEncodedFormEntity(params, "UTF-8");
            httpPost.setEntity(entity);

            // 执行请求
            CloseableHttpResponse response = httpClient.execute(httpPost);

            // 获取响应状态码
            int statusCode = response.getStatusLine().getStatusCode();

            httpClient.close();
        } catch (Exception e) {
            log.error("runJenkinsJob error ", e);
        }
    }
    /**
     * 智能用例执行，ui自动化模版进行了更换，所以新建一个方法处理
     * @param prtUrl
     * @param onlineUrl
     * @param photoname
     * @param executeId
     * @param sampleId
     */
    public void runJenkinsAIJob(String prtUrl, String onlineUrl, String photoname,
            String executeId, long sampleId, String testLaneId) {
        log.error("runJenkinsJob request onlineUrl {}, executeId {}, sampleId {}",
                onlineUrl, executeId, sampleId);
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost;
            httpPost = new HttpPost("https://ui-test.staging.kuaishou.com/job/tianhe_test_AI/buildWithParameters");
            uiAutomationCallCount = (uiAutomationCallCount + 1) % 5;

            String username = "admin";
            String password = "11814b606b7787935b2fe3c0fa95e2b393";
            httpPost.setHeader("Authorization", "Basic " + java.util.Base64.getEncoder().encodeToString((username + ":" + password).getBytes()));

            // 设置请求参数
            List<BasicNameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("case_path", "test_case/ai/test_tianhe_ai"
                    + ".py::TestTianhe::test_tianhe_ai_sample"));
            // 把url里面的&替换成@@@@， 不然jenkins模版执行的时候会把&当作分割符号，导致执行错误
            params.add(new BasicNameValuePair("put",
                    "true@@@15895880069@@@csr123456@@@" + prtUrl.replace("&", "@@@@")
                            + "@@@" + photoname + "@@@" + executeId + "@@@" + sampleId + "@@@" + testLaneId));

            HttpEntity entity = new UrlEncodedFormEntity(params, "UTF-8");
            httpPost.setEntity(entity);

            // 执行请求
            CloseableHttpResponse response = httpClient.execute(httpPost);

            // 获取响应状态码
            int statusCode = response.getStatusLine().getStatusCode();

            httpClient.close();
        } catch (Exception e) {
            log.error("runJenkinsJob error ", e);
        }
    }

    /**
     * 对于生成的用例进行自动校验，通过调用ui自动化进行页面元素xpath检测完成
     * @param prtUrl
     * @param sampleId
     */
    public void runJenkinsAISelfDetectionJob(String prtUrl, long sampleId, String testLaneId) {
        log.error("runJenkinsJob request sampleId {}", sampleId);
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost;
            httpPost = new HttpPost("https://ui-test.staging.kuaishou.com/job/tianhe_test_AI_sample_check/buildWithParameters");
            uiAutomationCallCount = (uiAutomationCallCount + 1) % 5;

            String username = "admin";
            String password = "11814b606b7787935b2fe3c0fa95e2b393";
            httpPost.setHeader("Authorization", "Basic " + java.util.Base64.getEncoder().encodeToString((username + ":" + password).getBytes()));

            // 设置请求参数
            List<BasicNameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("case_path", "test_case/ai/test_tianhe_ai"
                    + ".py::TestTianhe::test_tianhe_ai_check"));
            params.add(new BasicNameValuePair("put",
                    "true@@@15895880069@@@csr123456@@@" + prtUrl.replace("&", "@@@@") + "@@@" + sampleId + "@@@" + testLaneId));

            HttpEntity entity = new UrlEncodedFormEntity(params, "UTF-8");
            httpPost.setEntity(entity);

            // 执行请求
            CloseableHttpResponse response = httpClient.execute(httpPost);

            // 获取响应状态码
            int statusCode = response.getStatusLine().getStatusCode();

            httpClient.close();
        } catch (Exception e) {
            log.error("runJenkinsJob error ", e);
        }
    }
}
