package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.KspayBaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.FundsRiskFeatureViewDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsRiskFeatureViewDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.FundsRiskFeatureViewMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.FundRiskFeatureViewQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-01
 */
@Repository
public class FundsRiskFeatureViewDAOImpl extends KspayBaseDAOImpl<FundsRiskFeatureViewDO, FundRiskFeatureViewQueryCondition>
        implements FundsRiskFeatureViewDAO {
    @Autowired
    private FundsRiskFeatureViewMapper fundsRiskFeatureViewMapper;
    @Override
    protected void fillLikeQueryCondition(FundRiskFeatureViewQueryCondition condition,
            QueryWrapper<FundsRiskFeatureViewDO> queryWrapper) {

    }

    @Override
    protected void fillQueryCondition(FundRiskFeatureViewQueryCondition condition,
            QueryWrapper<FundsRiskFeatureViewDO> queryWrapper) {

    }

    @Override
    protected BaseMapper<FundsRiskFeatureViewDO> getMapper() {
        return fundsRiskFeatureViewMapper;
    }

    @Override
    public List<FundsRiskFeatureViewDO> queryByCenterIds(Collection<Long> centerIds, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsRiskFeatureViewDO> queryByTeamIds(Long centerId, Collection<Long> teamIds,
            CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsRiskFeatureViewDO> queryByCenterId(Long centerId, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsRiskFeatureViewDO> queryByTeamId(Long centerId, Long teamId, CombineQueryParam queryParam) {
        return null;
    }

//    @Override
//    public int updateFeatureViewNoUpdateTime(FundsRiskFeatureViewDO fundsRiskFeatureViewDO) {
//        return fundsRiskFeatureViewMapper.updateFeatureViewNoUpdateTime(fundsRiskFeatureViewDO.getFeatureName(),
//                fundsRiskFeatureViewDO.getDepartment(),
//                fundsRiskFeatureViewDO.getBusinessDomain(),
//                fundsRiskFeatureViewDO.getTeamId(),
//                fundsRiskFeatureViewDO.getTeamName(),
//                fundsRiskFeatureViewDO.getTeamWorker(),
//                fundsRiskFeatureViewDO.getRiskCoe(),
//                fundsRiskFeatureViewDO.getStatus(),
//                "task",
//                fundsRiskFeatureViewDO.getId());
//    }

    @Override
    public List<FundsRiskFeatureViewDO> queryByCenterIdsTeamIds(Collection<Long> centerIds, Collection<Long> teamIds,
            CombineQueryParam queryParam) {
        return null;
    }
}
