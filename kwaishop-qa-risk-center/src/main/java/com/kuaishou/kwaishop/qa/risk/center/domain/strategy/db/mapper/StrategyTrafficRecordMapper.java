package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTrafficRecordDO;

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface StrategyTrafficRecordMapper extends BaseMapper<StrategyTrafficRecordDO> {

    @Select("SELECT * FROM strategy_traffic_record WHERE id = #{id}")
    StrategyTrafficRecordDO findById(@Param("id") Long id);

    @Select("SELECT * FROM strategy_traffic_record")
    List<StrategyTrafficRecordDO> findAll();

    @Select("SELECT * FROM strategy_traffic_record WHERE record_task_id = #{taskId}")
    List<StrategyTrafficRecordDO> queryAllByTaskId(@Param("taskId") String taskId);

    @Select({"<script>",
            "SELECT * FROM strategy_traffic_record WHERE id IN ",
            "<foreach item='id' index='index' collection='ids' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            "</script>"})
    List<StrategyTrafficRecordDO> queryAllByIds(@Param("ids") List<Long> ids);


    @Insert("INSERT INTO strategy_traffic_record(scene_name, "
            +
            "record_time, traffic_data, expected_result, ext, create_time, update_time, creator, modifier, deleted, version) "
            +
            "VALUES(#{sceneName}, #{recordTime}, #{trafficData}, #{expectedResult}, #{ext}, "
            +
            "#{createTime}, #{updateTime}, #{creator}, #{modifier}, #{deleted}, #{version})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(StrategyTrafficRecordDO record);

    @Update("UPDATE strategy_traffic_record "
            +
            "SET scene_name=#{sceneName}, record_time=#{recordTime}, traffic_data=#{trafficData},"
            +
            " expected_result=#{expectedResult}, ext=#{ext}, "
            +
            "create_time=#{createTime}, update_time=#{updateTime}, creator=#{creator}, "
            +
            "modifier=#{modifier}, deleted=#{deleted}, version=#{version} WHERE id=#{id}")
    void update(StrategyTrafficRecordDO record);

    @Delete("DELETE FROM strategy_traffic_record WHERE id = #{id}")
    void delete(@Param("id") Long id);


    @Insert({
            "<script>",
            "INSERT INTO strategy_traffic_record "
                    +
                    "(scene_name, record_time, traffic_data, expected_result, "
                    +
                    "create_time, update_time, creator, "
                    +
                    "modifier, deleted, version, record_task_id) VALUES ",
            "<foreach collection='records' item='record' separator=','>",
            "(#{record.sceneName}, #{record.recordTime}, #{record.trafficData}, #{record.expectedResult},"
                    +
                    "#{record.createTime}, #{record.updateTime}, #{record.creator}, "
                    +
                    "#{record.modifier}, #{record.deleted}, #{record.version}, #{record.recordTaskId})",
            "</foreach>",
            "</script>"
    })
    void batchInsert(@Param("records") List<StrategyTrafficRecordDO> records);

    @Update({
            "<script>",
            "<foreach collection='records' item='record' open='' separator=';' close=''>",
            "UPDATE strategy_traffic_record",
            "SET scene_name=#{record.sceneName}, record_time=#{record.recordTime},"
                    +
                    " traffic_data=#{record.trafficData}, expected_result=#{record.expectedResult}, ext=#{record.ext}, "
                    +
                    "create_time=#{record.createTime}, update_time=#{record.updateTime}, "
                    +
                    "creator=#{record.creator}, modifier=#{record.modifier}, deleted=#{record.deleted}, version=#{record.version}",
            "WHERE id=#{record.id}",
            "</foreach>",
            "</script>"
    })
    void batchUpdate(@Param("records") List<StrategyTrafficRecordDO> records);

    @Update({
            "UPDATE strategy_traffic_record",
            "SET actual_result = #{record.actualResult},",
            "diff_conclusion = #{record.diffConclusion},",
            "replay_time = #{record.replayTime},",
            "replay_lane_id = #{record.replayLaneId}",
            "WHERE id = #{record.id}"
    })
    void updateDiffInfo(@Param("record") StrategyTrafficRecordDO record);

    @Select("SELECT traffic_data FROM strategy_traffic_record WHERE id = #{caseId}")
    String queryTrafficData(String caseId);

    @Select("SELECT record_task_id FROM strategy_traffic_record WHERE id = #{caseId}")
    String queryTaskIdByCaseId(Long caseId);

    @Select("SELECT * FROM strategy_traffic_record WHERE record_task_id = #{taskId}")
    List<StrategyTrafficRecordDO> queryAllCaseByTaskId(String taskId);
}
