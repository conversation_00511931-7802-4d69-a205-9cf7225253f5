package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.RiskInjectionRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.RiskInjectionRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.RiskInjectionRecordMapper;

@Repository
public class RiskInjectionRecordDAOImpl implements RiskInjectionRecordDAO {



    @Autowired
    private RiskInjectionRecordMapper riskInjectionRecordMapper;


    @Override
    public void insertRiskInjectionRecord(RiskInjectionRecordDO riskInjectionRecordDO) {
        riskInjectionRecordMapper.insertRiskInjectionRecord(riskInjectionRecordDO);
    }

    @Override
    public List<RiskInjectionRecordDO> queryRiskInjectionId(Long riskInjectionId) {
        return riskInjectionRecordMapper.queryRiskInjectionId(riskInjectionId);
    }
}
