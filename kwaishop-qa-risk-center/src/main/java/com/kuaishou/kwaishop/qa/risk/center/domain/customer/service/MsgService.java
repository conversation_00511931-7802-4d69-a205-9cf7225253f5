package com.kuaishou.kwaishop.qa.risk.center.domain.customer.service;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.BatchMsgSendC2BRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.MsgSendB2CRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.MsgSendB2CResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.MsgSendC2BRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.MsgSendC2BResponse;

public interface MsgService {


    MsgSendC2BResponse msgSendC2B(MsgSendC2BRequest request);

    MsgSendB2CResponse msgSendB2C(MsgSendB2CRequest request);

    MsgSendC2BResponse batchMsgSendC2B(BatchMsgSendC2BRequest request) throws Exception;

}
