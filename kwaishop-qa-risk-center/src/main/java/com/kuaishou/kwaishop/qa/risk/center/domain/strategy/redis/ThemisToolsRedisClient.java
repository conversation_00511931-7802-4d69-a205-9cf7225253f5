package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.redis;

import org.springframework.stereotype.Service;

import com.alibaba.druid.util.StringUtils;
import com.kuaishou.framework.jedis.JedisClusterByZooKeeper;

import redis.clients.jedis.JedisCommands;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-21
 */
@Service
public class ThemisToolsRedisClient {
    // 使用Redis同步客户端
    private final JedisClusterByZooKeeper jedisClusterByZooKeeper = ThemisQaRiskRedisConfig.kwaishopThemisTools.get();

    /**
     * @param timeout 过期时间(s)
     */
    public void set(String key, String value, int timeout) {
        JedisCommands jedisCommands = jedisClusterByZooKeeper.get();
        jedisCommands.setex(key, timeout, value);
    }

    public String get(String key) {
        JedisCommands jedisCommands = jedisClusterByZooKeeper.get();
        return jedisCommands.get(key);
    }

    public boolean setNx(String key, int expireTime) {
        JedisCommands commands = jedisClusterByZooKeeper.get();
        String res = commands.set(key, key, "NX", "EX", expireTime);
        return StringUtils.equalsIgnoreCase(res, "ok");
    }

    public Long incr(String key) {
        JedisCommands jedisCommands = jedisClusterByZooKeeper.get();
        return jedisCommands.incr(key);
    }

}
