package com.kuaishou.kwaishop.qa.risk.center.db.query.risk;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-01
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FundRiskFeatureViewBranchQueryCondition extends BaseQueryCondition {
    private String featureViewId;
    private String repoName;
    private String branchName;
}
