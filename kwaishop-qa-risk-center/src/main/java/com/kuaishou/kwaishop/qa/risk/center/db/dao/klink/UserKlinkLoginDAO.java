package com.kuaishou.kwaishop.qa.risk.center.db.dao.klink;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.klink.UserKlinkLoginDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.model.UserKlinkLoginBO;

public interface UserKlinkLoginDAO {

    PageBO<UserKlinkLoginDO> queryUserLoginInfoList(UserKlinkLoginBO userKlinkLoginBO);

    long insertOrUpdateUserKlinkLogin(UserKlinkLoginDO userKlinkLoginDO);

    UserKlinkLoginDO queryUserLoginInfoByUserId(long userId);
}
