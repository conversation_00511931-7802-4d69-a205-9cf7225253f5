package com.kuaishou.kwaishop.qa.risk.center.db.dao.accuracy.impl;


import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.accuracy.AffectEntranceInfoDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.AccuracyBaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.accuracy.AffectEntranceInfoDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.accuracy.AffectEntranceInfoMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.accuracy.AffectEntranceInfoQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.AffectEntranceInfoBO;

import lombok.extern.slf4j.Slf4j;

@Repository
@Slf4j
public class AffectEntranceInfoDAOImpl extends AccuracyBaseDAOImpl<AffectEntranceInfoDO, AffectEntranceInfoQueryCondition>
        implements AffectEntranceInfoDAO {

    @Autowired
    private AffectEntranceInfoMapper affectEntranceInfoMapper;

    @Override
    public List<AffectEntranceInfoDO> queryAffectEntranceInfo(AffectEntranceInfoBO affectEntranceInfoBO) {
        return queryList(AffectEntranceInfoQueryCondition.builder()
                .taskId(affectEntranceInfoBO.getTaskId())
                .build());
    }

    @Override
    protected void fillQueryCondition(AffectEntranceInfoQueryCondition condition, QueryWrapper<AffectEntranceInfoDO> queryWrapper) {

        if (condition.getTaskId() != null && StringUtils.isNotBlank(condition.getTaskId())) {
            queryWrapper.and(q -> q.eq("task_id", condition.getTaskId()));
        }

    }


    @Override
    protected BaseMapper<AffectEntranceInfoDO> getMapper() {
        return affectEntranceInfoMapper;
    }


}
