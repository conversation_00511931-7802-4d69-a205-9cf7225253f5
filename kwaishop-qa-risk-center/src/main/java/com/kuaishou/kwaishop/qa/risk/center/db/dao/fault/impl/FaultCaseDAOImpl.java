package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.impl;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.FaultCaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.fault.FaultCaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.fault.FaultCaseQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCaseBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultCaseCallTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultCaseLevelTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultCaseRelationshipTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultCaseSourceEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultCaseTagTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.utils.JsonUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-08
 */
@Repository
@Slf4j
public class FaultCaseDAOImpl extends BaseDAOImpl<FaultCaseDO, FaultCaseQueryCondition> implements FaultCaseDAO {

    @Autowired
    private FaultCaseMapper faultCaseMapper;


    @Override
    protected void fillQueryCondition(FaultCaseQueryCondition condition, QueryWrapper<FaultCaseDO> queryWrapper) {
        if (condition.getCenterId() != null && condition.getCenterId() > 0) {
            queryWrapper.and(q -> q.eq("center_id", condition.getCenterId()));
        }
        if (condition.getTeamId() != null && condition.getTeamId() > 0) {
            queryWrapper.and(q -> q.eq("team_id", condition.getTeamId()));
        }
        if (StringUtils.isNotBlank(condition.getName())) {
            queryWrapper.and(q -> q.like("name", condition.getName()));
        }
        if (StringUtils.isNotBlank(condition.getCallerKsn())) {
            queryWrapper.and(q -> q.eq("caller_ksn", condition.getCallerKsn()));
        }
        if (StringUtils.isNotBlank(condition.getCalleeMethodName())) {
            queryWrapper.and(q -> q.like("caller_method_name", condition.getCallerMethodName()));
        }
        if (StringUtils.isNotBlank(condition.getCallerFullMethodName())) {
            queryWrapper.and(q -> q.like("caller_full_method_name", condition.getCallerFullMethodName()));
        }
        if (StringUtils.isNotBlank(condition.getCallerType()) && FaultCaseCallTypeEnum.of(condition.getCallerType()) != null) {
            queryWrapper.and(q -> q.eq("caller_type", condition.getCallerType()));
        }
        if (StringUtils.isNotBlank(condition.getCalleeKsn())) {
            queryWrapper.and(q -> q.like("callee_ksn", condition.getCalleeKsn()));
        }
        if (StringUtils.isNotBlank(condition.getCalleeName())) {
            queryWrapper.and(q -> q.like("callee_name", condition.getCalleeName()));
        }
        if (StringUtils.isNotBlank(condition.getCalleeMethodName())) {
            queryWrapper.and(q -> q.like("callee_method_name", condition.getCalleeMethodName()));
        }
        if (StringUtils.isNotBlank(condition.getCalleeFullMethodName())) {
            queryWrapper.and(q -> q.like("callee_full_method_name", condition.getCalleeFullMethodName()));
        }
        if (StringUtils.isNotBlank(condition.getCalleeType()) && FaultCaseCallTypeEnum.of(condition.getCalleeType()) != null) {
            queryWrapper.and(q -> q.eq("callee_type", condition.getCalleeType()));
        }
        if (StringUtils.isNotBlank(condition.getSource()) && FaultCaseSourceEnum.of(condition.getSource()) != null) {
            queryWrapper.and(q -> q.eq("source", condition.getSource()));
        }
        if (StringUtils.isNotBlank(condition.getUniqueId())) {
            queryWrapper.and(q -> q.eq("unique_id", condition.getUniqueId()));
        }
        if (condition.getCallerLevel() != null && condition.getCallerLevel() > 0
                && FaultCaseLevelTypeEnum.of(condition.getCallerLevel()) != null) {
            queryWrapper.and(q -> q.eq("caller_level", condition.getCallerLevel()));
        }
        if (condition.getCalleeLevel() != null && condition.getCalleeLevel() > 0
                && FaultCaseLevelTypeEnum.of(condition.getCalleeLevel()) != null) {
            queryWrapper.and(q -> q.eq("callee_level", condition.getCalleeLevel()));
        }
        if (CollectionUtils.isNotEmpty(condition.getCallerLevelList())) {
            queryWrapper.and(q -> q.in("caller_level", condition.getCallerLevelList()));
        }
        if (CollectionUtils.isNotEmpty(condition.getCalleeLevelList())) {
            queryWrapper.and(q -> q.in("callee_level", condition.getCalleeLevelList()));
        }
        if (StringUtils.isNotBlank(condition.getRelationship())
                && FaultCaseRelationshipTypeEnum.of(condition.getRelationship()) != null) {
            queryWrapper.and(q -> q.eq("relationship", condition.getRelationship()));
        }
        if (condition.getTag() != null && FaultCaseTagTypeEnum.of(condition.getTag()) != null) {
            queryWrapper.and(q -> q.eq("tag", condition.getTag()));
        }
        if (condition.getStartTimeGe() != null && condition.getStartTimeGe() > 0) {
            queryWrapper.and(q -> q.ge("create_time", condition.getStartTimeGe()));
        }
        if (StringUtils.isNotBlank(condition.getBusinessTag())) {
            queryWrapper.and(q -> q.like("business_tag", condition.getBusinessTag()));
        }

    }

    @Override
    protected BaseMapper<FaultCaseDO> getMapper() {
        return faultCaseMapper;
    }

    @Override
    public List<FaultCaseDO> queryFaultCaseList(FaultCaseBO faultCaseBO) {
        FaultCaseQueryCondition condition = FaultCaseQueryCondition.builder()
                .id(faultCaseBO.getId())
                .ids(faultCaseBO.getIds())
                .name(faultCaseBO.getName())
                .centerId(faultCaseBO.getCenterId())
                .teamId(faultCaseBO.getTeamId())
                .calleeKsn(faultCaseBO.getCalleeKsn())
                .calleeName(faultCaseBO.getCalleeName())
                .callerKsn(faultCaseBO.getCallerKsn())
                .calleeMethodName(faultCaseBO.getCalleeMethodName())
                .callerMethodName(faultCaseBO.getCallerMethodName())
                .callerFullMethodName(faultCaseBO.getCallerFullMethodName())
                .calleeFullMethodName(faultCaseBO.getCalleeFullMethodName())
                .callerType(faultCaseBO.getCallerType())
                .calleeType(faultCaseBO.getCalleeType())
                .callerLevel(faultCaseBO.getCallerLevel())
                .calleeLevel(faultCaseBO.getCalleeLevel())
                .callerLevelList(faultCaseBO.getCallerLevelList())
                .calleeLevelList(faultCaseBO.getCalleeLevelList())
                .source(faultCaseBO.getSource())
                .relationship(faultCaseBO.getRelationship())
                .businessTag(faultCaseBO.getBusinessTag())
                .orderByCreateTimeDesc(true)
                .build();
        log.info("queryplan condition, {}", JsonUtils.toJsonString(condition));
        return queryList(condition);
    }

    @Override
    public List<FaultCaseDO> getFaultCaseByFaultPlan(FaultCaseBO faultCaseBO) {
        return faultCaseMapper.getFaultCaseByFaultPlan(faultCaseBO.getIds());
    }

    @Override
    public Map<Long, FaultCaseDO> queryCaseByIdsAll(Set<Long> caseIds) {
        Map<Long, FaultCaseDO> res = new HashMap<>();
        caseIds.forEach(id -> {
            FaultCaseDO faultCaseByOne = faultCaseMapper.getFaultCaseByOne(id);
            res.put(id, faultCaseByOne);
        });
        return res;
    }

    @Override
    public FaultCaseDO queryCaseById(Long id) {
        return faultCaseMapper.getFaultCaseByOne(id);
    }

    @Override
    public List<FaultCaseDO> queryFaultCaseByCenterId(Long centerId, Long st, Long et) {
        FaultCaseQueryCondition condition = FaultCaseQueryCondition.builder()
                .centerId(centerId)
                .startTimeGe(st)
                .endTimeLe(et)
                .build();
        return queryList(condition);
    }

    @Override
    public PageBO<FaultCaseDO> queryPageFaultCaseList(FaultCaseBO faultCaseBO) {
        FaultCaseQueryCondition condition = FaultCaseQueryCondition.builder()
                .id(faultCaseBO.getId())
                .ids(faultCaseBO.getIds())
                .name(faultCaseBO.getName())
                .centerId(faultCaseBO.getCenterId())
                .teamId(faultCaseBO.getTeamId())
                .calleeKsn(faultCaseBO.getCalleeKsn())
                .calleeName(faultCaseBO.getCalleeName())
                .callerKsn(faultCaseBO.getCallerKsn())
                .calleeMethodName(faultCaseBO.getCalleeMethodName())
                .callerMethodName(faultCaseBO.getCallerMethodName())
                .callerFullMethodName(faultCaseBO.getCallerFullMethodName())
                .calleeFullMethodName(faultCaseBO.getCalleeFullMethodName())
                .callerType(faultCaseBO.getCallerType())
                .calleeType(faultCaseBO.getCalleeType())
                .callerLevel(faultCaseBO.getCallerLevel())
                .calleeLevel(faultCaseBO.getCalleeLevel())
                .source(faultCaseBO.getSource())
                .tag(faultCaseBO.getTag())
                .businessTag(faultCaseBO.getBusinessTag())
                .callerLevelList(faultCaseBO.getCallerLevelList())
                .calleeLevelList(faultCaseBO.getCalleeLevelList())
                .relationship(faultCaseBO.getRelationship())
                .pageNo(faultCaseBO.getPageNo())
                .pageSize(faultCaseBO.getPageSize())
                .orderByCreateTimeDesc(true)
                .build();
        return queryPageList(condition);
    }

    @Override
    public int logicDeleted(Long id, String operator) {
        return faultCaseMapper.logicDeleted(operator, id);
    }

    @Override
    public boolean insertOrUpdate(FaultCaseDO faultCaseDO) {
        if (faultCaseDO != null) {
            return faultCaseMapper.insertOrUpdate(faultCaseDO) > 0;
        }
        return false;
    }

    @Override
    public boolean batchInsertOrUpdate(List<FaultCaseDO> faultCaseDOS) {
        if (CollectionUtils.isNotEmpty(faultCaseDOS)) {
            return faultCaseMapper.batchInsertOrUpdate(faultCaseDOS) > 0;
        }
        return false;
    }

    @Override
    public FaultCaseDO queryByUnique(String uniqueId) {
        FaultCaseQueryCondition condition = FaultCaseQueryCondition.builder()
                .uniqueId(uniqueId)
                .build();
        return queryOne(condition);
    }

    @Override
    public List<FaultCaseDO> querySyncCaseByKSN(Long centerId, Long teamId, String ksn) {
        FaultCaseQueryCondition condition = FaultCaseQueryCondition.builder()
                .callerKsn(ksn)
                .source(FaultCaseSourceEnum.SYNC.getType())
                .centerId(centerId)
                .teamId(teamId)
                .build();
        return queryList(condition);
    }

    @Override
    public List<FaultCaseDO> queryCaseByIds(Collection<Long> ids) {
        FaultCaseQueryCondition condition = FaultCaseQueryCondition.builder()
                .ids(ids)
                .build();
        return queryList(condition);
    }

    @Override
    public int batchUpdateCaseTag(String operator, Integer tag, Collection<Long> ids) {
        return faultCaseMapper.batchUpdateTag(operator, tag, ids, System.currentTimeMillis());
    }

    @Override
    public int batchUpdateBusinessCaseTag(String operator, String businessTag, Collection<Long> ids) {
        return faultCaseMapper.batchUpdateBusinessTag(operator, businessTag, ids, System.currentTimeMillis());
    }

    @Override
    public int batchUpdateById(String operator, List<FaultCaseBO> faultCaseBOList) {
        if (CollectionUtils.isEmpty(faultCaseBOList)) {
            return 0;
        }
        faultCaseBOList.forEach(e -> {
            FaultCaseDO faultCaseDO = faultCaseMapper.selectById(e.getId());
            if (faultCaseDO != null && faultCaseDO.getId() > 0) {
                if (StringUtils.isNotEmpty(e.getBusinessTag())) {
                    faultCaseDO.setBusinessTag(e.getBusinessTag());
                }
                if (e.getIndexLevel() != null) {
                    faultCaseDO.setIndexLevel(e.getIndexLevel());
                }
                String ext = faultCaseDO.getExt();
                Map<String, Object> extMap = new HashMap<>();
                if (e.getNotNeedCover()) {
                    if (ext != null) {
                        extMap = ObjectMapperUtils.fromJson(ext);
                        extMap.put("notNeedCover", e.getNotNeedCover());
                        extMap.put("notNeedCoverReason", e.getNotNeedCoverReason());
                    }
                    faultCaseDO.setExt(ObjectMapperUtils.toJSON(extMap));
                } else {
                    if (ext != null) {
                        extMap = ObjectMapperUtils.fromJson(ext);
                        extMap.put("notNeedCover", e.getNotNeedCover());
                        extMap.remove("notNeedCoverReason");
                    }
                    faultCaseDO.setExt(ObjectMapperUtils.toJSON(extMap));
                }
                faultCaseDO.setUpdateTime(System.currentTimeMillis());
                faultCaseDO.setModifier(operator);
                faultCaseMapper.updateById(faultCaseDO);
            }
        });
        return faultCaseBOList.size();
    }

}
