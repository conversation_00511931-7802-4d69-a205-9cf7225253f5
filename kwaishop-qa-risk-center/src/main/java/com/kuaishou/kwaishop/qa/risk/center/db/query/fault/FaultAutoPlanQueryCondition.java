package com.kuaishou.kwaishop.qa.risk.center.db.query.fault;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FaultAutoPlanQueryCondition extends BaseQueryCondition {


    private Long scenarioId;

    private String taskId;

    private String uic;

    private String ruleId;

    private Integer status;


}
