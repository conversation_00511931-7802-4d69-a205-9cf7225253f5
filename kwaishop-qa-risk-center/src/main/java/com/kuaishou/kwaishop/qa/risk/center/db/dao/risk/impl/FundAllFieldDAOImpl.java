package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.FundAllFieldDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundAllFieldDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.FundAllFieldMapper;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023/11/7 16:50
 */
@Repository
public class FundAllFieldDAOImpl implements FundAllFieldDAO {

    @Autowired
    private FundAllFieldMapper fundAllFieldMapper;

    @Override
    public void insertField(FundAllFieldDO fundAllFieldDO) {
        fundAllFieldMapper.insertField(fundAllFieldDO);

    }

    @Override
    public List<FundAllFieldDO> getSimpleFieldByUnique(String bizDef, String sourceName, String tableName, String columnName) {
        return fundAllFieldMapper.getSimpleFieldByUnique(bizDef, sourceName, tableName, columnName);
    }

    @Override
    public int updateFieldByUnique(String fieldHash, FundAllFieldDO fundAllFieldDO) {
        return 0;
    }

    @Override
    public List<FundAllFieldDO> getAllFields() {
        return null;
    }
}
