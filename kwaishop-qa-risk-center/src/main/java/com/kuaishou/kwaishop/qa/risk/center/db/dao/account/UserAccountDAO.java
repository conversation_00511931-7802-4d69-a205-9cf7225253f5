package com.kuaishou.kwaishop.qa.risk.center.db.dao.account;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.BaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAccountBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-28
 */

public interface UserAccountDAO extends BaseDAO<UserAccountDO> {

    List<UserAccountDO> queryList(UserAccountBO userAccountBO);

    UserAccountDO queryByUid(Long uid);

    PageBO<UserAccountDO> queryPageList(UserAccountBO userAccountBO);

    //根据用户user_id查数据
    List<UserAccountDO> queryUserAccountByUserId(Long userId);

    // 更新账户表的可借用状态
    int updateRentStatus(Integer status, String modifier, Long userId);

    // 增加租借次数
    int addRentNum(Integer increment, Long userId);

    //归还减少租借次数
    int reduceRentNum(Integer increment, Long userId);

    void updateToken(UserAccountDO userAccountDO);

    /**
     * 获取公司测试账号-根据账号区间
     *
     * @param from 账号区间起始号段
     * @param to   账号区间末位号段
     * @return
     */
    List<UserAccountDO> queryBusinessAccountByRule(Integer from, Integer to);
}
