package com.kuaishou.kwaishop.qa.risk.center.domain.account.convert;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TestAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.RentAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.AccountRentalDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ExtendRentRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryPageRentRecordsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryRentRecordsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.RentAccountRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-26
 */
public interface RentAccountConvert {

    RentAccountBO buildReturnAccountBO(RentAccountDO request);

    RentAccountBO buildRentAccountBO(UserAccountDO userAccountDO, RentAccountRequest request);

    RentAccountBO buildRentAccountBO(UserAccountDO userAccountDO, long userId, String borrower, int duration);

    RentAccountBO buildRentAccountBO(TestAccountDO testAccountDO, long userId, String borrower, int duration, long bId);

    RentAccountBO buildQueryPageRentRecords(QueryPageRentRecordsRequest request);

    AccountRentalDTO buildDTOByRentAccountDo(RentAccountDO data, String centerName, String teamName);

    RentAccountBO buildQueryRentRecords(QueryRentRecordsRequest request);

    RentAccountBO buildExtendRentRequest(ExtendRentRequest request, RentAccountDO rentAccountDO);
}
