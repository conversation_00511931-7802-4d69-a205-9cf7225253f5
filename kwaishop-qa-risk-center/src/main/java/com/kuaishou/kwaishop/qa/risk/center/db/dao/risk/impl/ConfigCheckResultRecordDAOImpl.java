package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.KspayBaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.ConfigCheckResultRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.ConfigCheckResultRecordDo;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.ConfigCheckResultRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.ConfigCheckResulRecordQueryCondition;

@Repository
public class ConfigCheckResultRecordDAOImpl extends KspayBaseDAOImpl<ConfigCheckResultRecordDo,
        ConfigCheckResulRecordQueryCondition> implements ConfigCheckResultRecordDAO {
    @Autowired
    private ConfigCheckResultRecordMapper configCheckResultRecordMapper;

    @Override
    public long insert(ConfigCheckResultRecordDo configCheckResultRecordDo) {
        return configCheckResultRecordMapper.insert(configCheckResultRecordDo);
    }

    @Override
    public List<ConfigCheckResultRecordDo> queryConfigCheckResultRecord(
            ConfigCheckResulRecordQueryCondition configCheckResulRecordQueryCondition) {
        return configCheckResultRecordMapper.queryConfigRulesCheckResultList(
                configCheckResulRecordQueryCondition.getReportId(),
                configCheckResulRecordQueryCondition.getId());
    }

    @Override
    public List<ConfigCheckResultRecordDo> queryConfigCheckResultRecordById(Long id) {
        return configCheckResultRecordMapper.queryConfigRulesCheckResultById(id);
    }

    @Override
    public List<ConfigCheckResultRecordDo> queryConfigCheckResultRecordByReportId(Long reportId) {
        return configCheckResultRecordMapper.queryConfigRulesCheckResultByReportId(reportId);
    }


    @Override
    protected void fillLikeQueryCondition(ConfigCheckResulRecordQueryCondition condition,
            QueryWrapper<ConfigCheckResultRecordDo> queryWrapper) {

    }

    @Override
    protected void fillQueryCondition(ConfigCheckResulRecordQueryCondition condition,
            QueryWrapper<ConfigCheckResultRecordDo> queryWrapper) {

    }

    @Override
    protected BaseMapper<ConfigCheckResultRecordDo> getMapper() {
        return configCheckResultRecordMapper;
    }
}
