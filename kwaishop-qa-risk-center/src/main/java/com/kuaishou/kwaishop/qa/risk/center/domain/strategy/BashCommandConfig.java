package com.kuaishou.kwaishop.qa.risk.center.domain.strategy;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-30
 */
@Data
public class BashCommandConfig {

    private String taskName;

    /**
     * 默认为com.kuaishou.kwaishop.apollo.strategy.spark.application.StrategyFlowRecord
     */
    private String sparkClass;

    /**
     * 默认为viewfs://hadoop-lt-cluster/home/<USER>/data/udf/1563/kwaishop-apollo-strategy-spark-1.0.403-SNAPSHOT.jar
     */
    private String sparkJar;

    private String sql;


}
