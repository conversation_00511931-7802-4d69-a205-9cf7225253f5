package com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Sample;


import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-27
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tianhe_risk_sample_excute_record")
public class TianheExecuteSampleDO implements Serializable {

    private static final long serialVersionUID = 439884604270833021L;

    @TableId(value = "id", type = IdType.AUTO)
    private long id;
    /**
     * 用例名称
     */
    private String name;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 更新时间
     */
    private Long updateTime;
    /**
     * 是否被删除
     */
    private Integer deleted;
    /**
     * 指标创建人工号
     */
    private String creator;
    /**
     * 最后修改者工号
     */
    private String modifier;
    /**
     * 可编辑人群
     */
    private String operators;
    /**
     * 扩展字段
     */
    private String ext;
    /**
     * 执行记录ID
     */
    private String executeId;
    /**
     * 用例ID
     */
    private Long sampleId;
    /**
     * 执行状态
     */
    private Integer executeStatus;
    /**
     * 变更单ID
     */
    private String changeOrderId;
    /**
     * 动作类型
     */
    private Integer actionType;
    /**
     * AI生成用例xpath
     */
    private String xpath;
}
