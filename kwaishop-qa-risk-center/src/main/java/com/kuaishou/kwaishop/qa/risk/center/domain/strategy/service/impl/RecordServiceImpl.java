package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskIntegerConfigKey.flowRecordIdDataCacheExecutorSize;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.AbortPolicy;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.cache.LoadingCache;
import com.kuaishou.framework.concurrent.BatchAsyncCacheLoader;
import com.kuaishou.framework.concurrent.DynamicThreadExecutor;
import com.kuaishou.framework.concurrent.ExecutorsEx;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.infra.framework.common.util.KsCacheBuilder;
import com.kuaishou.infra.framework.datasource.KsMasterVisitedManager;
import com.kuaishou.kconf.common.json.JsonMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.common.Enum.FlowStatus;
import com.kuaishou.kwaishop.qa.risk.center.common.Enum.FlowType;
import com.kuaishou.kwaishop.qa.risk.center.config.mq.StrategyFlowRecallMq;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert.RecordConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.ClickHouseConnector;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper.StrategyFlowRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordExtraDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordListRequestPojo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordStartEndPDate;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.PageResult;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecodeDetailClickHouseDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecordActionHitResultCkDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecordRequestHitResultCkDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecordStrategyHitResultCkDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowMqCommonDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowRecordBo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowRecordDo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.redis.ThemisToolsRedisClient;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.RecordService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.recordHandler.SparkLogHandler;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordAddRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordDetailListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordVoResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordVoResponse.Builder;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetAllInstantsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.InstantInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.RealTime;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StrategyLogVo;
import com.kuaishou.kwaishop.qa.risk.center.utils.http.KessHttpUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.kess.KessUrlEnum;
import com.kuaishou.kwaishop.qa.risk.center.utils.kess.dto.KessDto;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-19
 */
@Slf4j
@Service
public class RecordServiceImpl implements RecordService {

    @Autowired
    private StrategyFlowRecordMapper strategyFlowRecordMapper;

    @Autowired
    private StrategyFlowRecallMq strategyFlowRecallMq;

    @Autowired
    private ThemisToolsRedisClient themisToolsRedisClient;

    @Autowired
    private SparkLogHandler sparkLogHandler;

    private static final Integer TIME_SLEEP = 2000;

    private static final long MAX_NUMBER_COUNT = 3000;

    private static final int EXPIRE_TIME = 1000000000;

    private static final int DATE_VALUE = 90;

    private DynamicThreadExecutor flowRecordIdDataCacheExecutor =
            DynamicThreadExecutor.dynamic(flowRecordIdDataCacheExecutorSize::get, n -> {
                ThreadPoolExecutor threadPoolExecutor =
                        ExecutorsEx.newRejectingThreadPool(n, n, "flowRecordIdDataCacheExecutor", new AbortPolicy());
                threadPoolExecutor.allowCoreThreadTimeOut(true);
                return threadPoolExecutor;
            });


    private final LoadingCache<Long, FlowRecordStartEndPDate> flowRecordIdDataCache =
            KsCacheBuilder.newBuilder().maximumSize(MAX_NUMBER_COUNT).refreshAfterWrite(60, TimeUnit.SECONDS)
                    .expireAfterAccess(1, TimeUnit.DAYS).concurrencyLevel(10)
                    .build(new BatchAsyncCacheLoader<Long, FlowRecordStartEndPDate>(1, Duration.ofSeconds(3),
                            flowRecordIdDataCacheExecutor) {
                        @Override
                        public FlowRecordStartEndPDate load(Long id) throws Exception {
                            return getFlowRecordId(id);
                        }
                    });


    @Override
    public FlowRecordStartEndPDate getFlowRecordId(Long id) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        if (id == 0) {
            FlowRecordStartEndPDate result = new FlowRecordStartEndPDate();
            String formattedDate =
                    formatter.format(new Date(System.currentTimeMillis() - Duration.ofDays(DATE_VALUE).toMillis()));
            String sql = "SELECT max(pdate) FROM kwaishop_sellerdata.strategy_flow_record where pdate > ".concat(
                    "'" + formattedDate + "'");
            log.info("getFlowRecordId.sql:{}", sql);
            String pDate = ClickHouseConnector.executeSimpleSqlReturnString(sql);

            result.setStartPDate(LocalDate.parse(pDate, formatter1).minusDays(3).format(formatter1));
            result.setEndPDate(pDate);
            return result;
        } else {
            FlowRecordStartEndPDate result = new FlowRecordStartEndPDate();
            StrategyFlowRecordDo flowRecordDo = strategyFlowRecordMapper.getFlowRecordById(id);

            String formattedDate = formatter.format(new Date(flowRecordDo.getFlowStartTime()));
            result.setStartPDate(formattedDate);

            if (Objects.isNull(flowRecordDo.getFlowEndTime()) || Objects.equals(NumberUtils.LONG_ZERO,
                    flowRecordDo.getFlowEndTime())) {
                String formattedDate1 =
                        formatter.format(new Date(flowRecordDo.getFlowStartTime() + Duration.ofDays(1).toMillis()));
                result.setStartPDate(formatter.format(new Date(flowRecordDo.getFlowStartTime())));
                result.setEndPDate(formattedDate1);
            } else {
                result.setEndPDate(formatter.format(new Date(flowRecordDo.getFlowEndTime())));
                result.setStartPDate(formatter.format(new Date(flowRecordDo.getFlowStartTime())));
            }
            return result;
        }
    }


    @Override
    public void flowRecordAdd(FlowRecordAddRequest request) {
        flowRecordAddCheck(request);
        StrategyFlowRecordDo recordDo = RecordConvert.addRequest2Do(request);
        strategyFlowRecordMapper.addStrategyFlowRecord(recordDo);
        StrategyFlowMqCommonDto dto = new StrategyFlowMqCommonDto();
        dto.setType("flowRecordAdd");
        dto.setId(recordDo.getId());
        strategyFlowRecallMq.sendMsg(dto, TIME_SLEEP);
    }

    @Override
    public PageResult<StrategyFlowRecordBo> flowRecordList(FlowRecordListRequest request) {
        FlowRecordListRequestPojo requestPojo = RecordConvert.listRequest2Pojo(request);
        List<StrategyFlowRecordDo> list = strategyFlowRecordMapper.strategyFlowRecordList(requestPojo);
        int total = strategyFlowRecordMapper.strategyFlowRecordCount(requestPojo);

        PageResult<StrategyFlowRecordBo> result = new PageResult<>();
        result.setPageNum(request.getPageNum());
        result.setPageSize(request.getPageSize());
        result.setTotal(total);
        result.setData(RecordConvert.doList2BoList(list));
        return result;
    }

    @Override
    public FlowRecordVoResponse flowRecordDetail(Long id) {
        Builder builder = FlowRecordVoResponse.newBuilder();
        StrategyFlowRecordDo recordDo = strategyFlowRecordMapper.getFlowRecordById(id);
        builder.setName(recordDo.getName());
        builder.setDescription(recordDo.getDescription());
        builder.setFlowType(recordDo.getFlowType());

        if (FlowType.REAL_TIME.getCode() == recordDo.getFlowType()) {
            builder.setRealTime(
                    RealTime.newBuilder().addAllSceneKeyList(Splitter.on(",").splitToList(recordDo.getSceneKeyList()))
                            .build());
        } else if (FlowType.STRATEGY_LOG.getCode() == recordDo.getFlowType()) {
            FlowRecordExtraDto extraDto = JsonMapperUtils.fromJson(recordDo.getExtra(), FlowRecordExtraDto.class);
            builder.setStrategyLog(StrategyLogVo.newBuilder()
                    .addAllSceneKeyList(Splitter.on(",").splitToList(recordDo.getSceneKeyList()))
                    .setTotal(recordDo.getTotal())
                    .setPDatePartition(Joiner.on(',').join(extraDto.getPDatePartitionList()))
                    .setPHourMinPartition(Joiner.on(',').join(extraDto.getPHourMinPartitionList()))
                    .setRequestHitResult(extraDto.getRequestHitResult())
                    .setHitStrategy(Joiner.on(',').join(extraDto.getHitStrategyList()))
                    .setHitAction(Joiner.on(',').join(extraDto.getHitActionList()))
                    .setWhereClause(extraDto.getWhereClause()).build());

        } else if (FlowType.CUSTOM_HIVE.getCode() == recordDo.getFlowType()) {
        }

        return builder.build();
    }

    @Override
    public void recordTaskHandler(Long id) {
        KsMasterVisitedManager.setMasterVisited();
        StrategyFlowRecordDo recordDo = strategyFlowRecordMapper.getFlowRecordById(id);
        log.info("RecordServiceImpl.recordTaskHandler.recordDo:{}", JsonMapperUtils.toJson(recordDo));
        if (FlowType.STRATEGY_LOG.getCode() == recordDo.getFlowType()) {
            sparkLogHandler.handler(id);
        } else if (FlowType.REAL_TIME.getCode() == recordDo.getFlowType()) {
            //实时流量直接录制完成
            strategyFlowRecordMapper.updateFlowStatusAndFlowEndTimeById(id, FlowStatus.FINISHED.getCode(),
                    System.currentTimeMillis());
        } else if (FlowType.CUSTOM_HIVE.getCode() == recordDo.getFlowType()) {
            //实时流量直接录制完成
            strategyFlowRecordMapper.updateFlowStatusAndFlowEndTimeById(id, FlowStatus.FINISHED.getCode(),
                    System.currentTimeMillis());
        }
    }

    @Override
    @SneakyThrows
    public PageResult<RecodeDetailClickHouseDto> flowRecordDetailList(FlowRecordDetailListRequest request) {
        FlowRecordStartEndPDate flowRecordStartEndPDate = flowRecordIdDataCache.get(request.getFlowRecordId());
        String listSql = RecordConvert.detailListRequest2ListSql(request, flowRecordStartEndPDate, request.getPDate());
        log.info("RecordServiceImpl.flowRecordDetailList.listSql:{}", listSql);
        List<RecodeDetailClickHouseDto> recodeDetailList = ClickHouseConnector.executeSimpleSqlReturnRecord(listSql);

        String countSql =
                RecordConvert.detailListRequest2CountSql(request, flowRecordStartEndPDate, request.getPDate());
        log.info("RecordServiceImpl.flowRecordDetailList.countSql:{}", countSql);
        Long total = ClickHouseConnector.executeSimpleSqlReturnLong(countSql);

        PageResult<RecodeDetailClickHouseDto> result = new PageResult<>();
        result.setTotal(total.intValue());
        result.setPageNum(request.getPageNum());
        result.setPageSize(request.getPageSize());
        result.setData(recodeDetailList);
        return result;
    }

    @Override
    @SneakyThrows
    public RecodeDetailClickHouseDto getRecodeDetail(String id, Long flowRecordId) {
        if (Objects.isNull(flowRecordId) || flowRecordId == NumberUtils.LONG_ZERO) {
            throw new RuntimeException("flowRecord必传");
        }
        FlowRecordStartEndPDate flowRecordStartEndPDate = flowRecordIdDataCache.get(flowRecordId);
        String listSql = RecordConvert.detailRequest2Sql(id, flowRecordStartEndPDate);
        log.info("RecordServiceImpl.flowRecordDetailList.listSql:{}", listSql);
        List<RecodeDetailClickHouseDto> recodeDetailList = ClickHouseConnector.executeSimpleSqlReturnRecord(listSql);

        return recodeDetailList.get(0);
    }

    @Override
    public void getRecordDetailInfo(String id, String pDate, Long flowRecordId) {
        if (StringUtils.isBlank(id)) {
            throw new RuntimeException("id不能为空");
        }
        if (Objects.equals(flowRecordId, NumberUtils.LONG_ZERO)) {
            throw new RuntimeException("flowRecordId不能为空");
        }


    }

    @Override
    public Iterable<InstantInfo> getAllInstants(GetAllInstantsRequest request) {

        List<InstantInfo> res = selectHost(request.getServiceName(), null, request.getStage());
        log.info("InstantInfo:{}", JsonMapperUtils.toJson(res));

        return res;
    }

    @Override
    @SneakyThrows
    public void testOneService(String pdate) {

    }

    @Override
    public void setRedis(String key, String value) {
        themisToolsRedisClient.set(key, value, EXPIRE_TIME);
    }

    @Override
    public String getRedis(String key) {
        return themisToolsRedisClient.get(key);
    }

    @Override
    @SneakyThrows
    public String getFlowRecordAnalysisResult(Long id) {
        StrategyFlowRecordDo flowRecord = strategyFlowRecordMapper.getFlowRecordById(id);
        if (FlowType.REAL_TIME.getCode() == flowRecord.getFlowType()) {
            return "<h3>实时流量暂时无分析结果</h3>";
        } else {
            String result = "<!DOCTYPE html>" + "<html lang=\"en\">" + "<head>" + "    <meta charset=\"UTF-8\">"
                    + "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">"
                    + "    <title>Font Size Adjustment</title>" + "    <style>" + "        th, td {"
                    + "            font-size: 16px; /* 设置表格字体大小 */"
                    + "            font-family: Arial, sans-serif; /* 设置表格字体 */" + "        }" + "        "
                    + "        pre {" + "            font-size: 16px; /* 设置与表格相同的字体大小 */"
                    + "            font-family: Arial, sans-serif; /* 设置与表格相同的字体 */"
                    + "            white-space: pre-wrap; /* 保留格式化但允许换行 */"
                    + "            margin: 0; /* 去除多余的间距 */" + "        }" + "    </style>" + "</head>"
                    + "<body>";
            result += "<h3>预期录制条数:" + flowRecord.getTotal() + "</h3>";
            FlowRecordStartEndPDate flowRecordStartEndPDate = flowRecordIdDataCache.get(id);

            /**
             * ck 中总数计算
             */
            String recordTotalSql = RecordConvert.analysisRecordTotalCount(id, flowRecordStartEndPDate);
            Long total = ClickHouseConnector.executeSimpleSqlReturnLong(recordTotalSql);
            result += "<h3>实际录制条数:" + total + "</h3><hr>";
            /**
             * 请求命中数计算
             */
            String recordRequestHitResultSql =
                    RecordConvert.analysisRecordRequestHitResult(id, flowRecordStartEndPDate);
            List<RecordRequestHitResultCkDto> recordRequestHitResult =
                    (List<RecordRequestHitResultCkDto>) ClickHouseConnector.executeSimpleSqlReturnClazz(
                            recordRequestHitResultSql, RecordRequestHitResultCkDto.class);
            Map<Boolean, RecordRequestHitResultCkDto> recordRequestHitResultMap = recordRequestHitResult.stream()
                    .collect(Collectors.toMap(RecordRequestHitResultCkDto::getRequestHitResult, Function.identity()));
            result += "<h3>请求命中分布数:</h3>";
            result += "<h4><pre>    请求命中数:" + (recordRequestHitResultMap.get(true) == null ? 0
                                                                                                :
                                                    recordRequestHitResultMap.get(
                                                                    true)
                                                            .getTotal()) + "</pre></h4>";
            result += "<h4><pre>    请求未命中数:" + (recordRequestHitResultMap.get(false) == null ? 0
                                                                                                   :
                                                      recordRequestHitResultMap.get(
                                                                      false)
                                                              .getTotal()) + "</pre></h4><hr>";
            /**
             * 策略命中数计算
             */
            String recordStrategyHitResultSql =
                    RecordConvert.analysisRecordStrategyHitResult(id, flowRecordStartEndPDate);
            List<RecordStrategyHitResultCkDto> recordStrategyHitResult =
                    (List<RecordStrategyHitResultCkDto>) ClickHouseConnector.executeSimpleSqlReturnClazz(
                            recordStrategyHitResultSql, RecordStrategyHitResultCkDto.class);
            result += "<h3>策略命中分布数:</h3>";
            List<Integer> collect = recordStrategyHitResult.stream().map(x -> x.getHitStrategyList().length())
                    .collect(Collectors.toList());
            Integer max = Collections.max(collect);
            log.info("RecordServiceImpl.getFlowRecordAnalysisResult.max:{}", max);
            result +=
                    "<table>" + "  <thead>" + "    <tr>" + "      <th>命中策略</th>" + "      <th><pre>  命中量</pre></th>"
                            + "    </tr>" + "  </thead>" + "  <tbody>";
            for (RecordStrategyHitResultCkDto ckDto : recordStrategyHitResult) {
                result += "<tr>";
                result += "<td>" + StringUtils.rightPad(ckDto.getHitStrategyList(), max) + "</td><td><pre>  "
                        + ckDto.getTotal() + "</pre></td>";
                result += "</tr>";
            }
            result += "</tbody>" + "</table><hr>";

            /**
             * 动作命中数计算
             */
            String recordActionHitResultSql = RecordConvert.analysisRecordActionHitResult(id, flowRecordStartEndPDate);
            List<RecordActionHitResultCkDto> recordActionHitResult =
                    (List<RecordActionHitResultCkDto>) ClickHouseConnector.executeSimpleSqlReturnClazz(
                            recordActionHitResultSql, RecordActionHitResultCkDto.class);
            result += "<h3>动作命中分布数:</h3>";
            List<Integer> collect1 =
                    recordActionHitResult.stream().map(x -> x.getHitActionList().length()).collect(Collectors.toList());
            Integer max1 = Collections.max(collect1);
            log.info("RecordServiceImpl.getFlowRecordAnalysisResult.max1:{}", max1);
            result +=
                    "<table>" + "  <thead>" + "    <tr>" + "      <th>命中动作</th>" + "      <th><pre>  命中量</pre></th>"
                            + "    </tr>" + "  </thead>" + "  <tbody>";
            for (RecordActionHitResultCkDto ckDto : recordActionHitResult) {
                result += "<tr>";
                result += "<td>" + ckDto.getHitActionList() + "</td><td><pre>  " + ckDto.getTotal() + "</pre></td>";
                result += "</tr>";
            }
            result += "</tbody>" + "</table>";

            result += "</body>" + "</html>";
            return result;
        }

    }

    @Override
    @SneakyThrows
    public String getFlowRecordAnalysisResultWithMd(Long id) {
        String result = "|命中策略|命中量|\n";
        result += "|------|------|\n";
        FlowRecordStartEndPDate flowRecordStartEndPDate = flowRecordIdDataCache.get(id);
        String recordStrategyHitResultSql = RecordConvert.analysisRecordStrategyHitResult(id, flowRecordStartEndPDate);
        List<RecordStrategyHitResultCkDto> recordStrategyHitResult =
                ClickHouseConnector.executeSimpleSqlReturnClazz(recordStrategyHitResultSql,
                        RecordStrategyHitResultCkDto.class);
        for (RecordStrategyHitResultCkDto ckDto : recordStrategyHitResult) {
            result += "|" + ckDto.getHitStrategyList() + "|" + ckDto.getTotal() + "|\n";
        }
        return result;
    }

    @Override
    @SneakyThrows
    public void recordFinalMessage(Long id) {
        log.info("RecordServiceImpl.recordFinalMessage.id:{}", id);
        StrategyFlowRecordDo recordDo = strategyFlowRecordMapper.getFlowRecordById(id);
        FlowRecordStartEndPDate flowRecordStartEndPDate = flowRecordIdDataCache.get(id);
        /**
         * ck 中总数计算
         */
        String recordTotalSql = RecordConvert.analysisRecordTotalCount(id, flowRecordStartEndPDate);
        Long total = ClickHouseConnector.executeSimpleSqlReturnLong(recordTotalSql);
        log.info("RecordServiceImpl.recordFinalMessage.total:{}", total);

        FlowRecordExtraDto extraDto = JsonMapperUtils.fromJson(recordDo.getExtra(), FlowRecordExtraDto.class);

        log.info("RecordServiceImpl.recordFinalMessage.id:{},extraDto:{}", id, JsonMapperUtils.toJson(extraDto));
        strategyFlowRecordMapper.updateExtraById(id, JsonMapperUtils.toJson(extraDto));
    }

    private void flowRecordAddCheck(FlowRecordAddRequest request) {
        if (StringUtils.isBlank(request.getName())) {
            throw new RuntimeException("请输入记录名称");
        }
        if (FlowType.STRATEGY_LOG.getCode() == request.getFlowType()) {
            if (request.getStrategyLog().getTotal() == NumberUtils.INTEGER_ZERO) {
                throw new RuntimeException("请输入数量");
            }
            if (StringUtils.isEmpty(request.getStrategyLog().getWhereClause())) {
                if (CollectionUtils.isEmpty(request.getStrategyLog().getSceneKeyListList())) {
                    throw new RuntimeException("请输入场景key");
                }
                if (CollectionUtils.isEmpty(request.getStrategyLog().getPDatePartitionList())) {
                    throw new RuntimeException("请输入日期分区");
                }
            } else {
                if (CollectionUtils.isNotEmpty(request.getStrategyLog().getSceneKeyListList())) {
                    throw new RuntimeException("自定义where条件和场景key无法同时存在");
                }
                if (CollectionUtils.isNotEmpty(request.getStrategyLog().getPDatePartitionList())) {
                    throw new RuntimeException("自定义where条件和日期分区无法同时存在");
                }
                if (request.getStrategyLog().getRequestHitResult() != NumberUtils.INTEGER_ZERO) {
                    throw new RuntimeException("自定义where条件和请求命中无法同时存在");
                }
                if (CollectionUtils.isNotEmpty(request.getStrategyLog().getPHourMinPartitionList())) {
                    throw new RuntimeException("自定义where条件和小时分钟分区无法同时存在");
                }
                if (CollectionUtils.isNotEmpty(request.getStrategyLog().getHitStrategyList())) {
                    throw new RuntimeException("自定义where条件和命中策略无法同时存在");
                }
                if (CollectionUtils.isNotEmpty(request.getStrategyLog().getHitActionList())) {
                    throw new RuntimeException("自定义where条件和命中动作无法同时存在");
                }
            }
        }
    }


    /**
     * 获取host工具
     */
    public List<InstantInfo> selectHost(String rpcName, String cookie, String stage) {
        String api = "/scheduler/api/readInstanceRouteTable";
        String apiUrl =
                stage.equals("STAGING") ? KessUrlEnum.STAGING.getUrl() + api : KessUrlEnum.ONLINE.getUrl() + api;

        Map<String, String> header = new HashMap<>();
        header.put("Cookie", cookie);


        Map<String, Object> para = new HashMap<>();
        para.put("cluster", "PRODUCTION");
        para.put("service", rpcName);

        String res = KessHttpUtils.postJson(apiUrl, para.toString(), header);


        Map<String, Object> result = (Map<String, Object>) ObjectMapperUtils.fromJson(res).get("result");

        Map<String, Object> rp = (Map<String, Object>) result.get("original");

        Set<String> strings = rp.keySet();

        String host = "";
        String port = "";

        String lane = "";
        String hostname = "";
        String group = "";
        String ip = "";
        List<InstantInfo> instantInfos = new ArrayList<>();
        for (String key : strings) {
            Map<String, Object> value = (Map<String, Object>) rp.get(key);
            Set<String> strings1 = value.keySet();


            ip = (String) value.get("host");
            Map<String, Object> entries = (Map<String, Object>) value.get("entries");
            port = String.valueOf((Integer) entries.get("grpc"));
            hostname = (String) value.get("hostname");
            lane = (String) value.get("lane");
            group = (String) value.get("group");

            instantInfos.add(covertInstantInfo(ip, port, hostname, lane, group));


        }
        return instantInfos;
    }

    public InstantInfo covertInstantInfo(String ip, String port, String hostname, String lane, String group) {
        // 使用常量来表示默认值
        String prod = "PROD";
        String defaultName = "DEFAULT";

        // 优化条件判断和赋值
        group = lane.contains("PRT") ? "" : group;
        lane = lane.isEmpty() ? prod : lane;
        group = (lane.equals(prod) && group.isEmpty()) ? defaultName : group;

        // 构建主机名
        String hostNameV1 = String.format("%s_%s_%s", lane, group, hostname);

        // 返回构建的 InstantInfo 对象
        return InstantInfo.newBuilder().setIp(ip).setPort(port).setHostName(hostNameV1).build();
    }


    public String selectFunName(String kessName, KessDto hostPort, String cookie, String stage) {

        String api = "/debugger/v1/listMethods";
        String urlLine =
                stage.equals("STAGING") ? KessUrlEnum.STAGING.getUrl() + api : KessUrlEnum.ONLINE.getUrl() + api;


        Map<String, Object> host = new HashMap<>();

        host.put(hostPort.getHost(), hostPort.getPort());


        Map<String, Object> data = new HashMap<>();

        data.put("kessName", kessName);
        data.put("hostinfo", host.toString());
        data.put("Cookie", cookie);


        String res = KessHttpUtils.get(urlLine, data);


        return res;

    }

}


