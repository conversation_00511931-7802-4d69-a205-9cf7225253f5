package com.kuaishou.kwaishop.qa.risk.center.tianhe.Enums;

public enum TianheSampleMessageType {
    CREATE("0"),
    EXECUTE("1");

    private final String value;

    // 构造方法
    TianheSampleMessageType(String value) {
        this.value = value;
    }

    // 获取 value（key -> value）
    public String getValue() {
        return value;
    }

    // 通过 value 查找 key（value -> key）
    public static TianheSampleMessageType fromValue(String value) {
        for (TianheSampleMessageType type : TianheSampleMessageType.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }
}
