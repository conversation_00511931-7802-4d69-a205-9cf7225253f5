package com.kuaishou.kwaishop.qa.risk.center.domain.problem.convert.impl;

import java.util.Objects;

import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.problem.ProblemDetailDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.convert.ProblemDetailConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.bo.ProblemDetailBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.enums.ProblemLevelTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.enums.ProblemMissingTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.CreateProblemRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.ProblemDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.QueryProblemDetailListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.QueryProblemDetailPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.UpdateProblemRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-06
 */
@Component
public class ProblemDetailConvertImpl implements ProblemDetailConvert {

    @Override
    public ProblemDetailDO buildCreateDO(ProblemDetailBO problemDetailBO) {
        return ProblemDetailDO.builder()
                .detailType(problemDetailBO.getDetailType())
                .problemLink(problemDetailBO.getProblemLink())
                .name(problemDetailBO.getName())
                .level(problemDetailBO.getLevel())
                .problemDesc(problemDetailBO.getProblemDesc())
                .problemReason(problemDetailBO.getProblemReason())
                .missingType(problemDetailBO.getMissingType())
                .missingReason(problemDetailBO.getMissingReason())
                .problemStartTime(problemDetailBO.getProblemStartTime())
                .problemEndTime(problemDetailBO.getProblemEndTime())
                .creator(problemDetailBO.getOperator())
                .modifier(problemDetailBO.getOperator())
                .centerId(problemDetailBO.getCenterId())
                .teamId(problemDetailBO.getTeamId())
                .build();
    }

    @Override
    public ProblemDetailDO buildUpdateDO(ProblemDetailDO existDO, ProblemDetailBO problemDetailBO) {
        existDO.setProblemLink(problemDetailBO.getProblemLink());
        existDO.setName(problemDetailBO.getName());
        existDO.setLevel(problemDetailBO.getLevel());
        existDO.setProblemDesc(problemDetailBO.getProblemDesc());
        existDO.setProblemReason(problemDetailBO.getProblemReason());
        existDO.setMissingType(problemDetailBO.getMissingType());
        existDO.setMissingReason(problemDetailBO.getMissingReason());
        existDO.setProblemStartTime(problemDetailBO.getProblemStartTime());
        existDO.setProblemEndTime(problemDetailBO.getProblemEndTime());
        existDO.setModifier(problemDetailBO.getOperator());
        return existDO;
    }

    @Override
    public ProblemDetailBO buildCreateBO(CreateProblemRequest request) {
        return ProblemDetailBO.builder()
                .operator(request.getOperator())
                .detailType(request.getDetailType())
                .problemLink(request.getProblemLink())
                .name(request.getName())
                .level(request.getLevel())
                .centerId(request.getCenterId())
                .teamId(request.getTeamId())
                .problemDesc(request.getProblemDesc())
                .problemReason(request.getProblemReason())
                .missingType(request.getMissingType())
                .missingReason(request.getMissingReason())
                .problemStartTime(request.getProblemStartTime())
                .problemEndTime(request.getProblemEndTime())
                .build();
    }

    @Override
    public ProblemDetailBO buildUpdateBO(UpdateProblemRequest request) {
        return ProblemDetailBO.builder()
                .id(request.getId())
                .operator(request.getOperator())
                .detailType(request.getDetailType())
                .problemLink(request.getProblemLink())
                .name(request.getName())
                .level(request.getLevel())
                .centerId(request.getCenterId())
                .teamId(request.getTeamId())
                .problemDesc(request.getProblemDesc())
                .problemReason(request.getProblemReason())
                .missingType(request.getMissingType())
                .missingReason(request.getMissingReason())
                .problemStartTime(request.getProblemStartTime())
                .problemEndTime(request.getProblemEndTime())
                .build();
    }

    @Override
    public ProblemDetailBO buildQueryListBO(QueryProblemDetailListRequest request) {
        return ProblemDetailBO.builder()
                .id(request.getId())
                .detailType(request.getDetailType())
                .name(request.getName())
                .level(request.getLevel())
                .centerId(request.getCenterId())
                .teamId(request.getTeamId())
                .missingType(request.getMissingType())
                .build();
    }

    @Override
    public ProblemDetailBO buildQueryPageListBO(QueryProblemDetailPageListRequest request) {
        return ProblemDetailBO.builder()
                .id(request.getId())
                .detailType(request.getDetailType())
                .name(request.getName())
                .level(request.getLevel())
                .centerId(request.getCenterId())
                .teamId(request.getTeamId())
                .missingType(request.getMissingType())
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .build();
    }

    @Override
    public ProblemDTO buildProblemDTO(ProblemDetailDO problemDetailDO, String centerName, String teamName) {
        return ProblemDTO.newBuilder()
                .setId(problemDetailDO.getId())
                .setDetailType(problemDetailDO.getDetailType())
                .setProblemLink(problemDetailDO.getProblemLink())
                .setName(problemDetailDO.getName())
                .setLevel(problemDetailDO.getLevel())
                .setCenterId(problemDetailDO.getCenterId())
                .setTeamId(problemDetailDO.getTeamId())
                .setProblemDesc(problemDetailDO.getProblemDesc())
                .setProblemReason(problemDetailDO.getProblemReason())
                .setMissingType(problemDetailDO.getMissingType())
                .setMissingReason(problemDetailDO.getMissingReason())
                .setProblemStartTime(problemDetailDO.getProblemStartTime())
                .setProblemEndTime(problemDetailDO.getProblemEndTime())
                .setMissingTypeDesc(Objects.requireNonNull(ProblemMissingTypeEnum.of(problemDetailDO.getMissingType())).getDesc())
                .setLevelDesc(Objects.requireNonNull(
                        ProblemLevelTypeEnum.of(problemDetailDO.getLevel(), problemDetailDO.getDetailType())).getDesc())
                .setCreator(problemDetailDO.getCreator())
                .setModifier(problemDetailDO.getModifier())
                .setCreateTime(problemDetailDO.getCreateTime())
                .setUpdateTime(problemDetailDO.getUpdateTime())
                .setCenterName(centerName)
                .setTeamName(teamName)
                .build();
    }
}
