package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskIntegerConfigKey.flowRecordIdDataCacheExecutorSize;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.AbortPolicy;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.cache.LoadingCache;
import com.google.common.collect.Sets;
import com.kuaishou.framework.concurrent.BatchAsyncCacheLoader;
import com.kuaishou.framework.concurrent.DynamicThreadExecutor;
import com.kuaishou.framework.concurrent.ExecutorsEx;
import com.kuaishou.framework.supplier.DynamicRateLimiter;
import com.kuaishou.framework.supplier.DynamicSuppliers;
import com.kuaishou.infra.framework.common.util.KsCacheBuilder;
import com.kuaishou.infra.framework.datasource.KsMasterVisitedManager;
import com.kuaishou.infra.framework.kafka.KafkaProducers;
import com.kuaishou.intown.json.JSON;
import com.kuaishou.intown.json.JSONObject;
import com.kuaishou.kconf.common.json.JsonMapperUtils;
import com.kuaishou.kwaishop.apollo.policy.center.protobuf.policy.ActionDto;
import com.kuaishou.kwaishop.apollo.policy.center.protobuf.policy.PolicyForTrafficReplayResponse;
import com.kuaishou.kwaishop.qa.risk.center.common.Enum.FlowType;
import com.kuaishou.kwaishop.qa.risk.center.common.Enum.ReplayStatus;
import com.kuaishou.kwaishop.qa.risk.center.common.Enum.ReplayTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.config.mq.StrategyFlowRecallMq;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert.ReplayConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.ClickHouseConnector;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper.StrategyFlowRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper.StrategyFlowReplayMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.DiffDetailResultDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordExtraDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordStartEndPDate;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowReplayListRequestPojo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.PageResult;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecodeDetailClickHouseDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.ReplayActionHitResultCkDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.ReplayConfig;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.ReplayConfigDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.ReplayDetailClickHouseDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.ReplayKafkaDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.ReplayStrategyHitResultCkDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowMqCommonDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowRealtimeKafkaDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowRecordDo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowReplayBo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowReplayDo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.RecordService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.ReplayService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.replayHandler.ReplayStrategyRpcHandler;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.replayHandler.StrategyCheckResult;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.util.KdevResponseUtils;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.CheckReplayStatusByPipelineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.CheckReplayStatusByPipelineResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowReplayAddRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowReplayListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetAllInstantsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetFlowReplayDetailListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.InstantInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StartReplayByPipelineRequest;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-17
 */
@Service
@Slf4j
public class ReplayServiceImpl implements ReplayService {

    @Autowired
    private StrategyFlowReplayMapper strategyFlowReplayMapper;

    @Autowired
    private StrategyFlowRecallMq strategyFlowRecallMq;

    @Autowired
    private StrategyFlowRecordMapper strategyFlowRecordMapper;

    @Autowired
    private ReplayStrategyRpcHandler replayStrategyRpcHandler;

    @Autowired
    private RecordService recordService;

    private ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(100);

    private static final Integer TIME_SLEEP = 2000;

    private static final long MAX_NUMBER_COUNT = 3000;

    private static final int EACH_LOOP = 25;

    private static final int EXPIRE_TIME = 1000000000;

    private static final long MOCK_ID = 206L;

    private static final long DATE_VALUE = 45;

    private static final Set<String> LIST_SCENE_KEYS =
            Sets.newHashSet("S_Commodity_Recall_Audit_Monitoring", "S_Exposure_Recall", "S_flow_quick_add_item",
                    "S_trade_quick_add_item", "S_TEST", "S_trade_price_change_item", "S_quickly_sell_item",
                    "S_sell_after_long_sleep_item", "S_RISK_ANCHOR_TAGGING", "S_ANCHOR_SELL_IN_JUDGE",
                    "S_PRODUCT_RELEASE_MACHINE_AUDIT", "S_DOOR_TO_TAKE", "S_luoshuwen03_kafka",
                    "S_PRODUCT_PUBLISH_TEST", "S_cuiyanwei_hudi_test", "S_11", "S_lsw_test_scene_feature",
                    "S_Commodity_Recall_Audit_Monitoring_Baomai", "S_Exposure_Recall_Baomai", "S_azptest",
                    "S_luoshuwen03_testTask", "S_luoshuwen03_sceneTask1", "S_online_luoshuwen03_kafka_test",
                    "S_online_luoshuwen03_mq_test", "S_1234q", "S_123qwert1", "S_luoshuwen03_test_taskbind");

    private DynamicThreadExecutor flowReplayIdDataCacheExecutor =
            DynamicThreadExecutor.dynamic(flowRecordIdDataCacheExecutorSize::get, n -> {
                ThreadPoolExecutor threadPoolExecutor =
                        ExecutorsEx.newRejectingThreadPool(n, n, "flowReplayIdDataCacheExecutor", new AbortPolicy());
                threadPoolExecutor.allowCoreThreadTimeOut(true);
                return threadPoolExecutor;
            });

    private final LoadingCache<Long, FlowRecordStartEndPDate> flowReplayIdDataCache =
            KsCacheBuilder.newBuilder().maximumSize(MAX_NUMBER_COUNT).refreshAfterWrite(60, TimeUnit.SECONDS)
                    .expireAfterAccess(1, TimeUnit.DAYS).concurrencyLevel(10)
                    .build(new BatchAsyncCacheLoader<Long, FlowRecordStartEndPDate>(1, Duration.ofSeconds(3),
                            flowReplayIdDataCacheExecutor) {
                        @Override
                        public FlowRecordStartEndPDate load(Long id) throws Exception {
                            try {
                                return getFlowReplayId(id);
                            } catch (Exception e) {
                                log.error("getFlowReplayId error, id:{}", id, e);
                                return null;
                            }
                        }
                    });

    private final LoadingCache<Long, DynamicRateLimiter> flowReplayIdQpsLimiterCache =
            KsCacheBuilder.newBuilder().maximumSize(MAX_NUMBER_COUNT).refreshAfterWrite(60, TimeUnit.SECONDS)
                    .expireAfterAccess(1, TimeUnit.DAYS).concurrencyLevel(10)
                    .build(new BatchAsyncCacheLoader<Long, DynamicRateLimiter>(1, Duration.ofSeconds(3),
                            flowReplayIdDataCacheExecutor) {
                        @Override
                        public DynamicRateLimiter load(Long id) throws Exception {
                            return DynamicSuppliers.dynamicRateLimiter(() -> {
                                KsMasterVisitedManager.setMasterVisited();
                                StrategyFlowReplayDo flowReplay = strategyFlowReplayMapper.getFlowReplayById(id);
                                return flowReplay.getReplayQps();
                            });
                        }
                    });

    private final LoadingCache<String, List<StrategyFlowReplayDo>> sceneKeyFlowReplayDoCache =
            KsCacheBuilder.newBuilder().maximumSize(MAX_NUMBER_COUNT).refreshAfterWrite(60, TimeUnit.SECONDS)
                    .expireAfterAccess(1, TimeUnit.DAYS).concurrencyLevel(10)
                    .build(new BatchAsyncCacheLoader<String, List<StrategyFlowReplayDo>>(1, Duration.ofSeconds(3),
                            flowReplayIdDataCacheExecutor) {
                        @Override
                        public List<StrategyFlowReplayDo> load(String sceneKey) throws Exception {
                            try {
                                KsMasterVisitedManager.setMasterVisited();
                                return strategyFlowReplayMapper.getAllRealTimeReplayList(sceneKey);
                            } catch (Exception e) {
                                log.error("getAllRealTimeReplayList error, sceneKey:{}", sceneKey, e);
                                return null;
                            }
                        }
                    });


    public FlowRecordStartEndPDate getFlowReplayId(Long id) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        DateTimeFormatter formatter1 = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        if (id == 0) {
            FlowRecordStartEndPDate result = new FlowRecordStartEndPDate();
            String formattedDate =
                    formatter.format(new Date(System.currentTimeMillis() - Duration.ofDays(DATE_VALUE).toMillis()));
            String sql = "SELECT max(pdate) FROM kwaishop_sellerdata.strategy_flow_replay where pdate > ".concat(
                    "'" + formattedDate + "'");
            String pDate = ClickHouseConnector.executeSimpleSqlReturnString(sql);

            result.setStartPDate(LocalDate.parse(pDate, formatter1).minusDays(3).format(formatter1));
            result.setEndPDate(pDate);
            return result;
        } else {
            FlowRecordStartEndPDate result = new FlowRecordStartEndPDate();
            KsMasterVisitedManager.setMasterVisited();
            StrategyFlowReplayDo flowReplayDo = strategyFlowReplayMapper.getFlowReplayById(id);
            log.info("根据id获得的流量回放数据:{}", JsonMapperUtils.toJson(flowReplayDo));

            String formattedDate = formatter.format(new Date(flowReplayDo.getReplayStartTime()));
            result.setStartPDate(formattedDate);

            if (Objects.isNull(flowReplayDo.getReplayEndTime()) || Objects.equals(NumberUtils.LONG_ZERO,
                    flowReplayDo.getReplayEndTime())) {
                String formattedDate1 =
                        formatter.format(new Date(flowReplayDo.getReplayStartTime() + Duration.ofDays(1).toMillis()));
                result.setStartPDate(formatter.format(new Date(flowReplayDo.getReplayStartTime())));
                result.setEndPDate(formattedDate1);
            } else {
                result.setEndPDate(formatter.format(new Date(flowReplayDo.getReplayEndTime())));
                result.setStartPDate(formatter.format(new Date(flowReplayDo.getReplayStartTime())));
            }
            return result;
        }
    }

    @Override
    public void flowReplayAdd(FlowReplayAddRequest request) {
        StrategyFlowRecordDo flowRecord = strategyFlowRecordMapper.getFlowRecordById(request.getFlowRecordId());
        log.info("flowReplayAdd flowRecord流水线记录: {}", JsonMapperUtils.toJson(flowRecord));
        flowReplayAddCheck(request, flowRecord);
        //对流量回放为泳道的进行特殊处理，转成ip和port的形式
        if (request.getReplayConfigOne().getReplayType() == ReplayTypeEnum.LAND_ID.getCode()
                || request.getReplayConfigTwo().getReplayType() == ReplayTypeEnum.LAND_ID.getCode()) {
            log.info("flowReplayAdd 转换成ip和port的形式前前前 request : {}", JsonMapperUtils.toJson(request));
            request = covertFlowRecordToIpAndPort(request);
            log.info("flowReplayAdd 转换成ip和port的形式后后后后 request : {}", JsonMapperUtils.toJson(request));
        }


        FlowRecordExtraDto extraDto = Optional.ofNullable(flowRecord.getExtra())
                .map(extra -> JsonMapperUtils.fromJson(extra, FlowRecordExtraDto.class))
                .orElse(new FlowRecordExtraDto());

        StrategyFlowReplayDo replayDo = ReplayConvert.addRequest2Do(request, extraDto);
        strategyFlowReplayMapper.addStrategyFlowReplay(replayDo);

        if (FlowType.STRATEGY_LOG.getCode() == flowRecord.getFlowType()
                || FlowType.CUSTOM_HIVE.getCode() == flowRecord.getFlowType()) {
            StrategyFlowMqCommonDto dto = new StrategyFlowMqCommonDto();
            dto.setType("flowReplayAdd");
            dto.setId(replayDo.getId());
            dto.setStartPageNum(NumberUtils.INTEGER_ONE);
            dto.setEndPageNum(dto.getStartPageNum() + NumberUtils.INTEGER_TWO);
            log.info("发送流量回放消息：{}", JsonMapperUtils.toJson(dto));
            strategyFlowRecallMq.sendMsg(dto, TIME_SLEEP);
        }
    }

    private FlowReplayAddRequest covertFlowRecordToIpAndPort(FlowReplayAddRequest request) {
        // 获取所有实例信息
        Iterable<InstantInfo> instants = recordService.getAllInstants(GetAllInstantsRequest.newBuilder()
                .setServiceName("kwaishop-apollo-strategy-center")
                .setStage("PROD")
                .build());
        log.info("实例信息: {}", JsonMapperUtils.toJson(instants));
        // 提取出公共的逻辑为一个方法
        Function<com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayConfig, String> getProdInfo = replayConfig -> {
            log.info("Received ReplayConfig: " + replayConfig);

            if (replayConfig.getReplayType() == ReplayTypeEnum.LAND_ID.getCode()) {
                log.info("ReplayType matches LAND_ID");

                return StreamSupport.stream(instants.spliterator(), false)
                        .peek(instantInfo -> log.info("Processing instantInfo: " + instantInfo))
                        .filter(instantInfo -> {
                            boolean containsLandId = instantInfo.getHostName().contains(replayConfig.getLandId());
                            log.info("HostName: " + instantInfo.getHostName() + ", LandId: "
                                    +
                                    replayConfig.getLandId() + ", containsLandId: " + containsLandId);
                            return containsLandId;
                        })
                        .map(JsonMapperUtils::toJson)
                        .peek(json -> log.info("Mapped to JSON: " + json))
                        .findFirst()
                        .orElseGet(() -> {
                            log.info("No matching instantInfo found for LandId: " + replayConfig.getLandId());
                            return "";
                        });
            }

            log.info("ReplayType does not match LAND_ID");
            return "";
        };

        // 获取两个 ReplayConfig 的 prodInfo
        String prodInfoReplayConfigOne = getProdInfo.apply(request.getReplayConfigOne());
        String prodInfoReplayConfigTwo = getProdInfo.apply(request.getReplayConfigTwo());

        FlowReplayAddRequest.Builder builder = request.toBuilder();
        if (!prodInfoReplayConfigOne.isEmpty()) {
            builder.setReplayConfigOne(buildReplayConfig(request.getReplayConfigOne(), prodInfoReplayConfigOne));
        }

        if (!prodInfoReplayConfigTwo.isEmpty()) {
            builder.setReplayConfigTwo(buildReplayConfig(request.getReplayConfigTwo(), prodInfoReplayConfigTwo));
        }
        return builder.build();
    }

    // 提取出构建 ReplayConfig 的逻辑
    private com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayConfig buildReplayConfig(
            com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayConfig originalConfig, String prodInfo) {
        // 在这里转一下，把json形式的，转成下划线形式的
        log.info("转换前的prodInfo: {}", prodInfo);
        prodInfo = convertJsonToTargetFormat(prodInfo);
        log.info("转换后的prodInfo: {}", prodInfo);
        return com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayConfig.newBuilder()
                .setReplayType(originalConfig.getReplayType())
                .setLandId(originalConfig.getLandId())
                .addPodInfoList(prodInfo)
                .build();
    }

    public String convertJsonToTargetFormat(String jsonString) {
        try {
            // 使用 Fastjson 解析 JSON 字符串
            JSONObject jsonObject = JSON.parseObject(jsonString);

            // 提取字段
            String hostName = jsonObject.getString("hostName");
            String ip = jsonObject.getString("ip");
            String port = jsonObject.getString("port");

            // 拼接成目标格式
            String result = String.format("%s:%s_%s", hostName, ip, port);

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }



    @Override
    public StrategyFlowReplayBo flowReplayDetail(Long id) {
        StrategyFlowReplayDo strategyFlowReplayDo = strategyFlowReplayMapper.selectById(id);
        return ReplayConvert.do2Bo(strategyFlowReplayDo);
    }

    @Override
    public PageResult<StrategyFlowReplayBo> flowReplayList(FlowReplayListRequest request) {
        FlowReplayListRequestPojo requestPojo = ReplayConvert.listRequest2Pojo(request);
        List<StrategyFlowReplayDo> replayDoList = strategyFlowReplayMapper.strategyFlowReplayList(requestPojo);
        int total = strategyFlowReplayMapper.strategyFlowReplayCount(requestPojo);

        PageResult<StrategyFlowReplayBo> pageResult = new PageResult<>();
        pageResult.setData(ReplayConvert.doList2BoList(replayDoList));
        pageResult.setTotal(total);
        pageResult.setPageNum(request.getPageNum());
        pageResult.setPageSize(request.getPageSize());

        return pageResult;
    }

    @Override
    @SneakyThrows
    public void flowReplayOfflineHandler(Long id, int startPageNum, int endPageNum) {
        DynamicRateLimiter dynamicRateLimiter = flowReplayIdQpsLimiterCache.get(id);
        KsMasterVisitedManager.setMasterVisited();
        StrategyFlowReplayDo replayDo = strategyFlowReplayMapper.selectById(id);
        if (ReplayStatus.FINISHED.getCode() == replayDo.getReplayStatus()) {
            log.info("flowReplayOfflineHandler 回放已完成");
            return;
        }
        Long flowRecordId = replayDo.getFlowRecordId();
        ReplayConfigDto replayConfigDto = JsonMapperUtils.fromJson(replayDo.getReplayConfig(), ReplayConfigDto.class);

        int pageSize = 3;
        for (int pageNum = startPageNum; pageNum <= endPageNum; pageNum++) {
            int offset = (pageNum - 1) * pageSize;
            String sql = ReplayConvert.replayQuerySql(flowRecordId, recordService.getFlowRecordId(flowRecordId));
            sql = sql + " limit " + offset + "," + pageSize;
            List<RecodeDetailClickHouseDto> recodeDetailList = ClickHouseConnector.executeSimpleSqlReturnRecord(sql);
            // recodeDetailList 为空   flowRecordId == 224
            if (CollectionUtils.isEmpty(recodeDetailList)) {
                log.info("recodeDetailList为空");
                strategyFlowReplayMapper.updateReplayStatusAndReplayEndTimeById(id, ReplayStatus.FINISHED.getCode(),
                        System.currentTimeMillis());
                StrategyFlowMqCommonDto dto = new StrategyFlowMqCommonDto();
                dto.setType("flowReplayFinalMessage");
                dto.setId(replayDo.getId());
                strategyFlowRecallMq.sendMsg(dto, Duration.ofMinutes(1).toMillis());
                return;
            }
            log.info("ReplayServiceImpl.flowReplayHandler.id:{}, pageNum:{}, size:{}", id, pageNum,
                    recodeDetailList.size());

            for (RecodeDetailClickHouseDto dto : recodeDetailList) {
                dynamicRateLimiter.acquire();
                StrategyCheckResult result1 = getResult(replayConfigDto.getReplayConfigOne(), replayConfigDto, dto);
                StrategyCheckResult result2 = getResult(replayConfigDto.getReplayConfigTwo(), replayConfigDto, dto);
                log.info("ReplayServiceImpl.flowReplayHandler.result1:{},result2:{}", JsonMapperUtils.toJson(result1),
                        JsonMapperUtils.toJson(result2));

                boolean checkResult = checkTwoResult(result1, result2);
                ReplayKafkaDto replayKafkaDto =
                        ReplayConvert.buildReplayKafkaDto(dto, replayDo.getId(), result1, result2, !checkResult);
                KafkaProducers.sendString("kwaishop_apollo_strategy_flow_replay",
                        JsonMapperUtils.toJson(replayKafkaDto));
            }
        }
        StrategyFlowMqCommonDto dto = new StrategyFlowMqCommonDto();
        dto.setType("flowReplayAdd");
        dto.setId(replayDo.getId());
        dto.setStartPageNum(endPageNum + NumberUtils.INTEGER_ONE);
        dto.setEndPageNum(dto.getStartPageNum() + NumberUtils.INTEGER_TWO);
        strategyFlowRecallMq.sendMsg(dto);
    }

    @Override
    @SneakyThrows
    public void flowReplayRealTimeHandler(StrategyFlowRealtimeKafkaDto message) {
        List<StrategyFlowReplayDo> strategyFlowReplayDos = sceneKeyFlowReplayDoCache.get(message.getSceneKey());
        if (CollectionUtils.isEmpty(strategyFlowReplayDos)) {
            return;
        }

        for (StrategyFlowReplayDo replayDo : strategyFlowReplayDos) {
            RecodeDetailClickHouseDto dto = ReplayConvert.convert2ClickHouseDto(message, replayDo);
            ReplayConfigDto replayConfigDto =
                    JsonMapperUtils.fromJson(replayDo.getReplayConfig(), ReplayConfigDto.class);
            StrategyCheckResult result1 = getResult(replayConfigDto.getReplayConfigOne(), replayConfigDto, dto);
            StrategyCheckResult result2 = getResult(replayConfigDto.getReplayConfigTwo(), replayConfigDto, dto);
            log.info("ReplayServiceImpl.flowReplayHandler.result1:{},result2:{}", JsonMapperUtils.toJson(result1),
                    JsonMapperUtils.toJson(result2));

            //0 没有 diff。1有diff
            boolean checkResult = checkTwoResult(result1, result2);
            ReplayKafkaDto replayKafkaDto =
                    ReplayConvert.buildReplayKafkaDto(dto, replayDo.getId(), result1, result2, !checkResult);
            KafkaProducers.sendString("kwaishop_apollo_strategy_flow_replay", JsonMapperUtils.toJson(replayKafkaDto));
        }


    }

    @Override
    public void testQpsFlow(int qps, int time) {
        // 每个请求之间的时间间隔（以毫秒为单位）
        long interval = 1000 / qps;

        Runnable task = () -> queryTask();

        for (int i = 0; i < qps * time; i++) {
            scheduledExecutorService.schedule(task, i * interval, TimeUnit.MILLISECONDS);
        }
    }

    @Override
    @SneakyThrows
    public PageResult<ReplayDetailClickHouseDto> getFlowReplayDetailList(GetFlowReplayDetailListRequest request) {
        FlowRecordStartEndPDate flowRecordStartEndPDate = flowReplayIdDataCache.get(request.getFlowReplayId());
        String listSql = ReplayConvert.detailListRequest2ListSql(request, flowRecordStartEndPDate, request.getPDate());
        log.info("ReplayServiceImpl.getFlowReplayDetailList.listSql:{}", listSql);
        List<ReplayDetailClickHouseDto> replayDetailList =
                ClickHouseConnector.executeSimpleSqlReturnClazz(listSql, ReplayDetailClickHouseDto.class);
        fillDiffResultDesc(replayDetailList);

        String countSql =
                ReplayConvert.detailListRequest2CountSql(request, flowRecordStartEndPDate, request.getPDate());
        log.info("ReplayServiceImpl.getFlowReplayDetailList.countSql:{}", countSql);
        Long total = ClickHouseConnector.executeSimpleSqlReturnLong(countSql);

        PageResult<ReplayDetailClickHouseDto> result = new PageResult<>();
        result.setTotal(total.intValue());
        result.setPageNum(request.getPageNum());
        result.setPageSize(request.getPageSize());
        result.setData(replayDetailList);
        return result;
    }

    @Override
    @SneakyThrows
    public ReplayDetailClickHouseDto getFlowReplayDetail(String id, Long flowReplayId) {
        if (Objects.isNull(flowReplayId) || flowReplayId == NumberUtils.LONG_ZERO) {
            throw new RuntimeException("flowReplayId必传");
        }
        FlowRecordStartEndPDate flowRecordStartEndPDate = flowReplayIdDataCache.get(flowReplayId);
        String listSql = ReplayConvert.detailRequest2Sql(id, flowRecordStartEndPDate);
        log.info("RecordServiceImpl.flowRecordDetailList.listSql:{}", listSql);
        List<ReplayDetailClickHouseDto> recodeDetailList =
                ClickHouseConnector.executeSimpleSqlReturnClazz(listSql, ReplayDetailClickHouseDto.class);

        StrategyFlowReplayDo flowReplay = strategyFlowReplayMapper.getFlowReplayById(flowReplayId);
        RecodeDetailClickHouseDto recodeDetail = recordService.getRecodeDetail(id, flowReplay.getFlowRecordId());
        ReplayDetailClickHouseDto replayDetailClickHouseDto = recodeDetailList.get(0);
        replayDetailClickHouseDto.setFeatureKeyMap(recodeDetail.getFeatureKeyMap());
        return replayDetailClickHouseDto;
    }

    @Override
    @SneakyThrows
    public String getReplayResultAnalysis(Long id) {
        try {
            log.info("getReplayResultAnalysis参数 id为:{}", id);
            FlowRecordStartEndPDate flowRecordStartEndPDate = flowReplayIdDataCache.get(id);
            log.info("getReplayResultAnalysis的flowRecordStartEndPDate为:{}", JsonMapperUtils.toJson(flowRecordStartEndPDate));
            String result = "<!DOCTYPE html>" + "<html lang=\"en\">" + "<head>" + "    <meta charset=\"UTF-8\">"
                    + "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">"
                    + "    <title>Font Size Adjustment</title>" + "    <style>" + "        th, td {"
                    + "            font-size: 16px; /* 设置表格字体大小 */"
                    + "            font-family: Arial, sans-serif; /* 设置表格字体 */" + "        }" + "        "
                    + "        pre {" + "            font-size: 16px; /* 设置与表格相同的字体大小 */"
                    + "            font-family: Arial, sans-serif; /* 设置与表格相同的字体 */"
                    + "            white-space: pre-wrap; /* 保留格式化但允许换行 */"
                    + "            margin: 0; /* 去除多余的间距 */" + "        }" + "    </style>" + "</head>" + "<body>";
            String analysisCountSql = ReplayConvert.getAnalysisCount(id, flowRecordStartEndPDate);
            Long total = ClickHouseConnector.executeSimpleSqlReturnLong(analysisCountSql);
            log.info("目前回放条数：{}", total);
            result += "<h3>目前回放条数:" + total + "</h3><hr>";

            String analysisDiffCountSql = ReplayConvert.getAnalysisDiffCount(id, flowRecordStartEndPDate);
            Long diffTotal = ClickHouseConnector.executeSimpleSqlReturnLong(analysisDiffCountSql);
            log.info("目前回放diff条数：{}", diffTotal);
            result += "<h3>目前回放diff条数:" + diffTotal + "</h3><hr>";

            /**
             * 策略命中数计算
             */
            String replayStrategyHitResultSql = ReplayConvert.analysisRecordStrategyHitDiff(id, flowRecordStartEndPDate);
            List<ReplayStrategyHitResultCkDto> recordStrategyHitResult =
                    ClickHouseConnector.executeSimpleSqlReturnClazz(replayStrategyHitResultSql,
                            ReplayStrategyHitResultCkDto.class);
            log.info("getReplayResultAnalysis.recordStrategyHitResult:{}", JsonMapperUtils.toJson(recordStrategyHitResult));
            result += "<h3>策略命中分布数:</h3>";
            List<Integer> collect = recordStrategyHitResult.stream()
                    .map(x -> Objects.isNull(x.getHitStrategyList()) ? 0 : x.getHitStrategyList().length())
                    .collect(Collectors.toList());
            Integer max = Collections.max(collect);
            log.info("RecordServiceImpl.getFlowRecordAnalysisResult.max:{}", max);
            result += "<table>" + "  <thead>" + "    <tr>" + "      <th>命中策略</th>"
                    + "      <th><pre>流量1命中量</pre></th> <th><pre>流量2命中量</pre></th>" + "    </tr>" + "  </thead>"
                    + "  <tbody>";
            for (ReplayStrategyHitResultCkDto ckDto : recordStrategyHitResult) {
                result += "<tr>";
                result += "<td>" + StringUtils.rightPad(ckDto.getHitStrategyList(), max) + "</td><td><pre>  "
                        + ckDto.getOneTotal() + "</pre></td>" + "<td>" + ckDto.getTwoTotal() + "</td>";
                result += "</tr>";
            }
            result += "</tbody>" + "</table><hr>";

            /**
             * 动作命中数计算
             */
            String replayActionHitResultSql = ReplayConvert.analysisRecordActionHitDiff(id, flowRecordStartEndPDate);
            List<ReplayActionHitResultCkDto> recordActionHitResult =
                    ClickHouseConnector.executeSimpleSqlReturnClazz(replayActionHitResultSql,
                            ReplayActionHitResultCkDto.class);
            log.info("getReplayResultAnalysis.recordActionHitResult:{}", JsonMapperUtils.toJson(recordActionHitResult));
            result += "<h3>动作命中分布数:</h3>";
            List<Integer> collect1 = recordActionHitResult.stream()
                    .map(x -> Objects.isNull(x.getHitActionList()) ? 0 : x.getHitActionList().length())
                    .collect(Collectors.toList());
            Integer max1 = Collections.max(collect1);
            log.info("RecordServiceImpl.getFlowRecordAnalysisResult.max1:{}", max1);
            result += "<table>" + "  <thead>" + "    <tr>" + "      <th>命中动作</th>"
                    + "      <th><pre>流量1命中量</pre></th> <th><pre>流量2命中量</pre></th>" + "    </tr>" + "  </thead>"
                    + "  <tbody>";
            for (ReplayActionHitResultCkDto ckDto : recordActionHitResult) {
                result += "<tr>";
                result += "<td>" + ckDto.getHitActionList() + "</td><td><pre>  " + ckDto.getOneTotal() + "</pre></td>"
                        + "<td><pre>  " + ckDto.getTwoTotal() + "</pre></td>";
                result += "</tr>";
            }
            result += "</tbody>" + "</table>";

            result += "</body>" + "</html>";
            return result;
        } catch (Exception e) {
            log.error("getReplayResultAnalysis.error", e);
            e.printStackTrace();
            return "无数据";
        }
    }

    @Override
    @SneakyThrows
    public void stopReplay(Long id) {
        strategyFlowReplayMapper.updateReplayStatusAndReplayEndTimeById(id, ReplayStatus.FINISHED.getCode(),
                System.currentTimeMillis());
    }

    @Override
    @SneakyThrows
    public void replayFinalMessage(Long id) {
        FlowRecordStartEndPDate flowRecordStartEndPDate = flowReplayIdDataCache.get(id);
        String diffCountSql = ReplayConvert.detailListRequest2CountDiffSql(id, flowRecordStartEndPDate);
        log.info("ReplayServiceImpl.replayFinalMessage.diffCountSql:{}", diffCountSql);
        Long diffTotal = ClickHouseConnector.executeSimpleSqlReturnLong(diffCountSql);
        log.info("ReplayServiceImpl.replayFinalMessage.diffTotal:{}", diffTotal);

        String countSql = ReplayConvert.detailListRequest2CountSql(id, flowRecordStartEndPDate);
        log.info("ReplayServiceImpl.replayFinalMessage.countSql:{}", countSql);
        Long total = ClickHouseConnector.executeSimpleSqlReturnLong(countSql);
        log.info("ReplayServiceImpl.replayFinalMessage.total:{}", total);


        StrategyFlowReplayDo replayDo = strategyFlowReplayMapper.selectById(id);
        DiffDetailResultDto diffDetailResultDto = new DiffDetailResultDto();
        if (StringUtils.isNotBlank(replayDo.getDiffDetailResult())) {
            diffDetailResultDto =
                    JsonMapperUtils.fromJson(replayDo.getDiffDetailResult(), DiffDetailResultDto.class);
        }

        if (total > NumberUtils.LONG_ZERO) {
            double result = diffTotal * 100.0 / total;
            DecimalFormat decimalFormat = new DecimalFormat("#.00");
            String formattedResult = decimalFormat.format(result);
            diffDetailResultDto.setDiffPercent(formattedResult + "%");
            strategyFlowReplayMapper.updateDiffResultById(id, 1, JsonMapperUtils.toJson(diffDetailResultDto));
        } else {
            diffDetailResultDto.setDiffPercent("0.00%");
            strategyFlowReplayMapper.updateDiffResultById(id, 0, JsonMapperUtils.toJson(diffDetailResultDto));
        }
    }

    private static final Long FLOW_MOCK_ID = 221L;
    private static final Integer SLEEP_TIME = 3000;

    @Override
    public String startReplayByPipeline(StartReplayByPipelineRequest request) {
        // 0. 前置参数校验、选择用哪个录制id
        // 1. 第一步创建回放任务
        log.info("startReplayByPipeline第一步创建回放任务:{}", JsonMapperUtils.toJson(request));
        // 2. 第二步启动回放任务
        ReplayServiceImpl replayService = new ReplayServiceImpl();
        FlowReplayAddRequest build = FlowReplayAddRequest.newBuilder().setName("流水线回放" + request.getKdevJobLogId())
                .setReplayConfigOne(com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayConfig.newBuilder()
                        .setReplayType(1).build()).setReplayConfigTwo(
                        com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayConfig.newBuilder()
                                .setReplayType(2).setLandId("PRT.test").build()).setReplayQps(10)
                .setOperator(request.getExecuteUser()).setFlowRecordId(FLOW_MOCK_ID) // 这边先写死一个
                .build();
        log.info("流水线创建任务参数: {}", JsonMapperUtils.toJson(build));
        replayService.flowReplayAdd(build);
        // 先等待一段时间，去查这个任务有没有新建成功，目前是30s
        try {
            log.info("开始等待30s");
            Thread.sleep(SLEEP_TIME);
        } catch (Exception e) {
            e.printStackTrace();
        }
        FlowReplayListRequestPojo pojo = new FlowReplayListRequestPojo();
        pojo.setName("流水线回放" + request.getKdevJobLogId());
        List<StrategyFlowReplayDo> strategyFlowReplayDos = strategyFlowReplayMapper.strategyFlowReplayList(pojo);
        log.info("流水线回放查到的数据size是{}", strategyFlowReplayDos.size());
        log.info("流水线回放查到的数据是:{}", JsonMapperUtils.toJson(strategyFlowReplayDos));

        return String.valueOf(strategyFlowReplayDos.get(0).getId());
    }

    @Override
    public CheckReplayStatusByPipelineResponse checkReplayStatusByPipeline(CheckReplayStatusByPipelineRequest request) {

        log.info("checkReplayStatusByPipeline:{}", JsonMapperUtils.toJson(request));
        String replayTaskId = request.getPipelineId();
        // 查询是否回放完成
        FlowReplayListRequestPojo pojo = new FlowReplayListRequestPojo();
        pojo.setId(Long.parseLong(replayTaskId));
        List<StrategyFlowReplayDo> strategyFlowReplayDos = strategyFlowReplayMapper.strategyFlowReplayList(pojo);
        if (strategyFlowReplayDos.get(0).getReplayStatus() != ReplayStatus.FINISHED.getCode()) {
            log.info("泳道流量未完成");
            // 没完成，先返回未完成的
            return CheckReplayStatusByPipelineResponse.newBuilder().setStatus(KdevResponseUtils.SUCCESS_STATUS)
                    .setMessage(KdevResponseUtils.SUCCESS_MESSAGE)
                    .setData(KdevResponseUtils.setCheckReplayStatusData("null", "null")).build();
        }

        // 已经完成的，就去获取结果

        ReplayService replayService = new ReplayServiceImpl();
        String replayResultAnalysis = replayService.getReplayResultAnalysis(Long.parseLong(replayTaskId));

        // 在这里做卡点，目前全部不卡点，只返回通过
        return CheckReplayStatusByPipelineResponse.newBuilder().setStatus(KdevResponseUtils.SUCCESS_STATUS)
                .setMessage(KdevResponseUtils.SUCCESS_MESSAGE)
                .setData(KdevResponseUtils.setCheckReplayStatusData("true", replayResultAnalysis)).build();
    }

    public void queryTask() {
        //        final PolicyTreeResponse[] policyTreeResponse = {null};
        //        Scope.runWithExistScope(new Scope(), () -> {
        //            TraceContext traceContext = TraceContext.newBuilder().setLaneId("PRT.test").build();
        //            TraceContextUtil.initTraceContext(traceContext);
        //            PolicyRequest request = PolicyRequest.newBuilder().setSceneKey("S_yiliao_query_sousuo")
        //                    .setParamJson("{\"queryWords\":\"李东辉测试使用\"}").build();
        //            policyTreeResponse[0] = kwaishopApolloPolicyService.policyTree(request);
        //        });
    }


    void flowReplayAddCheck(FlowReplayAddRequest request, StrategyFlowRecordDo flowRecord) {
        if (StringUtils.isBlank(request.getName())) {
            throw new RuntimeException("回放名称不允许为空");
        }
        if (request.getFlowRecordId() == NumberUtils.LONG_ZERO) {
            throw new RuntimeException("关联的流量录制id不允许为空");
        }
        if (StringUtils.isNotBlank(request.getWhiteFeatureKey()) && StringUtils.isNotBlank(
                request.getBlackFeatureKey())) {
            throw new RuntimeException("特征白名单和特征黑名单不允许同时存在");
        }
        if (request.getReplayQps() == NumberUtils.INTEGER_ZERO) {
            throw new RuntimeException("回放qps不允许为空");
        }
        if (Objects.isNull(flowRecord)) {
            throw new RuntimeException("流量回放记录不存在");
        }
    }

    private StrategyCheckResult getResult(ReplayConfig replayConfig, ReplayConfigDto replayConfigDto,
            RecodeDetailClickHouseDto dto) {
        StrategyCheckResult result = new StrategyCheckResult();
        String sceneKey = dto.getSceneKey();
        String featureKeyMap = dto.getFeatureKeyMap();
        String sceneType = "";
        if (StringUtils.isNotBlank(replayConfigDto.getSceneKey())) {
            sceneKey = replayConfigDto.getSceneKey();
        }
        if (CollectionUtils.isNotEmpty(replayConfigDto.getWhiteFeatureKey()) && !replayConfigDto.getWhiteFeatureKey()
                .get(0).isEmpty()) {
            Map<String, Object> featureMap =
                    JsonMapperUtils.ofJsonMap(featureKeyMap, HashMap.class, String.class, Object.class);
            featureMap.entrySet().removeIf(entry -> !replayConfigDto.getWhiteFeatureKey().contains(entry.getKey()));
            featureKeyMap = JsonMapperUtils.toJson(featureMap);
        }
        if (CollectionUtils.isNotEmpty(replayConfigDto.getBlackFeatureKey()) && !replayConfigDto.getBlackFeatureKey()
                .get(0).isEmpty()) {
            Map<String, Object> featureMap =
                    JsonMapperUtils.ofJsonMap(featureKeyMap, HashMap.class, String.class, Object.class);
            featureMap.entrySet().removeIf(entry -> replayConfigDto.getBlackFeatureKey().contains(entry.getKey()));
            featureKeyMap = JsonMapperUtils.toJson(featureMap);
        }

        if (LIST_SCENE_KEYS.contains(sceneKey)) {
            sceneType = "LIST";
        } else {
            sceneType = "TREE";
        }

        if (ReplayTypeEnum.RECORD_RESULT.getCode() == replayConfig.getReplayType()) {
            //来自录制结果
            result.setRequestHitResult(Boolean.valueOf(dto.getRequestHitResult()));
            result.setHitStrategyList(
                    JsonMapperUtils.ofJsonCollection(dto.getHitStrategyList(), List.class, String.class));
            result.setHitActionList(JsonMapperUtils.ofJsonCollection(dto.getHitActionList(), List.class, String.class));
        } else if (ReplayTypeEnum.GROUP.getCode() == replayConfig.getReplayType()) {
            //服务分组请求结果
            PolicyForTrafficReplayResponse response =
                    replayStrategyRpcHandler.policyByGroup(sceneKey, featureKeyMap, replayConfig.getGroup(), sceneType);
            log.info("ReplayServiceImpl.getGroupResult.response:{}", JsonMapperUtils.toJson(response));
            rescResult(response, result, sceneType);
        } else if (ReplayTypeEnum.LAND_ID.getCode() == replayConfig.getReplayType()) {
            //泳道请求结果
//            PolicyForTrafficReplayResponse response =
//                    replayStrategyRpcHandler.policyByLand(sceneKey, featureKeyMap, replayConfig.getLandId(), sceneType);
//            log.info("ReplayServiceImpl.getLandIdResult.response:{}", JsonMapperUtils.toJson(response));
//            rescResult(response, result, sceneType);
            // 线上没法用这种泳道请求结果，只能转成ip和port结果
            PolicyForTrafficReplayResponse response =
                    replayStrategyRpcHandler.policyByIpPort(sceneKey, featureKeyMap, replayConfig.getIpPortDtoList(),
                            sceneType);
            log.info("ReplayServiceImpl.getLandIdResult.response:{}", JsonMapperUtils.toJson(response));
            rescResult(response, result, sceneType);
        } else if (ReplayTypeEnum.IP_PORT.getCode() == replayConfig.getReplayType()) {
            //ip port请求
            PolicyForTrafficReplayResponse response =
                    replayStrategyRpcHandler.policyByIpPort(sceneKey, featureKeyMap, replayConfig.getIpPortDtoList(),
                            sceneType);
            log.info("ReplayServiceImpl.getIpPortResult.response:{}", JsonMapperUtils.toJson(response));
            rescResult(response, result, sceneType);
        }
        return result;
    }

    private boolean checkTwoResult(StrategyCheckResult result1, StrategyCheckResult result2) {
        if (twoListEqual(result1.getHitStrategyList(), result2.getHitStrategyList()) && twoListEqual(
                result1.getHitActionList(), result2.getHitActionList())) {
            return true;
        }
        return false;
    }

    private boolean twoListEqual(List<String> list1, List<String> list2) {
        if (CollectionUtils.isEmpty(list1) && CollectionUtils.isEmpty(list2)) {
            return true;
        }
        if (CollectionUtils.isEmpty(list1) || CollectionUtils.isEmpty(list2)) {
            return false;
        }
        if (list1.containsAll(list2) && list2.containsAll(list1)) {
            return true;
        }
        return false;
    }

    private void fillDiffResultDesc(List<ReplayDetailClickHouseDto> replayDetailList) {
        for (ReplayDetailClickHouseDto dto : replayDetailList) {
            if (Objects.equals(dto.getDiffResult(), NumberUtils.INTEGER_ZERO)) {
                dto.setDiffResultDesc("无");
            } else {
                dto.setDiffResultDesc("有");
            }
        }
    }

    private void rescResult(PolicyForTrafficReplayResponse response, StrategyCheckResult result, String sceneType) {
        if ("LIST".equalsIgnoreCase(sceneType)) {
            result.setHitStrategyList(List.of(String.valueOf(response.getListResponse().getStrategyId())));
            result.setHitActionList(List.of(String.valueOf(response.getListResponse().getPolicyCode())));
            result.setRequestHitResult(CollectionUtils.isNotEmpty(result.getHitActionList()));
        } else {
            result.setHitStrategyList(response.getTreeResponse().getStrategyIdList().stream().filter(Objects::nonNull)
                    .map(String::valueOf).collect(Collectors.toList()));
            result.setHitActionList(response.getTreeResponse().getActionsList().stream().map(ActionDto::getActionId)
                    .collect(Collectors.toList()));
            result.setRequestHitResult(CollectionUtils.isNotEmpty(result.getHitActionList()));
        }
    }

    @SneakyThrows
    @Override
    public String getRealTimeCache(String sceneKey) {
        return JsonMapperUtils.toJson(sceneKeyFlowReplayDoCache.get(sceneKey));
    }
}
