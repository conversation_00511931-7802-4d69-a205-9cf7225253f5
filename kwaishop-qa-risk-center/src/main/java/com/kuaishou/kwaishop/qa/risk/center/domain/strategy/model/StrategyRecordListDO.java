package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.STRATEGY_RECORD_LIST_TABLE_NAME;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@SuperBuilder
@AllArgsConstructor
@TableName(STRATEGY_RECORD_LIST_TABLE_NAME)
public class StrategyRecordListDO {

    @TableId
    private Long id;
    private String sceneName;
    private String recordPartition;
    private Integer status;
    private Integer deleted;
    private Long createTime;
    private Long updateTime;
    private Long taskId;
    private String creator;
    private String recordSql;
    private Long version;
    private String taskName;
    private String taskDescription;
    private String expectLaneId;

}