package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.FaultAlarmRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultAlarmRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.fault.FaultAlarmRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.fault.FaultAlarmRecordQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultAlarmRecordBO;

import lombok.extern.slf4j.Slf4j;

@Repository
@Slf4j
public class FaultAlarmRecordDAOImpl extends BaseDAOImpl<FaultAlarmRecordDO, FaultAlarmRecordQueryCondition> implements FaultAlarmRecordDAO {


    @Autowired
    private FaultAlarmRecordMapper faultAlarmRecordMapper;


    @Override
    protected BaseMapper<FaultAlarmRecordDO> getMapper() {
        return faultAlarmRecordMapper;
    }

    @Override
    protected void fillQueryCondition(FaultAlarmRecordQueryCondition condition, QueryWrapper<FaultAlarmRecordDO> queryWrapper) {
        if (condition.getRuleId() != null && condition.getRuleId() > 0) {
            queryWrapper.and(q -> q.eq("rule_id", condition.getRuleId()));
        }
        if (condition.getRuleName() != null && StringUtils.isNotBlank(condition.getRuleName())) {
            queryWrapper.and(q -> q.eq("rule_name", condition.getRuleName()));
        }
        if (condition.getUic() != null && StringUtils.isNotBlank(condition.getUic())) {
            queryWrapper.and(q -> q.eq("uic", condition.getUic()));
        }
    }

    @Override
    public long insert(FaultAlarmRecordDO recordDO) {
        try {
            return saveOrUpdate(recordDO);
        } catch (Exception ex) {
            return 0;
        }
    }


    @Override
    public List<FaultAlarmRecordDO> queryRecordList(FaultAlarmRecordBO recordBO) {
        FaultAlarmRecordQueryCondition condition = FaultAlarmRecordQueryCondition.builder()
                .ruleId(recordBO.getRuleId())
                .uic(recordBO.getUic())
                .ruleName(recordBO.getRuleName())
                .build();
        return queryList(condition);
    }


}
