package com.kuaishou.kwaishop.qa.risk.center.db.query.risk;

import java.util.Collection;

import com.kuaishou.kwaishop.qa.risk.center.db.query.detail.DetailBaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-16
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RiskDetailQueryCondition extends DetailBaseQueryCondition {

    /**
     * 风险明细名
     */
    private String name;

    /**
     * 风险类型
     */
    private Integer riskType;

    /**
     * 校对类型
     */
    private Integer verifyType;

    /**
     * 校对有效性
     */
    private Integer verifyEffective;

    /**
     * 预案有效性
     */
    private Integer planEffective;

    /**
     * 风险等级
     */
    private Integer level;

    /**
     * 创建人
     */
    private Collection<String> creators;

    /**
     * 业务域
     */
    private Long businessId;

    /**
     * db操作类型
     */
    private Integer dbType;

    /**
     * 字段是否有对账覆盖
     */
    private Integer tableColumnCover;

    /**
     * 对账是否覆盖
     */
    private Integer riskCover;

    /**
     * 预案是否覆盖
     */
    private Integer planCover;
}
