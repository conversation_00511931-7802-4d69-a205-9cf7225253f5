package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.AccountTagManageBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.service.impl.AccountTagAddApprovalServiceImpl;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.AddTagMetaInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.AddTagMetaInfoResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.AddTagsToTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.AddTagsToTestAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.CreateTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.CreateTestAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.DisableAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.DisableAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.DisableTagRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.DisableTagResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.GetAccountTagsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.GetAccountTagsResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.GetTagMetaInfosRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.GetTagMetaInfosResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.KrpcTestAccountAccountTagManageServiceGrpc.TestAccountAccountTagManageServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.PageTestAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.PageTestUserRentAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryMyRentTestAccountsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryMyRentTestAccountsResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryMyTestAccountsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryMyTestAccountsResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryTestAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryTestAccountsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryTestAccountsResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.RemoveTagsToSubjectRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.RemoveTagsToTestAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.RentTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.RentTestAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.ReturnTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.ReturnTestAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.TestBPMRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.TestBPMResponse;

import lombok.extern.slf4j.Slf4j;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/7 16:28
 * @注释
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class AccountTagManageServiceImpl extends TestAccountAccountTagManageServiceImplBaseV2 {

    @Autowired
    private AccountTagManageBizService accountTagManageBizService;

    @Autowired
    private AccountTagAddApprovalServiceImpl accountTagAddApprovalService;

    /**
     * 查询标签原信息
     */
    @Override
    public GetTagMetaInfosResponse getTagMetaInfos(GetTagMetaInfosRequest request) {
        log.info("[AccountTagManageServiceImpl] getTagMetaInfos request: {}", protoToJsonString(request));
        try {
            return GetTagMetaInfosResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllTags(accountTagManageBizService.getTagMetaInfos(request))
                    .build();
        } catch (BizException e) {
            log.error("[AccountTagManageServiceImpl] getTagMetaInfos bizError, req: {}, exception: ",
                    protoToJsonString(request), e);
            return GetTagMetaInfosResponse.newBuilder()
                    .setResult(e.getCode())
                    .build();
        }
    }

    /**
     * 添加标签原信息
     */
    @Override
    public AddTagMetaInfoResponse addTagMetaInfo(AddTagMetaInfoRequest request) {
        log.info("[AccountTagManageServiceImpl] addTagMetaInfo request: {}", protoToJsonString(request));
        try {
            accountTagManageBizService.addTagMetaInfo(request);
            return AddTagMetaInfoResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setDesc("成功添加标签")
                    .build();
        } catch (BizException e) {
            log.error("[AccountTagManageServiceImpl] addTagMetaInfo bizError, req: {}, exception: ",
                    protoToJsonString(request), e);
            return AddTagMetaInfoResponse.newBuilder()
                    .setResult(e.getCode())
                    .build();
        }
    }

    /**
     * 给主体打标(支持打多个)
     */
    @Override
    public AddTagsToTestAccountResponse addTagsToTestAccount(AddTagsToTestAccountRequest request) {
        log.info("[AccountTagManageServiceImpl] addTagsToTestAccount request: {}", protoToJsonString(request));
        try {
            accountTagManageBizService.addTagsToTestAccount(request);
            return AddTagsToTestAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setDesc("成功添加标签到指定账号")
                    .build();
        } catch (BizException e) {
            log.error("[AccountTagManageServiceImpl] addTagsToTestAccount bizError, req: {}, exception: ",
                    protoToJsonString(request), e);
            return AddTagsToTestAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .build();
        }
    }

    /**
     * 给主体下标
     */
    @Override
    public RemoveTagsToTestAccountResponse removeTagsToTestAccount(RemoveTagsToSubjectRequest request) {
        log.info("[AccountTagManageServiceImpl] removeTagsToTestAccount request: {}", protoToJsonString(request));
        try {
            accountTagManageBizService.removeTagsFromTestAccount(request);
            return RemoveTagsToTestAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setDesc("成功从账号移除标签")
                    .build();
        } catch (BizException e) {
            log.error("[AccountTagManageServiceImpl] removeTagsToTestAccount bizError, req: {}, exception: ",
                    protoToJsonString(request), e);
            return RemoveTagsToTestAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .build();
        }
    }

    /**
     * 创建测试账号
     */
    @Override
    public CreateTestAccountResponse createTestAccount(CreateTestAccountRequest request) {
        log.info("[AccountTagManageServiceImpl] createTestAccount request: {}", protoToJsonString(request));
        try {
            accountTagManageBizService.createTestAccount(request);
            return CreateTestAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[AccountTagManageServiceImpl] createTestAccount bizError, req: {}, exception: ",
                    protoToJsonString(request), e);
            return CreateTestAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        }
    }

    /**
     * 租借测试账号
     */
    @Override
    public RentTestAccountResponse rentTestAccount(RentTestAccountRequest request) {
        log.info("[AccountTagManageServiceImpl] rentTestAccount request: {}", protoToJsonString(request));
        try {
            accountTagManageBizService.rentTestAccount(request);
            return RentTestAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[AccountTagManageServiceImpl] rentTestAccount bizError, req: {}, exception: ",
                    protoToJsonString(request), e);
            return RentTestAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        }
    }


    /**
     * 查询所有测试账号列表
     */
    @Override
    public QueryTestAccountsResponse queryTestAccounts(QueryTestAccountsRequest request) {
        log.info("[AccountTagManageServiceImpl] queryTestAccounts request: {}", protoToJsonString(request));
        try {
            PageTestAccountDTO pageTestUserAccountDTO =
                    accountTagManageBizService.queryTestAccountsWithTags(request);
            return QueryTestAccountsResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(pageTestUserAccountDTO)
                    .build();
        } catch (BizException e) {
            log.error("[AccountTagManageServiceImpl] queryTestAccounts bizError, req: {}, exception: ",
                    protoToJsonString(request), e);
            return QueryTestAccountsResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        }
    }

    /**
     * 查询测试账号明细（弃用）
     */
    @Override
    public QueryTestAccountResponse queryTestAccount(QueryTestAccountRequest request) {
        log.info("[AccountTagManageServiceImpl] queryTestAccount request: {}", protoToJsonString(request));
        try {
            return QueryTestAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(accountTagManageBizService.queryTestAccount(request))
                    .build();
        } catch (BizException e) {
            log.error("[AccountTagManageServiceImpl] queryTestAccount bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryTestAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountTagManageServiceImpl] queryTestAccount error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryTestAccountResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    /**
     * 查询我的测试账号
     */
    @Override
    public QueryMyTestAccountsResponse queryMyTestAccounts(QueryMyTestAccountsRequest request) {
        log.info("[AccountTagManageServiceImpl] queryMyTestAccounts request: {}", protoToJsonString(request));
        try {
            PageTestAccountDTO pageTestAccountDTO =
                    accountTagManageBizService.queryMyTestAccounts(request);
            return QueryMyTestAccountsResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(pageTestAccountDTO)
                    .build();
        } catch (BizException e) {
            log.error("[AccountTagManageServiceImpl] queryMyTestAccounts bizError, req: {}, exception: ",
                    protoToJsonString(request), e);
            return QueryMyTestAccountsResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        }
    }

    /**
     * 查询我租借的所有账号
     */
    @Override
    public QueryMyRentTestAccountsResponse queryMyRentTestAccounts(QueryMyRentTestAccountsRequest request) {
        log.info("[AccountTagManageServiceImpl] queryMyRentTestAccounts request: {}", protoToJsonString(request));
        try {
            PageTestUserRentAccountDTO pageTestUserRentAccountDTO =
                    accountTagManageBizService.queryMyRentTestAccounts(request);
            return QueryMyRentTestAccountsResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(pageTestUserRentAccountDTO)
                    .build();
        } catch (BizException e) {
            log.error("[AccountTagManageServiceImpl] queryMyRentTestAccounts bizError, req: {}, exception: ",
                    protoToJsonString(request), e);
            return QueryMyRentTestAccountsResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        }
    }

    /**
     * 注销账号
     */
    @Override
    public DisableAccountResponse disableAccount(DisableAccountRequest request) {
        log.info("[AccountTagManageServiceImpl] queryMyRentTestAccounts request: {}", protoToJsonString(request));
        try {
            accountTagManageBizService.disableAccount(request);
            return DisableAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[AccountTagManageServiceImpl] queryMyRentTestAccounts bizError, req: {}, exception: ",
                    protoToJsonString(request), e);
            return DisableAccountResponse.newBuilder()
                    .setErrorMsg(e.getMessage())
                    .build();
        }
    }

    @Override
    public DisableTagResponse disableTag(DisableTagRequest request) {
        log.info("[AccountTagManageServiceImpl] disableTag request: {}", protoToJsonString(request));
        try {
            accountTagManageBizService.deleteTag(request);
            return DisableTagResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[AccountTagManageServiceImpl] queryMyRentTestAccounts bizError, req: {}, exception: ",
                    protoToJsonString(request), e);
            return DisableTagResponse.newBuilder()
                    .setErrorMsg(e.getMessage())
                    .build();
        }
    }

    /**
     * 获取账号tag
     */
    @Override
    public GetAccountTagsResponse getAccountTags(GetAccountTagsRequest request) {
        return null;
    }

    @Override
    public ReturnTestAccountResponse returnTestAccount(ReturnTestAccountRequest request) {
        log.info("[AccountTagManageServiceImpl] ReturnTestAccount request: {}", protoToJsonString(request));
        try {
            accountTagManageBizService.returnTestAccount(request);
            return ReturnTestAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[AccountTagManageServiceImpl] ReturnTestAccount bizError, req: {}, exception: ",
                    protoToJsonString(request), e);
            return ReturnTestAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        }
    }

    public TestBPMResponse testBPM(TestBPMRequest request) {
        log.info("[AccountTagManageServiceImpl] testBPM request: {}", protoToJsonString(request));
        try {
            String data = request.getData();
            Map<String, Object> variables = ObjectMapperUtils.fromJson(data);
            CreateTestAccountRequest createTestAccountRequest = CreateTestAccountRequest.newBuilder()
                    .setOperator(variables.get("userName").toString())
                    .setCenterId(0)
                    .setTeamId(0)
                    .setBid(0)
                    .setAccount(variables.get("account").toString())
                    .setPassword(variables.get("password").toString())
                    .setLoginType(Integer.parseInt(variables.get("loginType").toString()))
                    .setUserId(Long.parseLong(variables.get("userId").toString()))
                    .build();

            accountTagManageBizService.createTestAccount(createTestAccountRequest);

            return TestBPMResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[AccountTagManageServiceImpl] ReturnTestAccount bizError, req: {}, exception: ",
                    protoToJsonString(request), e);
            return TestBPMResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        }
    }
}
