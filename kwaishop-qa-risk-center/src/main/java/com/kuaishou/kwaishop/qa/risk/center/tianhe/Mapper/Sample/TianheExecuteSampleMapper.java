package com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.Sample;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Sample.TianheExecuteSampleDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-27
 */
@Mapper
@DataSourceRouting("kwaishopQaRiskCenter")
public interface TianheExecuteSampleMapper extends BaseMapper<TianheExecuteSampleDO> {
    @Update("UPDATE tianhe_risk_sample_excute_record "
            + "SET status = #{status}, execute_status = #{executeStatus},"
            + "ext = JSON_SET(ext, '$.diff_rate', #{diffRate})"
            + "WHERE execute_id = #{executeId} AND sample_id = #{sampleId} AND action_type = #{actionType} AND xpath = #{xpath}")
    int updateStatus(@Param("executeId") String executeId, @Param("sampleId") Long sampleId, @Param("actionType") Integer actionType,
                     @Param("xpath") String xpath, @Param("status") Integer status, @Param("diffRate") String diffRate,
                     @Param("executeStatus") Integer executeStatus);

}
