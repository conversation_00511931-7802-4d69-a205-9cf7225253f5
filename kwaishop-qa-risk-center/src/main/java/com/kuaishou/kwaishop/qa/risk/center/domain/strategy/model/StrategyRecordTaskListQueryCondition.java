package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class StrategyRecordTaskListQueryCondition extends BaseQueryCondition {
    private String creator;

    private Integer status;
}