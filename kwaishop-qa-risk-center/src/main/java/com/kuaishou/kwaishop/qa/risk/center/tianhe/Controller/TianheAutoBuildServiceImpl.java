package com.kuaishou.kwaishop.qa.risk.center.tianhe.Controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AutoExcuteSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AutoExcuteSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AutoExtractSampleFromImageRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AutoExtractSampleFromImageResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AutoExtractSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AutoExtractSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.JudgeImgRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.JudgeImgResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.KrpcTianheAutoBuildServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Service.TianheAutoBuildService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@KrpcService(server = "custom-servers-config", serviceName = "kwaishop-qa-risk-center")
public class TianheAutoBuildServiceImpl extends KrpcTianheAutoBuildServiceGrpc.TianheAutoBuildServiceImplBaseV2 {

    @Autowired
    private TianheAutoBuildService tianheAutoBuildService;

    @Override
    public AutoExtractSampleResponse test(AutoExtractSampleRequest request) {
        return tianheAutoBuildService.testSample(request);
    }
    @Override
    public AutoExtractSampleFromImageResponse autoExtractSampleFromImage(AutoExtractSampleFromImageRequest request) {
        return tianheAutoBuildService.autoExtractSampleFromImage(request);
    }
    @Override
    public AutoExtractSampleResponse autoExtractSample(AutoExtractSampleRequest request) {
        return tianheAutoBuildService.autoExtractSample(request);
    }

    @Override
    public AutoExcuteSampleResponse autoExcuteSampleById(AutoExcuteSampleRequest request) {
        return tianheAutoBuildService.autoExcuteSampleById(request);
    }

    @Override
    public AutoExtractSampleResponse updateAISample(AutoExcuteSampleRequest request) {
        return tianheAutoBuildService.updateAISample(request);
    }

    @Override
    public JudgeImgResponse judgeImgResult(JudgeImgRequest request) {
        return tianheAutoBuildService.judgeImgResult(request);
    };
}
