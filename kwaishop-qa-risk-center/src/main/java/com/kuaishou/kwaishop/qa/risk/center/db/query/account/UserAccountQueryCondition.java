package com.kuaishou.kwaishop.qa.risk.center.db.query.account;

import java.util.Collection;

import com.kuaishou.kwaishop.qa.risk.center.db.query.detail.DetailBaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-28
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserAccountQueryCondition extends DetailBaseQueryCondition {

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户bid
     */
    private Long bId;

    /**
     * 登录类型，1- 86手机号登录，2- 1264手机号登录，3- 邮箱登录
     */
    private Integer loginType;

    /**
     * 账号类型，1-平台导入账号，2-个人导入账号
     */
    private Integer accountType;

    /**
     * 数据类型，1-全数据类型，2-单uid数据类型
     */
    private Integer dataType;

    /**
     * 账号状态，1-可借用，2-不可借用
     */
    private Integer status;

    private Collection<Long> userIds;

    private Collection<Integer> loginTypes;

    private Collection<Integer> accountTypes;

    private Collection<Integer> dataTypes;

    private Collection<Integer> statuses;

    private Integer merchantPermission;

    private Integer distributorPermission;

    private Integer celebrityPermission; //达人分下权限

    private Integer recruitingLeader;

    private String account; // 手机号
}
