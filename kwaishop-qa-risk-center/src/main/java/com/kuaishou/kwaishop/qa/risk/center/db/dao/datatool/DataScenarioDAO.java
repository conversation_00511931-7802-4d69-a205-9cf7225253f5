package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataScenarioDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.DataScenarioBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2023-03-15
 */
public interface DataScenarioDAO {

    long insertDataScenario(DataScenarioDO dataScenarioDO);

    long updateDataScenario(DataScenarioDO dataScenarioDO);

    List<Long> queryAutoScenarioIdList();

    int logicDeleted(Long id, String operator);

    /**
     * 根据name模糊查询
     * @param name
     * @return
     */
    List<DataScenarioDO> queryDataScenarioLikeName(String name);
    PageBO<DataScenarioDO> queryPageDataScenarioList(DataScenarioBO dataScenarioBO);

    List<DataScenarioDO> queryDataScenarioList(DataScenarioDO dataScenarioDO);

    DataScenarioDO queryDataScenarioById(Long id);
}
