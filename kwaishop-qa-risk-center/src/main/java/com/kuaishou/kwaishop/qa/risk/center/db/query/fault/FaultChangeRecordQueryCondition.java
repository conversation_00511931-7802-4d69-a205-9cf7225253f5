package com.kuaishou.kwaishop.qa.risk.center.db.query.fault;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FaultChangeRecordQueryCondition extends BaseQueryCondition {

    /**
     * 场景id
     */
    private Long scenarioId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 演练记录id
     */
    private Long faultRecordId;

    /**
     * 分支
     */
    private String branch;

}
