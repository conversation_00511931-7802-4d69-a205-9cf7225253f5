package com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class RiskInfoCombineBO extends CombineBO {

    /**
     * 监控有效总数
     */
    private Long verifyEffectiveCount;

    /**
     * 预案有效总数
     */
    private Long planEffectiveCount;

    /**
     * 高风险总数
     */
    private Long highLevelCount;

    /**
     * 中风险总数
     */
    private Long mediumLevelCount;

    /**
     * 低分险总数
     */
    private Long lowLevelCount;

    /**
     * 监控覆盖数
     */
    private Long riskCoverCount;

    /**
     * 监控覆盖率
     */
    private Long riskCoverRate;

    /**
     * 监控有效率
     */
    private Long verifyEffectiveRate;

    /**
     * 预案有效率
     */
    private Long planEffectiveRate;

    /**
     * 高风险占比
     */
    private Long highLevelRate;

    /**
     * 中风险占比
     */
    private Long mediumLevelRate;

    /**
     * 低风险占比
     */
    private Long lowLevelRate;

    /**
     * 新风险增数
     */
    private Long newRiskCount;

    /**
     * 预案覆盖数
     */
    private Long planCoverCount;

    /**
     * 预案覆盖率
     */
    private Long planCoverRate;

    /**
     * 资金敞口总数
     */
    private Long fundExpTotal;

}
