package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service;

import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.DiffResult;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.PolicyTestResponse;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTestDataPool;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-29
 */
public interface DiffService {
    DiffResult calculateDiff(StrategyTestDataPool strategyTestDataPool, PolicyTestResponse policyResponse);

    void calculateDiffToDataPool(StrategyTestDataPool strategyTestDataPool, PolicyTestResponse policyTestResponse, String laneId);

    void changeDiffConclusion(ReplayRequest request);
}
