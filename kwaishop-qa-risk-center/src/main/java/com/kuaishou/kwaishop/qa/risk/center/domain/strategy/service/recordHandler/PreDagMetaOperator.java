package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.recordHandler;

import com.kuaishou.protobuf.dp.scheduler.PreDagMetaV2;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-30
 */
public class PreDagMetaOperator {

    public static final int GROUP_ID = 1563;

    public static final String PRINCIPAL_NAME = "THEMIS_STRATEGY";

    public static final String PRINCIPAL = "themis_strategy/<EMAIL>";

    public static final String OWNER = "lidonghui03";

    public static final String DAG_BIZ_TAG = "ThemisStrategy";

    public static final String SCHEDULE_INTERVAL = "0 0 * * *";

    /**
     * 填充上DAG基本属性
     *
     * @param nonPeriodic true: 非例行dag, false：例行dag
     *                    非例行dag的定义：
     *                    不会进行系统自动定时调度的任务，仅可使用多次补数据触发回溯，且具有以下特征：
     *                    1. 不会被任务依赖，但允许依赖其它任务
     *                    2. 不会未就绪报警，不进入基线报警（优先级使用单独优先级）
     *                    3. 不会进入DAG_RUN血缘，不可级联补数
     *                    4. 不可变更为例行任务
     *                    非例行dag的适合场景：
     *                    1. 只补数不例行
     *                    2. 一次性调度
     * @return
     */
    public static PreDagMetaV2 getBasicPreDagMetaV2(String taskName) {
        int priority = 1; // 优先级 范围 0-2
        int dagConcurrency = 3;
        boolean dependsOnPast = false; // 是否依赖上一周期的执行结果，上一周期执行成功本周期才能执行
        return PreDagMetaV2.newBuilder()
                .setOwner(OWNER)
                .setGroupId(GROUP_ID)
                .setPriority(priority)
                .setDependsOnPast(dependsOnPast)
                .setScheduleInterval(SCHEDULE_INTERVAL)
                .setDagConcurrency(dagConcurrency)
                .setBusinessTag(DAG_BIZ_TAG)
                .setNonPeriodic(true)
                .setSource(PRINCIPAL_NAME)
                .build().toBuilder().setDagShowName(taskName).build();
    }
}
