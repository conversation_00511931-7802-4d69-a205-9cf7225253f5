package com.kuaishou.kwaishop.qa.risk.center.db.query.fault;

import java.util.Collection;

import com.kuaishou.kwaishop.qa.risk.center.db.query.detail.DetailBaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-15
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FaultPlanRecordQueryCondition extends DetailBaseQueryCondition {

    private Long planId;

    private Long caseId;

    /**
     * 预期类型
     */
    private Integer conformType;

    /**
     * 问题类型
     */
    private Integer problemType;

    /**
     * 记录状态
     */
    private Integer status;

    private Boolean queryReport;


    private Long updateTimeGe;

    private Long updateTimeLe;

    private Collection<Integer> statusList;

    /**
     * 场景id
     */
    private Long scenarioId;
}
