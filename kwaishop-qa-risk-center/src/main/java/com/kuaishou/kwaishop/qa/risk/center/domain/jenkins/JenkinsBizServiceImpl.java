package com.kuaishou.kwaishop.qa.risk.center.domain.jenkins;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.offbytwo.jenkins.JenkinsServer;
import com.offbytwo.jenkins.model.BuildResult;

import lombok.extern.slf4j.Slf4j;



/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-05
 */
@Service
@Lazy
@Slf4j
public class JenkinsBizServiceImpl implements JenkinsBizService {

    public void exec(String parm) {
        BuildResult rt = null;
        Map<String, String> parameters = new HashMap<>();
        parameters.put("case_path", "test_case/jinjian_zzh");
        parameters.put("robotkey", "630ed201-1e98-4704-893f-5052ebbc35ac-pay");
        parameters.put("wait_time", "30");

        try {
            JenkinsServer  jenkinsServer = new JenkinsServer(new URI("https://ui-test.staging.kuaishou.com"), "chenneng", "cntest12345");

            if (jenkinsServer.isRunning()) {
                jenkinsServer.getJob(parm).build(parameters);
                rt = jenkinsServer.getJob(parm).getLastBuild().details().getResult();
            }

        } catch (Exception e) {
            log.error(e.getMessage());
        }
        log.info("++++++++++++++", rt);

    }
}
