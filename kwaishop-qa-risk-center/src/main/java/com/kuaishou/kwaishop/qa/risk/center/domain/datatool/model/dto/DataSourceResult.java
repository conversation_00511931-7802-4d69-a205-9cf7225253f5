package com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.dto;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-16
 */

@Data
@Builder
public class DataSourceResult {

    private Long dataSourceId;
    private Integer sourceType;//数据源类型
    private Long sourceQueryId; //数据源查询执行id
    private String sourceResult; //查询结果
    //private Long dataCnt; //数据源查询结果条数
}
