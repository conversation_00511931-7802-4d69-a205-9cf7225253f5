package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import java.sql.ResultSet;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.kconf.common.json.JsonMapperUtils;
import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.config.kwaisql.DpAccessFetch;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert.ResultSetMapperUtil;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.ClickHouseConnector;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordStartEndPDate;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.IpPortDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.PageResult;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecodeDetailClickHouseDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.ReplayDetailClickHouseDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowReplayBo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.PipeLineService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.PolicyRpcService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.ReplayService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.util.CommonRespUtils;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.util.KdevResponseUtils;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.CheckReplayStatusByPipelineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.CheckReplayStatusByPipelineResponseFixed;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ClickHouseQueryTestRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ClickHouseQueryTestResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowReplayAddRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowReplayAddResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowReplayDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowReplayDetailResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowReplayListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowReplayListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetDateFromCacheRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetDateFromCacheResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetFlowReplayAnalysisResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetFlowReplayAnalysisResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetFlowReplayDetailListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetFlowReplayDetailListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetFlowReplayDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetFlowReplayDetailResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.KrpcStrategyFlowReplayServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StartReplayByPipelineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StartReplayByPipelineResponseFixed;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StopReplayRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StopReplayResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestGroupRpcRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestGroupRpcResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestIpHostListRpcRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestIpHostRpcRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestIpHostRpcResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestLandIdRpcRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestLandIdRpcResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestQpsFlowRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestQpsFlowResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-17
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class StrategyFlowReplayServiceImpl
        extends KrpcStrategyFlowReplayServiceGrpc.StrategyFlowReplayServiceImplBaseV2 {

    @Autowired
    private PolicyRpcService policyRpcService;

    @Autowired
    private DpAccessFetch dpAccessFetch;

    @Autowired
    private ReplayService replayService;

    @Autowired
    private PipeLineService pipeLineService;

    @Override
    public FlowReplayAddResponse flowReplayAdd(FlowReplayAddRequest request) {
        log.info("StrategyFlowReplayServiceImpl.flowReplayAdd.request:{}", JsonMapperUtils.toJson(request));
        try {
            replayService.flowReplayAdd(request);
            return FlowReplayAddResponse.newBuilder().setResp(CommonRespUtils.successResp()).build();
        } catch (Exception e) {
            log.info("StrategyFlowReplayServiceImpl.flowReplayAdd.error.request:{}", JsonMapperUtils.toJson(request),
                    e);
            return FlowReplayAddResponse.newBuilder().setResp(CommonRespUtils.errorResp(e)).build();
        }
    }

    @Override
    public FlowReplayListResponse flowReplayList(FlowReplayListRequest request) {
        log.info("StrategyFlowReplayServiceImpl.flowReplayList.request:{}", JsonMapperUtils.toJson(request));
        try {
            PageResult<StrategyFlowReplayBo> result = replayService.flowReplayList(request);
            return FlowReplayListResponse.newBuilder().setResp(CommonRespUtils.successResp())
                    .setData(JsonMapperUtils.toJson(result)).build();
        } catch (Exception e) {
            log.info("StrategyFlowReplayServiceImpl.flowReplayList.error.request:{}", JsonMapperUtils.toJson(request),
                    e);
            return FlowReplayListResponse.newBuilder().setResp(CommonRespUtils.errorResp(e)).build();
        }
    }

    @Override
    public FlowReplayDetailResponse flowReplayDetail(FlowReplayDetailRequest request) {
        log.info("StrategyFlowReplayServiceImpl.flowReplayDetail.request:{}", JsonMapperUtils.toJson(request));
        try {
            StrategyFlowReplayBo replayBo = replayService.flowReplayDetail(request.getId());
            return FlowReplayDetailResponse.newBuilder().setResp(CommonRespUtils.successResp())
                    .setData(JsonMapperUtils.toJson(replayBo)).build();
        } catch (Exception e) {
            log.error("StrategyFlowReplayServiceImpl.flowReplayDetail.error.request:{}",
                    JsonMapperUtils.toJson(request), e);
            return FlowReplayDetailResponse.newBuilder().setResp(CommonRespUtils.errorResp(e)).build();
        }
    }

    @Override
    public TestQpsFlowResponse testQpsFlow(TestQpsFlowRequest request) {
        log.info("StrategyFlowReplayServiceImpl.testQpsFlow.request:{}", JsonMapperUtils.toJson(request));
        try {
            replayService.testQpsFlow(request.getQps(), request.getTime());
            return TestQpsFlowResponse.newBuilder().setResp(CommonRespUtils.successResp()).build();
        } catch (Exception e) {
            log.error("StrategyFlowReplayServiceImpl.testQpsFlow.error.request:{}", JsonMapperUtils.toJson(request), e);
            return TestQpsFlowResponse.newBuilder().setResp(CommonRespUtils.errorResp(e)).build();
        }
    }

    @Override
    public TestLandIdRpcResponse testLandIdRpc(TestLandIdRpcRequest request) {
        //        log.info("StrategyFlowReplayServiceImpl.testLandIdRpc.request:{}", JsonMapperUtils.toJson(request));
        //        try {
        ////            String result = policyRpcService.policyTreeLandId(request.getLandId());
        //            return TestLandIdRpcResponse.newBuilder()
        //                    .setResp(CommonRespUtils.successResp())
        //                    .build();
        //        } catch (Exception e) {
        //            log.error("StrategyFlowReplayServiceImpl.testLandIdRpc.error.request:{}", JsonMapperUtils
        //            .toJson(request), e);
        //            return TestLandIdRpcResponse.newBuilder()
        //                    .setResp(CommonRespUtils.errorResp(e))
        //                    .build();
        //        }
        return null;
    }

    @Override
    public TestIpHostRpcResponse testIpHostRpc(TestIpHostRpcRequest request) {
        log.info("StrategyFlowReplayServiceImpl.testIpHostRpc.request:{}", JsonMapperUtils.toJson(request));
        try {
            String result =
                    policyRpcService.policyTrafficByIpPort(request.getIp(), request.getPort(), request.getSceneKey(),
                            request.getParam());
            return TestIpHostRpcResponse.newBuilder().setResp(CommonRespUtils.successResp()).setData(result).build();
        } catch (Exception e) {
            log.error("StrategyFlowReplayServiceImpl.testIpHostRpc.error.request:{}", JsonMapperUtils.toJson(request),
                    e);
            return TestIpHostRpcResponse.newBuilder().setResp(CommonRespUtils.errorResp(e)).build();
        }
    }

    @Override
    public TestIpHostRpcResponse testIpHostListRpc(TestIpHostListRpcRequest request) {
        log.info("StrategyFlowReplayServiceImpl.testIpHostRpc.request:{}", JsonMapperUtils.toJson(request));
        try {
            List<IpPortDto> collect = request.getPodInfoListList().stream().map(podInfo -> {
                IpPortDto dto = new IpPortDto();
                dto.setIp(podInfo.getIp());
                dto.setPort(Integer.parseInt(podInfo.getPort()));
                return dto;
            }).collect(Collectors.toList());

            String result =
                    policyRpcService.policyTrafficByIpPort(collect, request.getSceneKey(), request.getFeatureMap());
            return TestIpHostRpcResponse.newBuilder().setResp(CommonRespUtils.successResp()).setData(result).build();
        } catch (Exception e) {
            log.error("StrategyFlowReplayServiceImpl.testIpHostRpc.error.request:{}", JsonMapperUtils.toJson(request),
                    e);
            return TestIpHostRpcResponse.newBuilder().setResp(CommonRespUtils.errorResp(e)).build();
        }
    }

    @Override
    public TestGroupRpcResponse testGroupRpc(TestGroupRpcRequest request) {
        //        log.info("StrategyFlowReplayServiceImpl.testGroupRpc.request:{}", JsonMapperUtils.toJson(request));
        //        try {
        //            String result = policyRpcService.policyTreeGroup(request.getGroup());
        //            return TestGroupRpcResponse.newBuilder()
        //                    .setResp(CommonRespUtils.successResp())
        //                    .setData(result)
        //                    .build();
        //        } catch (Exception e) {
        //            log.info("StrategyFlowReplayServiceImpl.testGroupRpc.error.request:{}", JsonMapperUtils.toJson
        //            (request), e);
        //            return TestGroupRpcResponse.newBuilder()
        //                    .setResp(CommonRespUtils.errorResp(e))
        //                    .build();
        //        }
        return null;
    }

    @Override
    public ClickHouseQueryTestResponse clickHouseQueryTest(ClickHouseQueryTestRequest request) {
        log.info("StrategyFlowReplayServiceImpl.clickHouseQueryTest.request:{}", JsonMapperUtils.toJson(request));
        try {
            ResultSet resultSet = ClickHouseConnector.executeSimpleSql(request.getSql());
            List<RecodeDetailClickHouseDto> recodeDetailClickHouseDtos =
                    ResultSetMapperUtil.mapResultSetToObject(resultSet, RecodeDetailClickHouseDto.class);
            return ClickHouseQueryTestResponse.newBuilder().setResp(CommonRespUtils.successResp())
                    .setData(JsonMapperUtils.toJson(recodeDetailClickHouseDtos)).build();
        } catch (Exception e) {
            log.error("StrategyFlowReplayServiceImpl.clickHouseQueryTest.error.request:{}",
                    JsonMapperUtils.toJson(request), e);
            return ClickHouseQueryTestResponse.newBuilder().setResp(CommonRespUtils.errorResp(e)).build();
        }
    }

    @Override
    public GetFlowReplayDetailListResponse getFlowReplayDetailList(GetFlowReplayDetailListRequest request) {
        log.info("StrategyFlowReplayServiceImpl.getFlowReplayDetailList.request:{}", JsonMapperUtils.toJson(request));
        try {
            PageResult<ReplayDetailClickHouseDto> result = replayService.getFlowReplayDetailList(request);
            return GetFlowReplayDetailListResponse.newBuilder().setResp(CommonRespUtils.successResp())
                    .setData(JsonMapperUtils.toJson(result)).build();
        } catch (Exception e) {
            log.error("StrategyFlowReplayServiceImpl.getFlowReplayDetailList.error,request:{}",
                    JsonMapperUtils.toJson(request), e);
            return GetFlowReplayDetailListResponse.newBuilder().setResp(CommonRespUtils.errorResp(e)).build();
        }
    }

    @Override
    public GetFlowReplayDetailResponse getFlowReplayDetail(GetFlowReplayDetailRequest request) {
        log.info("StrategyFlowReplayServiceImpl.getFlowReplayDetail.request:{}", JsonMapperUtils.toJson(request));
        try {
            ReplayDetailClickHouseDto flowReplayDetail =
                    replayService.getFlowReplayDetail(request.getId(), request.getFlowReplayId());
            return GetFlowReplayDetailResponse.newBuilder().setResp(CommonRespUtils.successResp())
                    .setData(JsonMapperUtils.toJson(flowReplayDetail)).build();
        } catch (Exception e) {
            log.error("StrategyFlowReplayServiceImpl.getFlowReplayDetail.error,request:{}",
                    JsonMapperUtils.toJson(request), e);
            return GetFlowReplayDetailResponse.newBuilder().setResp(CommonRespUtils.errorResp(e)).build();
        }
    }

    @Override
    public GetDateFromCacheResponse getReplayCache(GetDateFromCacheRequest request) {
        log.info("StrategyFlowReplayServiceImpl.getReplayCache.request:{}", JsonMapperUtils.toJson(request));
        FlowRecordStartEndPDate fLowReplay = replayService.getFlowReplayId(request.getId());
        return GetDateFromCacheResponse.newBuilder().setResp(CommonRespUtils.successResp())
                .setData(JsonMapperUtils.toJson(fLowReplay)).build();
    }

    @Override
    public GetFlowReplayAnalysisResultResponse getFlowReplayAnalysisResult(GetFlowReplayAnalysisResultRequest request) {
        String replayResultAnalysis = replayService.getReplayResultAnalysis(request.getId());
        return GetFlowReplayAnalysisResultResponse.newBuilder().setResp(CommonRespUtils.successResp())
                .setData(replayResultAnalysis).build();
    }

    @Override
    public StopReplayResponse stopReplay(StopReplayRequest request) {
        log.info("StrategyFlowReplayServiceImpl.stopReplay.request:{}", JsonMapperUtils.toJson(request));
        replayService.stopReplay(request.getId());
        return StopReplayResponse.newBuilder().setResp(CommonRespUtils.successResp()).setData("success").build();
    }

    @Override
    public StartReplayByPipelineResponseFixed startReplayByPipeline(StartReplayByPipelineRequest request) {
        log.info("StrategyFlowReplayServiceImpl.startReplayByPipeline.request:{}", JsonMapperUtils.toJson(request));
        // 触发接口，是否要异步回调
        String replayTaskId = "";
        try {
            replayTaskId = pipeLineService.startReplayByPipeline(request);
            return StartReplayByPipelineResponseFixed.newBuilder()
                    .setStatus(KdevResponseUtils.SUCCESS_STATUS)
                    .setMessage("success")
                    .setData(KdevResponseUtils.setDataFixed(replayTaskId))
                    .build();
        } catch (Exception e) {
            log.info("抛出异常");
            e.printStackTrace();
            return StartReplayByPipelineResponseFixed.newBuilder()
                    .setStatus(KdevResponseUtils.ERROR_STATUS)
                    .setMessage("fail")
                    .setData(KdevResponseUtils.setDataFixed(replayTaskId))
                    .build();
        }
    }

    @Override
    public CheckReplayStatusByPipelineResponseFixed checkReplayStatusByPipeline(CheckReplayStatusByPipelineRequest request) {
        log.info("StrategyFlowReplayServiceImpl.checkReplayStatusByPipeline.request:{}", JsonMapperUtils.toJson(request));
        // 触发接口，是否要异步回调

        return pipeLineService.checkReplayStatusByPipeline(request);
    }
}
