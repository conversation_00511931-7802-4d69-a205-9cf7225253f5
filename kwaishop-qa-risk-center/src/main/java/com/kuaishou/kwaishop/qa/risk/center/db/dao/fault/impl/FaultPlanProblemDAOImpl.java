package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.impl;

import java.util.Collection;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.FaultPlanProblemDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultPlanProblemDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.fault.FaultPlanProblemMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.fault.FaultPlanProblemQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultPlanProblemBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultPlanStatusEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2023-12-28
 */

@Repository
public class FaultPlanProblemDAOImpl extends BaseDAOImpl<FaultPlanProblemDO, FaultPlanProblemQueryCondition>
        implements FaultPlanProblemDAO {

    @Autowired
    private FaultPlanProblemMapper faultPlanProblemMapper;


    @Override
    public List<FaultPlanProblemDO> queryByCenterIds(Collection<Long> centerIds, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FaultPlanProblemDO> queryByTeamIds(Long centerId, Collection<Long> teamIds, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FaultPlanProblemDO> queryByCenterId(Long centerId, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FaultPlanProblemDO> queryByTeamId(Long centerId, Long teamId, CombineQueryParam queryParam) {


        return null;
    }

    @Override
    public List<FaultPlanProblemDO> queryByCenterIdsTeamIds(Collection<Long> centerIds, Collection<Long> teamIds, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public int logicDelete(Long id, String operator) {
        return faultPlanProblemMapper.logicDeleted(operator, id);

    }

    @Override
    public List<FaultPlanProblemDO> queryFaultPlanProblemList(FaultPlanProblemBO faultPlanBO) {
        FaultPlanProblemQueryCondition condition = FaultPlanProblemQueryCondition.builder()
                .id(faultPlanBO.getId())
                .caseName(faultPlanBO.getCaseName())
                .status(faultPlanBO.getStatus())
                .planId(faultPlanBO.getPlanId())
                .build();
        return queryList(condition);
    }

    @Override
    public PageBO<FaultPlanProblemDO> queryPageFaultPlanProblemList(FaultPlanProblemBO faultPlanBO) {
        FaultPlanProblemQueryCondition condition = FaultPlanProblemQueryCondition.builder()
                .id(faultPlanBO.getId())
                .caseName(faultPlanBO.getCaseName())
                .status(faultPlanBO.getStatus())
                .planId(faultPlanBO.getPlanId())
                .pageNo(faultPlanBO.getPageNo())
                .pageSize(faultPlanBO.getPageSize())
                .build();
        return queryPageList(condition);
    }

    @Override
    public List<FaultPlanProblemDO> queryFaultPlanProblemListByTime(Long centerId, Long st, Long et) {

        FaultPlanProblemQueryCondition condition = FaultPlanProblemQueryCondition.builder()
                .centerId(centerId)
                .startTimeGe(st)
                .endTimeLe(et).build();
        return queryList(condition);
    }

    @Override
    public List<FaultPlanProblemDO> queryFaultPlanProblemListByCenterId(Long centerId) {

        FaultPlanProblemQueryCondition condition = FaultPlanProblemQueryCondition.builder()
                .centerId(centerId)
                .build();

        return queryList(condition);
    }

    @Override
    protected void fillQueryCondition(FaultPlanProblemQueryCondition condition, QueryWrapper<FaultPlanProblemDO> queryWrapper) {
        if (condition.getCenterId() != null && condition.getCenterId() > 0) {
            queryWrapper.and(q -> q.eq("center_id", condition.getCenterId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getCenterIds())) {
            queryWrapper.and(q -> q.in("center_id", condition.getCenterIds()));
        }
        if (condition.getTeamId() != null && condition.getTeamId() > 0) {
            queryWrapper.and(q -> q.eq("team_id", condition.getTeamId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getTeamIds())) {
            queryWrapper.and(q -> q.in("team_id", condition.getTeamIds()));
        }
        if (StringUtils.isNotBlank(condition.getCaseName())) {
            queryWrapper.and(q -> q.like("case_name", condition.getCaseName()));
        }
        if (condition.getPlanId() != null && condition.getPlanId() > 0) {
            queryWrapper.and(q -> q.in("plan_id", condition.getPlanId()));
        }
        if (condition.getStatus() != null && condition.getStatus() > 0 && FaultPlanStatusEnum.of(condition.getStatus()) != null) {
            queryWrapper.and(q -> q.eq("status", condition.getStatus()));
        }
    }

    @Override
    protected BaseMapper<FaultPlanProblemDO> getMapper() {
        return faultPlanProblemMapper;
    }
}
