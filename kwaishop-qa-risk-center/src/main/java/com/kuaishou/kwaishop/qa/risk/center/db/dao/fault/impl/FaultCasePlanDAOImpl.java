package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.impl;

import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.FaultCasePlanDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultCasePlanDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.fault.FaultCasePlanMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.fault.FaultCasePlanQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCasePlanBO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2024-03-18
 */
@Repository
@Slf4j
public class FaultCasePlanDAOImpl extends BaseDAOImpl<FaultCasePlanDO, FaultCasePlanQueryCondition> implements FaultCasePlanDAO {

    @Autowired
    private FaultCasePlanMapper faultCasePlanMapper;
    @Override
    public List<FaultCasePlanDO> queryByCenterIds(Collection<Long> centerIds, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FaultCasePlanDO> queryByTeamIds(Long centerId, Collection<Long> teamIds, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FaultCasePlanDO> queryByCenterId(Long centerId, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FaultCasePlanDO> queryByTeamId(Long centerId, Long teamId, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FaultCasePlanDO> queryByCenterIdsTeamIds(Collection<Long> centerIds, Collection<Long> teamIds, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public PageBO<FaultCasePlanDO> queryFaultPlanIdsByCaseId(FaultCasePlanBO faultCasePlanBO) {
        FaultCasePlanQueryCondition faultCasePlanQueryCondition = FaultCasePlanQueryCondition.builder()
                .teamId(faultCasePlanBO.getTeamId())
                .centerId(faultCasePlanBO.getCenterId())
                .caseId(faultCasePlanBO.getCaseId())
                .pageNo(faultCasePlanBO.getPageNo())
                .pageSize(faultCasePlanBO.getPageSize())
                .orderByCreateTimeDesc(true)
                .build();
        return queryPageList(faultCasePlanQueryCondition);
    }

    @Override
    public void deleteFaultCasePlanByPlanId(String operator, Long planId) {
        faultCasePlanMapper.logicDeletedByPlanId(operator, planId);
    }

    @Override
    protected void fillQueryCondition(FaultCasePlanQueryCondition condition, QueryWrapper<FaultCasePlanDO> queryWrapper) {
        if (condition.getCenterId() != null && condition.getCenterId() > 0) {
            queryWrapper.and(q -> q.eq("center_id", condition.getCenterId()));
        }
        if (condition.getTeamId() != null && condition.getTeamId() > 0) {
            queryWrapper.and(q -> q.eq("team_id", condition.getTeamId()));
        }
        if (condition.getCaseId() != null && condition.getCaseId() > 0) {
            queryWrapper.and(q -> q.eq("case_id", condition.getCaseId()));
        }


    }

    @Override
    protected BaseMapper<FaultCasePlanDO> getMapper() {
        return faultCasePlanMapper;
    }
}
