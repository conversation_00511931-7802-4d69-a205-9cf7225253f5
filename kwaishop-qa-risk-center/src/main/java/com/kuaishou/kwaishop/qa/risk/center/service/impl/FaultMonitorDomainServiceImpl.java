package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.utils.ProtobufUtil.protoToJsonString;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.biz.FaultMonitorService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.GetAlarmGroupCallBackRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.GetAlarmGroupCallBackResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.KrpcFaultMonitorDomainServiceGrpc.FaultMonitorDomainServiceImplBaseV2;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class FaultMonitorDomainServiceImpl extends FaultMonitorDomainServiceImplBaseV2 {

    @Autowired
    private FaultMonitorService faultMonitorService;

    @Override
    public GetAlarmGroupCallBackResponse getAlarmGroupCallBack(GetAlarmGroupCallBackRequest request) {
        log.info("[FaultMonitorDomainServiceImpl] GetAlarmGroupCallBack request: {}", protoToJsonString(request));
        try {
            faultMonitorService.getAlarmGroupCallBack(request);
            return GetAlarmGroupCallBackResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultMonitorDomainServiceImpl] GetAlarmGroupCallBack bizError, req: {}, exception: ", protoToJsonString(request), e);
            return GetAlarmGroupCallBackResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultMonitorDomainServiceImpl] GetAlarmGroupCallBack error, req: {}, exception: ", protoToJsonString(request), e);
            return GetAlarmGroupCallBackResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }
}
