package com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.SampleSet;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-28
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tianhe_risk_sample_set")
public class TianheSampleSetDO implements Serializable {
    private static final long serialVersionUID = -5796230882065479687L;
    @TableId(value = "id", type = IdType.AUTO)
    private long id;
    /**
     * 用例集名称
     */
    private String setName;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 更新时间
     */
    private Long updateTime;
    /**
     * 是否被删除
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 用例
     */
    private String samples;
    /**
     * 是否进行巡检
     */
    private Integer isInspectionRequired;
    /**
     * 巡检频率
     */
    private String inspectionFrequency;
    /**
     * 应用ID
     */
    private Long appId;
}
