package com.kuaishou.kwaishop.qa.risk.center.db.dao.entity.impl;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.entity.EntityDataDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDataDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.entity.EntityDataMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.entity.EntityDataQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityDataBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityDataTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-29
 */
@Repository
public class EntityDataDAOImpl extends BaseDAOImpl<EntityDataDO, EntityDataQueryCondition> implements EntityDataDAO {

    @Autowired
    private EntityDataMapper entityDataMapper;


    @Override
    protected void fillQueryCondition(EntityDataQueryCondition condition, QueryWrapper<EntityDataDO> queryWrapper) {
        if (condition.getEntityType() != null && EntityTypeEnum.of(condition.getEntityType()) != null) {
            queryWrapper.and(q -> q.eq("entity_type", condition.getEntityType()));
        }
        if (condition.getEntityId() != null && condition.getEntityId() > 0) {
            queryWrapper.and(q -> q.eq("entity_id", condition.getEntityId()));
        }
        if (condition.getDataType() != null && EntityDataTypeEnum.of(condition.getDataType()) != null) {
            queryWrapper.and(q -> q.eq("data_type", condition.getDataType()));
        }
        if (StringUtils.isNotBlank(condition.getDt())) {
            queryWrapper.and(q -> q.eq("dt", condition.getDt()));
        }
        if (CollectionUtils.isNotEmpty(condition.getEntityIds())) {
            queryWrapper.and(q -> q.in("entity_id", condition.getEntityIds()));
        }
    }

    @Override
    protected BaseMapper<EntityDataDO> getMapper() {
        return entityDataMapper;
    }

    @Override
    public EntityDataDO queryEntityData(EntityDataBO entityDataBO) {
        EntityDataQueryCondition condition = EntityDataQueryCondition.builder()
                .id(entityDataBO.getId())
                .entityType(entityDataBO.getEntityType())
                .entityId(entityDataBO.getEntityId())
                .dataType(entityDataBO.getDataType())
                .dt(entityDataBO.getDt())
                .entityIds(entityDataBO.getEntityIds())
                .build();
        return queryOne(condition, true);
    }

    @Override
    public List<EntityDataDO> queryEntityDataList(EntityDataBO entityDataBO) {
        EntityDataQueryCondition condition = EntityDataQueryCondition.builder()
                .id(entityDataBO.getId())
                .entityType(entityDataBO.getEntityType())
                .entityId(entityDataBO.getEntityId())
                .dataType(entityDataBO.getDataType())
                .dt(entityDataBO.getDt())
                .entityIds(entityDataBO.getEntityIds())
                .build();
        return queryList(condition);
    }

    @Override
    public long insertOrUpdate(EntityDataDO entityDataDO) {
        return entityDataMapper.insertOrUpdate(entityDataDO);
    }
}
