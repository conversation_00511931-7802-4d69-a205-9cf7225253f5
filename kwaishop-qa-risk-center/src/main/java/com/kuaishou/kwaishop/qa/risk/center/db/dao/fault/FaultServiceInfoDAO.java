package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.DataPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultServiceInfoDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.fault.FaultServiceInfoQueryCondition;


public interface FaultServiceInfoDAO {

    List<FaultServiceInfoDO> getBaseSrcMethod(String srcService);

    long insertOrUpdateServiceInfo(FaultServiceInfoDO infoDO);

    FaultServiceInfoDO queryFaultServiceInfo(FaultServiceInfoQueryCondition condition);

    DataPageBO<FaultServiceInfoDO> queryRecommendSrcMethod(FaultServiceInfoQueryCondition condition);

    FaultServiceInfoDO queryFaultServiceInfoById(long id);
}
