package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-18
 */
@Data
public class FlowReplayListRequestPojo {

    private Long id;

    private String name;

    private Long flowRecordId;

    private String sceneKey;

    /**
     * 回放状态
     * @see com.kuaishou.kwaishop.qa.risk.center.common.Enum.ReplayStatus
     */
    private Integer replayStatus;

    private Integer diffResult;

    private String landId;

    private String group;

    private String ipPort;

    private String createUser;

    private Long replayStartTimeStart;

    private Long replayStartTimeEnd;

    private Long replayEndTimeStart;

    private Long replayEndTimeEnd;
}
