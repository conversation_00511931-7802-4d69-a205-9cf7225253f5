package com.kuaishou.kwaishop.qa.risk.center.tianhe.Controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AddSampleSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AddSampleSetResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteCaseFromSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteCaseFromSetResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteSampleSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteSampleSetResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ExecuteSampleSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ExecuteSampleSetResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.InsertCaseIntoSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.InsertCaseIntoSetResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.KrpcTianheSampleSetServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.QuerySampleBySetIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.QuerySampleBySetIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.QuerySampleSetExecuteListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.QuerySampleSetExecuteListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.SampleSetDebugRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.SampleSetDebugResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.TianheDebugRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.TianheDebugResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateSampleSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateSampleSetResponse;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Service.TianheSampleSetService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-19
 */
@Service
@Slf4j
@KrpcService(server = "custom-servers-config", serviceName = "kwaishop-qa-risk-center")
public class TianheSampleSetServiceImpl extends KrpcTianheSampleSetServiceGrpc.TianheSampleSetServiceImplBaseV2 {
    @Autowired
    private TianheSampleSetService tianheSampleSetService;

    @Override
    public QuerySampleBySetIdResponse querySampleBySetId(QuerySampleBySetIdRequest request) {
        return tianheSampleSetService.querySampleBySetIdResponse(request);
    }

    @Override
    public AddSampleSetResponse addSampleSet(AddSampleSetRequest request) {
        return tianheSampleSetService.addSampleSetResponse(request);
    }

    @Override
    public DeleteSampleSetResponse deleteSampleSet(DeleteSampleSetRequest request) {
        return tianheSampleSetService.deleteSampleSetResponse(request);
    }

    @Override
    public UpdateSampleSetResponse updateSampleSet(UpdateSampleSetRequest request) {
        return tianheSampleSetService.updateSampleSetResponse(request);
    }

    @Override
    public InsertCaseIntoSetResponse insertCaseIntoSet(InsertCaseIntoSetRequest request) {
        return tianheSampleSetService.insertCaseIntoSetResponse(request);
    }

    @Override
    public DeleteCaseFromSetResponse deleteCaseFromSet(DeleteCaseFromSetRequest request) {
        return tianheSampleSetService.deleteCaseFromSetResponse(request);
    }

    @Override
    public SampleSetDebugResponse sampleSetDebug(SampleSetDebugRequest request) {
        return tianheSampleSetService.sampleSetDebugResponse(request);
    }

    @Override
    public ExecuteSampleSetResponse executeSampleSet(ExecuteSampleSetRequest request) {
        return tianheSampleSetService.executeSampleSetResponse(request);
    }

    @Override
    public QuerySampleSetExecuteListResponse querySampleSetExecuteList(QuerySampleSetExecuteListRequest request) {
        return tianheSampleSetService.querySampleSetExecuteListResponse(request);
    }

    @Override
    public TianheDebugResponse debug(TianheDebugRequest request) {
        return tianheSampleSetService.debugResponse(request);
    }
}
