package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.biz.DataCaseBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.biz.DataCaseExeBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CaseExeResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CaseExeResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CreateDataCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CreateDataCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DeleteDataCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DeleteDataCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.KrpcDataCaseDomainServiceGrpc.DataCaseDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseByIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseByIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseDayRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseDayResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseLikeNameRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseLikeNameResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryPageDataCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryPageDataCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.RunDataCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.RunDataCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.UpdateDataCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.UpdateDataCaseResponse;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2022-12-06
 */

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class DataCaseDomainServiceImpl extends DataCaseDomainServiceImplBaseV2 {
    @Autowired
    private DataCaseBizService dataCaseBizService;

    @Autowired
    private DataCaseExeBizService dataCaseExeBizService;

    @Override
    public CreateDataCaseResponse createDataCase(CreateDataCaseRequest request) {
        log.info("[DataSourceDomainServiceImpl] createDataSource request: {}", ProtobufUtil.protoToJsonString(request));
        return dataCaseBizService.createDataCase(request);
    }

    @Override
    public UpdateDataCaseResponse updateDataCase(UpdateDataCaseRequest request) {
        log.info("[DataSourceDomainServiceImpl] updateDataSource request: {}", ProtobufUtil.protoToJsonString(request));
        return dataCaseBizService.updateDataCase(request);
    }

    @Override
    public QueryPageDataCaseResponse queryPageDataCase(QueryPageDataCaseRequest request) {
        return dataCaseBizService.queryPageDataCase(request);
    }

    @Override
    public QueryDataCaseLikeNameResponse queryDataCaseLikeName(QueryDataCaseLikeNameRequest request) {
        return dataCaseBizService.queryDataCaseLikeName(request);
    }

    @Override
    public QueryDataCaseDayResponse queryDateCase(QueryDataCaseDayRequest request) {
        return dataCaseBizService.queryDateCase(request);
    }

    @Override
    public QueryDataCaseByIdResponse queryDataCaseById(QueryDataCaseByIdRequest request) {
        return dataCaseBizService.queryDataCaseById(request);
    }

    @Override
    public QueryDataCaseResponse queryDataCase(QueryDataCaseRequest request) {
        return dataCaseBizService.queryDataCase(request);
    }

    @Override
    public DeleteDataCaseResponse deleteDataCase(DeleteDataCaseRequest request) {
        return dataCaseBizService.deleteDataCase(request);
    }

    @Override
    public RunDataCaseResponse runDataCase(RunDataCaseRequest request) {
        return dataCaseBizService.runDataCase(request);
    }

    @Override
    public CaseExeResultResponse queryPageCaseReportList(CaseExeResultRequest request) {
        return dataCaseBizService.queryPageCaseReportList(request);
    }

}
