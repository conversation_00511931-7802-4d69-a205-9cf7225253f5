package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-19
 */
@Data
public class StrategyFlowRecordDo {

    // 主键id
    private Long id;

    // 任务名称
    private String name;

    // 任务描述
    private String description;

    // 场景key列表
    private String sceneKeyList;

    // 流量总条数
    private Integer total;

    // 流量类型，1实时流量、2离线执行日志流量、3自定义hive流量
    private Integer flowType;

    // 流量状态，1录制中，2录制完成
    private Integer flowStatus;

    // 录制开始时间
    private Long flowStartTime;

    // 录制结束时间
    private Long flowEndTime;

    // 更新人
    private String createUser;

    // 创建人
    private String updateUser;

    // 创建时间
    private Long createTime;

    // 更新时间
    private Long updateTime;

    // 扩展字段
    private String extra;

    // 是否删除 0:否（默认）1：是
    private Integer deleted;
}
