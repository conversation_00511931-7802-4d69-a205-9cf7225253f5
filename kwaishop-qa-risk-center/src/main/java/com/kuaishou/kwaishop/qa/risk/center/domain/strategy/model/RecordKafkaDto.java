package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-19
 */
@Data
public class RecordKafkaDto {

    private String id;

    private String pDate;

    private String recordPDate;

    private Long flowRecordId;

    private String traceId;

    private String sceneKey;

    private String sceneType;

    private String featureKeyMap;

    private Boolean requestHitResult;

    private List<String> hitActionList;

    private List<String> hitStrategyList;

    private String conditionExecuteDetailInfo;

    private String ruleExecuteDetailInfo;

    private String bizKeyInfo;

    private String recordCreateTime;

    private String recordEndTime;

    private String createTime;

}
