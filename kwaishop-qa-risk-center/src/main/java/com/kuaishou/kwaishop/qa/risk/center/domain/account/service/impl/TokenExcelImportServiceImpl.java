package com.kuaishou.kwaishop.qa.risk.center.domain.account.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.UserAccountConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportAccountExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportTokenExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserImportTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.AccountExcelImportService;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.BaseExcelModel;

import groovy.util.logging.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-12-10
 */
@Service
@Lazy
@Slf4j
public class TokenExcelImportServiceImpl extends AbstractExcelImportService
        implements AccountExcelImportService {

    @Autowired
    private UserAccountConvert userAccountConvert;

    @Override
    public Integer getCode() {
        return UserImportTypeEnum.TOKEN.getCode();
    }

    @Override
    public List<UserAccountDO> buildAccountTokenDO(List<BaseExcelModel> excelModels, ImportTokenExcelBO excelBO) {
        return excelModels.stream()
                .map(e -> userAccountConvert.tokenExcelAccountDO(e, excelBO))
                .collect(Collectors.toList());
    }

    @Override
    protected List<UserAccountDO> buildAccountDO(List<BaseExcelModel> excelModels, ImportAccountExcelBO excelBO) {
        return null;
    }

    @Override
    protected String getScene() {
        return null;
    }

    @Override
    protected BaseExcelModel getModel() {
        return null;
    }
}
