package com.kuaishou.kwaishop.qa.risk.center.db.dao.kat;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;

@Repository
@Component
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public class KspayKatDAO {
    private final NamedParameterJdbcTemplate jdbcTemplate;
    private static final Logger logger = LoggerFactory.getLogger(KspayKatDAO.class);

    public KspayKatDAO(NamedParameterJdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @Transactional
    public void updateKatCaseRecord(String katRecordJson, String branchName, String repoName) {
        String sql = "UPDATE funds_risk_feature_view_branch SET `kat_record` = :katRecord "
                + "WHERE `branch_name` = :branchName AND `repo_name` = :repoName";

        try {
            // 构建参数
            MapSqlParameterSource params = new MapSqlParameterSource();
            params.addValue("katRecord", katRecordJson);
            params.addValue("branchName", branchName);
            params.addValue("repoName", repoName);

            logger.info("[FundsRiskFeatureBranchDAOImpl] updateKatCaseRecord SQL: {}", sql);
            logger.info("[FundsRiskFeatureBranchDAOImpl] updateKatCaseRecord params: {}", params);

            // 执行更新
            jdbcTemplate.update(sql, params);
        } catch (Exception e) {
            logger.error("[FundsRiskFeatureBranchDAOImpl] Error updating katRecord: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @Transactional
    public String getExistingKatCaseRecord(String branchName, String repoName) {
        logger.info("[FundsRiskFeatureBranchDAOImpl] getExistingKatCaseRecord branchName: {}, repoName: {}",
                branchName, repoName);
        String sql = "SELECT `kat_record` FROM funds_risk_feature_view_branch WHERE `branch_name` = :branchName AND `repo_name` = :repoName";
        try {
            // 构建参数
            MapSqlParameterSource params = new MapSqlParameterSource();
            params.addValue("branchName", branchName);
            params.addValue("repoName", repoName);
            logger.info("[FundsRiskFeatureBranchDAOImpl] getExistingKatCaseRecord SQL: {}", sql);
            logger.info("[FundsRiskFeatureBranchDAOImpl] getExistingKatCaseRecord params: {}", params);
            return jdbcTemplate.queryForObject(sql, params, String.class);
        } catch (EmptyResultDataAccessException emptyResultDataAccessException) {
            logger.warn("[FundsRiskFeatureBranchDAOImpl] getExistingKatCaseRecord EmptyResultDataAccessException: {}",
                    emptyResultDataAccessException.getMessage());
            return null;
        } catch (IncorrectResultSizeDataAccessException incorrectResultSizeDataAccessException) {
            logger.warn("[FundsRiskFeatureBranchDAOImpl] getExistingKatCaseRecord IncorrectResultSizeDataAccessException: {}",
                    incorrectResultSizeDataAccessException.getMessage());
            return null; // 或者抛出自定义异常
        } catch (Exception e) {
            logger.error("[FundsRiskFeatureBranchDAOImpl] Error getting katRecord: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}