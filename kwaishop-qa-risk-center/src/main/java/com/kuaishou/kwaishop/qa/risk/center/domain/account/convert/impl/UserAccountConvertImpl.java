package com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.impl;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.AccountTagDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TestAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAuthDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.UserAccountConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.UserAuthConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.AccountTokenBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.EmailImportExcelModel;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportAccountExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportTokenExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.PhoneImportExcelModel;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.RentAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.SingleUidImportExcelModel;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TokenImportExcelModel;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.RentalStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserAccountTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserDataTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserImportTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserLoginTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.RentAccountService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserAuthService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportAccountExcelRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportAccountTokenRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryPageUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UserAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UserAuthDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.CreateTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryTestAccountsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.ImportKlinkTokenRequest;
import com.kuaishou.kwaishop.qa.risk.center.utils.date.LocalDateUtil;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.BaseExcelModel;
import com.kuaishou.kwaishop.qa.risk.center.utils.kess.KessUtil;
import com.kuaishou.kwaishop.qa.risk.center.utils.kess.dto.KessRun;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
@Slf4j
@Component
public class UserAccountConvertImpl implements UserAccountConvert {

    @Autowired
    private UserAuthService userAuthService;

    @Autowired
    private UserAuthConvert userAuthConvert;

    @Autowired
    private RentAccountService rentAccountService;

    @Autowired
    private AccountTagDAO accountTagDAO;

    @Override
    public UserAccountBO buildQueryBO(QueryUserAccountRequest request) {
        return UserAccountBO.builder()
                .operator(request.getOperator())
                .userId(request.getUserId())
                .loginType(request.getLoginType())
                .dataType(request.getDataType())
                .accountType(request.getAccountType())
                .status(request.getStatus())
                .build();
    }

    @Override
    public UserAccountBO buildQueryBOV2(QueryTestAccountsRequest request) {
        return UserAccountBO.builder()
                .operator(request.getOperator())
                .userId(request.getUserId())
                .bId(request.getBid())
                .loginType(request.getLoginType())
                .dataType(request.getDataType())
                .accountType(request.getAccountType())
                .status(request.getStatus())
                .build();
    }

    @Override
    public UserAccountBO buildQueryPageBO(QueryPageUserAccountRequest request) {
        return UserAccountBO.builder()
                .operator(request.getOperator())
                .userId(request.getUserId())
                .loginType(request.getLoginType())
                .dataType(request.getDataType())
                .accountType(request.getAccountType())
                .status(request.getStatus())
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .account(request.getAccount())
                .build();
    }

    public Integer boolToInteger(boolean b) {
        return b ? 1 : 0;
    }

    @Override
    public UserAccountBO buildCreateBO(ImportUserAccountRequest request) {
        return UserAccountBO.builder()
                .operator(request.getOperator())
                .userId(request.getUserId())
                .account(request.getAccount())
                .password(request.getPassword())
                .loginType(request.getLoginType())
                .centerId(request.getCenterId())
                .teamId(request.getTeamId())
                .status(request.getStatus())
                .build();
    }

    @Override
    public TestAccountDO buildCreateDO(CreateTestAccountRequest request) {

        TestAccountDO testAccountDO = TestAccountDO.builder()
                .id(request.getUserId())
                .ownerId(System.currentTimeMillis())
                .ownerName(request.getOperator())
                .account(request.getAccount())
                .password(request.getPassword())
                .loginType(request.getLoginType())
                .kwaiId(request.getUserId())
                .bId(request.getBid())
                .creator(request.getOperator())
                .modifier(request.getOperator())
                .build();
        Map<String, Object> para = new HashMap<>();
        para.put("sellerId", request.getUserId());

        KessRun kessRun = KessRun.builder().jsonData(para).rpcMethod("GetSellerInfoForM")
                .rpcName("kwaishop-shop-page-service").build();

        String s = "";
        try {
            s = KessUtil.run(kessRun);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        String shopRes = s;
        Map<String, Object> shopInfo = ObjectMapperUtils.fromJSON(shopRes, Map.class);
        if ((int) shopInfo.get("result") == 1) {
            Map<String, Object> sellerBaseInfo1 = ObjectMapperUtils.fromJSON((String) shopInfo.get("data"), Map.class);
            Map<String, Object> sellerBaseInfo = (Map) sellerBaseInfo1.get("sellerBaseInfo");
            String sellerName = (String) sellerBaseInfo.get("sellerName");
            String sellerType = (String) sellerBaseInfo.get("sellerType");
            String shopName = (String) sellerBaseInfo.get("shopName");
            String shopType = (String) sellerBaseInfo.get("shopTypeDesc");
            boolean celebrityPermission = (Integer) sellerBaseInfo.get("darenStatus") == 1 ? true : false;
            Map<String, Object> shopScores = (Map) sellerBaseInfo1.get("shopScore");
            Double shopScore = (Double) shopScores.get("shopScore");
            Double darenScore = (Double) shopScores.get("darenScore");
            Map<String, Object> shopBusinessDetail = (Map) sellerBaseInfo.get("shopBusinessDetail");
            Integer deposit = (Integer) shopBusinessDetail.get("deposit");

            testAccountDO.setSellerName(sellerName);
            testAccountDO.setStoreName(shopName);
            testAccountDO.setStoreType(shopType);
            testAccountDO.setCelebrityPermission(celebrityPermission);
            testAccountDO.setShopScore(String.valueOf(shopScore));
            testAccountDO.setPromoterScore(String.valueOf(darenScore));
            //testAccountDO.setDeposit(deposit.longValue());
        }
        return testAccountDO;
    }

    @Override
    public UserAccountDO buildCreateDO(UserAccountBO accountBO) {
        if (accountBO.getPassword() == null || accountBO.getAccount() == null
                || accountBO.getPassword().isEmpty() || accountBO.getAccount().isEmpty()) {
            accountBO.setDataType(UserDataTypeEnum.SINGLE_UID.getCode());
            accountBO.setStatus(UserStatusEnum.CAN_NOT_APPLY.getCode());
            accountBO.setAccount("");
            accountBO.setPassword("");
        } else {
            accountBO.setDataType(UserDataTypeEnum.ALL_DATA.getCode());
        }
        return UserAccountDO.builder()
                .creator(accountBO.getOperator())
                .modifier(accountBO.getOperator())
                .userId(accountBO.getUserId())
                .account(accountBO.getAccount())
                .password(accountBO.getPassword())
                .accountType(UserAccountTypeEnum.PERSONAL.getCode())
                .loginType(accountBO.getLoginType())
                .status(accountBO.getStatus())
                .centerId(accountBO.getCenterId())
                .teamId(accountBO.getTeamId())
                .build();
    }

    @Override
    public UserAccountDTO buildDTO(UserAccountDO userAccountDO, String centerName, String teamName, String op) {
        log.info("creator is {},op is {},isEqual:{}", userAccountDO.getCreator(), op,
                userAccountDO.getCreator().equals(op));
        UserAccountDTO userAccountDTO = UserAccountDTO.newBuilder()
                .setCenterId(userAccountDO.getCenterId())
                .setCenterName(centerName)
                .setTeamId(userAccountDO.getTeamId())
                .setTeamName(teamName)
                .setUserId(userAccountDO.getUserId())
                .setAccount(userAccountDO.getAccount() == null ? "" : userAccountDO.getAccount())
                .setPassword(userAccountDO.getPassword() == null ? "" : userAccountDO.getPassword())
                .setLoginType(userAccountDO.getLoginType() == null ? 0 : userAccountDO.getLoginType())
                .setLoginTypeDesc(userAccountDO.getLoginType() == null
                        ?
                        ""
                        :
                        Objects.requireNonNull(UserLoginTypeEnum.of(userAccountDO.getLoginType())).getDesc())
                .setCreator(userAccountDO.getCreator())
                .setCreateTime(LocalDateUtil.milliToStringYMDHM(userAccountDO.getCreateTime()))
                .setUpdateTime(LocalDateUtil.milliToStringYMDHM(userAccountDO.getUpdateTime()))
                .setModifier(userAccountDO.getModifier())
                .setModifiable(userAccountDO.getCreator().equals(op) ? 1 : 0)
                .setStatus(userAccountDO.getStatus())
                .build();
        return getRentInfo(userAccountDTO, op);
    }


    /**
     * 获取租借信息，把租借人和是否可租借状态展示出来
     */
    public UserAccountDTO getRentInfo(UserAccountDTO userAccountDTO, String op) {
        long userId = userAccountDTO.getUserId();
        UserAccountDTO.Builder res = UserAccountDTO.newBuilder(userAccountDTO);
        // 查询租借表，有多少人在租借，以及自己有没有在租借中。
        List<RentAccountDO> rentAccountDOS = rentAccountService.queryRentRecords(RentAccountBO.builder()
                .userId(userId)
                .rentalStatus(RentalStatusEnum.BORROWED.getCode())
                .build());
        if (rentAccountDOS.isEmpty() && userAccountDTO.getStatus() == UserStatusEnum.CAN_APPLY.getCode()) {
            // 说明是可借用，目前没人借用，本人自己也是可借用。
            return res.setStatusDesc(UserStatusEnum.CAN_APPLY.getDesc())
                    .setModifiable(1) // 如果没有人借用，说明可更改。
                    .build();
        }
        //自动化账号 不可借用
        if (userAccountDTO.getStatus() == UserStatusEnum.CAN_NOT_APPLY_AUTOMATION.getCode()) {
            return res.setStatusDesc(UserStatusEnum.CAN_NOT_APPLY_AUTOMATION.getDesc())
                    .setModifiable(0) // 自动化账号，不可更改属性
                    .build();
        }
        rentAccountDOS.forEach(rentAccountDO -> {
            res.addBorrower(rentAccountDO.getBorrower());
            if (rentAccountDO.getBorrower().equals(op)) {
                res.setStatusDesc(UserStatusEnum.CAN_NOT_APPLY.getDesc())
                        .setStatus(UserStatusEnum.CAN_NOT_APPLY.getCode())
                        .setModifiable(1);
            }
        });
        if (rentAccountDOS.size() >= 5) {
            res.setStatusDesc(UserStatusEnum.CAN_NOT_APPLY.getDesc())
                    .setStatus(UserStatusEnum.CAN_NOT_APPLY.getCode());
        }
        if (res.getStatusDesc() == null || Objects.equals(res.getStatusDesc(), "")) {
            res.setStatus(UserStatusEnum.CAN_APPLY.getCode())
                    .setStatusDesc(UserStatusEnum.CAN_APPLY.getDesc());
        }
        res.setModifiable(0); //有人在借用，不可更改。
        return res.build();
    }

    @Override
    public ImportAccountExcelBO buildExcelBO(ImportAccountExcelRequest request) {
        return ImportAccountExcelBO.builder()
                .operator(request.getOperator())
                .cdnUrl(request.getCdnUrl())
                .importType(UserImportTypeEnum.PHONE_IMPORT.getCode())
                .build();
    }

    @Override
    public UserAccountDO phoneExcelAccountDO(BaseExcelModel baseExcelModel, ImportAccountExcelBO excelBO) {
        PhoneImportExcelModel excelModel = (PhoneImportExcelModel) baseExcelModel;
        if (excelModel.getPassword() == null || excelModel.getAccount() == null) {
            excelModel.setPassword("");
            excelModel.setAccount("");
        }
        return UserAccountDO.builder()
                .account(excelModel.getAccount())
                .password(excelModel.getPassword())
                .loginType(excelModel.getLoginType())
                .createTime(System.currentTimeMillis())
                .userId(excelModel.getUserId())
                //                .ext(ObjectMapperUtils.toJSON(excelModel))
                .creator(excelBO.getOperator())
                .modifier(excelBO.getOperator())
                .build();
    }

    @Override
    public UserAccountDO tokenExcelAccountDO(BaseExcelModel baseExcelModel, ImportTokenExcelBO excelBO) {
        TokenImportExcelModel tokenModel = (TokenImportExcelModel) baseExcelModel;
        return UserAccountDO.builder()
                .userId(tokenModel.getUserId())
                .ext(buildJsonString(tokenModel))
                .build();
    }

    private String buildJsonString(TokenImportExcelModel tokenModel) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("kuaishou.api_st", tokenModel.getSt());
        jsonObject.addProperty("ssecurity", tokenModel.getSsecurity());
        jsonObject.addProperty("tokenClientSalt", tokenModel.getTokenClientSalt());
        return jsonObject.toString();
    }

    @Override
    public UserAccountDO emailExcelAccountDO(BaseExcelModel baseExcelModel, ImportAccountExcelBO excelBO) {
        EmailImportExcelModel excelModel = (EmailImportExcelModel) baseExcelModel;
        return UserAccountDO.builder()
                .account(excelModel.getEmail())
                .password(excelModel.getPassword())
                .loginType(UserLoginTypeEnum.EMAIL_LOGIN.getCode())
                .accountType(excelBO.getAccountType())
                .userId(excelModel.getUserId())
                .dataType(UserDataTypeEnum.ALL_DATA.getCode())
                .centerId(excelBO.getCenterId())
                .teamId(excelBO.getTeamId())
                .ext(ObjectMapperUtils.toJSON(excelModel))
                .creator(excelBO.getOperator())
                .modifier(excelBO.getOperator())
                .build();
    }

    @Override
    public UserAccountDO singleUidExcelAccountDO(BaseExcelModel baseExcelModel, ImportAccountExcelBO excelBO) {
        SingleUidImportExcelModel excelModel = (SingleUidImportExcelModel) baseExcelModel;
        return UserAccountDO.builder()
                .loginType(UserLoginTypeEnum.UNKNOWN.getCode())
                .accountType(excelBO.getAccountType())
                .status(UserStatusEnum.CAN_NOT_APPLY.getCode())
                .userId(excelModel.getUserId())
                .dataType(UserDataTypeEnum.SINGLE_UID.getCode())
                .ext(ObjectMapperUtils.toJSON(excelModel))
                .centerId(excelBO.getCenterId())
                .teamId(excelBO.getTeamId())
                .creator(excelBO.getOperator())
                .modifier(excelBO.getOperator())
                .build();
    }

    @Override
    public AccountTokenBO buildAccountTokenBO(UserAccountDO userAccountDO) {
        return AccountTokenBO.builder()
                .account(userAccountDO.getAccount())
                .password(userAccountDO.getPassword())
                .loginType(userAccountDO.getLoginType())
                .userId(String.valueOf(userAccountDO.getUserId()))
                .build();
    }

    @Override
    public UserAccountDTO buildUserAuthDTO(UserAccountDTO userAccountDTO, Map<Long, UserAuthDO> map) {
        if (userAccountDTO == null) {
            throw new IllegalArgumentException("userAccountDTO cannot be null");
        }
        Long userId = userAccountDTO.getUserId();
        if (map == null || !map.containsKey(userId)) {
            // 塞一个空
            return UserAccountDTO.newBuilder(userAccountDTO)
                    .setUserAuthDto(UserAuthDTO.newBuilder().build())
                    .build();
        }
        UserAuthDO userAuthDO = map.get(userId);
        return UserAccountDTO.newBuilder(userAccountDTO)
                .setUserAuthDto(userAuthConvert.buildUserAuthDTO(userAuthDO))
                .build();
    }

    @Override
    public ImportTokenExcelBO buildTokenExcelBO(ImportAccountTokenRequest request) {
        return ImportTokenExcelBO.builder()
                .cdnUrl(request.getCdnUrl())
                .build();
    }

    @Override
    public ImportTokenExcelBO buildTokenExcelBO(ImportKlinkTokenRequest request) {
        return ImportTokenExcelBO.builder()
                .cdnUrl(request.getCdnUrl())
                .build();
    }

    @Override
    public UserAccountDO parseUserAccount(JsonArray jsonArray) {
        UserAccountDO userAccountDO = new UserAccountDO();
        // 遍历 JSON 数组并获取每个键值对
        for (JsonElement jsonElement : jsonArray) {
            JsonObject jsonObject = jsonElement.getAsJsonObject();

            if (jsonObject.get("key").getAsString().equals("uid")) {
                userAccountDO.setUserId(jsonObject.get("value").getAsLong());
            }

            if (jsonObject.get("key").getAsString().equals("mobile")) {
                userAccountDO.setAccount(jsonObject.get("value").getAsString());
            }

            if (jsonObject.get("key").getAsString().equals("password")) {
                userAccountDO.setPassword(jsonObject.get("value").getAsString());
            }

        }
        //目前仅申请+1264登录
        userAccountDO.setLoginType(2);
        //平台导入
        userAccountDO.setAccountType(1);
        userAccountDO.setDataType(1);
        //默认可借用
        userAccountDO.setStatus(1);
        //申请代表租借人+1
        userAccountDO.setRentNum(1);

        return userAccountDO;
    }


    //    @Override
    //    public UserAuthDTO buildUserAuthDTO(UserAuthDO userAuthDO) {
    //        return UserAuthDTO.newBuilder()
    //                .setDeposit(MathUtil.bigDecimalToLong(userAuthDO.getDeposit()))
    //                .setDistributorPermission(cover(userAuthDO.getDistributorPermission()))
    //                .setMerchantPermission(cover(userAuthDO.getMerchantPermission()))
    //                .setLeaderPermission(cover(userAuthDO.getRecruitingLeader()))
    //                .setPromoterPermission(cover(userAuthDO.getCelebrityPermission()))
    //                .setIsSeller(cover(userAuthDO.getIsSeller()))
    //                .setStoreName(userAuthDO.getStoreName())
    //                .setStoreType(userAuthDO.getStoreType())
    //                .build();
    //    }
    //
    //    public boolean cover(Integer integer) {
    //        return integer == 1;
    //    }


}
