package com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.service.impl;


import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.AccountTagManageBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.service.ApprovalService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.ProcessKeyEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.DPMApprovalParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.ApprovalBizCodeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.vo.ApprovalEntityDataVO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.RemoveTagsToSubjectRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.StartApprovalRequest;

import lombok.extern.slf4j.Slf4j;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2025/1/20 15:38
 * @注释
 */
@Slf4j
@Service
@Lazy
public class AccountTagDeleteApprovalServiceImpl implements ApprovalService {

    @Autowired
    private AccountTagManageBizService accountTagManageBizService;

    @Override
    public String getCode() {
        return ApprovalBizCodeEnum.ACCOUNT_TAG_DELETE.getCode();
    }

    @Override
    public ApprovalEntityDataVO approvalForm(StartApprovalRequest request, String code, DPMApprovalParam param) {
        List<String> tagVal = request.getCodeValueList();
        //目前仅支持单个code
        String tag = tagVal.get(0);
        ApprovalEntityDataVO vo = new ApprovalEntityDataVO();
        String desc = ApprovalBizCodeEnum.fromBizCode(request.getBizCode()).getDesc();
        String jsonString = "[{\"fieldName\": \"申请说明\", \"fieldKey\": \"apply_explain\""
                + ", \"fieldValue\": \"账号下标权限申请\", \"fieldType\": \"highLight\"}, "
                + "{\"fieldKey\": \"input\", \"fieldValue\": \"%s\", \"fieldName\": \"信息描述\"}, "
                + "{\"fieldKey\": \"input\", \"fieldValue\": \"%s\", \"fieldName\": \"userId\"}, "
                + "{\"fieldKey\": \"input\", \"fieldValue\": \"%s\", \"fieldName\": \"标签code\"}]";
        String businessSummary = String.format(jsonString, desc, request.getUserId(), tag);
        String businessId = "account_test" + System.currentTimeMillis();
        vo.setBusinessExplain(businessId);
        vo.setBusinessId(businessId);
        vo.setFormType(Objects.requireNonNull(ProcessKeyEnum.of(tag)).getProcessKey());
        vo.setProcessKey(Objects.requireNonNull(ProcessKeyEnum.of(tag)).getProcessKey());
        vo.setProcessDefinitionKey(Objects.requireNonNull(ProcessKeyEnum.of(tag)).getProcessKey());
        param.setBusinessSummary(businessSummary);
        if (request.getUserId() > 0) {
            vo.setUserId(String.valueOf(request.getUserId()));
            param.setUserId(String.valueOf(request.getUserId()));
        }
        vo.setProcessVariables(param);
        vo.setUsername(request.getUserName());
        vo.setBusinessSummary(businessSummary);
        return vo;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void execute(Map<String, Object> variables) {
        try {
            log.info("执行账号下标操作");
            String tagCodes;
            List<String> tagCodesList;
            if (EnvUtils.isStaging()) {
                tagCodes = (String) variables.get("codeValue");
                String[] strs = tagCodes.substring(1, tagCodes.length() - 1).split(", ");
                tagCodesList = Arrays.asList(strs);
            } else {
                String businessSummaryStr = (String) variables.get("businessSummary");
                Gson gson = new Gson();
                Type listType = new TypeToken<List<Map<String, Object>>>() {
                }.getType();
                List<Map<String, Object>> businessSummary = gson.fromJson(businessSummaryStr, listType);
                //目前仅支持单个打标，直接取第一个
                Map<String, Object> stringObjectMap = businessSummary.get(3);
                //Map<String, Object> fieldValue = (Map<String, Object>) stringObjectMap.get("fieldValue");
                String tagValue = (String) stringObjectMap.get("fieldValue");
                tagCodesList = Arrays.asList(tagValue);
            }
            RemoveTagsToSubjectRequest removeTagsToTestAccountRequest = RemoveTagsToSubjectRequest.newBuilder()
                    .setOperator((String) variables.get("userName"))
                    .setUserId(Long.parseLong((String) variables.get("userId")))
                    .addAllTagCodes(tagCodesList)
                    .build();
            accountTagManageBizService.removeTagsFromTestAccount(removeTagsToTestAccountRequest);
        } catch (Exception e) {
            log.info("bpm处理账号下标失败:{}", ObjectMapperUtils.toJSON(variables));
            e.printStackTrace();
        }
    }
}
