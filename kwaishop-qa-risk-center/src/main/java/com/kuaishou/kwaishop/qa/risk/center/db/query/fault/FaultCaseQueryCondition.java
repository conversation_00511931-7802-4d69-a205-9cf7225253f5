package com.kuaishou.kwaishop.qa.risk.center.db.query.fault;

import java.util.Collection;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-08
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FaultCaseQueryCondition extends BaseQueryCondition {

    private String name;

    private Long centerId;

    private Long teamId;

    /**
     * 唯一键
     */
    private String uniqueId;

    /**
     * 主调服务ksn
     */
    private String callerKsn;

    /**
     * 主调服务方法名
     */
    private String callerMethodName;

    /**
     * 主调服务方法全名
     */
    private String callerFullMethodName;

    /**
     * 主调服务类型
     */
    private String callerType;

    /**
     * 主调服务等级
     */
    private Integer callerLevel;

    /**
     * 被调服务ksn
     */
    private String calleeKsn;

    /**
     * 被调服务名称
     * */
    private String calleeName;

    /**
     * 被调服务方法名
     */
    private String calleeMethodName;

    /**
     * 被调服务方法全名
     */
    private String calleeFullMethodName;

    /**
     * 被调服务类型
     */
    private String calleeType;

    /**
     * 被调服务等级
     */
    private Integer calleeLevel;

    /**
     * 强弱依赖类型
     */
    private String relationship;

    /**
     * 数据来源
     */
    private String source;

    private Integer tag;

    private String businessTag;

    private Collection<Integer> callerLevelList;

    private Collection<Integer> calleeLevelList;
}
