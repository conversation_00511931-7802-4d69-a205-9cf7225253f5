package com.kuaishou.kwaishop.qa.risk.center.domain.combine.biz.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.OPERATOR_EMPTY_ERROR;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.biz.RiskInfoBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.convert.RiskCombineConvertHandler;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.RiskInfoCombineBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.service.RiskInfoCombineService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.QueryGroupByRiskSummaryDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.QueryRiskSummaryDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.RiskSummaryDataDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-22
 */
@Service
@Slf4j
@Lazy
public class RiskInfoBizServiceImpl implements RiskInfoBizService {

    @Autowired
    private RiskInfoCombineService riskInfoCombineService;

    @Autowired
    private RiskCombineConvertHandler handler;


    @Override
    public RiskSummaryDataDTO queryRiskSummaryData(QueryRiskSummaryDataRequest request) {
        if (StringUtils.isBlank(request.getOperator())) {
            throw new BizException(OPERATOR_EMPTY_ERROR);
        }
        CombineQueryParam queryParam = handler.buildSummaryQueryParam(request);
        RiskInfoCombineBO riskInfoCombineBO = riskInfoCombineService.getSummaryData(queryParam);
        return handler.buildSummaryDTO(riskInfoCombineBO);
    }

    @Override
    public List<RiskSummaryDataDTO> queryGroupByRiskSummaryData(QueryGroupByRiskSummaryDataRequest request) {
        if (StringUtils.isBlank(request.getOperator())) {
            throw new BizException(OPERATOR_EMPTY_ERROR);
        }
        CombineQueryParam queryParam = handler.buildGroupingByQueryParam(request);
        List<RiskInfoCombineBO> riskInfoCombineBOS = riskInfoCombineService.getGroupingByData(queryParam);
        return riskInfoCombineBOS.stream().map(handler::buildSummaryDTO).collect(Collectors.toList());
    }

}
