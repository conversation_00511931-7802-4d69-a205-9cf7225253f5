package com.kuaishou.kwaishop.qa.risk.center.domain.errorcode.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCodeError;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.errorcode.BizAreaDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.errorcode.BizAreaDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.errorcode.BizAreaService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.AddBizAreaRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.BizAreaEntity;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.BizAreaListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.BizAreaListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.DeleteBizAreaRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.UpdateBizAreaRequest;

import lombok.extern.slf4j.Slf4j;

@Service
@Lazy
@Slf4j
public class BizAreaServiceImpl implements BizAreaService {
    @Autowired
    private BizAreaDAO bizAreaDAO;

    @Override
    public void addBizArea(AddBizAreaRequest addRequest) {
        BizAreaDO bizAreaDO = new BizAreaDO();
        bizAreaDO.setAreaCode(addRequest.getAreaCode());
        bizAreaDO.setAreaCodeDesc(addRequest.getAreaCodeDesc());
        bizAreaDO.setOwner(addRequest.getOwner());
        bizAreaDO.setCreator(addRequest.getCurUser());
        bizAreaDAO.insert(bizAreaDO);
    }

    @Override
    public BizAreaDO getBizAreaById(Long id) {
        if (id == null || id <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID);
        }
        return bizAreaDAO.getById(id);
    }

    @Override
    public void updateBizArea(UpdateBizAreaRequest updateRequest) {
        if (updateRequest == null || updateRequest.getId() <= 0) {
            throw new BizException(BasicErrorCode.PARAM_INVALID);
        }
        BizAreaDO bizAreaDO = new BizAreaDO();
        bizAreaDO.setId(updateRequest.getId());
        bizAreaDO.setAreaCode(updateRequest.getAreaCode());
        bizAreaDO.setAreaCodeDesc(updateRequest.getAreaCodeDesc());
        bizAreaDO.setOwner(updateRequest.getOwner());
        bizAreaDAO.updateById(bizAreaDO);
    }

    @Override
    public void deleteBizArea(DeleteBizAreaRequest deleteRequest) {
        BizAreaDO areaDO = bizAreaDAO.getById(deleteRequest.getId());
        if (areaDO == null) {
            throw new BizException(ErrorCodeError.DATA_NOT_EXSIT);
        }
        if (!areaDO.getCreator().equals(deleteRequest.getCurUser())) {
            throw new BizException(ErrorCodeError.NOT_PERMIT);
        }
        bizAreaDAO.deleteById(deleteRequest.getId());
    }

    @Override
    public BizAreaListResponse getBizAreaList(BizAreaListRequest listRequest) {

        IPage<BizAreaDO> bizAreaList;
        if (listRequest.getCreatedByMe()) {
            bizAreaList = bizAreaDAO.getBizAreaList(listRequest.getCurUser(), listRequest.getPageNum(),
                    listRequest.getPageSize());
        } else {
            bizAreaList = bizAreaDAO.getBizAreaList(null, listRequest.getPageNum(),
                    listRequest.getPageSize());
        }

        BizAreaListResponse.Builder builder = BizAreaListResponse.newBuilder();
        List<BizAreaDO> records = bizAreaList.getRecords();
        List<BizAreaEntity> bizAreaEntities = records.stream().map(bizAreaDO ->
                BizAreaEntity
                        .newBuilder()
                        .setId(bizAreaDO.getId())
                        .setAreaCode(bizAreaDO.getAreaCode())
                        .setAreaCodeDesc(bizAreaDO.getAreaCodeDesc())
                        .setCreator(bizAreaDO.getCreator())
                        .setOwner(bizAreaDO.getOwner())
                        .build()
        ).collect(Collectors.toList());

        builder.addAllBizAreaEntity(bizAreaEntities);
        builder.setPageNum(bizAreaList.getCurrent());
        builder.setTotalPage(bizAreaList.getTotal());
        return builder.build();
    }

    @Override
    public BizAreaDO queryByAreaCode(String areaCode) {
        return bizAreaDAO.queryByBizArea(areaCode);
    }


}
