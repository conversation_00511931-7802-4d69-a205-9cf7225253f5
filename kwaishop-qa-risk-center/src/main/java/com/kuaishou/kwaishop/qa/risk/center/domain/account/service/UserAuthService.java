package com.kuaishou.kwaishop.qa.risk.center.domain.account.service;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAuthDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAuthBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-21
 */
public interface UserAuthService {

    UserAuthDO queryAllUserAuth(Long userId);

    String queryScoreByUserId(String userId, UserAuthDO userAuthDO);

    void queryDepositByUserId(String userId, UserAuthDO userAuthDO);

    String queryBail(Long userId);

    void openAuth();

    void insertAuthInfo(Long userId);

    int updateAuthInfo(Long userId);

    List<UserAuthDO> queryUserAuthRecords(UserAuthBO userAuthBO);

    int update(UserAuthDO userAuthDO);
}
