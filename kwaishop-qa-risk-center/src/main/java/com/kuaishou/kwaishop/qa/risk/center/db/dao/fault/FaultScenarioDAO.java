package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultScenarioDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultScenarioBO;


public interface FaultScenarioDAO {

    long insertOrUpdateFaultScenario(FaultScenarioDO faultScenarioDO);

    FaultScenarioDO queryFaultScenario(FaultScenarioBO faultScenarioBO);

    PageBO<FaultScenarioDO> queryPageFaultScenario(FaultScenarioBO faultScenarioBO);

    List<FaultScenarioDO> queryListScenario(FaultScenarioBO faultScenarioBO);

    FaultScenarioDO queryFaultScenarioById(Long id);

    long deleteFaultScenario(long id);

}
