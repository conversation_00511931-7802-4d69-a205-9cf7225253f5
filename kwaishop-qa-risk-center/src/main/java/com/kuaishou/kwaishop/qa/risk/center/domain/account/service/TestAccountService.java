package com.kuaishou.kwaishop.qa.risk.center.domain.account.service;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentTestAccountRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagTestAccountRelDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TestAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TestAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TestAccountRentBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
public interface TestAccountService {

    PageBO<RentTestAccountRecordDO> queryMyRentRecords(TestAccountBO queryBO);

    PageBO<TestAccountDO> queryAllTestAccountRecords(TestAccountBO queryBO);

    PageBO<TestAccountDO> queryMyTestAccountRecords(TestAccountBO queryBO);

    PageBO<TestAccountDO> queryTestAccountPage(TestAccountBO queryBO);

    List<RentTestAccountRecordDO> queryAllRentRecords(TestAccountRentBO testAccountRentBO);

    List<TagTestAccountRelDO> queryAllTagsAccountsDetail();
}
