package com.kuaishou.kwaishop.qa.risk.center.tianhe.Enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-05-08
 */
public class TianheSampleActionCode {

    public TianheSampleActionCode() {
    }

    private static final Map<Integer, String> CODE_MAP = new HashMap<>();

    static {
        CODE_MAP.put(0, "页面查看");
        CODE_MAP.put(1, "元素点击");
    }

    public static String getMessageByCode(int code) {
        return CODE_MAP.get(code);
    }
}
