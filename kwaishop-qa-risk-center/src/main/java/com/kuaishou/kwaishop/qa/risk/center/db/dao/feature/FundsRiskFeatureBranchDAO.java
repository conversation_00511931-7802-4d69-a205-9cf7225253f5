package com.kuaishou.kwaishop.qa.risk.center.db.dao.feature;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureBranchDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundRiskFeatureDetailQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.bo.KatCaseRecordBO;
//import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetail;

public interface FundsRiskFeatureBranchDAO {
    List<FundsRiskFeatureBranchDO> queryFundRiskFeatureBranch(FundRiskFeatureDetailQueryCondition fundRiskFeatureDetailQueryCondition);

    FundsRiskFeatureBranchDO queryByFeatureViewId(int featureViewId);
    List<FundsRiskFeatureBranchDO> queryFundRiskFeatureBranchDataNoNull();
    List<FundsRiskFeatureBranchDO> queryFundRiskFeatureBranchDataNoRisk();
    List<FundsRiskFeatureBranchDO> queryFundRiskByRepoAndBranch(FundRiskFeatureDetailQueryCondition fundRiskFeatureDetailQueryCondition);
    FundsRiskFeatureBranchDO queryFundRiskByRepoAndBranchAndViewId(FundRiskFeatureDetailQueryCondition fundRiskFeatureDetailQueryCondition);

    FundsRiskFeatureBranchDO queryByMainId(String mainId);

    int updateSelectiveById(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO);
    int updateRiskStatusByMainId(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO);
    int updateAuditCoverByMainId(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO);

    int updateAllStatusByMainId(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO);

    int updateAllStatusByFeatureId(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO);

    int updateByFeatureViewId(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO);
    void updateExtraDataByFeatureId(Long id, String extraData);

    List<FundsRiskFeatureBranchDO> getKspayRiskStatistic();
    List<FundsRiskFeatureBranchDO> getKspayRiskStatisticWithTime(Long startTime, Long endTime);
    int updateRuleIsValidByMixed(String repoName, String branchName, Long featureViewId, String ruleIsValid);

    //chainId:1/0
    String queryRuleIsValid(String repoName, String branchName, Long featureViewId);

    void updateKatCaseRecord(List<KatCaseRecordBO> records, String branchName, String repoName);

}
