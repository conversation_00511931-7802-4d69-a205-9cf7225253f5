package com.kuaishou.kwaishop.qa.risk.center.db.dao.account.impl;


import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.UserDumpDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserDbForDumpDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.account.UserDumpMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.account.UserDumpCondition;
import com.kuaishou.kwaishop.qa.risk.center.utils.AESUtil;

@Repository

public class UserDumpDAOImpl extends BaseDAOImpl<UserDbForDumpDO, UserDumpCondition> implements UserDumpDAO {

    @Resource
    private UserDumpMapper userDumpMapper;


    @Override
    public void insertDump(UserDbForDumpDO userDbForDump) {
        userDbForDump.setAccount(AESUtil.encrypt(userDbForDump.getAccount()));
        userDbForDump.setPassword(AESUtil.encrypt(userDbForDump.getPassword()));
        userDumpMapper.insert(userDbForDump);

    }

    @Override
    public UserDbForDumpDO getDumpUser(Long userId) {
        UserDbForDumpDO userDbForDumpDO = userDumpMapper.getDumpUser(userId);
        userDbForDumpDO.setAccount(AESUtil.decrypt(userDbForDumpDO.getAccount()));
        userDbForDumpDO.setPassword(AESUtil.decrypt(userDbForDumpDO.getPassword()));
        return userDbForDumpDO;
    }


    @Override
    protected void fillQueryCondition(UserDumpCondition condition, QueryWrapper<UserDbForDumpDO> queryWrapper) {

    }

    @Override
    protected BaseMapper<UserDbForDumpDO> getMapper() {
        return userDumpMapper;
    }
}
