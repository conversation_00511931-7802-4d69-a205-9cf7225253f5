package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.KWAISQL_PARAM_ERROR;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.kuaishou.datarch.themis.sdk.KwaiSQL;
import com.kuaishou.datarch.themis.sdk.enums.EmEnv;
import com.kuaishou.datarch.themis.sdk.enums.EmJobStatus;
import com.kuaishou.datarch.themis.sdk.enums.EmProfile;
import com.kuaishou.datarch.themis.sdk.enums.EmSchema;
import com.kuaishou.datarch.themis.sdk.vo.KwaiSQLLogResult;
import com.kuaishou.dp.auth.dsc.client.DscAccessTokenProvider;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.config.constants.QueryTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.HiveRecordFetchService;

import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-14
 */
@Slf4j
@Service
public class HiveRecordFetchServiceImpl implements HiveRecordFetchService {


    /**
     * 生意通研数据研发组
     */
    private static final String PRINCIPAL = "platform_governance/<EMAIL>";

    @Setter
    @Getter
    private static String SECRET_KEY;
    @Setter
    @Getter
    private static String APP_KEY;
    @Setter
    @Getter
    private static String APP_SECRET;

    private static final Integer SYT_GROUP_ID = 1563;

    public static String getAuthToken() {
        try {
            return DscAccessTokenProvider.getToken(PRINCIPAL, SECRET_KEY);
        } catch (Exception e) {
            return "";
        }
    }

    @SneakyThrows
    public List<Map<String, Object>> fetchDpData(Integer queryType, String querySql, Long dataSourceId, String user, String taskId) {
        String queryId = "qaRisk_" + UUID.randomUUID().toString();
        Map<String, String> jobUserParams = new HashMap<>();
        jobUserParams.put(EmProfile.KUAISHOU_BIGDATA_JOB_AUTHC_PRINCIPAL.getKey(), PRINCIPAL);
        jobUserParams.put(EmProfile.KUAISHOU_BIGDATA_JOB_AUTHC_TYPE.getKey(), "USER");
        jobUserParams.put(EmProfile.KUAISHOU_BIGDATA_JOB_AUTHC_TOKEN.getKey(), getAuthToken());
        jobUserParams.put(EmProfile.KUAISHOU_BIGDATA_JOB_CUSTOM_QUERY_ID.getKey(), queryId);

        QueryTypeEnum queryTypeEnum = QueryTypeEnum.initQueryType(queryType);
        KwaiSQLLogResult kwaiSQLLogResult = null;
        //KwaiSQL kwaiSQL =
        log.info(String.format("执行kwaisql查询请求参数sql: %s, dataSourceId %s, queryId %s", querySql, dataSourceId, queryId));

        if (StringUtils.isBlank(querySql) || StringUtils.isBlank(user)) {
            throw new BizException(KWAISQL_PARAM_ERROR);
        }
        //dataSourceId 可传可不传，如果没传表示是在调试,设置默认值0
        if (dataSourceId == null) {
            dataSourceId = 0L;
        }
        SqlJobHandler sqlJobHandler = new SqlJobHandler();
        if (Objects.requireNonNull(queryTypeEnum) == QueryTypeEnum.HIVE) {
            kwaiSQLLogResult = KwaiSQL.builder()
                    .appKey(APP_KEY)
                    .appSecret(APP_SECRET)
                    .env(EmEnv.prod.getEnv())
                    .user(user)
                    .groupId(SYT_GROUP_ID)
                    .jobUserParams(jobUserParams)
                    .schema(EmSchema.hive)
                    .sql(querySql)
                    .handler(sqlJobHandler)
                    .build().asyncSubmitSQL();
        } else {
            log.info(String.format("kwaisql查询类型被跳过: queryType %s, ", queryType));
        }

        //提交之后结果返回，并更新dataSourceID 的执行状态
        // 状态，执行中，
        while (!sqlJobHandler.getCanReturn().get()) {
            try {
                TimeUnit.SECONDS.sleep(5);
            } catch (InterruptedException ignored) {
            }
            try {
                List<String> nextLog = kwaiSQLLogResult.nextLog();
                // 判断是否取消执行

                log.info("nextLog = {}", nextLog);
            } catch (Exception e) {
                log.info(String.format("执行过程中更新日志异常: e %s, ", e.getMessage()));
            }
        }
        // 结束后等待一秒更新最后一次增量日志
        try {
            TimeUnit.SECONDS.sleep(1);
        } catch (InterruptedException ignored) {
        }
        //更新最后一次日志
        // 后面写
        //        strategyTrafficRecordService.updateStatus(taskId, RecordListStatus.RECORDING.getCode());
        List<String> nextLog = kwaiSQLLogResult.nextLog();
        List<Map<String, Object>> data = sqlJobHandler.getAllResults();
        log.info("nextLog = {}", nextLog);
        log.info("data = {}", data);
        if (EmJobStatus.isSucceed(sqlJobHandler.getStatusRef().get())) {

            log.info("执行成功");
            return data;
        }
        return null;
    }

    @Override
    @SneakyThrows
    public List<Map<String, Object>> fetchIdpDataSync(QueryTypeEnum queryType, String querySql, String user) {
        if (StringUtils.isBlank(querySql) || StringUtils.isBlank(user)) {
            throw new BizException(KWAISQL_PARAM_ERROR);
        }

        /**
         * 【1】参数准备
         */
        Map<String, String> jobUserParams = buildjobUserParams();
        KwaiSQLLogResult kwaiSQLLogResult;
        KwaiSQL kwaiSQL = null;

        SqlJobHandler sqlJobHandler = new SqlJobHandler();
        if (QueryTypeEnum.HIVE.equals(queryType)) {
            kwaiSQL = KwaiSQL.builder()
                    .appKey(APP_KEY)
                    .appSecret(APP_SECRET)
                    .env(EmEnv.prod.getEnv())
                    .user(user)
                    .groupId(SYT_GROUP_ID)
                    .jobUserParams(jobUserParams)
                    .schema(EmSchema.hive)
                    .sql(querySql)
                    .handler(sqlJobHandler)
                    .build();
        } else {
            log.error(String.format("kwaisql查询类型被跳过: queryType %s, ", queryType));
        }

        /**
         * 【2】发起调用
         */
        kwaiSQLLogResult = kwaiSQL.asyncSubmitSQL();

        /**
         * 【3】同步等待结果返回
         */
        while (!sqlJobHandler.getCanReturn().get()) {
            try {
                TimeUnit.SECONDS.sleep(5);
            } catch (InterruptedException ignored) {
            }
            try {
                List<String> nextLog = kwaiSQLLogResult.nextLog();
                log.info("nextLog = {}", nextLog);
            } catch (Exception e) {
                log.error("执行过程中更新日志异常", e);
            }
        }

        // 结束后等待一秒更新最后一次增量日志
        try {
            TimeUnit.SECONDS.sleep(1);
        } catch (InterruptedException ignored) {
        }

        /**
         * 【4】获取结果并返回
         */
        List<Map<String, Object>> data = sqlJobHandler.getAllResults();
        if (EmJobStatus.isSucceed(sqlJobHandler.getStatusRef().get())) {
            return data;
        }
        return null;
    }

    @Override
    @SneakyThrows
    public SqlJobHandler fetchIdpDataAsync(QueryTypeEnum queryType, String querySql, String user) {
        if (StringUtils.isBlank(querySql) || StringUtils.isBlank(user)) {
            throw new BizException(KWAISQL_PARAM_ERROR);
        }

        /**
         * 【1】参数准备
         */
        Map<String, String> jobUserParams = buildjobUserParams();
        KwaiSQL kwaiSQL = null;

        SqlJobHandler sqlJobHandler = new SqlJobHandler("kafka");
        if (QueryTypeEnum.HIVE.equals(queryType)) {
            kwaiSQL = KwaiSQL.builder()
                    .appKey(APP_KEY)
                    .appSecret(APP_SECRET)
                    .env(EmEnv.prod.getEnv())
                    .user(user)
                    .groupId(SYT_GROUP_ID)
                    .jobUserParams(jobUserParams)
                    .schema(EmSchema.hive)
                    .sql(querySql)
                    .handler(sqlJobHandler)
                    .build();
        } else {
            log.error(String.format("kwaisql查询类型被跳过: queryType %s, ", queryType));
        }

        /**
         * 【2】发起调用
         */
        KwaiSQLLogResult kwaiSQLLogResult = kwaiSQL.asyncSubmitSQL();
        return sqlJobHandler;
    }

    private Map<String, String> buildjobUserParams() {
        String queryId = "qaRisk_" + UUID.randomUUID();
        Map<String, String> jobUserParams = new HashMap<>();
        jobUserParams.put(EmProfile.KUAISHOU_BIGDATA_JOB_AUTHC_PRINCIPAL.getKey(), PRINCIPAL);
        jobUserParams.put(EmProfile.KUAISHOU_BIGDATA_JOB_AUTHC_TYPE.getKey(), "USER");
        jobUserParams.put(EmProfile.KUAISHOU_BIGDATA_JOB_AUTHC_TOKEN.getKey(), getAuthToken());
        jobUserParams.put(EmProfile.KUAISHOU_BIGDATA_JOB_CUSTOM_QUERY_ID.getKey(), queryId);
        return jobUserParams;
    }

//    private static class SqlJobHandler implements IHandler {
//        private final AtomicReference<String> statusRef = new AtomicReference<>();
//        private final List<Map<String, Object>> allResults = new ArrayList<>();
//        private final AtomicReference<Exception> errorRef = new AtomicReference<>();
//        private final AtomicReference<Boolean> canReturn = new AtomicReference<>(false);
//
//        @Override
//        public void onNewResultArrive(List<Map<String, Object>> list, Boolean isAllResultArrived, int i, Exception e,
//                JobInstanceQueryRequestVo<?> jobInstanceQueryRequestVo) {
//            log.info("SqlJobHandler.onNewResultArrive.size:{}", list.size());
//            if (e != null) {
//                log.info(String.format("执行失败原因:e %s, RequestVo %s", e,
//                        JSONObject.toJSONString(jobInstanceQueryRequestVo)));
//                errorRef.set(e);
//                canReturn.set(true);
//
//            } else {
//                allResults.addAll(list);
//
//                if (isAllResultArrived) {
//                    log.info(String.format("执行成功: RequestVo %s", JSONObject.toJSONString(jobInstanceQueryRequestVo)));
//                    canReturn.set(true);
//                }
//            }
//        }
//
//        @Override
//        public void onError(Exception e, JobInstanceQueryRequestVo<?> jobInstanceQueryRequestVo) {
//            log.info(
//                    String.format("执行失败原因:e %s, RequestVo %s", e, JSONObject.toJSONString(jobInstanceQueryRequestVo)));
//            errorRef.set(e);
//
//        }
//
//        @Override
//        public void onJobStatusChange(String status, JobInstanceQueryRequestVo<?> jobInstanceQueryRequestVo) {
//            statusRef.set(status);
//            if (EmJobStatus.isError(status)) {
//                // 出现错误了 可以返回
//                canReturn.set(true);
//            }
//            log.info("SQL queryInfo->" + jobInstanceQueryRequestVo.getJobInstanceId() + ",status->" + status);
//        }
//
//        @Override
//        public void onPollStatus(String s, JobInstanceQueryRequestVo<?> jobInstanceQueryRequestVo) {
//
//        }
//    }




}
