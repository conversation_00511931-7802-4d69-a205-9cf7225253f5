package com.kuaishou.kwaishop.qa.risk.center.db.query.entity;

import java.util.Collection;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-09
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EntityQueryCondition extends BaseQueryCondition {
    /**
     * 实体类型
     */
    private Integer entityType;

    /**
     * 实体id
     */
    private Long entityId;

    /**
     * 实体名
     */
    private String nameLike;

    /**
     * 实体名
     */
    private String nameEq;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 负责人
     */
    private String leader;

    /**
     * 名称S
     */
    private Collection<String> names;

    /**
     * 实体ids
     */
    private Collection<Long> entityIds;

    /**
     * 负责人s
     */
    private Collection<String> leaders;

    /**
     * 审批单团队id
     */
    private String approveTeamId;

}
