package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.github.phantomthief.scope.Scope;
import com.google.common.base.Joiner;
import com.kuaishou.framework.scope.TraceContextUtil;
import com.kuaishou.kconf.common.json.JsonMapperUtils;
import com.kuaishou.krpc.config.ReferenceConfig;
import com.kuaishou.krpc.config.annotation.KrpcReference;
import com.kuaishou.krpc.metadata.KrpcCallOptions;
import com.kuaishou.kwaishop.apollo.policy.center.protobuf.policy.ActionDto;
import com.kuaishou.kwaishop.apollo.policy.center.protobuf.policy.KrpcKwaishopApolloPolicyServiceGrpc.IKwaishopApolloPolicyService;
import com.kuaishou.kwaishop.apollo.policy.center.protobuf.policy.PolicyRequest;
import com.kuaishou.kwaishop.apollo.policy.center.protobuf.policy.PolicyResponse;
import com.kuaishou.kwaishop.apollo.policy.center.protobuf.policy.PolicyTreeResponse;
import com.kuaishou.kwaishop.apollo.strategy.center.client.client.KwaishopApolloPolicyClient;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.IpPortDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.PolicyTestResponse;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTestDataPool;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.PolicyRpcService;
import com.kuaishou.protobuf.trace.TraceContext;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-28
 */
@Service
@Slf4j
public class PolicyRpcServiceImpl implements PolicyRpcService {

    @Resource
    private KwaishopApolloPolicyClient kwaishopApolloPolicyClient;

    @KrpcReference(serviceName = "kwaishop-apollo-strategy-center")
    private IKwaishopApolloPolicyService kwaishopApolloPolicyService;

    @Override
    public PolicyTestResponse policyTree(StrategyTestDataPool strategyTestDataPool, String laneId) {

        final PolicyTestResponse[] resultHolder = new PolicyTestResponse[1];

        try {
            String tmp = laneId.isEmpty() ? "PRT.test" : laneId;
            log.info("landId = {}", tmp);
            Scope.runWithExistScope(new Scope(), () -> {
                TraceContext traceContext = TraceContext.newBuilder().setLaneId(tmp).build();
                TraceContextUtil.initTraceContext(traceContext);
                // 在这个 scope 内里调用业务逻辑就发到泳道上了
                // 比如发起的 grpc 请求
                //                PolicyForTrafficReplayRequest request = PolicyForTrafficReplayRequest.newBuilder()
                //                        .setSceneKey(strategyTestDataPool.getSceneKey())
                //                        .setOriginalLogDto(strategyTestDataPool.getFeatureContext())
                //                        .build();
                //
                //                log.info("request = {}", GsonUtil.toJson(request));
                //                PolicyForTrafficReplayResponse policyTreeResponse = kwaishopApolloPolicyClient
                //                .policyForTrafficReplay(request,
                //                        1000, TimeUnit.MILLISECONDS, "strategy_test");

                PolicyTreeResponse policyTreeResponse = kwaishopApolloPolicyClient.policyTree(
                        PolicyRequest.newBuilder().setSceneKey(strategyTestDataPool.getSceneKey())
                                .setParamJson(strategyTestDataPool.getFeatureContext()).build(), 1000,
                        TimeUnit.MILLISECONDS, "strategy_test");

                log.info("policyTreeResponse:{}", policyTreeResponse);
                // 组装结果并放入数组
                resultHolder[0] = PolicyTestResponse.builder().id(strategyTestDataPool.getId()).actionIds(
                        policyTreeResponse.getActionsList().stream().map(ActionDto::getActionId)
                                .collect(Collectors.toList())).strategyIds(
                        policyTreeResponse.getStrategyIdList().stream().map(String::valueOf)
                                .collect(Collectors.toList())).build();
            });
            // 出了 scope 就不会发到泳道上

            //TODO 对结果进行处理，组装成我要的结果。

        } catch (Exception e) {
            log.error("policyTree error strategyTestDataPool = {}", strategyTestDataPool.getFeatureContext(), e);
            // 如果发生异常，返回一个空的 PolicyTestResponse
            resultHolder[0] = PolicyTestResponse.builder().id(strategyTestDataPool.getId()).actionIds(new ArrayList<>())
                    .strategyIds(new ArrayList<>()).build();
        }

        return resultHolder[0]; // 返回结果
    }

    @Override
    public String policy(StrategyTestDataPool strategyTestDataPool) {
        PolicyResponse policyResponse = kwaishopApolloPolicyClient.policy(
                PolicyRequest.newBuilder().setParamJson(strategyTestDataPool.getFeatureContext())
                        .setSceneKey(strategyTestDataPool.getSceneKey()).build(), 1000, TimeUnit.MILLISECONDS);
        //TODO 对结果进行处理，组装成我要的结果。
        return policyResponse.toString();
    }

    @Override
    public String strategyTreeDetail(String sceneKey) {


        //        KessRun kessRun = KessRun.builder()
        //                .jsonData()
        //                .rpcName(KWAISHOP_APOLLO_PINOCCHIO_CENTER)
        //                .rpcMethod(GET_ALL_SCORE_FOR_BUSINESS)
        //                .build();
        //        try {
        //            String run = KessUtil.run(kessRun);
        //        }
        return null;
    }


    @Override
    public String policyTrafficByLandId(String laneId, String sceneKey, String paramJson) {
        final PolicyTreeResponse[] policyTreeResponse = {null};
        Scope.runWithExistScope(new Scope(), () -> {
            TraceContext traceContext = TraceContext.newBuilder().setLaneId(laneId).build();
            TraceContextUtil.initTraceContext(traceContext);
            PolicyRequest request = PolicyRequest.newBuilder().setSceneKey(sceneKey)
                    .setParamJson(paramJson).build();
            policyTreeResponse[0] = kwaishopApolloPolicyService.policyTree(request);
        });
        return JsonMapperUtils.toJson(policyTreeResponse);
    }

    @Override
    public String policyTrafficByGroup(String group, String sceneKey, String paramJson) {
        PolicyRequest request = PolicyRequest.newBuilder().setSceneKey(sceneKey)
                .setParamJson(paramJson).build();
        KrpcCallOptions krpcCallOptions = KrpcCallOptions.newBuilder().group(group).build();
        PolicyTreeResponse policyTreeResponse = kwaishopApolloPolicyService.policyTree(request, krpcCallOptions);
        return JsonMapperUtils.toJson(policyTreeResponse);
    }

    @Override
    public String policyTrafficByIpPort(String ip, int port, String sceneKey, String paramJson) {
        IKwaishopApolloPolicyService apiClient = ReferenceConfig.<IKwaishopApolloPolicyService> newBuilder()
                .serviceNames(Collections.singletonList("kwaishop-apollo-strategy-center")) // 请求的服务名称，与Kess上注册的名称完全一致
                .interfaceName(IKwaishopApolloPolicyService.class.getName()).protocol("grpc")
                .directUrl("protocol://" + ip + ":" + port) // 指定接口
                .build().refer();

        PolicyRequest request = PolicyRequest.newBuilder().setSceneKey(sceneKey)
                .setParamJson(paramJson).build();
        log.info("PolicyRpcServiceImpl.policyTrafficByIpPort.request:{}", JsonMapperUtils.toJson(request));
        PolicyTreeResponse policyTreeResponse = apiClient.policyTree(request);
        log.info("PolicyRpcServiceImpl.policyTrafficByIpPort.response:{}", JsonMapperUtils.toJson(policyTreeResponse));
        return JsonMapperUtils.toJson(policyTreeResponse);
    }

    @Override
    public String policyTrafficByIpPort(List<IpPortDto> list, String sceneKey, String paramJson) {
        List<String> urlStr =
                list.stream().map(x -> "grpc://" + x.getIp() + ":" + x.getPort()).collect(Collectors.toList());
        log.info("PolicyRpcServiceImpl.policyTrafficByIpPort.urlStr:{}", JsonMapperUtils.toJson(urlStr));

        String join = Joiner.on(";").join(urlStr);
        log.info("PolicyRpcServiceImpl.policyTrafficByIpPort.join:{}", join);
        IKwaishopApolloPolicyService apiClient = ReferenceConfig.<IKwaishopApolloPolicyService> newBuilder()
                .serviceNames(Collections.singletonList("kwaishop-apollo-strategy-center")) // 请求的服务名称，与Kess上注册的名称完全一致
                .interfaceName(IKwaishopApolloPolicyService.class.getName()).protocol("grpc")
                .directUrl(join) // 指定接口
                .build().refer();

        PolicyRequest request = PolicyRequest.newBuilder().setSceneKey(sceneKey)
                .setParamJson(paramJson).build();
        log.info("PolicyRpcServiceImpl.policyTrafficByIpPort.request:{}", JsonMapperUtils.toJson(request));
        PolicyTreeResponse policyTreeResponse = apiClient.policyTree(request);
        log.info("PolicyRpcServiceImpl.policyTrafficByIpPort.response:{}", JsonMapperUtils.toJson(policyTreeResponse));
        return JsonMapperUtils.toJson(policyTreeResponse);
    }
}
