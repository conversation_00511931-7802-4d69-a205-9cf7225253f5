package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.common.constants.FeatureViewStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.KspayRiskKatService;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.bo.RiskKatResponseBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.AutoTestCaseInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.DbAssert;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KrpcKspayRiskKatServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayRiskKatQueryRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayRiskKatQueryResponse;

import lombok.extern.slf4j.Slf4j;
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class KspayRiskKatRpcImpl extends KrpcKspayRiskKatServiceGrpc.KspayRiskKatServiceImplBaseV2 {
    @Autowired
    private KspayRiskKatService kspayRiskKatService;

    @Override
    public KspayRiskKatQueryResponse kspayGetKatAutoTest(KspayRiskKatQueryRequest request) {
        try {
            FundsRiskFeatureDO fundsRiskFeatureDO = new FundsRiskFeatureDO();
            List<RiskKatResponseBO> riskKatResponseBOS = kspayRiskKatService.handleKatData(fundsRiskFeatureDO,
                    request.getType().equals("1") ? FeatureViewStatusEnum.FEATURE_STATUS_TESTING
                            : FeatureViewStatusEnum.FEATURE_STATUS_INTEGRATING,
                    request.getFeatureId(), request.getStagingLaneId());

            if (riskKatResponseBOS == null || riskKatResponseBOS.isEmpty()) {
                return KspayRiskKatQueryResponse.newBuilder()
                        .setStatus(String.valueOf(ErrorCode.BasicErrorCode.SERVER_ERROR))
                        .setMessage("riskKatResponseBOS is empty or null")
                        .build();
            }

            KspayRiskKatQueryResponse response = convertToKspayRiskKatQueryResponse(riskKatResponseBOS);
            return response;
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayGetKatAutoTest bizError, request: {}, exception: ", request, e);
            return KspayRiskKatQueryResponse.newBuilder()
                    .setStatus(String.valueOf(e.getCode()))
                    .setMessage(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayGetKatAutoTest error, request: {}, exception: ", request, e);
            return KspayRiskKatQueryResponse.newBuilder()
                    .setStatus(String.valueOf(ErrorCode.BasicErrorCode.SERVER_ERROR))
                    .setMessage(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    public static KspayRiskKatQueryResponse convertToKspayRiskKatQueryResponse(List<RiskKatResponseBO> responseBOS) {
        if (responseBOS == null || responseBOS.isEmpty()) {
            return KspayRiskKatQueryResponse.newBuilder()
                    .setStatus(String.valueOf(ErrorCode.BasicErrorCode.SERVER_ERROR))
                    .setMessage("responseBOS is empty or null")
                    .build();
        }

        KspayRiskKatQueryResponse.Builder responseBuilder = KspayRiskKatQueryResponse.newBuilder();

        for (RiskKatResponseBO responseBO : responseBOS) {
            responseBuilder.setStatus(responseBO.getStatus())
                    .setMessage(responseBO.getMessage());

            if (responseBO.getData() != null) {
                List<AutoTestCaseInfo> autoTestCaseInfos = responseBO.getData().stream()
                        .map(riskAutoCaseBO -> {
                            AutoTestCaseInfo.Builder testCaseBuilder = AutoTestCaseInfo.newBuilder()
                                    .setPackageName(riskAutoCaseBO.getPackageName())
                                    .setClassName(riskAutoCaseBO.getClassName())
                                    .setMethodName(riskAutoCaseBO.getMethodName())
                                    .setDesc(riskAutoCaseBO.getDesc());

                            Map<String, List<String>> dbAssertMap = riskAutoCaseBO.getDbAssert();
                            if (dbAssertMap != null) {
                                List<DbAssert> dbAssertList = dbAssertMap.entrySet().stream()
                                        .map(entry -> {
                                            DbAssert.Builder dbAssertBuilder = DbAssert.newBuilder()
                                                    .setTableName(entry.getKey())
                                                    .addAllColumnName(entry.getValue());
                                            return dbAssertBuilder.build();
                                        })
                                        .collect(Collectors.toList());
                                testCaseBuilder.addAllDbAssert(dbAssertList);
                            }

                            return testCaseBuilder.build();
                        })
                        .collect(Collectors.toList());

                responseBuilder.addAllData(autoTestCaseInfos);
            }
        }

        return responseBuilder.build();
    }
}