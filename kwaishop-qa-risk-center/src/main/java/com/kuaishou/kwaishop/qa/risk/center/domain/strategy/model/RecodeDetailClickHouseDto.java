package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert.Column;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-30
 */
@Data
public class RecodeDetailClickHouseDto {

    @Column(name = "id")
    private String id;

    @Column(name = "flowRecordId")
    private Long flowRecordId;

    @Column(name = "sceneKey")
    private String sceneKey;

    @Column(name = "recordCreateTime")
    private Long recordCreateTime;

    @Column(name = "recordEndTime")
    private Long recordEndTime;

    @Column(name = "recordPDate")
    private String recordPDate;

    @Column(name = "bizKeyInfo")
    private String bizKeyInfo;

    @Column(name = "hitStrategyList")
    private String hitStrategyList;

    @Column(name = "hitActionList")
    private String hitActionList;

    @Column(name = "createTime")
    private Long createTime;

    @Column(name = "sceneType")
    private String sceneType;

    @Column(name = "traceId")
    private String traceId;

    @Column(name = "requestHitResult")
    private String requestHitResult;

    @Column(name = "featureKeyMap")
    private String featureKeyMap;
}
