package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.biz.BaseEnumBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.biz.FaultCaseCombineBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.biz.RiskInfoBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfoDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfoResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseCombineDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseCombineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseCombineResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseLevelCombineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseLevelCombineResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseProblemCombineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseProblemCombineResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCombineProblemInfoDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultDetailsDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultDetailsGroupByMemberRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultDetailsGroupByMemberResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.KrpcCombineDomainServiceGrpc.CombineDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.QueryGroupByRiskSummaryDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.QueryGroupByRiskSummaryDataResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.QueryRiskSummaryDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.QueryRiskSummaryDataResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.RiskSummaryDataDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.EntityDataDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-23
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class CombineDomainServiceImpl extends CombineDomainServiceImplBaseV2 {

    @Autowired
    private RiskInfoBizService riskInfoBizService;

    @Autowired
    private BaseEnumBizService enumBizService;

    @Autowired
    private FaultCaseCombineBizService combineBizService;


    @Override
    public QueryRiskSummaryDataResponse queryRiskSummaryData(QueryRiskSummaryDataRequest request) {
        log.info("[CombineDomainServiceImpl] queryRiskSummaryData request: {}", protoToJsonString(request));
        try {
            RiskSummaryDataDTO res = riskInfoBizService.queryRiskSummaryData(request);
            return QueryRiskSummaryDataResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(res)
                    .build();
        } catch (BizException e) {
            log.error("[CombineDomainServiceImpl] queryRiskSummaryData bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryRiskSummaryDataResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[CombineDomainServiceImpl] queryRiskSummaryData bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryRiskSummaryDataResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryGroupByRiskSummaryDataResponse queryGroupByRiskSummaryData(QueryGroupByRiskSummaryDataRequest request) {
        log.info("[CombineDomainServiceImpl] queryGroupByRiskSummaryData request: {}", protoToJsonString(request));
        try {
            List<RiskSummaryDataDTO> res = riskInfoBizService.queryGroupByRiskSummaryData(request);
            return QueryGroupByRiskSummaryDataResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(res)
                    .build();
        } catch (BizException e) {
            log.error("[CombineDomainServiceImpl] queryGroupByRiskSummaryData bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryGroupByRiskSummaryDataResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[CombineDomainServiceImpl] queryGroupByRiskSummaryData bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryGroupByRiskSummaryDataResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public EnumInfoResponse getEnumInfo(EnumInfoRequest request) {
        log.info("[CombineDomainServiceImpl] getEnumInfo request: {}", protoToJsonString(request));
        try {
            EnumInfoDTO res = enumBizService.getEnumInfo(request);
            return EnumInfoResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(res)
                    .build();
        } catch (BizException e) {
            log.error("[CombineDomainServiceImpl] getEnumInfo bizError, req: {}, exception: ", protoToJsonString(request), e);
            return EnumInfoResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[CombineDomainServiceImpl] getEnumInfo bizError, req: {}, exception: ", protoToJsonString(request), e);
            return EnumInfoResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public FaultCaseCombineResponse faultCaseCombine(FaultCaseCombineRequest request) {
        log.info("[CombineDomainServiceImpl] faultCaseCombine request: {}", protoToJsonString(request));
        try {
            FaultCaseCombineDTO res = combineBizService.getFaultCaseCombine(request);
            return FaultCaseCombineResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(res)
                    .build();
        } catch (BizException e) {
            log.error("[CombineDomainServiceImpl] faultCaseCombine bizError, req: {}, exception: ", protoToJsonString(request), e);
            return FaultCaseCombineResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[CombineDomainServiceImpl] faultCaseCombine bizError, req: {}, exception: ", protoToJsonString(request), e);
            return FaultCaseCombineResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public FaultCaseProblemCombineResponse faultCaseProblemCombine(FaultCaseProblemCombineRequest request) {
        log.info("[CombineDomainServiceImpl] faultCaseProblemCombine request: {}", protoToJsonString(request));
        try {
            List<FaultCombineProblemInfoDTO> res = combineBizService.getCaseProblemCombine(request);
            return FaultCaseProblemCombineResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(res)
                    .build();
        } catch (BizException e) {
            log.error("[CombineDomainServiceImpl] faultCaseProblemCombine bizError, req: {}, exception: ", protoToJsonString(request), e);
            return FaultCaseProblemCombineResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[CombineDomainServiceImpl] faultCaseProblemCombine bizError, req: {}, exception: ", protoToJsonString(request), e);
            return FaultCaseProblemCombineResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public FaultDetailsGroupByMemberResponse faultDetailsGroupByMember(FaultDetailsGroupByMemberRequest request) {
        log.info("[CombineDomainServiceImpl] getFaultDetailsGroupByMember request: {}", protoToJsonString(request));
        try {
            List<FaultDetailsDTO> res = combineBizService.getFaultDetailsGroupByMember(request);
            return FaultDetailsGroupByMemberResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(res)
                    .build();
        } catch (BizException e) {
            log.error("[CombineDomainServiceImpl] getFaultDetailsGroupByMember bizError, req: {}, exception: ", protoToJsonString(request), e);
            return FaultDetailsGroupByMemberResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[CombineDomainServiceImpl] getFaultDetailsGroupByMember bizError, req: {}, exception: ", protoToJsonString(request), e);
            return FaultDetailsGroupByMemberResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public FaultCaseLevelCombineResponse faultCaseLevelCombine(FaultCaseLevelCombineRequest request) {
        log.info("[CombineDomainServiceImpl] faultCaseLevelCombine request: {}", protoToJsonString(request));
        try {
            List<EntityDataDTO> res = combineBizService.getCaseLevelCombine(request);
            return FaultCaseLevelCombineResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(res)
                    .build();
        } catch (BizException e) {
            log.error("[CombineDomainServiceImpl] faultCaseLevelCombine bizError, req: {}, exception: ", protoToJsonString(request), e);
            return FaultCaseLevelCombineResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[CombineDomainServiceImpl] faultCaseLevelCombine bizError, req: {}, exception: ", protoToJsonString(request), e);
            return FaultCaseLevelCombineResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }
}
