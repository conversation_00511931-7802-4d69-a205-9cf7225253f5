package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.common.biz.CommonLogBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.common.FrontLogRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.common.FrontLogResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.common.KrpcLogDomainServiceGrpc.LogDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.common.LogDataDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.common.LogDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.common.LogDataResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-28
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class LogDomainServiceImpl extends LogDomainServiceImplBaseV2 {

    @Autowired
    private CommonLogBizService logBizService;

    @Override
    public FrontLogResponse frontLog(FrontLogRequest request) {
        log.info("[LogDomainServiceImpl] frontLog request: {}", protoToJsonString(request));
        try {
            logBizService.createFrontLog(request);
            return FrontLogResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[LogDomainServiceImpl] frontLog bizError, req: {}, exception: ", protoToJsonString(request), e);
            return FrontLogResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[LogDomainServiceImpl] frontLog error, req: {}, exception: ", protoToJsonString(request), e);
            return FrontLogResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public LogDataResponse getLogData(LogDataRequest request) {
        log.info("[LogDomainServiceImpl] getLogData request: {}", protoToJsonString(request));
        try {
            List<LogDataDTO> res = logBizService.getLogData(request);
            return LogDataResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(res)
                    .build();
        } catch (BizException e) {
            log.error("[LogDomainServiceImpl] getLogData bizError, req: {}, exception: ", protoToJsonString(request), e);
            return LogDataResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[LogDomainServiceImpl] getLogData error, req: {}, exception: ", protoToJsonString(request), e);
            return LogDataResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }
}
