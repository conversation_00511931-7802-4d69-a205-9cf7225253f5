package com.kuaishou.kwaishop.qa.risk.center.domain.openapi.service.impl;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.domain.openapi.model.bo.RequestSendTemplateMessageBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.openapi.model.bo.ResponseSendTemplateMessageBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.openapi.service.KimMessageSendService;
import com.kuaishou.kwaishop.qa.risk.center.domain.openapi.service.OpenApi;


@Service
public class KimMessageSendServiceImpl implements KimMessageSendService {

    @Autowired
    private OpenApi openApi;

    @Override
    public ResponseSendTemplateMessageBO sendTemplateMessageV2(RequestSendTemplateMessageBO request) throws IOException {
        return openApi.sendTemplateMessageV2(request).execute().body();
    }

}
