package com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-02
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ProblemQueryParam extends CombineQueryParam {

    private Long problemStartTimeGe;

    private Long problemStartTimeLe;
}
