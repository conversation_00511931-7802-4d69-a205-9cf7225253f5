package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.detail.KspayDetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsRiskFeatureViewDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-01
 */
public interface FundsRiskFeatureViewDAO extends KspayDetailDAO<FundsRiskFeatureViewDO, CombineQueryParam> {
    long insert(FundsRiskFeatureViewDO fundsRiskFeatureViewDO);
    int updateSelectiveById(FundsRiskFeatureViewDO fundsRiskFeatureViewDO);
//    int updateFeatureViewNoUpdateTime(FundsRiskFeatureViewDO fundsRiskFeatureViewDO);
    int updateSelectiveByIdNoTime(FundsRiskFeatureViewDO fundsRiskFeatureViewDO);

    FundsRiskFeatureViewDO queryById(Long id);
}
