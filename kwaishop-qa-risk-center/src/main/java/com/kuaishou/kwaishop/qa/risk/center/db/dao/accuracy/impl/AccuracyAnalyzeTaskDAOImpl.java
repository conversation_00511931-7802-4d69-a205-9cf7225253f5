package com.kuaishou.kwaishop.qa.risk.center.db.dao.accuracy.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskJsonMapConfigKey.faultTeamApplicationList;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.accuracy.AccuracyAnalyzeTaskDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.AccuracyBaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.accuracy.AccuracyAnalyzeTaskDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.accuracy.AccuracyAnalyzeTaskMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.accuracy.AccuracyAnalyzeTaskQueryCondition;

import lombok.extern.slf4j.Slf4j;


@Repository
@Slf4j
public class AccuracyAnalyzeTaskDAOImpl extends AccuracyBaseDAOImpl<AccuracyAnalyzeTaskDO, AccuracyAnalyzeTaskQueryCondition>
        implements AccuracyAnalyzeTaskDAO {

    @Autowired
    private AccuracyAnalyzeTaskMapper accuracyAnalyzeTaskMapper;

    @Override
    public AccuracyAnalyzeTaskDO queryAccuracyAnalyzeTaskById(Long id) {

        return queryById(id);
    }

    @Override
    public List<AccuracyAnalyzeTaskDO> getAccuracyAnalyzeTaskByTime(Long startTime, Long endTime) {
        List<String> applications = new ArrayList<>();
        Map<String, List<String>> teamApplicationMap = faultTeamApplicationList.getMap();
        teamApplicationMap.forEach(
                (k, v) -> {
                    applications.addAll(v);
                }
        );
        AccuracyAnalyzeTaskQueryCondition condition = AccuracyAnalyzeTaskQueryCondition
                .builder().startTimeGe(startTime)
                .endTimeLe(endTime)
                .applications(applications)
                .type("branch")
                .status(7)
                .build();
        return queryList(condition);
    }

    @Override
    protected void fillQueryCondition(AccuracyAnalyzeTaskQueryCondition condition, QueryWrapper<AccuracyAnalyzeTaskDO> queryWrapper) {

        if (condition.getStartTimeGe() != null && condition.getStartTimeGe() > 0) {
            queryWrapper.and(q -> q.ge("gmt_created", condition.getStartTimeGe()));
        }
        if (condition.getEndTimeLe() != null && condition.getEndTimeLe() > 0) {
            queryWrapper.and(q -> q.le("gmt_created", condition.getEndTimeLe()));
        }
        if (CollectionUtils.isNotEmpty(condition.getApplications())) {
            queryWrapper.and(q -> q.in("application", condition.getApplications()));
        }
        if (condition.getType() != null && StringUtils.isNotBlank(condition.getType())) {
            queryWrapper.and(q -> q.eq("type", condition.getType()));
        }

    }

    @Override
    protected BaseMapper<AccuracyAnalyzeTaskDO> getMapper() {
        return accuracyAnalyzeTaskMapper;
    }

}
