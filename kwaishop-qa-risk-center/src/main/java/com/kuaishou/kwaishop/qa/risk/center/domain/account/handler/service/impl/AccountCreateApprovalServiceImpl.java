package com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_NOT_TEST_TYPE;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ID_NOT_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.PASSWORD_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.ApplyHandler.ADD_PROCESS_KEY;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.infra.passport.internal.sdk.common.PassportResponse;
import com.kuaishou.infra.passport.internal.sdk.service.PassportUserService;
import com.kuaishou.krpc.config.annotation.KrpcReference;
import com.kuaishou.kwaishop.biz.account.protobuf.AddTestAccountRequest;
import com.kuaishou.kwaishop.biz.account.protobuf.KrpcKwaishopTestAccountManageServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.UserAccountDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.AccountTagManageBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.service.ApprovalService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.DPMApprovalParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.ApprovalBizCodeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.vo.ApprovalEntityDataVO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.CreateTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.StartApprovalRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2025/4/9 15:34
 * @注释
 */
@Slf4j
@Service
@Lazy
public class AccountCreateApprovalServiceImpl implements ApprovalService {

    @Autowired
    private AccountTagManageBizService accountTagManageBizService;
    @Autowired
    private UserAccountDAO userAccountDAO;
    @Autowired
    private PassportUserService passportUserService;
    @KrpcReference(serviceName = "kwaishop-biz-account-center")
    private KrpcKwaishopTestAccountManageServiceGrpc.IKwaishopTestAccountManageService testAccountManageService;

    @Override
    public String getCode() {
        return ApprovalBizCodeEnum.ACCOUNT_ADD.getCode();
    }

    @Override
    public ApprovalEntityDataVO approvalForm(StartApprovalRequest request, String code, DPMApprovalParam param) {
        ApprovalEntityDataVO vo = new ApprovalEntityDataVO();
        String jsonString = "[{\"fieldName\":\"申请说明\",\"fieldKey\":\"apply_explain\",\"fieldValue\":{\"desc\":\"账号执行申请\""
                + ",\"userId\":\"%s\",\"申请人\":\"%s\"}}]";
        String businessSummary = String.format(jsonString, request.getUserId(), request.getUserName());
        String businessId = "account_test" + System.currentTimeMillis();
        vo.setBusinessExplain(businessId);
        vo.setBusinessId(businessId);
        vo.setFormType(ADD_PROCESS_KEY);
        vo.setProcessKey(ADD_PROCESS_KEY);
        vo.setProcessDefinitionKey(ADD_PROCESS_KEY);

        param.setBusinessSummary(businessSummary);
        if (request.getUserId() > 0) {
            vo.setUserId(String.valueOf(request.getUserId()));
            param.setUserId(String.valueOf(request.getUserId()));
        }
        param.setLoginType(request.getLoginType());
        param.setAccount(request.getAccount());
        param.setPassword(request.getPassword());
        vo.setProcessVariables(param);
        vo.setUsername(request.getUserName());
        vo.setBusinessSummary(businessSummary);
        return vo;
    }

    @Override
    public void execute(Map<String, Object> variables) {
        try {
            log.info("执行账号添加操作");
            CreateTestAccountRequest createTestAccountRequest = CreateTestAccountRequest.newBuilder()
                    .setOperator(variables.get("userName").toString())
                    .setCenterId(0)
                    .setTeamId(0)
                    .setBid(0)
                    .setAccount(variables.get("account").toString())
                    .setPassword(variables.get("password").toString())
                    .setLoginType(Integer.parseInt(variables.get("loginType").toString()))
                    .setUserId(Long.parseLong(variables.get("userId").toString()))
                    .build();
            createAccountCheck(createTestAccountRequest);
            //这里主要是同步到工作台账号kconf里
            AddTestAccountRequest addTestAccountRequest = AddTestAccountRequest.newBuilder()
                    .addAccounts(Long.parseLong(variables.get("userId").toString()))
                    .setSubjectType("SELLER")
                    .setOperator(variables.get("userName").toString())
                    .build();
            testAccountManageService.addTestAccount(addTestAccountRequest);
        } catch (Exception e) {
            log.info("bpm处理账号添加失败:{}", ObjectMapperUtils.toJSON(variables));
            e.printStackTrace();
        }
    }

    //同PRT环境校验逻辑
    private void createAccountCheck(CreateTestAccountRequest request) {
        if (StringUtils.isBlank(request.getAccount())) {
            throw new BizException(ACCOUNT_EMPTY_ERROR);
        }
        if (StringUtils.isBlank(request.getPassword())) {
            throw new BizException(PASSWORD_EMPTY_ERROR);
        }
        if (request.getBid() <= 0 && request.getUserId() <= 0) {
            throw new BizException(ID_NOT_EMPTY_ERROR);
        }
        UserAccountDO existDO = userAccountDAO.queryByUid(request.getUserId());
        PassportResponse<Boolean> ksTestAccount = passportUserService.isKsTestAccount(request.getUserId());
        if (ksTestAccount.isSuccess() && !ksTestAccount.getResult()) {
            if (existDO == null) {
                throw new BizException(ACCOUNT_NOT_TEST_TYPE);
            }
        }
    }
}
