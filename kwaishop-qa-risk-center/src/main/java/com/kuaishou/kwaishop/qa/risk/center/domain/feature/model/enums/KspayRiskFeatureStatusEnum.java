package com.kuaishou.kwaishop.qa.risk.center.domain.feature.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

public enum KspayRiskFeatureStatusEnum {
    UNKNOWN(0, "未知状态"),
    NORMAL(1, "已打标"),
    DEFAULT(2, "未打标"),
    ;

    private final Integer code;

    private final String desc;

    KspayRiskFeatureStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static KspayRiskFeatureStatusEnum of(Integer code) {
        for (KspayRiskFeatureStatusEnum kspayRiskFeatureStatusEnum: values()) {
            if (kspayRiskFeatureStatusEnum.getCode().equals(code) && kspayRiskFeatureStatusEnum.getCode() > 0) {
                return kspayRiskFeatureStatusEnum;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo() {
        List<EnumInfo> res = new ArrayList<>();
        for (KspayRiskFeatureStatusEnum typeEnum: values()) {
            if (typeEnum.getCode() > 0) {
                EnumInfo enumInfo = EnumInfo.newBuilder()
                        .setValue(typeEnum.getCode())
                        .setDesc(typeEnum.getDesc())
                        .build();
                res.add(enumInfo);
            }
        }
        return res;
    }
}
