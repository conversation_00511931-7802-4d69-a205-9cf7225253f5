package com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.GitToDepartmentMappingDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.GitToDepartmentMappingParamDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.feature.GitToDepartmentMappingMapper;

import lombok.extern.slf4j.Slf4j;

@Repository
@Service
@Lazy
@Slf4j
public class GitToDepartmentMappingDAOImpl implements GitToDepartmentMappingDAO {
    @Autowired
    private GitToDepartmentMappingMapper gitToDepartmentMappingMapper;

    @Override
    public GitToDepartmentMappingParamDO queryMappingByProjectId(Long gitProjectId) {
        return gitToDepartmentMappingMapper.queryMappingByProjectId(gitProjectId);
    }

    @Override
    public void insertOrUpdateMapping(GitToDepartmentMappingParamDO gitToDepartmentMappingParamDO) {
        gitToDepartmentMappingMapper.insertOrUpdateMapping(gitToDepartmentMappingParamDO);
    }

    @Override
    public void insertMapping(GitToDepartmentMappingParamDO gitToDepartmentMappingParamDO) {
        gitToDepartmentMappingMapper.insertMapping(gitToDepartmentMappingParamDO);
    }

    @Override
    public void updateMapping(GitToDepartmentMappingParamDO gitToDepartmentMappingParamDO) {
        gitToDepartmentMappingMapper.updateMapping(gitToDepartmentMappingParamDO);
    }

}