package com.kuaishou.kwaishop.qa.risk.center.db.query.account;


import java.math.BigDecimal;
import java.util.Collection;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-23
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class UserDumpCondition extends BaseQueryCondition {
    private Long id;
    private Long userId;
    private String storeName;
    private Integer storeType;
    private Integer isSeller;
    private BigDecimal deposit;
    private Integer merchantPermission;
    private Integer distributorPermission;
    private Integer celebrityPermission;
    private Integer recruitingLeader;
    private String comment;
    private Collection<Long> userIds;
}
