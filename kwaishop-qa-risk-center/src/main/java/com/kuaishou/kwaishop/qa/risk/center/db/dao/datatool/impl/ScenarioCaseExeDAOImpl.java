package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.DataBaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.ScenarioCaseExeDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.ScenarioCaseExeDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.datatool.ScenarioCaseExecuteMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.datatool.ScenarioCaseExeQueryCondition;


/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2023-03-15
 */
@Repository
public class ScenarioCaseExeDAOImpl extends DataBaseDAO<ScenarioCaseExeDO, ScenarioCaseExeQueryCondition> implements ScenarioCaseExeDAO {

    @Autowired
    private ScenarioCaseExecuteMapper scenarioCaseExecuteMapper;

    @Override
    protected void fillQueryCondition(ScenarioCaseExeQueryCondition condition, QueryWrapper<ScenarioCaseExeDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getCreatorName())) {
            queryWrapper.eq("creator_name", condition.getCreatorName());
        }
    }

    @Override
    protected BaseMapper<ScenarioCaseExeDO> getMapper() {
        return scenarioCaseExecuteMapper;
    }

    @Override
    public long insertScenarioCase(ScenarioCaseExeDO scenarioCaseExeDO) {
        scenarioCaseExeDO.setCreateTime(System.currentTimeMillis());
        scenarioCaseExeDO.setUpdateTime(System.currentTimeMillis());
        return scenarioCaseExecuteMapper.insert(scenarioCaseExeDO);
    }

    @Override
    public long updateScenarioCase(ScenarioCaseExeDO scenarioCaseExeDO) {
        return scenarioCaseExecuteMapper.updateById(scenarioCaseExeDO);
    }

    @Override
    public int logicDeleted(Long id, String operator) {
        return scenarioCaseExecuteMapper.logicDeleted(operator, id);
    }

    @Override
    public List<Long> queryCaseExeList(Long scenarioCaseExeId) {
        return scenarioCaseExecuteMapper.queryCaseExeIdsByScenarioExeId(scenarioCaseExeId);
    }

    @Override
    public List<ScenarioCaseExeDO> queryScenarioCaseList(ScenarioCaseExeDO scenarioCaseExeDO) {
        return null;
    }

    @Override
    public ScenarioCaseExeDO queryScenarioCaseById(Long id) {
        return null;
    }
}
