package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordListRequestPojo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowRecordDo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-19
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface StrategyFlowRecordMapper {

    @Insert("INSERT INTO strategy_flow_record (name, description, scene_key_list, total, flow_type, flow_status, "
            + "flow_start_time, flow_end_time, create_user, update_user, create_time, update_time, extra, deleted) "
            + "VALUES (#{name}, #{description}, #{sceneKeyList}, #{total}, #{flowType}, #{flowStatus}, "
            + "#{flowStartTime}, #{flowEndTime}, #{createUser}, #{updateUser}, #{createTime}, #{updateTime}, "
            + "#{extra}, #{deleted})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    Long addStrategyFlowRecord(StrategyFlowRecordDo flowRecordDo);

    @Select({"<script>", "select * from strategy_flow_record where 1 = 1", "<if test='id != null and id != 0'>",
            "  and id = #{id}", "</if> ", "<if test='name != null and name != \"\" '>",
            "  and `name` like concat('%', #{name}, '%')", "</if>", "<if test='sceneKey != null and sceneKey != \"\"'>",
            "  and scene_key_list like concat('%', #{sceneKey}, '%')", "</if>",
            "<if test='flowType != null and flowType != 0'>", "  and flow_type = #{flowType}", "</if>",
            "<if test='flowStatus != null and flowStatus != 0'>", "  and flow_status = #{flowStatus}", "</if>",
            "<if test='flowStartTimeStart != null and flowStartTimeEnd != 0 and flowStartTimeStart != null and  "
                    + "flowStartTimeEnd != 0'>",
            "  and (flow_start_time <![CDATA[ >= ]]> #{flowStartTimeStart} and  flow_start_time <![CDATA[ <= ]]> "
                    + "#{flowStartTimeStart})", "</if>", "<if test='createUser != null and createUser!=\"\"'>",
            "  and create_user = #{createUser}", "</if>", " order by id desc limit #{offset}, #{limit}  ", "</script>"})
    List<StrategyFlowRecordDo> strategyFlowRecordList(FlowRecordListRequestPojo requestPojo);

    @Select({"<script>", "select count(*) from strategy_flow_record where 1 = 1", "<if test='id != null and id != 0'>",
            "  and id = #{id}", "</if> ", "<if test='name != null and name != \"\" '>",
            "  and `name` like concat('%', #{name}, '%')", "</if>", "<if test='sceneKey != null and sceneKey != \"\"'>",
            "  and scene_key_list like concat('%', #{sceneKey}, '%')", "</if>",
            "<if test='flowType != null and flowType != 0'>", "  and flow_type = #{flowType}", "</if>",
            "<if test='flowStatus != null and flowStatus != 0'>", "  and flow_status = #{flowStatus}", "</if>",
            "<if test='flowStartTimeStart != null and flowStartTimeEnd != 0 and flowStartTimeStart != null and  "
                    + "flowStartTimeEnd != 0'>",
            "  and (flow_start_time <![CDATA[ >= ]]> #{flowStartTimeStart} and  flow_start_time <![CDATA[ <= ]]> "
                    + "#{flowStartTimeStart})", "</if>", "<if test='createUser != null and createUser!=\"\"'>",
            "  and create_user = #{createUser}", "</if>", "</script>"})
    int strategyFlowRecordCount(FlowRecordListRequestPojo requestPojo);

    @Select("select * from strategy_flow_record where id = #{id}")
    StrategyFlowRecordDo getFlowRecordById(Long id);

    @Select("select * from strategy_flow_record where flow_type = #{flowType}")
    List<StrategyFlowRecordDo> getFlowRecordByFlowType(int flowType);

    @Update("update strategy_flow_record set extra = #{extra} where id = #{id}")
    void updateExtraById(Long id, String extra);

    @Update("update strategy_flow_record set flow_status = #{flowStatus} where id = #{id}")
    void updateFlowStatusById(Long id, int flowStatus);

    @Update("update strategy_flow_record set flow_status = #{flowStatus}, flow_end_time = #{flowEndTime} where id = #{id}")
    void updateFlowStatusAndFlowEndTimeById(Long id, int flowStatus, long flowEndTime);


}
