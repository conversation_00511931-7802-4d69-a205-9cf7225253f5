package com.kuaishou.kwaishop.qa.risk.center.db.dao.feature;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.KspayPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundRiskIntelligenceFindDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundRiskIntelligenceQueryCondition;

public interface FundRiskIntelligenceFindDAO {
    KspayPageBO<FundRiskIntelligenceFindDO> queryPageRiskIntelligenceFindList(
            FundRiskIntelligenceQueryCondition fundRiskIntelligenceQueryCondition);

    FundRiskIntelligenceFindDO queryRiskFieldsByMainId(String id);

    int updateRiskLevelByMainId(FundRiskIntelligenceFindDO fundRiskIntelligenceFindDO);

}
