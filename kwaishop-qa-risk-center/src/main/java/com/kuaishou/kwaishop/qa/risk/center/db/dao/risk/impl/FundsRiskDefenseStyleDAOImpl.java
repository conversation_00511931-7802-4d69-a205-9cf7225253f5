package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import java.util.Collection;
import java.util.List;

import javax.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.KspayPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.KspayBaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.FundsRiskDefenseStyleDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsRiskDefenseStyleDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.FundsRiskDefenseStyleMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.FundsRiskDefenseStyleQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;

import lombok.extern.slf4j.Slf4j;



/**
 * <AUTHOR>
 * Created on 2022-12-26
 */
@Repository
@Slf4j
public class FundsRiskDefenseStyleDAOImpl extends KspayBaseDAOImpl<FundsRiskDefenseStyleDO, FundsRiskDefenseStyleQueryCondition>
        implements FundsRiskDefenseStyleDAO {
    @Autowired
    private FundsRiskDefenseStyleMapper fundsRiskDefenseStyleMapper;

    @PostConstruct
    public void init() {
        log.info("\n \n hoahfhfusheuhu \n \n");
    }

    @Override
    protected void fillLikeQueryCondition(FundsRiskDefenseStyleQueryCondition condition,
            QueryWrapper<FundsRiskDefenseStyleDO> queryWrapper) {
    }

    @Override
    protected void fillQueryCondition(FundsRiskDefenseStyleQueryCondition condition,
            QueryWrapper<FundsRiskDefenseStyleDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getSubRiskId())) {
            queryWrapper.and(q -> q.eq("sub_risk_id", condition.getSubRiskId()));
        }
        if (StringUtils.isNotBlank(condition.getDefenseType())) {
            queryWrapper.and(q -> q.eq("defense_type", condition.getDefenseType()));
        }
    }

    @Override
    protected BaseMapper<FundsRiskDefenseStyleDO> getMapper() {
        return fundsRiskDefenseStyleMapper;
    }

    @Override
    public List<FundsRiskDefenseStyleDO> queryByCenterIds(Collection<Long> centerIds, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsRiskDefenseStyleDO> queryByTeamIds(Long centerId, Collection<Long> teamIds,
            CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsRiskDefenseStyleDO> queryByCenterId(Long centerId, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsRiskDefenseStyleDO> queryByTeamId(Long centerId, Long teamId, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsRiskDefenseStyleDO> queryByCenterIdsTeamIds(Collection<Long> centerIds, Collection<Long> teamIds,
            CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public KspayPageBO<FundsRiskDefenseStyleDO> queryPageRiskDetailList(
            FundsRiskDefenseStyleQueryCondition fundsRiskDefenseStyleQueryCondition) {
        return queryPageList(fundsRiskDefenseStyleQueryCondition);

    }

    @Override
    public List<FundsRiskDefenseStyleDO> queryRiskDetailList(
            FundsRiskDefenseStyleQueryCondition fundsRiskDefenseStyleQueryCondition) {
        return queryList(fundsRiskDefenseStyleQueryCondition);
    }

    @Override
    public List<FundsRiskDefenseStyleDO> queryAllDefenseInfo(String checkId) {
        return fundsRiskDefenseStyleMapper.queryAllDefenseInfo(checkId);
    }

    @Override
    public FundsRiskDefenseStyleDO queryByDefenseId(String defenseId) {
        return queryById(Long.parseLong(defenseId));
    }

    @Override
    public List<FundsRiskDefenseStyleDO> queryAllBySubRiskId(String subRiskId) {
        return fundsRiskDefenseStyleMapper.queryAllBySubRiskId(subRiskId);
    }


}
