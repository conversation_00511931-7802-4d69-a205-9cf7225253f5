package com.kuaishou.kwaishop.qa.risk.center.db.dao.errorcode;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.errorcode.BizAreaDO;

public interface BizAreaDAO {

    void insert(BizAreaDO bizAreaDO);

    BizAreaDO getById(Long id);

    void updateById(BizAreaDO bizAreaDO);

    void deleteById(Long id);

    BizAreaDO queryByBizArea(String areaCode);

    IPage<BizAreaDO> getBizAreaList(String creator, Long current, Long size);
}
