package com.kuaishou.kwaishop.qa.risk.center.domain.account.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_NOT_EXIST_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.NOT_TEAM_MEMBER;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.RENT_MAX_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.RENT_REPETITION_ERROR;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.RentAccountDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.RentTestAccountRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.TestAccountDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TestAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.UserAccountBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.RentAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.RentalStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.RentAccountService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.biz.TeamExtService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UserAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.service.impl.EntityExtServiceImpl;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-25
 */
@Lazy
@Service
@Slf4j
public class RentAccountServiceImpl implements RentAccountService {

    private static final int MAX_RENTAL_COUNT = 5;
    @Autowired
    private RentAccountDAO rentAccountDAO;
    @Autowired
    private UserAccountBizService userAccountBizService;
    @Autowired
    private EntityExtServiceImpl entityExtService;
    @Autowired
    private RentTestAccountRecordDAO rentTestAccountRecordDAO;
    @Autowired
    private TestAccountDAO testAccountDAO;
    @Autowired
    private TeamExtService teamExtService;

    @Override
    public void rentAccount(RentAccountBO rentAccountBo) {
        //0.先看下是否在当前的团队里。
        List<UserAccountDTO> userAccountDTOS =
                userAccountBizService.queryUserAccountList(QueryUserAccountRequest.newBuilder()
                        .setUserId(rentAccountBo.getUserId())
                        .build());
        if (userAccountDTOS.isEmpty()) {
            throw new BizException(ACCOUNT_NOT_EXIST_ERROR);
        }
        long teamId = userAccountDTOS.get(0).getTeamId();
        // 根据teamId查到对应成员

        List<String> memberInfoList = teamId != 0L ? teamExtService.queryTeamMember(teamId) : null;
        if (memberInfoList != null && !isContainsBorrower(memberInfoList, rentAccountBo.getBorrower())) {
            // 如果不包含有这个
            throw new BizException(NOT_TEAM_MEMBER);
        }
        // 先查下是否已经借过这个账户
        List<RentAccountDO> rentAccountDOS = rentAccountDAO.queryList(RentAccountBO.builder()
                .userId(rentAccountBo.getUserId())
                .rentalStatus(RentalStatusEnum.BORROWED.getCode())
                .borrower(rentAccountBo.getBorrower())
                .build());
        if (!rentAccountDOS.isEmpty()) {
            throw new BizException(RENT_REPETITION_ERROR);
        }
        // 再检查当前有多少人租用过这个账户
        List<RentAccountDO> rentAccountInfos = queryRentRecords(RentAccountBO.builder()
                .userId(rentAccountBo.getUserId())
                .rentalStatus(RentalStatusEnum.BORROWED.getCode())
                .build());

        if (rentAccountInfos.size() >= MAX_RENTAL_COUNT) {
            // 说经已经超过5人在租借了
            throw new BizException(RENT_MAX_ERROR);
        }
        rentAccountDAO.rentAccount(rentAccountBo);
    }

    @Override
    public void rentAccountV2(RentAccountBO rentAccountBO) {
        // 先查下是否已经借过这个账户
        List<TestAccountDO> testAccountDOs;
        if (rentAccountBO.getBId() > 0) {
            testAccountDOs = testAccountDAO.queryByUid(rentAccountBO.getBId());
        } else {
            testAccountDOs = testAccountDAO.queryByUid(rentAccountBO.getUserId());
        }
        if (testAccountDOs.size() > 0) {
            TestAccountDO testAccountDO = testAccountDOs.get(0);
            if (testAccountDO.getStatus().equals(UserStatusEnum.CAN_NOT_APPLY.getCode())) {
                throw new BizException(RENT_MAX_ERROR);
            }
            rentTestAccountRecordDAO.rentAccount(rentAccountBO);
        }


    }

    @Override
    public void returnAccount(RentAccountBO rentAccountBo) {
        //给归还操作赋值
        rentAccountDAO.returnAccount(rentAccountBo);
    }

    @Override
    public List<RentAccountDO> queryRentRecords(RentAccountBO rentAccountBo) {
        return rentAccountDAO.queryList(rentAccountBo);
    }

    @Override
    public PageBO<RentAccountDO> queryPageRentRecords(RentAccountBO queryBO) {
        return rentAccountDAO.queryPageList(queryBO);
    }

    @Override
    public void extendRent(RentAccountBO rentAccountBO) {
        //延长租借
        rentAccountDAO.extendRent(rentAccountBO);
    }

    @Override
    public List<String> queryListByOperator(String borrower) {
        List<String> res = new ArrayList<>();
        List<RentAccountDO> rentAccountDOS = rentAccountDAO.queryListByOperator(borrower);
        rentAccountDOS.forEach(rentAccountDO -> {
            res.add(rentAccountDO.getAccount());
        });
        return res;
    }

    public boolean isContainsBorrower(List<String> memberInfoList, String borrow) {
        return memberInfoList.contains(borrow);
    }
}
