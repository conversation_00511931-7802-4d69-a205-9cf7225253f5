package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

public enum RecordListStatus {
    UN_KNOWN(0, "未知"),
    RECORDING(1, "录制中"),
    COMPLETED(2, "录制完成"),
    FAILED(3, "录制失败"),
    REPLAYING(4, "回放中"),
    REPLAY_COMPLETED(5, "回放完成");


    private final int code;
    private final String description;

    RecordListStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static RecordListStatus fromCode(int code) {
        for (RecordListStatus recordListStatus : RecordListStatus.values()) {
            if (recordListStatus.getCode() == code) {
                return recordListStatus;
            }
        }
        throw new IllegalArgumentException("Unknown status code: " + code);
    }

    @Override
    public String toString() {
        return this.description;
    }
}
