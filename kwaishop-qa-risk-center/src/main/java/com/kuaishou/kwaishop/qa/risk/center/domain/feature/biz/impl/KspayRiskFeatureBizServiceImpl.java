package com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz.impl;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkState;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.FEATURE_BRANCH_DATA_MARKED;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.INVALID_PARAM;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.RISK_FEATURE_BRANCH_UPDATE_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.RISK_FEATURE_UPDATE_SAME_PARAM;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.RISK_FEATURE_UPDATE_TWO_PARAM;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.RISK_FEATURE_VIEW_BRANCH_NOT_EXIST;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.RISK_FEATURE_VIEW_NOT_EXIST;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.KspayQaRiskMapConfig.BIZ_SPACE_MAP;
import static com.kuaishou.kwaishop.qa.risk.center.ral.client.QueryCheckAndRulesClient.markIfValid;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.ProtocolStringList;
import com.google.protobuf.util.JsonFormat;
import com.kuaishou.blobstore.common.utils.CollectionUtils;
import com.kuaishou.infra.boot.jdbc.datasource.TransactionalDataSourceRouting;
import com.kuaishou.kspay.util.GsonUtils;
import com.kuaishou.kwaishop.qa.risk.center.common.constants.FeatureViewStatus;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.KspayPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.MarkIfValidResponseBO;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.QueryChainsByIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.FundsRiskFeatureBranchDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.FundsRiskFeatureDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.impl.FundsRiskFeatureDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.impl.RiskFeatureDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureBranchDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureRequestDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundRiskFeatureDetailQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundsRiskFeatureQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.auth.service.AuthService;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.model.ResultCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz.KspayRiskFeatureBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.convert.KspayRiskFeatureConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.model.enums.KspayRiskFeatureBranchStateEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.model.enums.KspayRiskFeatureStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.KspayFundsRiskFeatureViewService;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.KspayTeamResourceService;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.impl.KspayFundsRiskFeatureViewServiceImpl;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.impl.KspayTeamResourceServiceImpl;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.FeatureStatisticInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.GetRiskEntryAndFieldsStatisticRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.GetRiskEntryAndFieldsStatisticResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KdevAccuracyMsgSendRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayBranchAndViewIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetail;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetailSimple;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetailSimpleV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureListParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureViewParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskUserMarkInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.MarkIfChainsValid;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.MarkIfChainsValidResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.PageKspayRiskFeatureListDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.PageQueryRiskFeatureStatisticRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryCheckChainsById;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryCheckChainsByIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryPageRiskFeatureListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryRiskFeatureDetailByIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryRiskFeatureDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayBranchRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayBranchRequestV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayFundsRiskFeatureViewBranchRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.AccuracyData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.FinanceMethod;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.KdevFeatureDetailData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.PayFinanceLossPoints;
import com.kuaishou.kwaishop.qa.risk.center.ral.client.QueryCheckAndRulesClient;

import lombok.extern.slf4j.Slf4j;
@Service
@Lazy
@Slf4j
public class KspayRiskFeatureBizServiceImpl implements KspayRiskFeatureBizService {

    @Autowired
    private AuthService authService;
    @Autowired
    private KspayFundsRiskFeatureViewService kspayFundsRiskFeatureViewService;
    @Autowired
    private KspayTeamResourceService kspayTeamResourceService;
    @Autowired
    private KspayRiskFeatureConvert kspayRiskFeatureConvert;
    @Autowired
    private FundsRiskFeatureDAOImpl fundsRiskFeatureDaoImpl;
    @Autowired
    private FundsRiskFeatureDAO fundsRiskFeatureDAO;
    @Autowired
    private FundsRiskFeatureBranchDAO fundsRiskFeatureBranchDAO;
    @Autowired
    private KspayTeamResourceServiceImpl kspayTeamResourceServiceImpl = new KspayTeamResourceServiceImpl();
    @Autowired
    private KspayFundsRiskFeatureViewServiceImpl kspayFundsRiskFeatureViewServiceImpl = new KspayFundsRiskFeatureViewServiceImpl();
    @Autowired
    private KspayRiskFeatureBizServiceImpl kspayRiskFeatureBizServiceImpl;
    @Autowired
    private KspayRiskFeatureBizService kspayRiskFeatureBizService;
    @Autowired
    private RiskFeatureDAO riskFeatureDAO;

    private final NamedParameterJdbcTemplate jdbcTemplate;
    @Autowired
    public KspayRiskFeatureBizServiceImpl(NamedParameterJdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /** 查询feature列表: 支持根据featureId查询branch详情*/
    @Override
    public PageKspayRiskFeatureListDTO queryPageRiskFeatureList(QueryPageRiskFeatureListRequest request) {
        // 构建查询条件
        FundsRiskFeatureQueryCondition condition = kspayRiskFeatureConvert.buildFundsRiskFeatureListQueryCondition(request);

        KspayPageBO<FundsRiskFeatureDO> fundRiskFeatureDO;

        if (request.getIsFromPlatformTask()) {
            fundRiskFeatureDO = queryRiskFeatureListByCondition(condition);
            filterTestType(fundRiskFeatureDO);
        } else {
            fundRiskFeatureDO = queryRiskFeatureListByCondition(condition);
        }

        log.info("fundsRiskFeatureDO: {}", toJSON(fundRiskFeatureDO));

        // 构建返回参数
        List<KspayRiskFeatureListParam> kspayRiskFeatureListParams = buildKspayRiskFeatureParams(fundRiskFeatureDO.getData(), request);

        // 分页数据
        Integer pageNo = fundRiskFeatureDO.getPageNo();
        Integer pageSize = fundRiskFeatureDO.getPageSize();
        Long total = fundRiskFeatureDO.getTotal();

        return kspayRiskFeatureConvert.convertToFeaturePageDTO(kspayRiskFeatureListParams, pageNo, pageSize, total);
    }

    @TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
    private KspayPageBO<FundsRiskFeatureDO> queryRiskFeatureListByCondition(FundsRiskFeatureQueryCondition condition) {
        if (condition.isQueryOrderByCreateTime()) {
//            return fundsRiskFeatureDAO.queryPageRiskFeatureList(condition);
            return queryPageRiskFeatureListV2(condition);
        } else {
//            return fundsRiskFeatureDAO.queryPageRiskFeatureListDescByUpdateTime(condition);
            return queryPageRiskFeatureListV2(condition);
        }
    }

    @TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
    public KspayPageBO<FundsRiskFeatureDO> queryPageRiskFeatureListV2(FundsRiskFeatureQueryCondition condition) {
        int offset = (condition.getPageNo() - 1) * condition.getPageSize();
        int limit = condition.getPageSize();
        int defaultPageSize = 20;
        log.info("queryPageRiskFeatureList featureQueryCondition:{}, offset:{}, limit:{}", toJSON(condition), offset, limit);
        List<FundsRiskFeatureDO> content = riskFeatureDAO.queryFeatureList(false, condition, offset, limit, defaultPageSize, jdbcTemplate);
        log.info("queryPageRiskFeatureList featureList:{}", toJSON(content));
        long totalElements = riskFeatureDAO.countFeatures(false, condition, jdbcTemplate);
        return new KspayPageBO<>(condition.getPageNo(), condition.getPageSize(), totalElements, content);
    }

    private void filterTestType(KspayPageBO<FundsRiskFeatureDO> fundRiskFeatureDO) {
        List<FundsRiskFeatureDO> filteredData = fundRiskFeatureDO.getData().stream()
                .filter(feature -> feature.getTestType() == null || feature.getTestType() < 1)
                .collect(Collectors.toList());

        fundRiskFeatureDO.setData(filteredData);
        fundRiskFeatureDO.setTotal((long) filteredData.size());
    }

    // 下面的queryPageRiskFeatureListOld废弃
    @Override
    public PageKspayRiskFeatureListDTO queryPageRiskFeatureListOld(QueryPageRiskFeatureListRequest request) {
//        baseKspayRiskPreCheck(request);
//        authService.authPreCheck(request.getCreator());
        if (request.getIsRisk() == 1) {
            // query all
            List<FundsRiskFeatureDO> fundsRiskFeatureDOS = fundsRiskFeatureDAO.queryFeatureListWithCondition(
                    kspayRiskFeatureConvert.buildFundsRiskFeatureListQueryCondition(request));
            log.info("isRiskQueryAllFeatureDO: {}", toJSON(fundsRiskFeatureDOS));

            List<KspayRiskFeatureListParam> kspayRiskFeatureListParams = buildHasRiskKspayRiskFeatureParams(fundsRiskFeatureDOS, request);
            // sort
            Collections.sort(kspayRiskFeatureListParams, Comparator.comparing(KspayRiskFeatureListParam::getUpdateTime).reversed());
            log.info("kspayHasRiskFeatureListParams:{}", kspayRiskFeatureListParams);
            int pageNo = request.getPageNo();
            int pageSize = request.getPageSize();
            Long total = (long) kspayRiskFeatureListParams.size();
            // 分页
            List<KspayRiskFeatureListParam> kspayPageListParams = paginate(kspayRiskFeatureListParams, pageNo, pageSize);
            log.info("kspayPageListParams:{}", kspayPageListParams);
            return kspayRiskFeatureConvert.convertToFeaturePageDTO(kspayPageListParams, pageNo, pageSize, total);
        } else if (request.getIsRisk() == 2) {
            // query all
            List<FundsRiskFeatureDO> fundsRiskFeatureDOS = fundsRiskFeatureDAO.queryFeatureListWithCondition(
                    kspayRiskFeatureConvert.buildFundsRiskFeatureListQueryCondition(request));
            log.info("queryAllFeatureDO: {}", toJSON(fundsRiskFeatureDOS));

            List<KspayRiskFeatureListParam> kspayRiskFeatureListParams = buildNoRiskKspayRiskFeatureParams(fundsRiskFeatureDOS, request);
            // sort
            Collections.sort(kspayRiskFeatureListParams, Comparator.comparing(KspayRiskFeatureListParam::getUpdateTime).reversed());
            log.info("kspayNoRiskFeatureListParams:{}", kspayRiskFeatureListParams);
            int pageNo = request.getPageNo();
            int pageSize = request.getPageSize();
            Long total = (long) kspayRiskFeatureListParams.size();
            // 分页
            List<KspayRiskFeatureListParam> kspayPageListParams = paginate(kspayRiskFeatureListParams, pageNo, pageSize);
            log.info("kspayPageListParams:{}", kspayPageListParams);
            return kspayRiskFeatureConvert.convertToFeaturePageDTO(kspayPageListParams, pageNo, pageSize, total);
        } else {
            KspayPageBO<FundsRiskFeatureDO> fundRiskFeatureDO = fundsRiskFeatureDAO.queryPageRiskFeatureListDescByUpdateTime(
                    kspayRiskFeatureConvert.buildFundsRiskFeatureListQueryCondition(request));
            log.info("fundsRiskFeatureDO: {}", toJSON(fundRiskFeatureDO));
            List<KspayRiskFeatureListParam> kspayRiskFeatureListParams = buildKspayRiskFeatureParams(fundRiskFeatureDO.getData(), request);
            // 分页数据
            Integer pageNo = fundRiskFeatureDO.getPageNo();
            Integer pageSize = fundRiskFeatureDO.getPageSize();
            Long total = fundRiskFeatureDO.getTotal();
            return kspayRiskFeatureConvert.convertToFeaturePageDTO(kspayRiskFeatureListParams, pageNo, pageSize, total);
        }
    }

    /** 查询featureBranch详情 */
    @Override
    public List<KspayRiskFeatureDetail> queryRiskFeatureDetail(QueryRiskFeatureDetailRequest request) {
        List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskFeatureBranch(
                kspayRiskFeatureConvert.buildFundsRiskFeatureDetailQueryCondition(request)
        );
        List<KspayRiskFeatureDetail> collect =
                fundsRiskFeatureBranchDOS.stream().map(kspayRiskFeatureConvert::buildKspayRiskFeatureDetail)
                        .collect(Collectors.toList());
        return collect;
    }

    /** 根据id查询主表 */
    @Override
    public KspayRiskFeatureDetail queryRiskFeatureDetailById(QueryRiskFeatureDetailByIdRequest request) {
        FundsRiskFeatureBranchDO fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryByMainId(request.getId());
        if (fundsRiskFeatureBranchDOS == null) {
            log.info("数据库feature_view_branch数据异常:{}", toJSON(fundsRiskFeatureBranchDOS));
            throw new BizException(RISK_FEATURE_VIEW_NOT_EXIST);
        }
        return kspayRiskFeatureConvert.buildKspayRiskFeatureDetail(fundsRiskFeatureBranchDOS);
    }

    // 分页方法
    public static <T> List<T> paginate(List<T> dataList, int pageNo, int pageSize) {
        if (dataList == null || dataList.isEmpty()) {
            return Collections.emptyList();
        }

        int total = dataList.size();
        int totalPages = (int) Math.ceil((double) total / pageSize);

        // 验证 pageNo
        if (pageNo < 1 || pageNo > totalPages) {
            throw new IllegalArgumentException("Invalid page number: " + pageNo);
        }

        int startIndex = (pageNo - 1) * pageSize;
        int endIndex = startIndex + pageSize - 1;
        if (endIndex >= total) {
            endIndex = total - 1;
        }

        List<T> currentPageData = dataList.subList(startIndex, endIndex + 1);
        return currentPageData;
    }


    private List<KspayRiskFeatureListParam> buildKspayRiskFeatureParams(List<FundsRiskFeatureDO> data,
                                                                        QueryPageRiskFeatureListRequest request) {
        List<KspayRiskFeatureListParam> res = new ArrayList<>();

        for (FundsRiskFeatureDO fundsRiskFeatureDO : data) {
            String featureId = fundsRiskFeatureDO.getFeatureId();
            List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskFeatureBranch(
                    kspayRiskFeatureConvert.buildFundRiskFeatureBranchQueryCondition(featureId));

            if (request.getIsFromPlatformTask()) {
                KspayRiskFeatureListParam kspayRiskFeatureListParam = kspayRiskFeatureConvert.buildKspayRiskFeatureListParam(
                        fundsRiskFeatureDO, kspayRiskFeatureConvert.buildKspayRiskFeatureBranch(fundsRiskFeatureBranchDOS));
                log.info("kspayRiskFeatureListParam: {}", toJSON(kspayRiskFeatureListParam));
                res.add(kspayRiskFeatureListParam);
            } else {
                handleNonCreateTimeQuery(fundsRiskFeatureDO, fundsRiskFeatureBranchDOS, res);
            }
        }
        return res;
    }

    private void handleNonCreateTimeQuery(FundsRiskFeatureDO fundsRiskFeatureDO,
                                          List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS,
                                          List<KspayRiskFeatureListParam> res) {
        Integer status = fundsRiskFeatureDO.getStatus();
        // 如果是集成中 || 集成完成 || 已上线状态 && 没有资损方法，自动更新子表riskStatue && auditCover == 1
        if (status.equals(FeatureViewStatus.FEATURE_STATUS_COMPLETED.getValue())
                || status.equals(FeatureViewStatus.INTEGRATING.getValue())
                || status.equals(FeatureViewStatus.INTEGRATION_DONE.getValue())) {
            for (FundsRiskFeatureBranchDO branchDO : fundsRiskFeatureBranchDOS) {
                if (branchDO.getFundRiskMethodAmount() == null || branchDO.getFundRiskMethodAmount().equals(0)) {
                    checkBranchData(branchDO);
                    int row = fundsRiskFeatureBranchDAO.updateAllStatusByMainId(
                            kspayRiskFeatureConvert.buildAutoUpdateBranchById(branchDO, "system",
                                    KspayRiskFeatureBranchStateEnum.MARKED.getCode(),
                                    KspayRiskFeatureBranchStateEnum.MARKED.getCode()));
                    checkState(row == 1, "featureBranch更新条数不符合预期 实际插入数据:{}", row);
                }
            }
        }
        // 根据子表更新主表risk_pass_status  2024.04.24 删除自动更新逻辑【改为人工打标确认】
//                try {
//                    int row = fundsRiskFeatureDAO.isNeedUpdateRiskPassStatus(featureId);
//                    if (row > 0) {
//                        log.info("更新RiskPassStatus: {}", toJSON(featureId));
//                        fundsRiskFeatureDAO.updateRiskPassStatus(featureId);
//                        checkState(row == 1, "数据库准入状态更新不符合预期 实际插入数据:{}", row);
//                    }
//                } catch (Exception e) {
//                    log.warn("isUpdateRiskPassStatus error.", e);
//                }

        KspayRiskFeatureListParam kspayRiskFeatureListParam = kspayRiskFeatureConvert.buildKspayRiskFeatureListParam(
                fundsRiskFeatureDO, kspayRiskFeatureConvert.buildKspayRiskFeatureBranch(fundsRiskFeatureBranchDOS));
        log.info("kspayRiskFeatureListParam: {}", toJSON(kspayRiskFeatureListParam));
        res.add(kspayRiskFeatureListParam);
    }

    private void checkBranchData(FundsRiskFeatureBranchDO branchDO) {
        try {
            if (!Objects.equals(branchDO.getRiskStatus(), KspayRiskFeatureBranchStateEnum.NOMARKED.getCode())
                    || !Objects.equals(branchDO.getAuditCover(), KspayRiskFeatureBranchStateEnum.NOMARKED.getCode())) {
                throw new BizException(FEATURE_BRANCH_DATA_MARKED);
            }
        } catch (Exception e) {
            log.warn("branchData is marked!!!");
        }
    }

    /**
     * 这个buildKspayRiskFeatureParams方法废弃
     * 拼接feature列表和feature_id对应的详情信息
     * */
    private List<KspayRiskFeatureListParam> buildKspayRiskFeatureParamsOld(List<FundsRiskFeatureDO> data, QueryPageRiskFeatureListRequest request) {
        List<KspayRiskFeatureListParam> res = new ArrayList<>();
        if (request.getIsRisk() == 1) {  // 1: query hasRisk
            for (FundsRiskFeatureDO fundsRiskFeatureDO: data) {
                String featureId = fundsRiskFeatureDO.getFeatureId();
                List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskFeatureBranch(
                        kspayRiskFeatureConvert.buildFundRiskFeatureBranchQueryCondition(featureId));
                log.info("isRis_value_1:{}", fundsRiskFeatureBranchDOS);
                if (fundsRiskFeatureBranchDOS.size() == 0) {
                    continue;
                }
                // 检查 fundsRiskFeatureBranchDOS 中是否有 fund_risk_method_amount 字段大于0的元素
                if (!fundsRiskFeatureBranchDOS.isEmpty()) {
                    if (fundsRiskFeatureBranchDOS.stream().anyMatch(fundsRiskFeatureBranchDO ->
                                                Optional.ofNullable(fundsRiskFeatureBranchDO.getFundRiskMethodAmount()).orElse(0) > 0)) {
                        // 如果所有元素都满足条件，返回 fundsRiskFeatureBranchDOS
                        String newFeatureId = fundsRiskFeatureBranchDOS.get(0).getFeatureViewId().toString();
                        List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchAll = fundsRiskFeatureBranchDAO.queryFundRiskFeatureBranch(
                                kspayRiskFeatureConvert.buildFundRiskFeatureBranchQueryCondition(newFeatureId));
                        KspayRiskFeatureListParam kspayRiskFeatureListParam = kspayRiskFeatureConvert.buildKspayRiskFeatureListParam(
                                fundsRiskFeatureDO, kspayRiskFeatureConvert.buildKspayRiskFeatureBranch(fundsRiskFeatureBranchAll));
                        res.add(kspayRiskFeatureListParam);
                    }
                }
            }
        } else if (request.getIsRisk() == 2) {  // 2: query noRisk
            for (FundsRiskFeatureDO fundsRiskFeatureDO: data) {
                String featureId = fundsRiskFeatureDO.getFeatureId();
                List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskFeatureBranch(
                        kspayRiskFeatureConvert.buildFundRiskFeatureBranchQueryCondition(featureId));
                log.info("isRis_value_222:{}", fundsRiskFeatureBranchDOS);

                // 检查 fundsRiskFeatureBranchDOS 中所有的 fund_risk_method_amount 字段是否为0或者为null
                if (!fundsRiskFeatureBranchDOS.isEmpty()) {
                    if (fundsRiskFeatureBranchDOS.stream().allMatch(fundsRiskFeatureBranchDO ->
                            fundsRiskFeatureBranchDO.getFundRiskMethodAmount() == null || fundsRiskFeatureBranchDO.getFundRiskMethodAmount() == 0)) {
                        // 如果所有元素都满足条件，返回 fundsRiskFeatureBranchDOS
                        String newFeatureId = fundsRiskFeatureBranchDOS.get(0).getFeatureViewId().toString();
                        List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchAll = fundsRiskFeatureBranchDAO.queryFundRiskFeatureBranch(
                                kspayRiskFeatureConvert.buildFundRiskFeatureBranchQueryCondition(newFeatureId));
                        KspayRiskFeatureListParam kspayRiskFeatureListParam = kspayRiskFeatureConvert.buildKspayRiskFeatureListParam(
                                fundsRiskFeatureDO, kspayRiskFeatureConvert.buildKspayRiskFeatureBranch(fundsRiskFeatureBranchAll));
                        res.add(kspayRiskFeatureListParam);
                    }
                }
            }
        } else {  // normal
            for (FundsRiskFeatureDO fundsRiskFeatureDO: data) {
                String featureId = fundsRiskFeatureDO.getFeatureId();
                List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskFeatureBranch(
                        kspayRiskFeatureConvert.buildFundRiskFeatureBranchQueryCondition(featureId));
                // 如果是集成中 || 集成完成 || 已上线状态 && 没有资损方法，自动更新子表riskStatue && auditCover == 1
                if (fundsRiskFeatureDO.getStatus().equals(FeatureViewStatus.FEATURE_STATUS_COMPLETED.getValue())
                        || fundsRiskFeatureDO.getStatus().equals(FeatureViewStatus.INTEGRATING.getValue())
                        || fundsRiskFeatureDO.getStatus().equals(FeatureViewStatus.INTEGRATION_DONE.getValue())) {
                    log.info("判断需求是否已到集成中状态:{}", toJSON(fundsRiskFeatureDO));
                    for (FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO : fundsRiskFeatureBranchDOS) {
                        if ((fundsRiskFeatureBranchDO.getFundRiskMethodAmount() == null
                                || fundsRiskFeatureBranchDO.getFundRiskMethodAmount().equals(0))) {
                            log.info("进入底层更新判断-fundsRiskFeatureBranchDO: {}", toJSON(fundsRiskFeatureBranchDO));
                            try {
                                assert Objects.equals(fundsRiskFeatureBranchDO.getRiskStatus(),
                                        KspayRiskFeatureBranchStateEnum.NOMARKED.getCode());
                                assert Objects.equals(fundsRiskFeatureBranchDO.getAuditCover(),
                                        KspayRiskFeatureBranchStateEnum.NOMARKED.getCode());
                            } catch (AssertionError e) {
                                throw new BizException(FEATURE_BRANCH_DATA_MARKED);
                            }
                            int row = fundsRiskFeatureBranchDAO.updateAllStatusByMainId(
                                    kspayRiskFeatureConvert.buildAutoUpdateBranchById(fundsRiskFeatureBranchDO,
                                            "system", KspayRiskFeatureBranchStateEnum.MARKED.getCode(),
                                            KspayRiskFeatureBranchStateEnum.MARKED.getCode()));
                            checkState(row == 1, "featureBranch更新条数不符合预期 实际插入数据:{}", row);
                        }
                    }
                }

                // 根据子表更新主表risk_pass_status  2024.04.24 删除自动更新逻辑【改为人工打标确认】
//                try {
//                    int row = fundsRiskFeatureDAO.isNeedUpdateRiskPassStatus(featureId);
//                    if (row > 0) {
//                        log.info("更新RiskPassStatus: {}", toJSON(featureId));
//                        fundsRiskFeatureDAO.updateRiskPassStatus(featureId);
//                        checkState(row == 1, "数据库准入状态更新不符合预期 实际插入数据:{}", row);
//                    }
//                } catch (Exception e) {
//                    log.warn("isUpdateRiskPassStatus error.", e);
//                }
                KspayRiskFeatureListParam kspayRiskFeatureListParam = kspayRiskFeatureConvert.buildKspayRiskFeatureListParam(
                        fundsRiskFeatureDO, kspayRiskFeatureConvert.buildKspayRiskFeatureBranch(fundsRiskFeatureBranchDOS));
                log.info("kspayRiskFeatureListParam: {}", toJSON(kspayRiskFeatureListParam));
                res.add(kspayRiskFeatureListParam);
            }
        }
        return res;
    }

    private List<KspayRiskFeatureListParam> buildHasRiskKspayRiskFeatureParams(List<FundsRiskFeatureDO> data,
                                                                               QueryPageRiskFeatureListRequest request) {
        List<KspayRiskFeatureListParam> res = new ArrayList<>();

        for (FundsRiskFeatureDO fundsRiskFeatureDO : data) {
            String featureId = fundsRiskFeatureDO.getFeatureId();
            List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskFeatureBranch(
                    kspayRiskFeatureConvert.buildFundRiskFeatureBranchQueryCondition(featureId));
            if (fundsRiskFeatureBranchDOS.size() == 0) {
                continue;
            }
            // 检查 fundsRiskFeatureBranchDOS 中是否有 fund_risk_method_amount 字段大于0的元素
            if (!fundsRiskFeatureBranchDOS.isEmpty()) {
                if (fundsRiskFeatureBranchDOS.stream().anyMatch(fundsRiskFeatureBranchDO ->
                        Optional.ofNullable(fundsRiskFeatureBranchDO.getFundRiskMethodAmount()).orElse(0) > 0)) {
                    // 如果所有元素都满足条件，返回 fundsRiskFeatureBranchDOS
                    String newFeatureId = fundsRiskFeatureBranchDOS.get(0).getFeatureViewId().toString();
                    List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchAll = fundsRiskFeatureBranchDAO.queryFundRiskFeatureBranch(
                            kspayRiskFeatureConvert.buildFundRiskFeatureBranchQueryCondition(newFeatureId));
                    KspayRiskFeatureListParam kspayRiskFeatureListParam = kspayRiskFeatureConvert.buildKspayRiskFeatureListParam(
                            fundsRiskFeatureDO, kspayRiskFeatureConvert.buildKspayRiskFeatureBranch(fundsRiskFeatureBranchAll));
                    res.add(kspayRiskFeatureListParam);
                }
            }
        }
        return res;
    }

    private List<KspayRiskFeatureListParam> buildNoRiskKspayRiskFeatureParams(List<FundsRiskFeatureDO> data,
                                                                               QueryPageRiskFeatureListRequest request) {
        List<KspayRiskFeatureListParam> res = new ArrayList<>();

        for (FundsRiskFeatureDO fundsRiskFeatureDO: data) {
            String featureId = fundsRiskFeatureDO.getFeatureId();
            List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskFeatureBranch(
                    kspayRiskFeatureConvert.buildFundRiskFeatureBranchQueryCondition(featureId));
            log.info("isRis_value_222:{}", fundsRiskFeatureBranchDOS);

            // 检查 fundsRiskFeatureBranchDOS 中所有的 fund_risk_method_amount 字段是否为0或者为null
            if (!fundsRiskFeatureBranchDOS.isEmpty()) {
                if (fundsRiskFeatureBranchDOS.stream().allMatch(fundsRiskFeatureBranchDO ->
                        fundsRiskFeatureBranchDO.getFundRiskMethodAmount() == null || fundsRiskFeatureBranchDO.getFundRiskMethodAmount() == 0)) {
                    // 如果所有元素都满足条件，返回 fundsRiskFeatureBranchDOS
                    String newFeatureId = fundsRiskFeatureBranchDOS.get(0).getFeatureViewId().toString();
                    List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchAll = fundsRiskFeatureBranchDAO.queryFundRiskFeatureBranch(
                            kspayRiskFeatureConvert.buildFundRiskFeatureBranchQueryCondition(newFeatureId));
                    KspayRiskFeatureListParam kspayRiskFeatureListParam = kspayRiskFeatureConvert.buildKspayRiskFeatureListParam(
                            fundsRiskFeatureDO, kspayRiskFeatureConvert.buildKspayRiskFeatureBranch(fundsRiskFeatureBranchAll));
                    res.add(kspayRiskFeatureListParam);
                }
            }
        }
        return res;
    }


    public List<FundsRiskFeatureBranchDO> isHasRiskQuery(String featureId, QueryPageRiskFeatureListRequest request) {  // bool
        // 执行数据库查询
        List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskFeatureBranch(
                kspayRiskFeatureConvert.buildFundRiskFeatureBranchQueryCondition(featureId, Integer.valueOf(request.getIsRisk())));
        if (fundsRiskFeatureBranchDOS.isEmpty()) {
            log.info("数据库feature_view_branch数据异常:{}", toJSON(fundsRiskFeatureBranchDOS));
        }
        if (request.getIsRisk() == 1) {
            // 遍历查询结果，检查是否存在 fund_risk > 0 的数据
            for (FundsRiskFeatureBranchDO item : fundsRiskFeatureBranchDOS) {
                if (item.getFundRiskMethodAmount() > 0) {
                    // 如果发现任意一个 fund_risk > 0，返回整个列表
                    return fundsRiskFeatureBranchDOS;
                }
            }
        } else {
            // 遍历查询结果，检查是否存在 fund_risk > 0 的数据
            for (FundsRiskFeatureBranchDO item : fundsRiskFeatureBranchDOS) {
                if (item.getFundRiskMethodAmount() > 0) {
                    // 如果发现任意一个 fund_risk > 0，返回整个列表
                    return fundsRiskFeatureBranchDOS;
                }
            }
        }
        return fundsRiskFeatureBranchDOS;
    }
    /**
     * 更新funds_risk_feature_view
     * 目前只会更新RiskPassStatus以及对应的update_time等字段
     *
     * 新增：插入用户打标数据到扩展字段
     * */
    @Override
    public void updateRiskFeatureView(KspayRiskFeatureViewParam request) {
        Long featureIdAsLong = Long.parseLong(request.getFeatureId());
        // 1全部有效，2部分有效，3无效
        Set<Integer> validValues = new HashSet<>(Arrays.asList(0, 1, 2, 3));
        if (!validValues.contains(request.getExtraData().getIsValidRiskFind())) {
            throw new BizException(INVALID_PARAM);
        }
        FundsRiskFeatureDO fundsRiskFeatureDO = fundsRiskFeatureDAO.queryByFeatureId(request.getFeatureId());
        log.info("fundsRiskFeatureDO:{}", fundsRiskFeatureDO);
        // 操作打标onlineConfirm状态【1：确认已完成 2：确认无需完成】
        if (request.getOnlineConfirm() > 0) {
            if (request.getOnlineConfirm() != 1 && request.getOnlineConfirm() != 2) {
                throw new BizException(INVALID_PARAM, "无效的onlineConfirm值: " + request.getOnlineConfirm());
            }
            // 这里不会更新updateTime，防止按照上线时间排序有问题
            int row = fundsRiskFeatureDAO.updateOnlineConfirm(request.getFeatureId(),
                    request.getOnlineConfirm(),
                    request.getUpdater());
            checkState(row == 1, "更新onlineConfirm数据不符合预期:{}", row);
            return;
        }

        // 检查并处理可能为null的createTime
        if (fundsRiskFeatureDO.getCreateTime() == null) {
            fundsRiskFeatureDO.setCreateTime(System.currentTimeMillis()); // 设置默认值为当前时间
        }
        if (fundsRiskFeatureDO == null) {
            log.info("数据库feature_view数据异常:{}", fundsRiskFeatureDO);
            throw new BizException(RISK_FEATURE_VIEW_NOT_EXIST);
        }

        Boolean isHasRisk = featureIsHasRisk(featureIdAsLong);

        if (request.getRiskPassStatus() != fundsRiskFeatureDO.getRiskPassStatus()) {
            FundsRiskFeatureDO updatedFeature = isHasRisk
                    ? kspayRiskFeatureConvert.buildHasRiskUpdateFeatureView(request)
                    : kspayRiskFeatureConvert.buildNoRiskUpdateFeatureView(request);
            log.info("updatedFeature:{}", updatedFeature);
            // 这里dao层不会更新updateTime，防止按照上线时间排序有问题
            int row = fundsRiskFeatureDAO.updateByFeatureId(updatedFeature);
            checkState(row == 1, "数据库更新条数不符合预期 实际插入数据:{}", row);
        } else {
            log.error("更新前后状态一致:{}", fundsRiskFeatureDO);
        }
    }

    @Override
    public void updateRiskFeatureViewTestType(KspayRiskFeatureViewParam request) {
        FundsRiskFeatureDO fundsRiskFeatureDO = fundsRiskFeatureDAO.queryByFeatureId(request.getFeatureId());
        log.info("fundsRiskFeatureDO:{}", fundsRiskFeatureDO);
        if (fundsRiskFeatureDO == null) {
            throw new BizException(RISK_FEATURE_VIEW_NOT_EXIST);
        }
        if (fundsRiskFeatureDO.getCreateTime() == null) {
            fundsRiskFeatureDO.setCreateTime(System.currentTimeMillis()); // 设置默认值为当前时间
        }

        // 检查 TestType 是否已经有值
        if (fundsRiskFeatureDO.getTestType() != null) {
            log.info("[updateRiskFeatureViewTestType] TestType 已存在，不执行更新操作");
        } else {
            FundsRiskFeatureDO updatedFeature = kspayRiskFeatureConvert.buildUpdateFeatureViewTestType(request);
            int row = fundsRiskFeatureDAO.updateTestTypeByFeatureId(updatedFeature);
            checkState(row == 1, "[updateRiskFeatureViewTestType] 数据库更新条数不符合预期 实际插入数据:{}", row);
        }
    }


    // 判断当前featureId对应feature是否有资损风险
    public Boolean featureIsHasRisk(Long featureId) {
        List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO
                .queryFundRiskFeatureBranch(FundRiskFeatureDetailQueryCondition.builder().featureViewId(featureId).build());

        if (fundsRiskFeatureBranchDOS.isEmpty()) {
            log.info("数据库feature_view_branch数据异常:{}", toJSON(fundsRiskFeatureBranchDOS));
            return false;
        } else {
            boolean allMethodsPositive = fundsRiskFeatureBranchDOS.stream()
                    .anyMatch(branch -> branch.getFundRiskMethodAmount() != null && branch.getFundRiskMethodAmount() > 0);
            if (allMethodsPositive) {
                log.info("有资损风险大于0的分支");
                return true;
            } else {
                log.info("所有分支资损风险方法都不大于0");
                return false;
            }
        }
    }

    /**
     * 更新funds_risk_feature_view_branch
     * 目前更新字段: risk_status, audit_cover 以及 risk_status_updater, audit_cover_updater
     * 这里feature_view_id不唯一，需要通过主键来查询和更新
     * 7.21增加返回featureId
     * */
    @Override
    public String updateRiskFeatureViewBranch(KspayRiskFeatureDetail request) {
        FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO = fundsRiskFeatureBranchDAO.queryByMainId(request.getId());
        if (fundsRiskFeatureBranchDO == null) {
            log.info("数据库feature_view_branch 数据异常:{}", toJSON(fundsRiskFeatureBranchDO));
            throw new BizException(RISK_FEATURE_VIEW_BRANCH_NOT_EXIST);
        } else {
            if (request.getRiskStatus() != 0 && request.getRiskStatus() != fundsRiskFeatureBranchDO.getRiskStatus()
                    && request.getAuditCover() != 0 && request.getRiskStatus() != fundsRiskFeatureBranchDO.getRiskStatus()) {
                throw new BizException(RISK_FEATURE_UPDATE_TWO_PARAM);
            } else if (request.getRiskStatus() != 0 && request.getRiskStatus() == fundsRiskFeatureBranchDO.getRiskStatus()) {
                throw new BizException(RISK_FEATURE_UPDATE_SAME_PARAM);
            } else if (request.getAuditCover() != 0 && request.getAuditCover() == fundsRiskFeatureBranchDO.getAuditCover()) {
                throw new BizException(RISK_FEATURE_UPDATE_SAME_PARAM);
            } else if (request.getRiskStatus() != 0 && request.getRiskStatus() != fundsRiskFeatureBranchDO.getRiskStatus()) {
                int row = fundsRiskFeatureBranchDAO.updateRiskStatusByMainId(kspayRiskFeatureConvert.buildUpdateFeatureViewBranch(request));
                log.info("表funds_risk_feature_view_branch更新riskStatus:{}", toJSON(request.getRiskStatus()));
                checkState(row == 1, "数据库更新条数不符合预期 实际插入数据:{}", row);
            } else if (request.getAuditCover() != 0 && request.getAuditCover() != fundsRiskFeatureBranchDO.getAuditCover()) {
                int row = fundsRiskFeatureBranchDAO.updateAuditCoverByMainId(kspayRiskFeatureConvert.buildUpdateFeatureViewBranch(request));
                log.info("数据库funds_risk_feature_view_branch更新auditCover:{}", toJSON(request.getAuditCover()));
                checkState(row == 1, "数据库更新条数不符合预期 实际插入数据:{}", row);
            } else {
                throw new BizException(RISK_FEATURE_BRANCH_UPDATE_ERROR);
            }
        }
        return request.getFeatureId();
    }

    /** 更新子表字段All状态，依赖前端传入值 */
    @Override
    public String updateFeaturesViewBranchAllStatus(KspayRiskFeatureDetail request) {
        FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO = fundsRiskFeatureBranchDAO.queryByMainId(request.getId());
        if (fundsRiskFeatureBranchDO == null) {
            log.info("数据库feature_view_branch 数据异常:{}", toJSON(fundsRiskFeatureBranchDO));
            throw new BizException(RISK_FEATURE_VIEW_BRANCH_NOT_EXIST);
        } else {
            if (request.getRiskStatus() != fundsRiskFeatureBranchDO.getRiskStatus()
                    && request.getAuditCover() != fundsRiskFeatureBranchDO.getAuditCover()) {
                int row = fundsRiskFeatureBranchDAO.updateAllStatusByMainId(kspayRiskFeatureConvert.buildUpdateFeatureViewBranch(request));
                log.info("表funds_risk_feature_view_branch更新allStatus:{}", toJSON(request));
                checkState(row == 1, "数据库更新条数不符合预期 实际插入数据:{}", row);
            } else if (request.getRiskStatus() != fundsRiskFeatureBranchDO.getRiskStatus()
                    || request.getAuditCover() != fundsRiskFeatureBranchDO.getAuditCover()) {  // 有一个不等也允许更新
                int row = fundsRiskFeatureBranchDAO.updateAllStatusByMainId(kspayRiskFeatureConvert.buildUpdateFeatureViewBranch(request));
                log.info("表funds_risk_feature_view_branch更新oneStatus:{}", toJSON(request));
                checkState(row == 1, "数据库更新条数不符合预期 实际插入数据:{}", row);
            } else {
                throw new BizException(RISK_FEATURE_BRANCH_UPDATE_ERROR);
            }
        }
        return request.getFeatureId();
    }

    /**
     * 1、根据featureId 一键更新子表所有 riskStatus & auditCover
     * 2、根据id更新
     * */
    @Override
    public String updateBranchAllStatusByFeatureId(KspayRiskFeatureDetail request) {
//        authService.authPreCheck(request.getUpdater());
        Integer fromRiskStatus = KspayRiskFeatureStatusEnum.NORMAL.getCode();
        Integer fromAuditCover = KspayRiskFeatureStatusEnum.NORMAL.getCode();
        Integer toRiskStatus = KspayRiskFeatureStatusEnum.NORMAL.getCode();
        Integer toAuditCover = KspayRiskFeatureStatusEnum.NORMAL.getCode();
        List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskFeatureBranch(
                kspayRiskFeatureConvert.buildFundsRiskFeatureDetailQueryCondition(request));
        if (fundsRiskFeatureBranchDOS == null) {
            log.info("数据库feature_view_branch 数据异常:{}", toJSON(fundsRiskFeatureBranchDOS));
            throw new BizException(RISK_FEATURE_VIEW_BRANCH_NOT_EXIST);
        } else {
            /** 直接更新  */
            for (FundsRiskFeatureBranchDO fundRiskFeatureBranchDO : fundsRiskFeatureBranchDOS) {
//                if (fundRiskFeatureBranchDO.getMethodEntrance().length() == 0) {  // 入口为空暂不允许更新，前端兼容展示为通过
                int row = fundsRiskFeatureBranchDAO.updateAllStatusByMainId(
                        kspayRiskFeatureConvert.buildUpdateFeatureViewBranchById(fundRiskFeatureBranchDO, request, toRiskStatus, toAuditCover));
                log.info("update feature_branch by featureId:{}", toJSON(request));
                checkState(row == 1, "数据库更新条数不符合预期 实际插入数据:{}", row);
            }
        }
        return request.getFeatureId();
    }

    // 仅查询
    @Override
    public List<KspayRiskFeatureDetailSimple> queryRiskByRepoAndBranchSimple(KspayBranchRequest request) {
        List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskByRepoAndBranch(
                kspayRiskFeatureConvert.buildFundsRiskRiskQueryCondition(request)
        );
        return fundsRiskFeatureBranchDOS.stream()
                .map(fundsRiskFeatureBranchDO -> kspayRiskFeatureConvert.convertToRiskDetailDTO(fundsRiskFeatureBranchDO, request))
                .collect(Collectors.toList());
    }

    @Override
    public List<KspayRiskFeatureDetailSimple> queryRiskByRepoAndBranch(KspayBranchRequest request) {
        List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskByRepoAndBranch(
                kspayRiskFeatureConvert.buildFundsRiskRiskQueryCondition(request)
        );
        if (fundsRiskFeatureBranchDOS == null) {
            log.info("数据库feature_view_branch 数据异常:{}", toJSON(fundsRiskFeatureBranchDOS));
            throw new BizException(RISK_FEATURE_VIEW_BRANCH_NOT_EXIST);
        }
        // todo 查询精准接口耗时高【先注释，后续如果需要再优化性能】
        List<KspayRiskFeatureDetailSimple> kspayRiskFeatureDetailSimples = new ArrayList<>();
        for (FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO : fundsRiskFeatureBranchDOS) {
            // 如果资损风险方法数<=0 && 无资损方法入口，直接将riskStatus和auditCover更新为已覆盖【避免仍需要人工打标歧义】
            Integer minMethodEntranceLength = 3;
            boolean noMethodEntrance = fundsRiskFeatureBranchDO.getMethodEntrance().length() < minMethodEntranceLength;
            log.info("资损方法noMethodEntrance:{}", toJSON(noMethodEntrance));
            try {
                if (fundsRiskFeatureBranchDO.getFundRiskMethodAmount() <= 0 && noMethodEntrance) {
                    log.info("queryRiskByRepoAndBranch更新前数据:{}", toJSON(fundsRiskFeatureBranchDO));
                    FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO1 = new FundsRiskFeatureBranchDO();
                    fundsRiskFeatureBranchDO1.setUpdater("system");
                    fundsRiskFeatureBranchDO1.setUpdateTime(System.currentTimeMillis());
                    fundsRiskFeatureBranchDO1.setRiskStatus(KspayRiskFeatureStatusEnum.NORMAL.getCode());
                    fundsRiskFeatureBranchDO1.setAuditCover(KspayRiskFeatureStatusEnum.NORMAL.getCode());
                    fundsRiskFeatureBranchDO1.setRiskStatusUpdater("system");
                    fundsRiskFeatureBranchDO1.setAuditCoverUpdater("system");
                    fundsRiskFeatureBranchDO1.setId(fundsRiskFeatureBranchDO.getId());
                    try {
                        int row = fundsRiskFeatureBranchDAO.updateAllStatusByMainId(fundsRiskFeatureBranchDO1);
                        checkState(row == 1, "queryRiskByRepoAndBranch前置更新不符合预期 实际插入数据:{}", row);
                    } catch (IllegalStateException e) {
                        log.info("queryRiskByRepoAndBranch前置更新未发生数据变化:{}", toJSON(fundsRiskFeatureBranchDO1));
                    } catch (Exception e) {
                        log.info("queryRiskByRepoAndBranch前置更新失败:{}", toJSON(fundsRiskFeatureBranchDO1));
                    }
                }
            } catch (Exception e) {
                log.info(String.format("更新riskFeatureBranch数据异常: e %s, ", e.getMessage()));
            }
            // 查询精准方法
//            List<MethodCovInfo> methodCovInfos =
//                    kspayFundsRiskFeatureViewService.kspayQueryBranchAccuracyFMethodsData(fundsRiskFeatureBranchDO.getRepoName(),
//                            fundsRiskFeatureBranchDO.getBranchName());
            // 更新完之后再次查询一下
            FundsRiskFeatureBranchDO fundsRiskFeatureBranchDOAfter =
                    fundsRiskFeatureBranchDAO.queryByMainId(fundsRiskFeatureBranchDO.getId().toString());
            KspayRiskFeatureDetailSimple kspayRiskFeatureDetailSimple =
                    kspayRiskFeatureConvert.convertToRiskDetailDTO(fundsRiskFeatureBranchDOAfter, request);
            kspayRiskFeatureDetailSimples.add(kspayRiskFeatureDetailSimple);
        }
        return kspayRiskFeatureDetailSimples;
//        return kspayRiskFeatureConvert.convertToRiskDetailDTO(methodCovInfos, fundsRiskFeatureBranchDOS, request);
    }

    // 去除其他处理逻辑，只给发消息使用
    @Override
    public List<KspayRiskFeatureDetailSimpleV2> queryRiskByRepoAndBranchV2Simple(KspayBranchRequestV2 request, ArrayList<String> qaManagerList) {
        List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskByRepoAndBranch(
                kspayRiskFeatureConvert.buildFundsRiskRiskQueryConditionV2(request)
        );
        List<KspayRiskFeatureDetailSimpleV2> kspayRiskFeatureDetailSimplesV2 = new ArrayList<>();
        KspayRiskFeatureDetailSimpleV2 kspayRiskFeatureDetailSimpleV2;
        for (FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO : fundsRiskFeatureBranchDOS) {
            kspayRiskFeatureDetailSimpleV2 = kspayRiskFeatureConvert
                    .convertToRiskDetailDTOV2(fundsRiskFeatureBranchDO, request, qaManagerList, true);
            kspayRiskFeatureDetailSimplesV2.add(kspayRiskFeatureDetailSimpleV2);
        }
        log.info("queryRiskByRepoAndBranchV2Simple: {}",
                toJSON(kspayRiskFeatureDetailSimplesV2));
        return kspayRiskFeatureDetailSimplesV2;

    }
    @Override
    public List<KspayRiskFeatureDetailSimpleV2> queryRiskByRepoAndBranchV2(KspayBranchRequestV2 request) {
        List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskByRepoAndBranch(
                kspayRiskFeatureConvert.buildFundsRiskRiskQueryConditionV2(request)
        );
        log.info("for kdev notice fundsRiskFeatureBranchDOS:{}" + fundsRiskFeatureBranchDOS.toString());
        if (fundsRiskFeatureBranchDOS == null) {
            log.info("for kdev notice数据库feature_view_branch 数据异常:{}", toJSON(fundsRiskFeatureBranchDOS));
            throw new BizException(RISK_FEATURE_VIEW_BRANCH_NOT_EXIST);
        }
        List<KspayRiskFeatureDetailSimpleV2> kspayRiskFeatureDetailSimplesV2 = new ArrayList<>();
        ArrayList<String> qamanagerList = new ArrayList<>();
        ArrayList<String> defalutqa = new ArrayList<>();
        defalutqa.add("renpan");

        for (FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO : fundsRiskFeatureBranchDOS) {
            /// 如果资损风险方法数<=0 && 无资损方法入口，直接将riskStatus和auditCover更新为已覆盖【避免仍需要人工打标歧义】
            Integer minMethodEntranceLength = 3;
            boolean noMethodEntrance = fundsRiskFeatureBranchDO.getMethodEntrance().length() < minMethodEntranceLength;
            log.info("V2接口资损方法noMethodEntrance:{}", toJSON(noMethodEntrance));
            try {
                if (fundsRiskFeatureBranchDO.getFundRiskMethodAmount() <= 0 && noMethodEntrance) {
                    log.info("queryRiskByRepoAndBranchV2更新前数据:{}", toJSON(fundsRiskFeatureBranchDO));
                    FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO1 = new FundsRiskFeatureBranchDO();
                    fundsRiskFeatureBranchDO1.setUpdater("system");
                    fundsRiskFeatureBranchDO1.setUpdateTime(System.currentTimeMillis());
                    fundsRiskFeatureBranchDO1.setRiskStatus(KspayRiskFeatureStatusEnum.NORMAL.getCode());
                    fundsRiskFeatureBranchDO1.setAuditCover(KspayRiskFeatureStatusEnum.NORMAL.getCode());
                    fundsRiskFeatureBranchDO1.setRiskStatusUpdater("system");
                    fundsRiskFeatureBranchDO1.setAuditCoverUpdater("system");
                    fundsRiskFeatureBranchDO1.setId(fundsRiskFeatureBranchDO.getId());
                    try {
                        int row = fundsRiskFeatureBranchDAO.updateAllStatusByMainId(fundsRiskFeatureBranchDO1);
                        checkState(row == 1, "queryRiskByRepoAndBranchV2前置更新不符合预期 实际插入数据:{}", row);
                    } catch (IllegalStateException e) {
                        log.info("queryRiskByRepoAndBranchV2前置更新未发生数据变化:{}", toJSON(fundsRiskFeatureBranchDO1));
                    } catch (Exception e) {
                        log.info("queryRiskByRepoAndBranchV2前置更新失败:{}", toJSON(fundsRiskFeatureBranchDO1));
                    }
                }
            } catch (Exception e) {
                log.info(String.format("V2接口更新riskFeatureBranch数据异常: e %s, ", e.getMessage()));
            }
            if  (request.getTeamId() != null) {
                qamanagerList = kspayTeamResourceService.fromBranchIdGetQa(Integer.valueOf(request.getTeamId()));
            }
            KspayRiskFeatureDetailSimpleV2 kspayRiskFeatureDetailSimpleV2;

            if (qamanagerList != null && qamanagerList.size() != 0) {
                //TODO 在这个地方进行

                /***
                 * 1、获取token  https://is-gateway.corp.kuaishou.com/token/get?appKey=5d564bdd-4a67-4cd1-93b0-06e785e7a054&secretKey=6d507869e0c14e9ba9b7db0d2ac98aa3
                 * 2、把消息发出去 https://is-gateway.corp.kuaishou.com/openapi/v2/message/batch/send?appKey=5d564bdd-4a67-4cd1-93b0-06e785e7a054&secretKey=6d507869e0c14e9ba9b7db0d2ac98aa3
                 */

                // 更新完之后再次查询一下
                FundsRiskFeatureBranchDO fundsRiskFeatureBranchDOAfter =
                        fundsRiskFeatureBranchDAO.queryByMainId(fundsRiskFeatureBranchDO.getId().toString());
                log.info("测试人员存在:fundsRiskFeatureBranchDOAfter:{}", fundsRiskFeatureBranchDOAfter);
                kspayRiskFeatureDetailSimpleV2 = kspayRiskFeatureConvert
                        .convertToRiskDetailDTOV2(fundsRiskFeatureBranchDOAfter, request, qamanagerList, false);
            } else {
                // 更新完之后再次查询一下
                FundsRiskFeatureBranchDO fundsRiskFeatureBranchDOAfter =
                        fundsRiskFeatureBranchDAO.queryByMainId(fundsRiskFeatureBranchDO.getId().toString());
                log.info("测试人员不存在或者信息错误，默认通知插件同学:fundsRiskFeatureBranchDOAfter:{},:{}", fundsRiskFeatureBranchDOAfter,
                        GsonUtils.toJSON(kspayRiskFeatureDetailSimplesV2));
                kspayRiskFeatureDetailSimpleV2 = kspayRiskFeatureConvert
                        .convertToRiskDetailDTOV2(fundsRiskFeatureBranchDOAfter, request, defalutqa, false);
            }

            kspayRiskFeatureDetailSimplesV2.add(kspayRiskFeatureDetailSimpleV2);
        }
        log.info("响应结构体是否正确" + kspayRiskFeatureDetailSimplesV2.toString());
        return kspayRiskFeatureDetailSimplesV2;
    }

    // 统计发现资损涉及的仓库，方法数，资损字段数
    @Override
    public GetRiskEntryAndFieldsStatisticResponse getKspayRiskStatistic(GetRiskEntryAndFieldsStatisticRequest request) {
        List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = new ArrayList<>();

        if (!request.getDepartment().isEmpty()) {
            List<FundsRiskFeatureDO> fundsRiskFeatureDOS = fundsRiskFeatureDAO.queryListByDepartment(request.getDepartment());

            for (FundsRiskFeatureDO fundsRiskFeatureDO : fundsRiskFeatureDOS) {
                List<FundsRiskFeatureBranchDO> branchDOS = fundsRiskFeatureBranchDAO.queryFundRiskFeatureBranch(
                        kspayRiskFeatureConvert.buildFundRiskFeatureBranchQueryCondition(fundsRiskFeatureDO.getFeatureId()));
                fundsRiskFeatureBranchDOS.addAll(branchDOS);
            }
        } else {
            if (request.getStartTime() == 0 && request.getEndTime() == 0) {
                fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.getKspayRiskStatistic();
            } else {
                fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.getKspayRiskStatisticWithTime(request.getStartTime(), request.getEndTime());
            }
        }

        // 根据请求参数判断统计有资损 || 无资损 || all
        List<FundsRiskFeatureBranchDO> filteredFundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDOS.stream()
                .filter(fundsRiskFeatureBranchDO -> {
                    Integer fundRiskMethodAmount = fundsRiskFeatureBranchDO.getFundRiskMethodAmount();
                    if (fundRiskMethodAmount == null) {
                        log.warn("fundRiskMethodAmount is null for featureId: {}", fundsRiskFeatureBranchDO.getFeatureViewId());
                        return request.getIsRisk() == 2; // 无资损情况下，null 也视为无资损
                    }
                    if (request.getIsRisk() == 1) { // 有资损
                        return fundRiskMethodAmount > 0;
                    } else if (request.getIsRisk() == 2) { // 无资损
                        return fundRiskMethodAmount <= 0;
                    } else { // all
                        return true;
                    }
                })
                .collect(Collectors.toList());

        // 初始化一个列表来存储所有riskTableFieldsJson
        List<String> allRiskTableFieldsJsons = new ArrayList<>();
        List<String> allMethodEntrances = new ArrayList<>();
        List<String> allRepoNames = new ArrayList<>();

        for (FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO : filteredFundsRiskFeatureBranchDOS) {
            if (fundsRiskFeatureBranchDO.getMethodEntrance() != null && !fundsRiskFeatureBranchDO.getMethodEntrance().isEmpty()) {
                allMethodEntrances.add(fundsRiskFeatureBranchDO.getMethodEntrance());
            }
            if (fundsRiskFeatureBranchDO.getRiskTableFields() != null && !fundsRiskFeatureBranchDO.getRiskTableFields().isEmpty()) {
                allRiskTableFieldsJsons.add(fundsRiskFeatureBranchDO.getRiskTableFields());
            }
            if (fundsRiskFeatureBranchDO.getRepoName() != null && !fundsRiskFeatureBranchDO.getRepoName().isEmpty()) {
                allRepoNames.add(fundsRiskFeatureBranchDO.getRepoName());
            }
        }

        // 计算并打印去重后的riskTableFields && MethodEntrance  && repoName 的数量
        int hasDuplicateRepo = new HashSet<>(allRepoNames).size();
        int hasDuplicateMethodCount = extractAndCountOperations(allMethodEntrances);
        int hasDuplicateFieldCount = printAndCountUniqueFields(allRiskTableFieldsJsons);
        // 不去重结果
        int noDuplicateRepo = allRepoNames.size();
        int noDuplicateMethodCount = countAllMethodEntrances(allMethodEntrances);
        int noDuplicateFieldCount = countAllRiskTableFields(allRiskTableFieldsJsons);

        // 根据request参数返回相应结果
        return GetRiskEntryAndFieldsStatisticResponse.newBuilder()
                .setResult(1)
                .setKspayAllRepo(request.getIsRepoDuplicate() ? hasDuplicateRepo : noDuplicateRepo)
                .setKspayAllEntry(request.getIsMethodDuplicate() ? hasDuplicateMethodCount : noDuplicateMethodCount)
                .setKspayAllFields(request.getIsFieldDuplicate() ? hasDuplicateFieldCount : noDuplicateFieldCount)
                .build();
    }

    // 看板统计数据接口
    @Override
    public List<FeatureStatisticInfo> kspayPageQueryRiskFeatureStatistic(PageQueryRiskFeatureStatisticRequest request) {
        FundsRiskFeatureRequestDO fundsRiskFeatureRequestDO = convertToFundsRiskFeatureRequestDO(request);

        // 查询数据
        Map<String, Object> fundsRiskFeatures = fundsRiskFeatureDaoImpl.getFundsRiskFeaturesByPage(fundsRiskFeatureRequestDO);
        List<FundsRiskFeatureDO> allFeatures = (List<FundsRiskFeatureDO>) fundsRiskFeatures.get("allFeatures");
        Long totalCount = (Long) fundsRiskFeatures.get("totalCount");

        // 获取解析数据
        List<FeatureStatisticInfo> featureStatisticInfos = parseRiskFeatureData(allFeatures);

        // 计算分页的起始位置和结束位置
        int offset = (request.getPageNo() - 1) * request.getPageSize();
        int limit = request.getPageSize();
        int endIndex = Math.min(offset + limit, featureStatisticInfos.size());

        // 分页处理
        List<FeatureStatisticInfo> pagedFeatureStatisticInfos = featureStatisticInfos.subList(offset, endIndex);

        // 打印日志
        log.info("[kspayPageQueryRiskFeatureStatistic] fundsRiskFeatureDOS:{}", allFeatures);

        // 返回结果
        return pagedFeatureStatisticInfos;
    }

    public List<FeatureStatisticInfo> parseRiskFeatureData(List<FundsRiskFeatureDO> allFeatures) {
        // 创建Gson实例，忽略未知字段
        Gson gson = new GsonBuilder()
                .excludeFieldsWithoutExposeAnnotation()
                .serializeNulls()
                .create();
        // 解析extraData并构建FeatureStatisticInfo
        List<FeatureStatisticInfo> featureStatisticInfos = allFeatures.stream()
                .filter(f -> f.isMarkedStatistic())
                .map(f -> {
                    FeatureStatisticInfo featureStatisticInfo = FeatureStatisticInfo.newBuilder()
                            .setFeatureName(f.getFeatureName())
                            .setUpdateTime(f.getUpdateTime())
                            .setQaList(f.getTeamWorker())
                            .setIsRisk(f.getIsRisk())
                            .setRiskPassStatus(f.getRiskPassStatus())
                            .setStatus(f.getStatus())
                            .setDepartment(f.getDepartment())
                            .setBusinessDomain(f.getBusinessDomain())
                            .build();

                    // 解析extraData
                    try {
                        String extraData = f.getExtraData();
                        if (extraData == null || extraData.trim().isEmpty()) {
                            // 如果extraData为null或空字符串，则使用默认值或跳过
                            log.warn("extraData is null or empty for feature: {}", f.getFeatureName());
                        } else {
                            JsonFormat.Parser parser = JsonFormat.parser();
                            KspayRiskUserMarkInfo.Builder riskUserMarkInfoBuilder = KspayRiskUserMarkInfo.newBuilder();
                            parser.merge(extraData, riskUserMarkInfoBuilder);
                            KspayRiskUserMarkInfo riskUserMarkInfo = riskUserMarkInfoBuilder.build();

                            if (riskUserMarkInfo != null) {
                                featureStatisticInfo = featureStatisticInfo.toBuilder()
                                        .setRiskUserMarkInfo(riskUserMarkInfo)
                                        .build();
                            }
                        }
                    } catch (InvalidProtocolBufferException e) {
                        log.error("Failed to parse extra data for feature: {}, error: {}", f.getFeatureName(), e.getMessage());
                    }
                    return featureStatisticInfo;
                })
                .collect(Collectors.toList());

        log.info("[kspayPageQueryRiskFeatureStatistic] fundsRiskFeatureDOS:{}", allFeatures);
        return featureStatisticInfos;
    }

    public static FundsRiskFeatureRequestDO convertToFundsRiskFeatureRequestDO(PageQueryRiskFeatureStatisticRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }
        FundsRiskFeatureRequestDO fundsRiskFeatureRequestDO = new FundsRiskFeatureRequestDO();

        fundsRiskFeatureRequestDO.setPageNo(request.getPageNo() != 0 ? request.getPageNo() : 1);
        fundsRiskFeatureRequestDO.setPageSize(request.getPageSize() != 0 ? request.getPageSize() : 20);
        fundsRiskFeatureRequestDO.setIsRisk(request.getIsRisk());
        fundsRiskFeatureRequestDO.setStartTime(request.getStartTime());
        fundsRiskFeatureRequestDO.setEndTime(request.getEndTime());
        fundsRiskFeatureRequestDO.setStatus(request.getStatusList().isEmpty() ? Collections.emptyList() : request.getStatusList());
        fundsRiskFeatureRequestDO.setRiskPassStatus(request.getRiskPassStatus());
        fundsRiskFeatureRequestDO.setDepartment(request.getDepartment());
        fundsRiskFeatureRequestDO.setBusinessDomain(request.getBusinessDomain());

        return fundsRiskFeatureRequestDO;
    }

    // msg重复请求检查
    private static final ConcurrentHashMap<String, LocalDateTime> REQUEST_CACHE = new ConcurrentHashMap<>();
    public boolean isDuplicateRequest(KdevAccuracyMsgSendRequest request) {
        String cacheKey = request.getRepoName() + ":" + request.getBranchName();
        LocalDateTime lastRequestTime = REQUEST_CACHE.get(cacheKey);
        // 如果lastRequestTime不存在，看作新请求
        if (lastRequestTime == null) {
            REQUEST_CACHE.put(cacheKey, LocalDateTime.now());
            return false;
        } else {
            // 检查是否超过1min
            if (LocalDateTime.now().isAfter(lastRequestTime.plus(1, ChronoUnit.MINUTES))) {
                REQUEST_CACHE.remove(cacheKey);
                REQUEST_CACHE.put(cacheKey, LocalDateTime.now());
                return false;
            } else {
                return true;
            }
        }
    }

    public void checkBizSpaceConfig(KdevAccuracyMsgSendRequest request) {
        HashMap<String, String> bizSpaceMap = BIZ_SPACE_MAP.get();
        List<String> bizSpaceList = new ArrayList<>(bizSpaceMap.keySet());
        log.info("[kdevAccuracyMsgSend] bizSpaceList: {}", toJSON(bizSpaceList));
        KspayBranchRequest branchRequest = KspayBranchRequest.newBuilder()
                .setRepoName(request.getRepoName())
                .setBranchName(request.getBranchName())
                .build();
        FundRiskFeatureDetailQueryCondition queryCondition = kspayRiskFeatureConvert.buildFundsRiskRiskQueryCondition(branchRequest);
        List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskByRepoAndBranch(queryCondition);
        if (fundsRiskFeatureBranchDOS.isEmpty()) {
            log.warn("[kdevAccuracyMsgSend] 未查询到关联分支，跳过处理.");
            return;
        }
        Optional<FundsRiskFeatureBranchDO> firstBranchOptional = fundsRiskFeatureBranchDOS.stream().findFirst();
        FundsRiskFeatureBranchDO firstBranchDO = firstBranchOptional.orElse(null);
        FundsRiskFeatureDO fundsRiskFeatureDO = fundsRiskFeatureDAO.queryByFeatureId(String.valueOf(firstBranchDO.getFeatureViewId()));
        if (fundsRiskFeatureDO == null) {
            log.warn("[kdevAccuracyMsgSend] 未查询到关联feature，跳过处理.");
            return;
        }
//        boolean isConfiguredSpace = bizSpaceList.stream()
//                .anyMatch(bizSpace -> bizSpace.equals(fundsRiskFeatureDO.getDepartment()));
        boolean isConfiguredSpace = bizSpaceList.stream()  // todo 临时逻辑，后面改回来
                .anyMatch(department -> department.equals("30") && department.equals(fundsRiskFeatureDO.getDepartment()));
        if (!isConfiguredSpace) {
            log.warn("[kdevAccuracyMsgSend] 所有配置空间均不符合条件，跳过处理.");
            return;
        }
    }
    @Override
    public void kdevAccuracyMsgSend(KdevAccuracyMsgSendRequest request) {
        // mq请求重复请求检查
        if (isDuplicateRequest(request)) {
            log.info("[KspayRiskFeatureBizServiceImpl.kdevAccuracyMsgSend] 重复请求，跳过处理: {}, {}",
                    request.getRepoName(), request.getBranchName());
            return;
        }
        // 只给配置的空间发消息
        checkBizSpaceConfig(request);
        // 消息发送逻辑
        try {
            List<KspayRiskFeatureDetail> riskFeatures = fetchAndProcessRiskFeatures(request);
            if (!riskFeatures.isEmpty()) {
                for (KspayRiskFeatureDetail featureDetail : riskFeatures) {
                    KdevFeatureDetailData currentFeatureDetail =
                            kspayTeamResourceServiceImpl.getfeaturedetail(Integer.parseInt(featureDetail.getFeatureId()));
                    ProtocolStringList kdevManagerProto = currentFeatureDetail.getDevManagersList();
                    ProtocolStringList qaManagersProto = currentFeatureDetail.getQaManagersList();
                    ArrayList<String> qaManagers = new ArrayList<>(qaManagersProto.size());
                    qaManagers.addAll(qaManagersProto);
                    log.info("[KspayRiskFeatureBizServiceImpl.kdevAccuracyMsgSend] qaManagers: {}",
                            qaManagers);
                    List<KspayRiskFeatureDetailSimpleV2> kspayRiskFeatureDetailSimpleV2s =
                            kspayRiskFeatureBizServiceImpl.queryRiskByRepoAndBranchV2Simple(KspayBranchRequestV2
                                    .newBuilder()
                                    .setBranchName(featureDetail.getBranchName())
                                    .setRepoName(featureDetail.getRepoName())
                                    .build(), qaManagers);
                    log.info("[KspayRiskFeatureBizServiceImpl.kdevAccuracyMsgSend] kspayRiskFeatureDetailSimpleV2s: {}",
                            kspayRiskFeatureDetailSimpleV2s);
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    //核对链面板
    public QueryCheckChainsByIdResponse queryChainsById(QueryCheckChainsById request) {
        try {
            QueryChainsByIdResponse response = QueryCheckAndRulesClient.queryCheckRuleById(request.getChainId());
            return QueryCheckChainsByIdResponse.newBuilder()
                    .setStatus(response.getStatus())
                    .setMessage(response.getMessage())
                    .setData(GsonUtils.toJSON(response.getData()))
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureBizServiceImpl.queryChainsById] error:{}", e);
            return QueryCheckChainsByIdResponse.newBuilder()
                    .setStatus(ResultCode.SERVICE_BUSY.getCode())
                    .setMessage(ResultCode.SERVICE_BUSY.getMessage())
                    .build();
        }

    }

    //确认规则有效性
    public MarkIfChainsValidResponse markIfChainsValid(MarkIfChainsValid request) {
        try {

            //如果规则有效
            if (request.getIsValid() == 1) {
                MarkIfValidResponseBO responseBO = markIfValid(request.getRuleId(), request.getRuleContent());
                //先查库
                //判断库里的字段有没有
                String ruleIsValid = fundsRiskFeatureBranchDAO.queryRuleIsValid(request.getRepoName(),
                        request.getBranchName(), request.getFeatureViewId());
                Map<Long, Integer> isValidMap = GsonUtils.fromJSON(ruleIsValid, Map.class);
                if (isValidMap == null) {
                    isValidMap = new HashMap<>();
                }
                isValidMap.put(request.getChainId(), request.getIsValid());
                //todo 存库
                fundsRiskFeatureBranchDAO.updateRuleIsValidByMixed(request.getRepoName(), request.getBranchName(),
                        request.getFeatureViewId(), GsonUtils.toJSON(isValidMap));
                return MarkIfChainsValidResponse.newBuilder()
                        .setStatus(responseBO.getStatus())
                        .setMessage(responseBO.getMessage())
                        .setData(GsonUtils.toJSON(responseBO.getData()))
                        .build();
                //修改后的规则有效并同步对帐
            } else if (request.getIsValid() == 2) {
                MarkIfValidResponseBO responseBO = markIfValid(request.getRuleId(), request.getRuleContent());
                //先查库
                //判断库里的字段有没有
                String ruleIsValid = fundsRiskFeatureBranchDAO.queryRuleIsValid(request.getRepoName(),
                        request.getBranchName(), request.getFeatureViewId());
                Map<Long, Integer> isValidMap = GsonUtils.fromJSON(ruleIsValid, Map.class);
                if (isValidMap == null) {
                    isValidMap = new HashMap<>();
                }
                isValidMap.put(request.getChainId(), request.getIsValid());
                //todo 存库
                fundsRiskFeatureBranchDAO.updateRuleIsValidByMixed(request.getRepoName(), request.getBranchName(),
                        request.getFeatureViewId(), GsonUtils.toJSON(isValidMap));
                return MarkIfChainsValidResponse.newBuilder()
                        .setStatus(responseBO.getStatus())
                        .setMessage(responseBO.getMessage())
                        .setData(GsonUtils.toJSON(responseBO.getData()))
                        .build();
                //如果规则无效无需同步对帐
            } else {
                String ruleIsValid = fundsRiskFeatureBranchDAO.queryRuleIsValid(request.getRepoName(),
                        request.getBranchName(), request.getFeatureViewId());
                Map<Long, Integer> isValidMap = GsonUtils.fromJSON(ruleIsValid, Map.class);
                if (isValidMap == null) {
                    isValidMap = new HashMap<>();
                }
                isValidMap.put(request.getChainId(), request.getIsValid());
                //todo 存库
                fundsRiskFeatureBranchDAO.updateRuleIsValidByMixed(request.getRepoName(), request.getBranchName(),
                        request.getFeatureViewId(), GsonUtils.toJSON(isValidMap));
                return MarkIfChainsValidResponse.newBuilder()
                        .setStatus(ResultCode.SUCCESS.getCode())
                        .setMessage(ResultCode.SUCCESS.getMessage())
                        .build();
            }


        } catch (Exception e) {
            log.error("[KspayRiskFeatureBizServiceImpl.markIfChainsValid] error:{}", e);
            return MarkIfChainsValidResponse.newBuilder()
                    .setStatus(ResultCode.SERVICE_BUSY.getCode())
                    .setMessage(ResultCode.SERVICE_BUSY.getMessage())
                    .build();
        }
    }

    // 计算资损方法入口总数【不去重】
    private int countAllMethodEntrances(List<String> methodEntrances) {
        int totalMethodCount = 0;

        // 检查methodEntrances是否为空或仅包含空字符串
        if (methodEntrances == null || methodEntrances.stream().allMatch(String::isEmpty)) {
            log.info("所有资损方法入口: 空列表");
            return totalMethodCount;
        }
        log.info("所有资损方法入口:{}", methodEntrances);
        for (String methodEntrance : methodEntrances) {
            if (!methodEntrance.isEmpty()) {
                // 分割整个methodEntrance字符串，而不是单个part
                String[] operations = methodEntrance.split(",");
                for (String operation : operations) {
                    int startIndex = operation.indexOf("#");
                    if (startIndex != -1) {
                        totalMethodCount++;
                    }
                }
            }
        }

        // 打印所有方法入口的数量
        log.info("所有资损方法入口数量:{}", totalMethodCount);
        // 返回所有方法入口的数量
        return totalMethodCount;
    }

    // 提取methodEntrance方法【去重后】
    private int extractAndCountOperations(List<String> methodEntrances) {
        HashSet<String> uniqueOperations = new HashSet<>();
        for (String methodEntrance : methodEntrances) {
            if (!methodEntrance.isEmpty()) {
                // 分割整个methodEntrance字符串，而不是单个part
                String[] operations = methodEntrance.split(",");
                for (String operation : operations) {
                    int startIndex = operation.indexOf("#");
                    if (startIndex != -1) {
                        operation = operation.substring(startIndex + 1).trim();
                        // 检查operation是否已存在于uniqueOperations中，避免重复添加
                        if (!uniqueOperations.contains(operation)) {
                            uniqueOperations.add(operation);
                        }
                    }
                }
            }
        }
        log.info("去重后资损方法:{}", uniqueOperations);
        return uniqueOperations.size();
    }

    // 废弃: 使用上面的方法了
    private int countMethodNames(List<String> methodEntrances) {
        // 去除空列表和去重
        List<String> cleanedMethods = methodEntrances.stream()
                .filter(method -> !method.isEmpty() && !"[]".equals(method))
                .distinct()
                .collect(Collectors.toList());
        // 正则表达式匹配
        int count = 0;
        for (String method : cleanedMethods) {
            Pattern pattern = Pattern.compile("#(.*?)\\(", Pattern.MULTILINE | Pattern.DOTALL);
            Matcher matcher = pattern.matcher(method);
            while (matcher.find()) {
                count++;
            }
        }
        // 打印去重后的数据
        log.info("去重后资损方法:{}", cleanedMethods);
        return count;
    }


    // 计算资损风险字段数【不考虑去重】
    private int countAllRiskTableFields(List<String> allRiskTableFieldsJsons) {
        int totalFieldCount = 0;
        log.info("字段数量allRiskTableFieldsJsons: {}", allRiskTableFieldsJsons);
        for (String json : allRiskTableFieldsJsons) {
            try {
                Gson gson = new Gson();
                List<Map<String, String>> fieldsList =
                        gson.fromJson(json, new TypeToken<List<Map<String, String>>>() { } .getType());
                for (Map<String, String> fieldMap : fieldsList) {
                    if (fieldMap.containsKey("field")) {
                        totalFieldCount++;
                    }
                }
            } catch (Exception e) {
                log.error("Error parsing JSON: {}", e.getMessage());
            }
        }

        // 打印所有字段的数量
        log.info("所有表字段数量:{}", totalFieldCount);
        // 返回所有字段的数量
        return totalFieldCount;
    }

    // 计算资损风险字段数【去重后】
    private int printAndCountUniqueFields(List<String> allRiskTableFieldsJsons) {
        // 合并所有字段
        String combinedFields = allRiskTableFieldsJsons.stream()
                .flatMap(json -> {
                    try {
                        Gson gson = new Gson();
                        List<Map<String, String>> fieldsList =
                                gson.fromJson(json, new TypeToken<List<Map<String, String>>>() { } .getType());
                        return fieldsList.stream()
                                .filter(fieldMap -> fieldMap.containsKey("field"))
                                .map(fieldMap -> fieldMap.get("field"));
                    } catch (Exception e) {
                        return Stream.empty();
                    }
                })
                .collect(Collectors.joining(","));

        // 使用HashSet去重并计算长度
        HashSet<String> uniqueFields = new HashSet<>(Arrays.asList(combinedFields.split(",")));
        // 打印去重后的字段
        log.info("去重后表字段:{}", toJSON(uniqueFields));
        // 返回唯一字段的数量
        return uniqueFields.size();
    }


    @Override
    public KspayRiskFeatureDetail queryRiskByRepoAndBranchAndViewId(KspayBranchAndViewIdRequest request) {
        FundsRiskFeatureBranchDO fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskByRepoAndBranchAndViewId(
                kspayRiskFeatureConvert.buildFundsRiskBranchQueryCondition(request)
        );
        if (fundsRiskFeatureBranchDOS == null) {
            log.info("数据库feature_view_branch 数据异常:{}", toJSON(fundsRiskFeatureBranchDOS));
            throw new BizException(RISK_FEATURE_VIEW_BRANCH_NOT_EXIST);
        }

        return kspayRiskFeatureConvert.buildKspayRiskFeatureDetail(fundsRiskFeatureBranchDOS);
    }

//    @Override
//    public void updateBranchByFeatureViewId(KspayRiskFeatureDetail request) {
        //        FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO = fundsRiskFeatureBranchDAO.queryByFeatureViewId(request.getFeatureViewId());
//        // 判断更新哪个字段
//        if (fundsRiskFeatureBranchDO == null) {
//            log.info("数据库feature_view_branch数据异常:{}", toJSON(fundsRiskFeatureBranchDO));
//        } else {
//            if (request.getRiskStatus() != 0 && request.getAuditCover() == 0
//                    && request.getRiskStatus() != fundsRiskFeatureBranchDO.getRiskStatus()) {
//                int row = fundsRiskFeatureBranchDAO.updateByFeatureViewId(kspayRiskFeatureConvert.buildUpdateFeatureViewBranch(request));
//                checkState(row == 1, "数据库更新条数不符合预期 实际插入数据;{}", row);
//            } else if (request.getAuditCover() != 0 && request.getRiskStatus() == 9
//                    && request.getAuditCover() != fundsRiskFeatureBranchDO.getAuditCover()) {
//                int row = fundsRiskFeatureBranchDAO.updateByFeatureViewId(kspayRiskFeatureConvert.buildUpdateFeatureViewBranch(request));
//                checkState(row == 1, "数据库更新条数不符合预期 实际插入数据;{}", row);
//            } else {
//                log.info("存在多个字段同时更新问题, 请排查:{}", toJSON(request));
//            }
//        }
//    }

    // 查询精准接口数据，与数据库数据对比，相关字段当前返回非空  && 不一致 -> 更新数据库
    public List<KspayRiskFeatureDetail> fetchAndProcessRiskFeatures(KdevAccuracyMsgSendRequest request) {
        List<KspayRiskFeatureDetail> riskFeatures = queryFeatureBranchDetail(request.getBranchName(), request.getRepoName());
        log.info("[KspayRiskFeatureBizServiceImpl.fetchAndProcessRiskFeatures] riskFeatures: {}",
                riskFeatures);
        for (KspayRiskFeatureDetail riskFeature : riskFeatures) {
            dealUpdateFeatureBranchInfo(riskFeature);
        }
        return queryFeatureBranchDetail(request.getBranchName(), request.getRepoName());
    }
    // 查询数据库，获取feature分支数据
    public List<KspayRiskFeatureDetail> queryFeatureBranchDetail(String branchName, String repoName) {
        KspayBranchRequest branchRequest = KspayBranchRequest.newBuilder()
                .setBranchName(branchName)
                .setRepoName(repoName)
                .setIsReturnEntry(false)
                .build();
        List<KspayRiskFeatureDetailSimple> featureDetailSimpleList =
                kspayRiskFeatureBizService.queryRiskByRepoAndBranchSimple(branchRequest);

        List<KspayRiskFeatureDetail> featureDetailList = new ArrayList<>();
        for (KspayRiskFeatureDetailSimple simple : featureDetailSimpleList) {
            KspayRiskFeatureDetail detail = KspayRiskFeatureDetail.newBuilder()
                    .setId(simple.getId())
                    .setFeatureViewId(simple.getFeatureViewId())
                    .setFeatureId(Integer.toString(simple.getFeatureId()))
                    .setRepoName(simple.getRepoName())
                    .setBranchName(simple.getBranchName())
                    .setMethodEntrance(simple.getMethodEntrance())
                    .setAccuracyUrl(simple.getAccuracyUrl())
                    .setChangeMethodCount(simple.getChangeMethodCount())
                    .setFundRiskMethodCount(simple.getFundRiskMethodCount())
                    .setTestResult(simple.getTestResult())
                    .setRiskStatus(simple.getRiskStatus())
                    .setAuditCover(simple.getAuditCover())
                    .setRiskTableFields(simple.getRiskTableFields())
                    .build();
            featureDetailList.add(detail);
        }
        return featureDetailList;
    }

    // 这里先复用KspayQaRiskFeatureViewBranchDataUpdateTask的处理逻辑
    public void dealUpdateFeatureBranchInfo(KspayRiskFeatureDetail branchDetail) {
        AccuracyData accuracyData;
        // 请求精准使用重试机制，最多三次
        accuracyData = kspayTeamResourceServiceImpl.getAccuracyDataWithRepeat(branchDetail.getRepoName(),
                branchDetail.getBranchName(), true, true);
        if (accuracyData == null) {
            log.info("[KspayRiskFeatureBizServiceImpl.dealUpdateFeatureBranchInfo] Accuracy data not available, "
                    + "skipping branch: {}", branchDetail);
            return;
        }
        log.info("KspayRiskFeatureBizServiceImpl.accuracyData: {}", accuracyData);
        boolean isNeedUpdateEntranceByAccuracy = kspayFundsRiskFeatureViewServiceImpl.
                isNeedUpdateMethodEntranceByAccuracy(branchDetail, accuracyData);
        boolean isNeedUpdateRiskTableFieldsByAccuracy = kspayFundsRiskFeatureViewServiceImpl.
                isNeedUpdateRiskTableFieldsByAccuracy(branchDetail, accuracyData);

        KspayFundsRiskFeatureViewBranchRequest.Builder branchRequestBuilder = KspayFundsRiskFeatureViewBranchRequest.newBuilder()
                .setFeatureViewId(String.valueOf(branchDetail.getFeatureViewId()))
                .setBranchName(branchDetail.getBranchName())
                .setRepoName(branchDetail.getRepoName());

        if (accuracyData != null && accuracyData.getResult() == 1) {
            List<FinanceMethod> financeMethods = accuracyData.getData().getFinanceMethodsList();
            List<Object> methodEntrance = new ArrayList<>();
            Set<Map<String, String>> payFinanceLossPointsMap = new HashSet<>();
            Gson gson = new GsonBuilder()
                    .setPrettyPrinting() // 保留格式化
                    .disableHtmlEscaping() // 防止空格和换行被转义
                    .create();

            // 如果需要更新methodEntrance和riskTableFields
            if (!CollectionUtils.isNullOrEmpty(financeMethods)) {
                for (FinanceMethod financeMethod : financeMethods) {
                    List<?> allEntryList = financeMethod.getAllEntryList();
                    if (!CollectionUtils.isNullOrEmpty(allEntryList)) {
                        methodEntrance.addAll(allEntryList);
                    }
                    log.info("<<<methodEntrance: {}", toJSON(methodEntrance));
                    if (!financeMethod.getMethodInfo().getPayFinanceLossPointsList().isEmpty()) {
                        for (PayFinanceLossPoints lossPoint : financeMethod.getMethodInfo().getPayFinanceLossPointsList()) {
                            Map<String, String> pointMap = new LinkedHashMap<>(); // 使用LinkedHashMap
                            pointMap.put("table", lossPoint.getTable());
                            pointMap.put("field", lossPoint.getField());
                            payFinanceLossPointsMap.add(pointMap);
                        }
                        log.info("task_payFinanceLossPointsMap: {}", payFinanceLossPointsMap);
                    }
                }
            } else {
                log.info("[KspayRiskFeatureBizServiceImpl.dealUpdateFeatureBranchInfo] Accuracy financeMethods not available, "
                        + "skipping branch: {}", branchDetail);
            }
            String riskTableFieldsJson = gson.toJson(payFinanceLossPointsMap);
            riskTableFieldsJson = riskTableFieldsJson.replaceAll("\\s", "");
            // 先设置共同的属性
            branchRequestBuilder
                    .setFeatureViewId(String.valueOf(branchDetail.getFeatureViewId()))
                    .setBranchName(branchDetail.getBranchName())
                    .setRepoName(branchDetail.getRepoName())
                    .setAccuracyUrl(accuracyData.getData().getReportUrl())
                    .setChangeMethodAmount(String.valueOf(accuracyData.getData().getChangeCnt()))
                    .setFundRiskMethodAmount(String.valueOf(accuracyData.getData().getFinnaceLossCnt()))
                    .setTestResult(String.valueOf(accuracyData.getData().getIsPass()))
                    .build();
            // 判断是否需要更新methodEntrance和riskTableFields
            if (isNeedUpdateEntranceByAccuracy || isNeedUpdateRiskTableFieldsByAccuracy) {
                // 根据需要设置methodEntrance和riskTableFields
                if (isNeedUpdateEntranceByAccuracy) {
                    branchRequestBuilder.setMethodEntrance(methodEntrance.toString());
                }
                if (isNeedUpdateRiskTableFieldsByAccuracy) {
                    branchRequestBuilder.setRiskTableFields(riskTableFieldsJson);
                }

            } else {
                log.info("[KspayRiskFeatureBizServiceImpl.dealUpdateFeatureBranchInfo] No methodEntrance and fields found, "
                        + "skipping update.");
            }
        } else {
            log.info("[KspayRiskFeatureBizServiceImpl.dealUpdateFeatureBranchInfo] Accuracy data not available, "
                    + "skipping branch: {}", branchDetail);
        }
        // 确保每次循环迭代结束时都构建branchRequest
        KspayFundsRiskFeatureViewBranchRequest branchRequest = branchRequestBuilder.build();
        log.info("[KspayRiskFeatureBizServiceImpl.dealUpdateFeatureBranchInfo] updateRiskFeatureViewBranch, "
                + "branchRequest: {}", toJSON(branchRequest));
        kspayFundsRiskFeatureViewService.kspayUpdateFundsRiskFeatureViewBranch(Long.valueOf(branchDetail.getId()), branchRequest);
    }

    // 资损打标统计数据看板接口





    /** 参数校验*/
    private void baseKspayRiskPreCheck(QueryPageRiskFeatureListRequest request) {
        checkArgument(isNotBlank(request.getDepartment()), "部门不能为空");
        checkArgument(isNotBlank(request.getStatus()), "状态不能为空");
    }

    private void updateKspayRiskFeatureViewPreCheck(KspayRiskFeatureViewParam request) {
        checkArgument(isNotBlank(request.getFeatureId()), "featureId不能为空");
//        checkArgument(request.getRiskPassStatus() != 0, "资金风险准出状态不能为空");   // 非必传
    }

    private void updateKspayBranchRiskStatusPreCheck(KspayRiskFeatureDetail request) {
        checkArgument(request.getRiskStatus() != 0, "资金风险测试结果不能为空");
    }

    private void updateKspayBranchAuditCoverPreCheck(KspayRiskFeatureDetail request) {
        checkArgument(request.getAuditCover() != 0, "核对是否覆盖不能为空");
    }
}
