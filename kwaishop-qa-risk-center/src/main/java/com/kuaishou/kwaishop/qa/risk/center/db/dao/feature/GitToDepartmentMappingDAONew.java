package com.kuaishou.kwaishop.qa.risk.center.db.dao.feature;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FirstDepartmentMappingDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.GitToDepartmentMappingDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.GitToDepartmentMappingQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.SecondDepartmentMappingDO;

@Repository
@Component
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public class GitToDepartmentMappingDAONew {
    private static final Logger logger = LoggerFactory.getLogger(GitToDepartmentMappingDAO.class);
    private BeanPropertyRowMapper<GitToDepartmentMappingDO> rowMapper = new BeanPropertyRowMapper<>(GitToDepartmentMappingDO.class);
    private BeanPropertyRowMapper<FirstDepartmentMappingDO> firstRowMapper = new BeanPropertyRowMapper<>(FirstDepartmentMappingDO.class);
    private BeanPropertyRowMapper<SecondDepartmentMappingDO> secondRowMapper = new BeanPropertyRowMapper<>(SecondDepartmentMappingDO.class);

    public List<SecondDepartmentMappingDO> queryDistinctSecondDepartmentInfo(GitToDepartmentMappingQueryCondition condition,
                                                                             NamedParameterJdbcTemplate jdbcTemplate) {
        StringBuilder sqlBuilder = new StringBuilder(String.format(
                "SELECT DISTINCT second_department_id, second_department_name FROM %s WHERE 1=1",
                getTableName()
        ));
        MapSqlParameterSource params = new MapSqlParameterSource();

        if (condition.getFirstDepartmentId() != null && condition.getFirstDepartmentId() != 0) {
            sqlBuilder.append(" AND first_department_id = :firstDepartmentId");
            params.addValue("firstDepartmentId", condition.getFirstDepartmentId());
        }
        if (condition.getSecondDepartmentId() != null && condition.getSecondDepartmentId() != 0) {
            sqlBuilder.append(" AND second_department_id = :secondDepartmentId");
            params.addValue("secondDepartmentId", condition.getSecondDepartmentId());
        }
        if (condition.getThirdDepartmentId() != null && condition.getThirdDepartmentId() != 0) {
            sqlBuilder.append(" AND third_department_id = :thirdDepartmentId");
            params.addValue("thirdDepartmentId", condition.getThirdDepartmentId());
        }
        if (condition.getGitProjectId() != null && !condition.getGitProjectId().equals(0L)) {
            sqlBuilder.append(" AND git_project_id = :gitProjectId");
            params.addValue("gitProjectId", condition.getGitProjectId());
        }
        if (condition.getGitName() != null && !condition.getGitName().trim().isEmpty()) {
            sqlBuilder.append(" AND git_name LIKE :gitName");
            params.addValue("gitName", "%" + condition.getGitName().trim() + "%");
        }
        if (condition.getFirstDepartmentName() != null && !condition.getFirstDepartmentName().trim().isEmpty()) {
            sqlBuilder.append(" AND first_department_name LIKE :firstDepartmentName");
            params.addValue("firstDepartmentName", "%" + condition.getFirstDepartmentName().trim() + "%");
        }
        if (condition.getSecondDepartmentName() != null && !condition.getSecondDepartmentName().trim().isEmpty()) {
            sqlBuilder.append(" AND second_department_name LIKE :secondDepartmentName");
            params.addValue("secondDepartmentName", "%" + condition.getSecondDepartmentName().trim() + "%");
        }
        if (condition.getThirdDepartmentName() != null && !condition.getThirdDepartmentName().trim().isEmpty()) {
            sqlBuilder.append(" AND third_department_name LIKE :thirdDepartmentName");
            params.addValue("thirdDepartmentName", "%" + condition.getThirdDepartmentName().trim() + "%");
        }

        String sql = sqlBuilder.toString();
        logger.info("[GitToDepartmentMappingDAOImpl] queryDistinctSecondDepartmentInfo sql: {}", sql);
        logger.info("[GitToDepartmentMappingDAOImpl] queryDistinctSecondDepartmentInfo params: {}", params.getValues());

        return jdbcTemplate.query(sql, params, secondRowMapper);
    }

    public List<FirstDepartmentMappingDO> queryDistinctFirstDepartmentInfo(GitToDepartmentMappingQueryCondition condition,
                                                                           NamedParameterJdbcTemplate jdbcTemplate) {
        StringBuilder sqlBuilder = new StringBuilder(String.format(
                "SELECT DISTINCT first_department_id, first_department_name FROM %s WHERE 1=1",
                getTableName()
        ));
        MapSqlParameterSource params = new MapSqlParameterSource();

        if (condition.getFirstDepartmentId() != null && condition.getFirstDepartmentId() != 0) {
            sqlBuilder.append(" AND first_department_id = :firstDepartmentId");
            params.addValue("firstDepartmentId", condition.getFirstDepartmentId());
        }
        if (condition.getSecondDepartmentId() != null && condition.getSecondDepartmentId() != 0) {
            sqlBuilder.append(" AND second_department_id = :secondDepartmentId");
            params.addValue("secondDepartmentId", condition.getSecondDepartmentId());
        }
        if (condition.getThirdDepartmentId() != null && condition.getThirdDepartmentId() != 0) {
            sqlBuilder.append(" AND third_department_id = :thirdDepartmentId");
            params.addValue("thirdDepartmentId", condition.getThirdDepartmentId());
        }
        if (condition.getGitProjectId() != null && !condition.getGitProjectId().equals(0L)) {
            sqlBuilder.append(" AND git_project_id = :gitProjectId");
            params.addValue("gitProjectId", condition.getGitProjectId());
        }
        if (condition.getGitName() != null && !condition.getGitName().trim().isEmpty()) {
            sqlBuilder.append(" AND git_name LIKE :gitName");
            params.addValue("gitName", "%" + condition.getGitName().trim() + "%");
        }
        if (condition.getFirstDepartmentName() != null && !condition.getFirstDepartmentName().trim().isEmpty()) {
            sqlBuilder.append(" AND first_department_name LIKE :firstDepartmentName");
            params.addValue("firstDepartmentName", "%" + condition.getFirstDepartmentName().trim() + "%");
        }
        if (condition.getSecondDepartmentName() != null && !condition.getSecondDepartmentName().trim().isEmpty()) {
            sqlBuilder.append(" AND second_department_name LIKE :secondDepartmentName");
            params.addValue("secondDepartmentName", "%" + condition.getSecondDepartmentName().trim() + "%");
        }
        if (condition.getThirdDepartmentName() != null && !condition.getThirdDepartmentName().trim().isEmpty()) {
            sqlBuilder.append(" AND third_department_name LIKE :thirdDepartmentName");
            params.addValue("thirdDepartmentName", "%" + condition.getThirdDepartmentName().trim() + "%");
        }

        String sql = sqlBuilder.toString();
        logger.info("[GitToDepartmentMappingDAOImpl] queryDistinctFirstDepartmentInfo sql: {}", sql);
        logger.info("[GitToDepartmentMappingDAOImpl] queryDistinctFirstDepartmentInfo params: {}", params.getValues());

        return jdbcTemplate.query(sql, params, firstRowMapper);
    }

    public List<GitToDepartmentMappingDO> queryGitToDepartmentMappingInfo(
            GitToDepartmentMappingQueryCondition condition,
            int offset,
            int limit,
            int defaultPageSize,
            NamedParameterJdbcTemplate jdbcTemplate) {
        StringBuilder sqlBuilder = new StringBuilder(String.format(
                "SELECT * FROM %s WHERE 1=1",
                getTableName()
        ));
        MapSqlParameterSource params = new MapSqlParameterSource();

        if (condition.getGitProjectId() != null && !condition.getGitProjectId().equals(0L)) {
            sqlBuilder.append(" AND git_project_id = :gitProjectId");
            params.addValue("gitProjectId", condition.getGitProjectId());
        }
        if (condition.getGitName() != null && !condition.getGitName().trim().isEmpty()) {
            sqlBuilder.append(" AND git_name LIKE :gitName");
            params.addValue("gitName", "%" + condition.getGitName().trim() + "%");
        }
        if (condition.getFirstDepartmentId() != null && condition.getFirstDepartmentId() != 0) {
            sqlBuilder.append(" AND first_department_id = :firstDepartmentId");
            params.addValue("firstDepartmentId", condition.getFirstDepartmentId());
        }
        if (condition.getSecondDepartmentId() != null && condition.getSecondDepartmentId() != 0) {
            sqlBuilder.append(" AND second_department_id = :secondDepartmentId");
            params.addValue("secondDepartmentId", condition.getSecondDepartmentId());
        }
        if (condition.getThirdDepartmentId() != null && condition.getThirdDepartmentId() != 0) {
            sqlBuilder.append(" AND third_department_id = :thirdDepartmentId");
            params.addValue("thirdDepartmentId", condition.getThirdDepartmentId());
        }
        if (condition.getFirstDepartmentName() != null && !condition.getFirstDepartmentName().trim().isEmpty()) {
            sqlBuilder.append(" AND first_department_name LIKE :firstDepartmentName");
            params.addValue("firstDepartmentName", "%" + condition.getFirstDepartmentName().trim() + "%");
        }
        if (condition.getSecondDepartmentName() != null && !condition.getSecondDepartmentName().trim().isEmpty()) {
            sqlBuilder.append(" AND second_department_name LIKE :secondDepartmentName");
            params.addValue("secondDepartmentName", "%" + condition.getSecondDepartmentName().trim() + "%");
        }
        if (condition.getThirdDepartmentName() != null && !condition.getThirdDepartmentName().trim().isEmpty()) {
            sqlBuilder.append(" AND third_department_name LIKE :thirdDepartmentName");
            params.addValue("thirdDepartmentName", "%" + condition.getThirdDepartmentName().trim() + "%");
        }

        sqlBuilder.append(" LIMIT :limit OFFSET :offset");
        params.addValue("limit", limit);
        params.addValue("offset", offset);

        String sql = sqlBuilder.toString();
        logger.info("[RiskFeatureSecondDepartmentWriteTask] queryGitToDepartmentMappingInfo sql: {}", sql);
        logger.info("[RiskFeatureSecondDepartmentWriteTask] queryGitToDepartmentMappingInfo params: {}", params.getValues());

        return jdbcTemplate.query(sql, params, rowMapper);
    }


    public List<String> querySecondDepartmentName(
            GitToDepartmentMappingQueryCondition condition,
            NamedParameterJdbcTemplate jdbcTemplate) {
        StringBuilder sqlBuilder = new StringBuilder(String.format(
                "SELECT DISTINCT second_department_name FROM %s WHERE 1=1",
                getTableName()
        ));
        MapSqlParameterSource params = new MapSqlParameterSource();

        if (condition.getGitProjectId() != null && !condition.getGitProjectId().equals(0L)) {
            sqlBuilder.append(" AND git_project_id = :gitProjectId");
            params.addValue("gitProjectId", condition.getGitProjectId());
        }
        if (condition.getGitName() != null && !condition.getGitName().trim().isEmpty()) {
            sqlBuilder.append(" AND git_name LIKE :gitName");
            params.addValue("gitName", "%" + condition.getGitName().trim() + "%");
        }
        if (condition.getFirstDepartmentId() != null && condition.getFirstDepartmentId() != 0) {
            sqlBuilder.append(" AND first_department_id = :firstDepartmentId");
            params.addValue("firstDepartmentId", condition.getFirstDepartmentId());
        }
        if (condition.getSecondDepartmentId() != null && condition.getSecondDepartmentId() != 0) {
            sqlBuilder.append(" AND second_department_id = :secondDepartmentId");
            params.addValue("secondDepartmentId", condition.getSecondDepartmentId());
        }
        if (condition.getThirdDepartmentId() != null && condition.getThirdDepartmentId() != 0) {
            sqlBuilder.append(" AND third_department_id = :thirdDepartmentId");
            params.addValue("thirdDepartmentId", condition.getThirdDepartmentId());
        }
        if (condition.getFirstDepartmentName() != null && !condition.getFirstDepartmentName().trim().isEmpty()) {
            sqlBuilder.append(" AND first_department_name LIKE :firstDepartmentName");
            params.addValue("firstDepartmentName", "%" + condition.getFirstDepartmentName().trim() + "%");
        }
        if (condition.getSecondDepartmentName() != null && !condition.getSecondDepartmentName().trim().isEmpty()) {
            sqlBuilder.append(" AND second_department_name LIKE :secondDepartmentName");
            params.addValue("secondDepartmentName", "%" + condition.getSecondDepartmentName().trim() + "%");
        }
        if (condition.getThirdDepartmentName() != null && !condition.getThirdDepartmentName().trim().isEmpty()) {
            sqlBuilder.append(" AND third_department_name LIKE :thirdDepartmentName");
            params.addValue("thirdDepartmentName", "%" + condition.getThirdDepartmentName().trim() + "%");
        }

        String sql = sqlBuilder.toString();
        logger.info("querySecondDepartmentName sql: {}", sql);
        return jdbcTemplate.queryForList(sql, params, String.class);
    }

    private String getTableName() {
        return "kspay_git_department_mapping";
    }
}