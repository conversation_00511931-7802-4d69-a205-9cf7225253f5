package com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2022-11-14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataQueryExecuteBO {

    /**
     * 数据源id
     */
    private Long dataSourceId;

    /**
     * 数据源查询执行名称
     */
    private String name;

    /**
     * 执行人中文名称
     */
    private String executorName;

    /**
     * 查询状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 查询开始时间
     */
    private Long startTime;

    /**
     * 查询结束时间
     */
    private Long endTime;

    /**
     * 查询日志
     */
    private String log;

    /**
     * 查询结果
     */
    private String result;


}
