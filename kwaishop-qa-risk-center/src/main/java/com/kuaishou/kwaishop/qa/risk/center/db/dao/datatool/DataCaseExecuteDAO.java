package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.CaseExecuteDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.CaseExecuteBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2022-12-11
 */
public interface DataCaseExecuteDAO {

    long insertCaseExecute(CaseExecuteDO caseExecuteDO);

    long updateCaseExecute(CaseExecuteDO caseExecuteDO);

    int logicDeleted(Long id, String operator);

    /**
     * 根据name模糊查询
     * @param name
     * @return
     */
    List<CaseExecuteDO> queryCaseExecuteLikeName(String name);

    PageBO<CaseExecuteDO> queryPageCaseExecuteList(CaseExecuteBO caseExecuteBO);

    List<CaseExecuteDO> queryCaseExecuteList(CaseExecuteDO dataCaseDO);
    CaseExecuteDO queryCaseExecuteById(Long id);

}
