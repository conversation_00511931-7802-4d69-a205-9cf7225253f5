package com.kuaishou.kwaishop.qa.risk.center.db.query.account;

import java.util.Collection;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-25
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class RentAccountQueryCondition extends BaseQueryCondition {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户bid
     */
    private Long bId;

    /**
     * 登录类型，1- 86手机号登录，2- 1264手机号登录，3- 邮箱登录
     */
    private Integer loginType;

    /**
     * 租借人
     */
    private String borrower;

    /**
     * 数据类型，1-全数据类型，2-单uid数据类型
     */
    private Integer dataType;

    /**
     * 账号状态，1-可借用，2-不可借用
     */
    private Integer rentalStatus;

    private Long teamId;

    private Long centerId;

    private Collection<Long> userIds;

    private Collection<Integer> rentalStatuses;

    private Collection<Integer> loginTypes;

    private Collection<Long> teamIds;

    private Collection<Long> centerIds;
}
