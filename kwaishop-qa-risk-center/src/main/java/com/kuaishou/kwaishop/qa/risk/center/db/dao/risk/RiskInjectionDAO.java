package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk;


import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.RiskInjectionDO;

public interface RiskInjectionDAO {

    //插入一条资金防控手段
    void insertRiskInjection(RiskInjectionDO riskInjectionDo);

    // 根据注入已有信息获取 风险关联id 及是否演练
    RiskInjectionDO queryRiskInjection(RiskInjectionDO riskInjectionDo);

    void updateRiskInjectionOperate(Long id);


    List<RiskInjectionDO> queryRiskInjectionRelation(RiskInjectionDO riskInjectionDo);



}
