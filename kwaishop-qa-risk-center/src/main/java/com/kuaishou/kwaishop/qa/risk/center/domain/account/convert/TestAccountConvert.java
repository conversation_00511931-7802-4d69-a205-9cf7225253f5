package com.kuaishou.kwaishop.qa.risk.center.domain.account.convert;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagTestAccountRelDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TestAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.AddTagsToTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryMyRentTestAccountsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryMyTestAccountsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryTestAccountsRequest;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/15 14:38
 * @注释
 */
public interface TestAccountConvert {
    TestAccountBO buildQueryBO(QueryMyTestAccountsRequest request);

    TestAccountBO buildQueryBO(QueryTestAccountsRequest request);

    TestAccountBO buildQueryBO(QueryMyRentTestAccountsRequest request);

    TestAccountBO buildQueryBO(QueryTestAccountRequest request);

    TagTestAccountRelDO buildTagTestAccountRelDO(String tag, AddTagsToTestAccountRequest request);
}
