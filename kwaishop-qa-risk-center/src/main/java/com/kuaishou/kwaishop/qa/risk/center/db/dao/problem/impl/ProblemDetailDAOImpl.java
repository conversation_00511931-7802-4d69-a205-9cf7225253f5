package com.kuaishou.kwaishop.qa.risk.center.db.dao.problem.impl;

import java.util.Collection;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.problem.ProblemDetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.problem.ProblemDetailDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.problem.ProblemDetailMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.problem.ProblemDetailQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.ProblemQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.bo.ProblemDetailBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.enums.ProblemDetailTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.enums.ProblemLevelTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.enums.ProblemMissingTypeEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-02
 */
@Repository
public class ProblemDetailDAOImpl extends BaseDAOImpl<ProblemDetailDO, ProblemDetailQueryCondition> implements ProblemDetailDAO {

    @Autowired
    private ProblemDetailMapper problemDetailMapper;

    @Override
    public List<ProblemDetailDO> queryByCenterIds(Collection<Long> centerIds, ProblemQueryParam queryParam) {
        ProblemDetailQueryCondition queryCondition = ProblemDetailQueryCondition.builder()
                .centerIds(centerIds)
                .orderByIdDesc(true)
                .problemStartTimeGe(queryParam.getProblemStartTimeGe())
                .problemStartTimeLe(queryParam.getProblemStartTimeLe())
                .build();
        return queryList(queryCondition);
    }

    @Override
    public List<ProblemDetailDO> queryByTeamIds(Long centerId, Collection<Long> teamIds, ProblemQueryParam queryParam) {
        ProblemDetailQueryCondition queryCondition = ProblemDetailQueryCondition.builder()
                .centerId(centerId)
                .teamIds(teamIds)
                .orderByIdDesc(true)
                .problemStartTimeGe(queryParam.getProblemStartTimeGe())
                .problemStartTimeLe(queryParam.getProblemStartTimeLe())
                .build();
        return queryList(queryCondition);
    }

    @Override
    public List<ProblemDetailDO> queryByCenterId(Long centerId, ProblemQueryParam queryParam) {
        ProblemDetailQueryCondition queryCondition = ProblemDetailQueryCondition.builder()
                .centerId(centerId)
                .orderByIdDesc(true)
                .problemStartTimeGe(queryParam.getProblemStartTimeGe())
                .problemStartTimeLe(queryParam.getProblemStartTimeLe())
                .build();
        return queryList(queryCondition);
    }

    @Override
    public List<ProblemDetailDO> queryByTeamId(Long centerId, Long teamId, ProblemQueryParam queryParam) {
        ProblemDetailQueryCondition queryCondition = ProblemDetailQueryCondition.builder()
                .centerId(centerId)
                .teamId(teamId)
                .orderByIdDesc(true)
                .problemStartTimeGe(queryParam.getProblemStartTimeGe())
                .problemStartTimeLe(queryParam.getProblemStartTimeLe())
                .build();
        return queryList(queryCondition);
    }

    @Override
    public List<ProblemDetailDO> queryByCenterIdsTeamIds(Collection<Long> centerIds, Collection<Long> teamIds,
            ProblemQueryParam queryParam) {
        ProblemDetailQueryCondition queryCondition = ProblemDetailQueryCondition.builder()
                .centerIds(centerIds)
                .teamIds(teamIds)
                .orderByIdDesc(true)
                .problemStartTimeGe(queryParam.getProblemStartTimeGe())
                .problemStartTimeLe(queryParam.getProblemStartTimeLe())
                .build();
        return queryList(queryCondition);
    }


    @Override
    public List<ProblemDetailDO> queryProblemDetailList(ProblemDetailBO problemDetailBO) {
        ProblemDetailQueryCondition queryCondition = ProblemDetailQueryCondition.builder()
                .id(problemDetailBO.getId())
                .teamId(problemDetailBO.getTeamId())
                .centerId(problemDetailBO.getCenterId())
                .name(problemDetailBO.getName())
                .detailType(problemDetailBO.getDetailType())
                .level(problemDetailBO.getLevel())
                .missingType(problemDetailBO.getMissingType())
                .orderByIdDesc(true)
                .build();
        return queryList(queryCondition);
    }

    @Override
    public PageBO<ProblemDetailDO> queryPageProblemDetailList(ProblemDetailBO problemDetailBO) {
        ProblemDetailQueryCondition queryCondition = ProblemDetailQueryCondition.builder()
                .id(problemDetailBO.getId())
                .teamId(problemDetailBO.getTeamId())
                .centerId(problemDetailBO.getCenterId())
                .name(problemDetailBO.getName())
                .detailType(problemDetailBO.getDetailType())
                .level(problemDetailBO.getLevel())
                .missingType(problemDetailBO.getMissingType())
                .pageNo(problemDetailBO.getPageNo())
                .pageSize(problemDetailBO.getPageSize())
                .orderByIdDesc(true)
                .build();
        return queryPageList(queryCondition);
    }

    @Override
    protected void fillQueryCondition(ProblemDetailQueryCondition condition,
            QueryWrapper<ProblemDetailDO> queryWrapper) {
        if (condition.getCenterId() != null && condition.getCenterId() > 0) {
            queryWrapper.and(q -> q.eq("center_id", condition.getCenterId()));
        }
        if (condition.getTeamId() != null && condition.getTeamId() > 0) {
            queryWrapper.and(q -> q.eq("team_id", condition.getTeamId()));
        }
        if (StringUtils.isNotBlank(condition.getName())) {
            queryWrapper.and(q -> q.like("name", condition.getName()));
        }
        if (condition.getDetailType() != null && ProblemDetailTypeEnum.of(condition.getDetailType()) != null) {
            queryWrapper.and(q -> q.eq("detail_type", condition.getDetailType()));
            if (condition.getLevel() != null
                    && ProblemLevelTypeEnum.of(condition.getLevel(), condition.getDetailType()) != null) {
                queryWrapper.and(q -> q.eq("level", condition.getLevel()));
            }
        }
        if (condition.getMissingType() != null && ProblemMissingTypeEnum.of(condition.getMissingType()) != null) {
            queryWrapper.and(q -> q.eq("missing_type", condition.getMissingType()));
        }
        if (condition.getProblemStartTimeGe() != null && condition.getProblemStartTimeGe() > 0) {
            queryWrapper.and(q -> q.ge("problem_start_time", condition.getProblemStartTimeGe()));
        }
        if (condition.getProblemStartTimeLe() != null && condition.getProblemStartTimeLe() > 0) {
            queryWrapper.and(q -> q.le("problem_start_time", condition.getProblemStartTimeLe()));
        }
    }

    @Override
    protected BaseMapper<ProblemDetailDO> getMapper() {
        return problemDetailMapper;
    }

    @Override
    public int logicDeleted(Long id, String operator) {
        return problemDetailMapper.logicDeleted(operator, id);
    }
}
