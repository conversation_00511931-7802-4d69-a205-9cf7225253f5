package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service;

import java.util.List;

import com.google.gson.JsonArray;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTestDataPool;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-22
 */

public interface DataService {
    List<StrategyTestDataPool> getDataPoolByRpc();

    JsonArray mockData();

    String run();

    List<StrategyTestDataPool> getDataPoolById(ReplayRequest request);
}
