package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert;

import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import com.google.api.client.util.Lists;
import com.google.common.base.Splitter;
import com.kuaishou.kconf.common.json.JsonMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.common.Enum.DeletedEnum;
import com.kuaishou.kwaishop.qa.risk.center.common.Enum.ReplayStatus;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.DiffDetailResultDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordExtraDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordStartEndPDate;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowReplayListRequestPojo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.IpPortDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecodeDetailClickHouseDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.ReplayConfig;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.ReplayConfigDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.ReplayKafkaDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowRealtimeKafkaDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowReplayBo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowReplayDo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.replayHandler.StrategyCheckResult;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.util.StringEscapeUtils;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowReplayAddRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowReplayListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetFlowReplayDetailListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.PodInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayConfig.Builder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-17
 */
public class ReplayConvert {

    public static StrategyFlowReplayDo addRequest2Do(FlowReplayAddRequest request, FlowRecordExtraDto extraDto) {
        ReplayConfigDto replayConfigDto = new ReplayConfigDto();
        replayConfigDto.setSceneKey(request.getSceneKey());
        replayConfigDto.setWhiteFeatureKey(Splitter.on(",").splitToList(request.getWhiteFeatureKey()));
        replayConfigDto.setBlackFeatureKey(Splitter.on(",").splitToList(request.getBlackFeatureKey()));
        replayConfigDto.setReplayConfigOne(pbReplayConfig2Config(request.getReplayConfigOne()));
        replayConfigDto.setReplayConfigTwo(pbReplayConfig2Config(request.getReplayConfigTwo()));

        StrategyFlowReplayDo replayDo = new StrategyFlowReplayDo();
        replayDo.setName(request.getName());
        replayDo.setFlowRecordId(request.getFlowRecordId());
        replayDo.setDescription(request.getDescription());
        replayDo.setDiffResult(NumberUtils.INTEGER_ZERO);
        replayDo.setDiffDetailResult(JsonMapperUtils.toJson(new DiffDetailResultDto()));
        replayDo.setReplayQps(request.getReplayQps());
        replayDo.setReplayConfig(JsonMapperUtils.toJson(replayConfigDto));
        replayDo.setReplayStatus(ReplayStatus.PROCESSING.getCode());
        replayDo.setReplayStartTime(System.currentTimeMillis());
        replayDo.setCreateTime(System.currentTimeMillis());
        replayDo.setUpdateTime(System.currentTimeMillis());
        replayDo.setCreateUser(request.getOperator());
        replayDo.setTotal(0); // 回放0条
        replayDo.setUpdateUser(request.getOperator());
        replayDo.setDeleted(DeletedEnum.VALID.getCode());
        return replayDo;
    }

    public static ReplayConfig pbReplayConfig2Config(
            com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayConfig pbReplayConfig) {
        ReplayConfig replayConfig = new ReplayConfig();
        replayConfig.setReplayType(pbReplayConfig.getReplayType());
        replayConfig.setLandId(pbReplayConfig.getLandId());
        replayConfig.setGroup(pbReplayConfig.getGroup());
        replayConfig.setIpPortDtoList(portInfos2IpPorts(pbReplayConfig.getPodInfoListList()));
        return replayConfig;
    }

    public static List<IpPortDto> portInfos2IpPorts(List<String> podInfoList) {
        if (podInfoList.isEmpty()) {
            return Lists.newArrayList();
        }


        return podInfoList.stream().map(ReplayConvert::podInfo2IpPort).collect(Collectors.toList());
    }

    public static IpPortDto podInfo2IpPort(String podInfo) {
        if (StringUtils.isBlank(podInfo)) {
            return null;
        }
        IpPortDto dto = new IpPortDto();
        String[] podInfoStrings = podInfo.split(":");
        // PROD_L1_fullName:ip_port 这种形式，进行解析。
        dto.setIp(podInfoStrings[1].split("_")[0]);
        dto.setPort(Integer.parseInt(podInfoStrings[1].split("_")[1]));
        dto.setFullName(podInfoStrings[0].split("_")[2]);
        return dto;
    }

    public static com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayConfig replayConfig2PbConfig(
            ReplayConfig replayConfig) {
        Builder builder = com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayConfig.newBuilder();
        builder.setReplayType(replayConfig.getReplayType());
        if (Objects.nonNull(replayConfig.getLandId())) {
            builder.setLandId(replayConfig.getLandId());
        }
        if (Objects.nonNull(replayConfig.getGroup())) {
            builder.setGroup(replayConfig.getGroup());
        }
        if (CollectionUtils.isNotEmpty(replayConfig.getIpPortDtoList())) {
            builder.addAllPodInfoList(ipPorts2PortInfos(replayConfig.getIpPortDtoList()));
        }
        return builder.build();
    }

    public static List<String> ipPorts2PortInfos(List<IpPortDto> ipPortDtos) {
        if (CollectionUtils.isNotEmpty(ipPortDtos)) {
            return Lists.newArrayList();
        }
        List<String> result = Lists.newArrayList();
        for (IpPortDto ipPortDto : ipPortDtos) {
            // PROD_L1_fullName:ip_port 这种形式，进行解析。
            result.add(String.format("%s_%s_%s", ipPortDto.getFullName(), ipPortDto.getIp(), ipPortDto.getPort()));
            //            PodInfo podInfo = ipPort2PodInfo(ipPortDto);
            //            if (Objects.nonNull(podInfo)) {
            //                result.add(podInfo);
            //            }
        }
        return result;
    }

    public static PodInfo ipPort2PodInfo(IpPortDto ipPortDto) {
        if (StringUtils.isBlank(ipPortDto.getFullName())) {
            return null;
        }
        PodInfo.Builder builder = PodInfo.newBuilder();
        if (StringUtils.isNotBlank(ipPortDto.getFullName())) {
            builder.setFullName(ipPortDto.getFullName());
        }
        if (StringUtils.isNotBlank(ipPortDto.getIp())) {
            builder.setIp(ipPortDto.getIp());
        }
        if (Objects.nonNull(ipPortDto.getPort())) {
            builder.setPort(String.valueOf(ipPortDto.getPort()));
        }
        return builder.build();
    }

    public static StrategyFlowReplayBo do2Bo(StrategyFlowReplayDo replayDo) {
        StrategyFlowReplayBo replayBo = new StrategyFlowReplayBo();
        replayBo.setId(replayDo.getId());
        replayBo.setName(replayDo.getName());
        replayBo.setFlowRecordId(replayDo.getFlowRecordId());
        replayBo.setDescription(replayDo.getDescription());
        replayBo.setTotal(replayDo.getTotal());
        replayBo.setDiffResult(replayDo.getDiffResult());
        if (replayDo.getDiffResult() == NumberUtils.INTEGER_ONE) {
            replayBo.setDiffResultDesc("有");
        } else {
            replayBo.setDiffResultDesc("无");
        }
        if (StringUtils.isNotBlank(replayDo.getDiffDetailResult())) {
            DiffDetailResultDto diffDetailResultDto = new DiffDetailResultDto();
            if (StringUtils.isNotBlank(replayDo.getDiffDetailResult())) {
                diffDetailResultDto =
                        JsonMapperUtils.fromJson(replayDo.getDiffDetailResult(), DiffDetailResultDto.class);
            }
            replayBo.setDiffDetailResult(diffDetailResultDto.getDiffPercent());
        }
        replayBo.setReplayQps(replayDo.getReplayQps());
        if (StringUtils.isNotBlank(replayDo.getReplayConfig())) {
            ReplayConfigDto replayConfigDto =
                    JsonMapperUtils.fromJson(replayDo.getReplayConfig(), ReplayConfigDto.class);
            replayBo.setSceneKey(replayConfigDto.getSceneKey());
            replayBo.setWhiteFeatureKey(convertListToCommaSeparatedString(replayConfigDto.getWhiteFeatureKey()));
            replayBo.setBlackFeatureKey(convertListToCommaSeparatedString(replayConfigDto.getBlackFeatureKey()));
            replayBo.setReplayConfigOne(replayConfigDto.getReplayConfigOne());
            replayBo.setReplayConfigTwo(replayConfigDto.getReplayConfigTwo());
        }
        replayBo.setReplayStatus(replayDo.getReplayStatus());
        replayBo.setReplayStatusDesc(ReplayStatus.getEnumById(replayDo.getReplayStatus()).getDesc());
        replayBo.setReplayStartTime(replayDo.getReplayStartTime());
        replayBo.setReplayEndTime(replayDo.getReplayEndTime());
        replayBo.setCreateUser(replayDo.getCreateUser());
        replayBo.setUpdateUser(replayDo.getUpdateUser());
        replayBo.setCreateTime(replayDo.getCreateTime());
        return replayBo;
    }

    public static String convertListToCommaSeparatedString(List<String> list) {
        if (list == null || list.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            sb.append(list.get(i));
            if (i < list.size() - 1) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

    public static List<StrategyFlowReplayBo> doList2BoList(List<StrategyFlowReplayDo> replayDoList) {
        return replayDoList.stream().map(ReplayConvert::do2Bo).collect(Collectors.toList());
    }

    public static FlowReplayListRequestPojo listRequest2Pojo(FlowReplayListRequest request) {
        FlowReplayListRequestPojo pojo = new FlowReplayListRequestPojo();
        if (request.getId() != NumberUtils.LONG_ZERO) {
            pojo.setId(request.getId());
        }
        if (StringUtils.isNotBlank(request.getName())) {
            pojo.setName(request.getName());
        }
        if (request.getFlowRecordId() != NumberUtils.LONG_ZERO) {
            pojo.setFlowRecordId(request.getFlowRecordId());
        }
        if (StringUtils.isNotBlank(request.getSceneKey())) {
            pojo.setSceneKey(request.getSceneKey());
        }
        if (StringUtils.isNotBlank(request.getCreateUser())) {
            pojo.setCreateUser(request.getCreateUser());
        }
        if (request.getReplayStatus() != NumberUtils.INTEGER_ZERO) {
            pojo.setReplayStatus(request.getReplayStatus());
        }
        if (request.getDiffResult() != NumberUtils.INTEGER_ZERO) {
            pojo.setDiffResult(request.getDiffResult());
        }
        if (request.getReplayStartTimeStart() != NumberUtils.LONG_ZERO) {
            pojo.setReplayStartTimeStart(request.getReplayStartTimeStart());
        }
        if (request.getReplayStartTimeEnd() != NumberUtils.LONG_ZERO) {
            pojo.setReplayStartTimeEnd(request.getReplayStartTimeEnd());
        }
        if (request.getReplayEndTimeStart() != NumberUtils.LONG_ZERO) {
            pojo.setReplayEndTimeStart(request.getReplayEndTimeStart());
        }
        if (request.getReplayEndTimeEnd() != NumberUtils.LONG_ZERO) {
            pojo.setReplayEndTimeEnd(request.getReplayEndTimeEnd());
        }
        return pojo;
    }

    public static ReplayKafkaDto buildReplayKafkaDto(RecodeDetailClickHouseDto ckDto, Long flowReplayId,
            StrategyCheckResult result1, StrategyCheckResult result2, boolean checkResult) {
        ReplayKafkaDto dto = new ReplayKafkaDto();
        dto.setId(ckDto.getId());
        dto.setFlowReplayId(flowReplayId);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = sdf.format(new Date(System.currentTimeMillis()));
        dto.setPDate(formattedDate);
        dto.setReplayPDate(formattedDate);
        dto.setRecordPDate(ckDto.getRecordPDate());
        dto.setSceneKey(ckDto.getSceneKey());
        dto.setFeatureKeyMap(ckDto.getFeatureKeyMap());
        dto.setBizKeyInfo(ckDto.getBizKeyInfo());
        dto.setCreateTime(String.valueOf(System.currentTimeMillis()));
        dto.setDiffResult(checkResult);

        dto.setRequestHitResult1(result1.getRequestHitResult());
        dto.setHitStrategyList1(result1.getHitStrategyList());
        dto.setHitActionList1(result1.getHitActionList());

        dto.setRequestHitResult2(result2.getRequestHitResult());
        dto.setHitStrategyList2(result2.getHitStrategyList());
        dto.setHitActionList2(result2.getHitActionList());

        return dto;
    }

    public static String detailListRequest2ListSql(GetFlowReplayDetailListRequest request,
            FlowRecordStartEndPDate flowRecordStartEndPDate, String pDate) {
        @SuppressWarnings("checkstyle:LineLength") String sql =
                "SELECT id,flowReplayId,replayPDate,recordPDate,sceneKey,featureKeyMap,diffResult,requestHitResult1,"
                        + "hitActionList1,hitStrategyList1,requestHitResult2,hitActionList2,hitStrategyList2,"
                        + "bizKeyInfo,createTime,pdate FROM " + "kwaishop_sellerdata.strategy_flow_replay where pdate ";

        if (StringUtils.isNotBlank(pDate)) {
            sql = sql.concat(" = ").concat("'" + pDate + "'");
        } else {
            sql = sql.concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'")
                    .concat(" and pdate <= ").concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");
        }
        if (StringUtils.isNotBlank(request.getId())) {
            sql = sql.concat(" and id = '").concat(request.getId()).concat("'");
        }
        if (request.getFlowReplayId() != NumberUtils.LONG_ZERO) {
            sql = sql.concat(" and flowReplayId = ").concat(String.valueOf(request.getFlowReplayId()));
        }
        if (StringUtils.isNotBlank(request.getSceneKey())) {
            sql = sql.concat(" and sceneKey = '").concat(request.getSceneKey()).concat("'");
        }
        if (request.getDiffResult() != NumberUtils.INTEGER_ZERO) {
            if (request.getDiffResult() == NumberUtils.INTEGER_ONE) {
                sql = sql.concat(" and diffResult = 1");
            } else {
                sql = sql.concat(" and diffResult = 0");
            }
        }
        if (StringUtils.isNotBlank(request.getBizKeyInfo())) {
            sql = sql.concat(" and bizKeyInfo like '%").concat(request.getBizKeyInfo()).concat("%'");
        }
        sql = sql.concat(" order by createTime desc");
        int offset = (request.getPageNum() - 1) * request.getPageSize();
        int limit = request.getPageSize();
        sql = sql.concat(" limit ").concat(String.valueOf(limit)).concat(" ").concat(" offset ")
                .concat(String.valueOf(offset));
        return sql;

    }

    public static String detailListRequest2CountSql(GetFlowReplayDetailListRequest request,
            FlowRecordStartEndPDate flowRecordStartEndPDate, String pDate) {
        @SuppressWarnings("checkstyle:LineLength") String sql =
                "SELECT count(*) FROM " + "kwaishop_sellerdata.strategy_flow_replay where pdate ";

        if (StringUtils.isNotBlank(pDate)) {
            sql = sql.concat(" = ").concat("'" + pDate + "'");
        } else {
            sql = sql.concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'")
                    .concat(" and pdate <= ").concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");
        }
        if (StringUtils.isNotBlank(request.getId())) {
            sql = sql.concat(" and id = '").concat(request.getId()).concat("'");
        }
        if (request.getFlowReplayId() != NumberUtils.LONG_ZERO) {
            sql = sql.concat(" and flowReplayId = ").concat(String.valueOf(request.getFlowReplayId()));
        }
        if (StringUtils.isNotBlank(request.getSceneKey())) {
            sql = sql.concat(" and sceneKey = '").concat(request.getSceneKey()).concat("'");
        }
        if (request.getDiffResult() != NumberUtils.INTEGER_ZERO) {
            if (request.getDiffResult() == NumberUtils.INTEGER_ONE) {
                sql = sql.concat(" and diffResult = 1");
            } else {
                sql = sql.concat(" and diffResult = 0");
            }
        }
        if (StringUtils.isNotBlank(request.getBizKeyInfo())) {
            sql = sql.concat(" and bizKeyInfo like '%").concat(request.getBizKeyInfo()).concat("%'");
        }
        return sql;

    }

    public static String detailListRequest2CountDiffSql(Long id,
            FlowRecordStartEndPDate flowRecordStartEndPDate) {
        @SuppressWarnings("checkstyle:LineLength") String sql =
                "SELECT count(*) FROM " + "kwaishop_sellerdata.strategy_flow_replay where pdate ";


        sql = sql.concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'")
                .concat(" and pdate <= ").concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");

        sql = sql.concat(" and flowReplayId = '").concat(String.valueOf(id)).concat("'");
        sql = sql.concat(" and diffResult = 1");
        return sql;

    }

    public static String detailListRequest2CountSql(Long id,
            FlowRecordStartEndPDate flowRecordStartEndPDate) {
        @SuppressWarnings("checkstyle:LineLength") String sql =
                "SELECT count(*) FROM " + "kwaishop_sellerdata.strategy_flow_replay where pdate ";


        sql = sql.concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'")
                .concat(" and pdate <= ").concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");

        sql = sql.concat(" and flowReplayId = '").concat(String.valueOf(id)).concat("'");
        return sql;

    }

    public static String detailRequest2Sql(String id, FlowRecordStartEndPDate flowRecordStartEndPDate) {
        @SuppressWarnings("checkstyle:LineLength") String sql =
                "SELECT id,flowReplayId,replayPDate,recordPDate,sceneKey,featureKeyMap,diffResult,requestHitResult1,"
                        + "hitActionList1,hitStrategyList1,requestHitResult2,hitActionList2,hitStrategyList2,"
                        + "bizKeyInfo,createTime,pdate FROM " + "kwaishop_sellerdata.strategy_flow_replay where pdate ";

        sql = sql.concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'").concat(" and pdate <= ")
                .concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");

        if (StringUtils.isNotBlank(id) && id.matches("[a-zA-Z0-9]+")) {
            sql += " AND id = '" + StringEscapeUtils.escapeSql(id) + "'";
        }
        return sql;
    }

    public static String replayQuerySql(Long flowRecordId, FlowRecordStartEndPDate flowRecordStartEndPDate) {
        String sql =
                "SELECT pdate,featureKeyMap, flowRecordId,sceneKey,recordCreateTime,hitStrategyList,recordPDate,"
                        + "id,featureKeyMap,"
                        + "recordEndTime,createTime,bizKeyInfo,traceId,requestHitResult,hitActionList,sceneType "
                        + "FROM "
                        + "kwaishop_sellerdata.strategy_flow_record where pdate ";

        sql = sql.concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'").concat(" and pdate <= ")
                .concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");

        sql = sql.concat(" and flowRecordId = '").concat(String.valueOf(flowRecordId)).concat("'");
        return sql;
    }

    public static String getWhereClause(
            List<String> pDates,
            List<String> pHourmins,
            String sceneKey,
            List<String> hitStrategys,
            List<String> hitActions,
            Integer requestHitResult) {
        String whereClause = " p_date in ('".concat(StringUtils.join(pDates, "','")).concat("')");
        if (CollectionUtils.isNotEmpty(pHourmins)) {
            whereClause =
                    whereClause.concat(" and p_hourmin in ('").concat(StringUtils.join(pHourmins, "','")).concat("')");
        }

        if (StringUtils.isNotEmpty(sceneKey)) {
            whereClause = whereClause.concat(" and scene_key in ('").concat(sceneKey).concat("')");
        }

        if (CollectionUtils.isNotEmpty(hitStrategys)) {
            whereClause = whereClause.concat(" and (");
            for (int i = 0; i < hitStrategys.size(); i++) {
                if (i != 0) {
                    whereClause = whereClause.concat(
                                    " or get_true_value_key(get_json_object(dag_node_map,'$.STRATEGY_NODE')) like '%")
                            .concat(hitStrategys.get(i)).concat("%'");
                } else {
                    whereClause = whereClause.concat(
                                    " get_true_value_key(get_json_object(dag_node_map,'$.STRATEGY_NODE')) like '%")
                            .concat(hitStrategys.get(i)).concat("%'");
                }
            }
            whereClause = whereClause.concat(" )");
        }

        if (CollectionUtils.isNotEmpty(hitActions)) {
            whereClause = whereClause.concat(" and (");
            for (int i = 0; i < hitActions.size(); i++) {
                if (i != 0) {
                    whereClause = whereClause.concat(
                                    " or get_true_value_key(get_json_object(dag_node_map,'$.ACTION_NODE')) like '%")
                            .concat(hitActions.get(i)).concat("%'");
                } else {
                    whereClause = whereClause.concat(
                                    " get_true_value_key(get_json_object(dag_node_map,'$.ACTION_NODE')) like '%")
                            .concat(hitActions.get(i)).concat("%'");
                }
            }
            whereClause = whereClause.concat(" )");
        }

        if (Objects.nonNull(requestHitResult) && requestHitResult != NumberUtils.INTEGER_ZERO) {
            if (requestHitResult == NumberUtils.INTEGER_ONE) {
                whereClause = whereClause.concat(" and request_hit_result = false");
            } else {
                whereClause = whereClause.concat(" and request_hit_result = true");
            }
        }

        return whereClause;
    }

    public static RecodeDetailClickHouseDto convert2ClickHouseDto(StrategyFlowRealtimeKafkaDto kafkaDto,
            StrategyFlowReplayDo replayDo) {
        RecodeDetailClickHouseDto dto = new RecodeDetailClickHouseDto();

        dto.setId(kafkaDto.getTraceId());
        dto.setFlowRecordId(replayDo.getFlowRecordId());
        dto.setTraceId(kafkaDto.getTraceId());
        dto.setSceneKey(kafkaDto.getSceneKey());
        dto.setSceneType(kafkaDto.getSceneType());
        dto.setBizKeyInfo(kafkaDto.getBizKeyInfo());

        Map<String, Object> featureKeyMap = JsonMapperUtils.fromJson(kafkaDto.getFeatureKeyMap());
        featureKeyMap.entrySet().removeIf(
                entry -> entry.getKey().startsWith("dictFeature") || entry.getKey().equals("POLICY_PERF_START_TIME_KEY")
                        || entry.getKey().equals("POLICY_PERF_START_TIME_FEATURE_KEY"));
        dto.setFeatureKeyMap(JsonMapperUtils.toJson(featureKeyMap));

        if (Objects.nonNull(kafkaDto.getActionHitIds()) && !kafkaDto.getActionHitIds().equals("null")
                && !kafkaDto.getActionHitIds().equals("[]")) {
            dto.setRequestHitResult(String.valueOf(true));
        } else {
            dto.setRequestHitResult(String.valueOf(false));
        }

        List<String> hitActionList = Lists.newArrayList();
        List<String> hitStrategyList = Lists.newArrayList();
        if ("TREE".equals(dto.getSceneType())) {
            String dagNodeMapStr = kafkaDto.getDagNodeMap();
            Map<String, Object> dagNodeMap = JsonMapperUtils.fromJson(dagNodeMapStr);
            Map<String, Boolean> actionNodeMap = (Map<String, Boolean>) dagNodeMap.get("ACTION_NODE");
            Map<String, Boolean> strategyNodeMap = (Map<String, Boolean>) dagNodeMap.get("STRATEGY_NODE");
            if (MapUtils.isNotEmpty(actionNodeMap)) {
                for (Entry<String, Boolean> entry : actionNodeMap.entrySet()) {
                    if (entry.getValue()) {
                        hitActionList.add(entry.getKey().split("_")[0]);
                    }
                }
            }
            if (MapUtils.isNotEmpty(strategyNodeMap)) {
                for (Entry<String, Boolean> entry : strategyNodeMap.entrySet()) {
                    if (entry.getValue()) {
                        hitStrategyList.add(entry.getKey().split("_")[0]);
                    }
                }
            }
            dto.setHitActionList(JsonMapperUtils.toJson(hitActionList));
            dto.setHitStrategyList(JsonMapperUtils.toJson(hitStrategyList));
        } else if ("LIST".equals(dto.getSceneType())) {
            Map<String, Boolean> strategyHitResult =
                    JsonMapperUtils.ofJsonMap(kafkaDto.getStrategyIdMap(), HashMap.class, String.class, Boolean.class);
            List<String> hitStrategys =
                    strategyHitResult.entrySet().stream().filter(x -> x.getValue()).map(Entry::getKey)
                            .collect(Collectors.toList());

            Map<String, String> actionHitMaps =
                    JsonMapperUtils.ofJsonMap(kafkaDto.getActionHitPolicyCodeMap(), HashMap.class, String.class,
                            String.class);
            List<String> hitActions =
                    actionHitMaps.entrySet().stream().map(x -> x.getKey().concat("_").concat(x.getValue()))
                            .collect(Collectors.toList());

            dto.setHitStrategyList(JsonMapperUtils.toJson(hitStrategys));
            dto.setHitActionList(JsonMapperUtils.toJson(hitActions));
        }

        return dto;
    }

    public static String getAnalysisCount(Long id,
            FlowRecordStartEndPDate flowRecordStartEndPDate) {
        @SuppressWarnings("checkstyle:LineLength") String sql =
                "SELECT count(*) FROM " + "kwaishop_sellerdata.strategy_flow_replay where pdate ";


        sql = sql.concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'")
                .concat(" and pdate <= ").concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");

        sql = sql.concat(" and flowReplayId = ").concat(String.valueOf(id));
        return sql;
    }

    public static String getAnalysisDiffCount(Long id,
            FlowRecordStartEndPDate flowRecordStartEndPDate) {
        @SuppressWarnings("checkstyle:LineLength") String sql =
                "SELECT count(*) FROM " + "kwaishop_sellerdata.strategy_flow_replay where pdate ";


        sql = sql.concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'")
                .concat(" and pdate <= ").concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");

        sql = sql.concat(" and flowReplayId = ").concat(String.valueOf(id));

        sql = sql.concat(" and diffResult = 1");
        return sql;
    }

    public static String analysisRecordStrategyHitDiff(Long flowRecordId,
            FlowRecordStartEndPDate flowRecordStartEndPDate) {
        String sql =
                "select"
                        + "     t.hitStrategyList as hitStrategyList,t1.total as oneTotal,t2.total as twoTotal"
                        + " from"
                        + " ("
                        + "     select"
                        + "         distinct hitStrategyList1 as hitStrategyList"
                        + " from"
                        + "     kwaishop_sellerdata.strategy_flow_replay"
                        + " where"
                        + "     {0}"
                        + " and flowReplayId = {1}"
                        + " union all"
                        + "     select"
                        + "         distinct hitStrategyList2 as hitStrategyList"
                        + " from"
                        + "     kwaishop_sellerdata.strategy_flow_replay"
                        + " where"
                        + "     {0}"
                        + " and flowReplayId = {1}"
                        + " ) t"
                        + " left join"
                        + " ("
                        + "     select"
                        + "         hitStrategyList1 as hitStrategyList, count(1) as total"
                        + "     from"
                        + "         kwaishop_sellerdata.strategy_flow_replay"
                        + "     where"
                        + "         {0}"
                        + "         and flowReplayId = {1}"
                        + "     group by hitStrategyList1"
                        + " ) t1 on t.hitStrategyList = t1.hitStrategyList"
                        + " left join"
                        + " ("
                        + "     select"
                        + "         hitStrategyList2 as hitStrategyList, count(1) as total"
                        + "     from"
                        + "         kwaishop_sellerdata.strategy_flow_replay"
                        + "     where"
                        + "         {0}"
                        + "         and flowReplayId = {1}"
                        + "     group by hitStrategyList2"
                        + " ) t2 on t.hitStrategyList = t2.hitStrategyList"
                        + " order by abs(t1.total - t2.total) desc";

        String pdateSelect = "pdate".concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'").concat(" and pdate <= ")
                .concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");
        return MessageFormat.format(sql, pdateSelect, flowRecordId);
    }

    public static String analysisRecordActionHitDiff(Long flowRecordId,
            FlowRecordStartEndPDate flowRecordStartEndPDate) {
        String sql =
                "select"
                        + "     t.hitActionList as hitActionList,t1.total as oneTotal,t2.total as twoTotal"
                        + " from"
                        + " ("
                        + "     select"
                        + "         distinct hitActionList1 as hitActionList"
                        + " from"
                        + "     kwaishop_sellerdata.strategy_flow_replay"
                        + " where"
                        + "     {0}"
                        + " and flowReplayId = {1}"
                        + " union all"
                        + "     select"
                        + "         distinct hitActionList2 as hitActionList"
                        + " from"
                        + "     kwaishop_sellerdata.strategy_flow_replay"
                        + " where"
                        + "     {0}"
                        + " and flowReplayId = {1}"
                        + " ) t"
                        + " left join"
                        + " ("
                        + "     select"
                        + "         hitActionList1 as hitActionList, count(1) as total"
                        + "     from"
                        + "         kwaishop_sellerdata.strategy_flow_replay"
                        + "     where"
                        + "         {0}"
                        + "         and flowReplayId = {1}"
                        + "     group by hitActionList1"
                        + " ) t1 on t.hitActionList = t1.hitActionList"
                        + " left join"
                        + " ("
                        + "     select"
                        + "         hitActionList2 as hitActionList, count(1) as total"
                        + "     from"
                        + "         kwaishop_sellerdata.strategy_flow_replay"
                        + "     where"
                        + "         {0}"
                        + "         and flowReplayId = {1}"
                        + "     group by hitActionList2"
                        + " ) t2 on t.hitActionList = t2.hitActionList"
                        + " order by abs(t1.total - t2.total) desc";

        String pdateSelect = "pdate".concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'").concat(" and pdate <= ")
                .concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");
        return MessageFormat.format(sql, pdateSelect, flowRecordId);
    }


}
