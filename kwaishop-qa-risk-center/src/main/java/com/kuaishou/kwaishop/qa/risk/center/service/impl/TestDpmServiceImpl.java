package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskJsonMapConfigKey.accountBPMConfig;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.ApplyHandler;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserAccountService;
import com.kuaishou.kwaishop.qa.risk.center.domain.openapi.service.EmployeeRoleService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.ApprovalDenyEndCallbackRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.ApprovalDenyEndCallbackResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.ApprovalSubmitCallbackRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.ApprovalSubmitCallbackResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.ApprovalWithdrawEndCallbackRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.ApprovalWithdrawEndCallbackResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.CustomizeVar;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.KrpcTestDpmServiceGrpc.TestDpmServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.NormalAuditorList;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.QueryBPMAccountCreatorRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.QueryBPMAccountCreatorResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.StartApprovalRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.StartApprovalResponse;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/8 11:39
 * @注释
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class TestDpmServiceImpl extends TestDpmServiceImplBaseV2 {
    @Resource
    private ApplyHandler applyHandler;

    @Autowired
    private UserAccountService userAccountService;

    @Autowired
    private EmployeeRoleService employeeRoleService;

    @Override
    public StartApprovalResponse startApproval(StartApprovalRequest request) {
        log.info("[TestDpmServiceImpl] startApproval, request: {}",
                ProtobufUtil.protoToJsonString(request));
        try {

            applyHandler.taskStart(request);
            return StartApprovalResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[TestDpmServiceImpl] startApproval bizError, exception: ", e);
            return StartApprovalResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[TestDpmServiceImpl] startApproval error, exception: ", e);
            return StartApprovalResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    public QueryBPMAccountCreatorResponse queryBPMAccountCreator(QueryBPMAccountCreatorRequest request) {
        log.info("[TestDpmServiceImpl] queryBPMAccountCreator, request: {}",
                ProtobufUtil.protoToJsonString(request));
        try {
            CustomizeVar customizeVar = request.getCustomizeVar();
            String userId = customizeVar.getUserId();
            UserAccountBO userAccountBO = new UserAccountBO();
            userAccountBO.setUserId(Long.parseLong(userId));
            List<UserAccountDO> userAccountDOS = userAccountService.queryList(userAccountBO);
            String userName = userAccountDOS.get(0).getCreator();
            //判断审批人是否离职，如果离职，指定人
            if (!isOnDuty(userName) || userName.equals("system")) {
                Map<Object, Object> map = accountBPMConfig.getMap();
                userName = (String) map.get("approver");
                log.info("approver:{}", userName);
            }
            QueryBPMAccountCreatorResponse queryBPMAccountCreatorResponse = QueryBPMAccountCreatorResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addNormalAuditorList(NormalAuditorList.newBuilder()
                            .setUsername(userName)
                            .setTaskName("审批人查询")
                            .build())
                    .build();
            log.info("[TestDpmServiceImpl] queryBPMAccountCreator, response: {}",
                    ProtobufUtil.protoToJsonString(queryBPMAccountCreatorResponse));
            return queryBPMAccountCreatorResponse;
        } catch (BizException e) {
            log.error("[TestDpmServiceImpl] queryBPMAccountCreator bizError, exception: ", e);
            return QueryBPMAccountCreatorResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[TestDpmServiceImpl] queryBPMAccountCreator error, exception: ", e);
            return QueryBPMAccountCreatorResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    /**
     * @param userName 邮箱前缀
     * @return true：在职  false：离职/不存在
     */
    public boolean isOnDuty(String userName) {
        String employee = employeeRoleService.getEmployee(userName);
        if (employee == null) {
            if (employeeRoleService.getEmployee(userName) == null) {
                return false;
            }
        }
        //获取用户部门
        JsonObject jsonObject = new Gson().fromJson(employee, JsonObject.class);
        //判断员工是否存在
        if (!jsonObject.get("status").getAsString().equals("0")) {
            return false;
        }
        String statusCode = jsonObject.getAsJsonObject("data")
                .get("statusCode").getAsString();
        //T表示离职状态
        if (statusCode.isEmpty() || statusCode.equals("T")) {
            return false;
        }
        return true;
    }

    @Override
    public ApprovalDenyEndCallbackResponse approvalDenyEndCallback(ApprovalDenyEndCallbackRequest request) {
        return super.approvalDenyEndCallback(request);
    }

    @Override
    public ApprovalWithdrawEndCallbackResponse approvalWithdrawEndCallback(ApprovalWithdrawEndCallbackRequest request) {
        return super.approvalWithdrawEndCallback(request);
    }

    @Override
    public ApprovalSubmitCallbackResponse approvalSubmitCallback(ApprovalSubmitCallbackRequest request) {
        return super.approvalSubmitCallback(request);
    }


}
