package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultScenarioChangeRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultChangeRecordBO;


public interface FaultScenarioChangeRecordDAO {

    FaultScenarioChangeRecordDO queryChangeRecordById(Long id);

    long insertOrUpdateChangeRecord(FaultScenarioChangeRecordDO changeRecordDO);

    FaultScenarioChangeRecordDO queryChangeRecord(FaultChangeRecordBO changeRecordBO);

    List<FaultScenarioChangeRecordDO> queryChangeRecordList(Long scenarioId, Boolean orderByCreateTimeDesc);

    long insertOrUpdateByFaultRecordId(FaultScenarioChangeRecordDO changeRecordDO);

    List<FaultScenarioChangeRecordDO> queryChangeRecordList(FaultChangeRecordBO recordBO);


}
