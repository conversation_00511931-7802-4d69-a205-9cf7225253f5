//package com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.impl;
//
//import org.springframework.stereotype.Repository;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
//import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
//import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressScenarioDAO;
//import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioDO;
//import com.kuaishou.kwaishop.qa.risk.center.db.query.entity.EntityQueryCondition;
//import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressScenarioBO;
//
///**
// * <AUTHOR> <<EMAIL>>
// * Created on 2022-09-16
// */
//@Repository
//public class ScenarioDAOImplImpl extends BaseDAOImpl<StressScenarioDO, EntityQueryCondition> implements StressScenarioDAO {
//
//
//    @Override
//    protected void fillQueryCondition(EntityQueryCondition condition, QueryWrapper<StressScenarioDO> queryWrapper) {
//
//    }
//
//    @Override
//    protected BaseMapper<StressScenarioDO> getMapper() {
//        return null;
//    }
//
//    @Override
//    public PageBO<StressScenarioDO> queryPageList(QueryStressScenarioBO bo) {
//        return null;
//    }
//}
