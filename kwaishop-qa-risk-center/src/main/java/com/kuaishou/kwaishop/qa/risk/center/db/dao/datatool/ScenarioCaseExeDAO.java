package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.ScenarioCaseExeDO;


/**
 * <AUTHOR> <zhang<PERSON><EMAIL>>
 * Create on 2023-03-15
 */
public interface ScenarioCaseExeDAO {
    long insertScenarioCase(ScenarioCaseExeDO scenarioCaseExeDO);

    long updateScenarioCase(ScenarioCaseExeDO scenarioCaseExeDO);

    int logicDeleted(Long id, String operator);

    List<Long> queryCaseExeList(Long scenarioCaseExeId);

    List<ScenarioCaseExeDO> queryScenarioCaseList(ScenarioCaseExeDO scenarioCaseExeDO);

    ScenarioCaseExeDO queryScenarioCaseById(Long id);
}
