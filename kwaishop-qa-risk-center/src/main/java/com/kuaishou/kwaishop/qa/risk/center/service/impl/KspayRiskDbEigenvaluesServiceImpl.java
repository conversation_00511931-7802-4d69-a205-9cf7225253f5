package com.kuaishou.kwaishop.qa.risk.center.service.impl;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.config.constants.ApplicationConstants;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.enums.DbEigenValuesPocilyEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.service.RiskDbEigenvaluesService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KrpcKspayRiskDbEigenvaluesServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayRiskGenerateEigenvaluesRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayRiskGenerateEigenvaluesResponse;

import groovy.util.logging.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023/11/1 20:47
 */

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class KspayRiskDbEigenvaluesServiceImpl extends KrpcKspayRiskDbEigenvaluesServiceGrpc.KspayRiskDbEigenvaluesServiceImplBaseV2 {

    @Autowired
    private RiskDbEigenvaluesService riskDbEigenvaluesService;

    @Override
    public KspayRiskGenerateEigenvaluesResponse kspayRiskGenerateEigenvalues(KspayRiskGenerateEigenvaluesRequest request) {
        String code = ApplicationConstants.FAILED;
        String msg;
        try {
            switch (DbEigenValuesPocilyEnum.valueOf(request.getPolicy())) {
                case UPDATE:
                    // 只更新字段和表的收集
                    code = riskDbEigenvaluesService.getDbAllFields().getCode();
                    msg = code;
                    break;
                case EXTRACT:
                    // 重新提取特征
                    code = riskDbEigenvaluesService.extractFields(null);
                    msg = code;
                    break;
                /* 请求校验平台拿表太慢，禁用
                case UPDATE_AND_EXTRACT:
                    // 更新和提取特征值
                    DbAllFieldDataBO dbAllFields = riskDbEigenvaluesService.getDbAllFields();
                    code = riskDbEigenvaluesService.extractFields(dbAllFields);
                    break;
                 */
                default:
                    msg = ApplicationConstants.NO_SUPPORT_POLICY;
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return KspayRiskGenerateEigenvaluesResponse.newBuilder()
                    .setCode("SYSTEM_ERROR")
                    .setMsg(e.getMessage())
                    .build();
        }
        return KspayRiskGenerateEigenvaluesResponse.newBuilder()
                .setCode(code)
                .setMsg(msg)
                .build();
    }

}