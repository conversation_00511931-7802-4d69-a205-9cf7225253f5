package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.biz.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.biz.StrategyCenterAutoTestBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.DiffConclusionStatus;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecordListStatus;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyRecordListDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTrafficRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.StrategyTestService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.StrategyTrafficRecordService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.PageReplayDetailDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.QueryTrafficRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.SimpleQueryRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StartRecordingRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StrategyRecordListDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StrategyTestRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StrategyTrafficRecordDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestAiOpenAiRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestAiOpenAiResponse;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-20
 */
@Service
public class StrategyCenterAutoTestBizServiceImpl implements StrategyCenterAutoTestBizService {

    @Autowired
    private StrategyTestService strategyTestService;

    @Autowired
    private StrategyTrafficRecordService strategyTrafficRecordService;

    @Override
    public void startTest(StrategyTestRequest request) {
        strategyTestService.startTest(request);
    }

    @Override
    public void startRecording(StartRecordingRequest request) {
        strategyTestService.startRecording(request);
    }

    @Override
    public void queryTraffic(QueryTrafficRequest request) {

    }

    @Override
    public PageReplayDetailDTO replayDetail(ReplayDetailRequest request) {

        PageBO<StrategyTrafficRecordDO> resList = strategyTrafficRecordService.queryReplayDetail(request);
        return buildPageReplayDetail(resList);
    }

    @Override
    public void replayRequest(ReplayRequest request) {
        strategyTestService.replayRequest(request);
    }

    @Override
    public String simpleQuery(SimpleQueryRequest request) {
        return strategyTestService.simpleQuery(request);
    }

    @Override
    public TestAiOpenAiResponse testAiOpenAi1(TestAiOpenAiRequest request) {
        return strategyTestService.testAiOpenAi1(request);
    }

//    @Override
//    public PageRecordTaskDetailDTO queryRecordTask(QueryPageRecordTaskRequest request) {
//        PageBO<StrategyRecordListDO> resList = strategyTrafficRecordService.queryRecordTask(request);
//        return buildPageRecordTask(resList);
//    }

//    private PageRecordTaskDetailDTO buildPageRecordTask(PageBO<StrategyRecordListDO> resList) {
//        return PageRecordTaskDetailDTO.newBuilder()
//                .setPageNo(resList.getPageNo())
//                .setPageSize(resList.getPageSize())
//                .setTotal(resList.getTotal())
//                .addAllDetails(buildPageRecordTaskDTO(resList.getData()))
//                .build();
//    }

    private List<StrategyRecordListDTO> buildPageRecordTaskDTO(List<StrategyRecordListDO> dataList) {
        List<StrategyRecordListDTO> strategyTrafficRecordDTOList = new ArrayList<>();
        dataList.forEach(data -> {
            strategyTrafficRecordDTOList.add(StrategyRecordListDTO.newBuilder()
                            .setSceneName(data.getSceneName())
                            .setCreateTime(data.getCreateTime())
                            .setTaskId(Long.parseLong(String.valueOf(data.getTaskId())))
                            .setRecordPartition(data.getRecordPartition())
                            .setRecordSql(data.getRecordSql())
                            .setStatus(RecordListStatus.fromCode(data.getStatus()).getDescription())
                            .setCreator(data.getCreator())
                    .build());
        });
        return strategyTrafficRecordDTOList;
    }

    private PageReplayDetailDTO buildPageReplayDetail(PageBO<StrategyTrafficRecordDO> resList) {
        return PageReplayDetailDTO.newBuilder()
                .setPageNo(resList.getPageNo())
                .setPageSize(resList.getPageSize())
                .setTotal(resList.getTotal())
                .addAllDetails(buildPageReplayDetailDTO(resList.getData()))
                .build();
    }

    private List<StrategyTrafficRecordDTO> buildPageReplayDetailDTO(List<StrategyTrafficRecordDO> dataList) {
        List<StrategyTrafficRecordDTO> strategyTrafficRecordDTOList = new ArrayList<>();
        dataList.forEach(data -> {
            strategyTrafficRecordDTOList.add(StrategyTrafficRecordDTO.newBuilder()
                            .setActualResult(data.getActualResult() != null ? data.getActualResult() : "")
                            .setCreator(data.getCreator())
                            .setCreateTime(data.getCreateTime())
                            .setExpectedResult(data.getExpectedResult())
                            .setRecordTaskId(data.getRecordTaskId())
                            .setDiffConclusion(DiffConclusionStatus.fromCode(data.getDiffConclusion()).getDescription())
                            .setRecordTime(data.getRecordTime())
                            .setReplayTime(data.getReplayTime() != null ? data.getReplayTime() : 0)
                            .setSceneName(data.getSceneName())
                            .setTrafficData(data.getTrafficData())
                            .setRecordTime(data.getRecordTime())
                            .setReplayLaneId(data.getReplayLaneId() != null ? data.getReplayLaneId() : "")
                            .setId(data.getId())
                    .build());
        });
        return strategyTrafficRecordDTOList;
    }
}
