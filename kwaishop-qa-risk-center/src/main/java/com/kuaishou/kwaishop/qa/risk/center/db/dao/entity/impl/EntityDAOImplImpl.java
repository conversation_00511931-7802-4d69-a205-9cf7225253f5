package com.kuaishou.kwaishop.qa.risk.center.db.dao.entity.impl;

import static java.util.stream.Collectors.groupingBy;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.entity.EntityDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.entity.EntityMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.entity.EntityQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityQueryBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-09
 */
@Repository
public class EntityDAOImplImpl extends BaseDAOImpl<EntityDO, EntityQueryCondition> implements EntityDAO {

    @Autowired
    private EntityMapper entityMapper;

    @Override
    public List<EntityDO> queryByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return entityMapper.selectBatchIds(ids);
    }

    @Override
    public EntityDO queryByName(Integer entityType, String name) {
        EntityQueryCondition queryCondition = EntityQueryCondition.builder()
                .entityType(entityType)
                .nameEq(name)
                .build();
        return queryOne(queryCondition);
    }

    @Override
    public EntityDO queryByName(Integer entityType, String name, Long notEqId) {
        EntityQueryCondition queryCondition = EntityQueryCondition.builder()
                .entityType(entityType)
                .nameEq(name)
                .idNotEqual(notEqId)
                .build();
        return queryOne(queryCondition);
    }

    @Override
    public EntityDO queryByName(Integer entityType, Long entityId, String name) {
        EntityQueryCondition queryCondition = EntityQueryCondition.builder()
                .entityType(entityType)
                .entityId(entityId)
                .nameEq(name)
                .build();
        return queryOne(queryCondition);
    }

    @Override
    public EntityDO queryByName(Integer entityType, Long entityId, String name, Long notEqId) {
        EntityQueryCondition queryCondition = EntityQueryCondition.builder()
                .entityType(entityType)
                .entityId(entityId)
                .nameEq(name)
                .idNotEqual(notEqId)
                .build();
        return queryOne(queryCondition);
    }

    @Override
    public List<EntityDO> queryByNames(Integer entityType, Long entityId, Collection<String> names) {
        EntityQueryCondition queryCondition = EntityQueryCondition.builder()
                .entityType(entityType)
                .entityId(entityId)
                .names(names)
                .orderByCreateTimeDesc(true)
                .build();
        return queryList(queryCondition);
    }

    @Override
    public List<EntityDO> queryLikeName(Integer entityType, Long entityId, String name) {
        EntityQueryCondition queryCondition = EntityQueryCondition.builder()
                .entityType(entityType)
                .entityId(entityId)
                .nameLike(name)
                .orderByCreateTimeDesc(true)
                .build();
        return queryList(queryCondition);
    }

    @Override
    public EntityDO queryLikeName(Integer entityType, String name) {
        EntityQueryCondition queryCondition = EntityQueryCondition.builder()
                .entityType(entityType)
                .nameLike(name)
                .orderByCreateTimeDesc(true)
                .build();
        List<EntityDO> res = queryList(queryCondition);
        if (CollectionUtils.isEmpty(res)) {
            return null;
        }
        return res.get(0);
    }

    @Override
    public List<EntityDO> queryListByName(Integer entityType, String name) {
        EntityQueryCondition queryCondition = EntityQueryCondition.builder()
                .entityType(entityType)
                .nameEq(name)
                .build();
        return queryList(queryCondition);
    }


    @Override
    public List<EntityDO> queryByEntityId(Integer entityType, Long entityId) {
        EntityQueryCondition queryCondition = EntityQueryCondition.builder()
                .entityType(entityType)
                .entityId(entityId)
                .orderByCreateTimeDesc(true)
                .build();
        return queryList(queryCondition);
    }

    @Override
    public List<EntityDO> queryByEntityType(Integer entityType) {
        EntityQueryCondition queryCondition = EntityQueryCondition.builder()
                .entityType(entityType)
                .orderByCreateTimeDesc(true)
                .build();
        return queryList(queryCondition);
    }

    @Override
    public Map<Long, EntityDO> queryGroupByEntityType(Integer entityType) {
        List<EntityDO> dataList = queryByEntityType(entityType);
        return CollectionUtils.isEmpty(dataList) ? Maps.newHashMap() : dataList.stream()
                .collect(Maps::newHashMap, (m, v) -> m.put(v.getId(), v), Map::putAll);
    }

    @Override
    public List<EntityDO> queryEntityList(EntityQueryBO baseBO) {
        EntityQueryCondition queryCondition = EntityQueryCondition.builder()
                .entityType(baseBO.getEntityType())
                .entityId(baseBO.getEntityId())
                .id(baseBO.getId())
                .nameEq(baseBO.getNameEq())
                .nameLike(baseBO.getNameLike())
                .status(baseBO.getStatus())
                .creator(baseBO.getOperator())
                .leader(baseBO.getLeader())
                .leaders(baseBO.getLeaders())
                .names(baseBO.getNames())
                .approveTeamId(baseBO.getApproveTeamId())
                .entityIds(baseBO.getEntityIds())
                .orderByCreateTimeDesc(true)
                .build();
        return queryList(queryCondition);
    }

    @Override
    public PageBO<EntityDO> queryEntityPageList(EntityQueryBO baseBO) {
        EntityQueryCondition queryCondition = EntityQueryCondition.builder()
                .entityType(baseBO.getEntityType())
                .entityId(baseBO.getEntityId())
                .id(baseBO.getId())
                .nameEq(baseBO.getNameEq())
                .nameLike(baseBO.getNameLike())
                .status(baseBO.getStatus())
                .creator(baseBO.getOperator())
                .leader(baseBO.getLeader())
                .leaders(baseBO.getLeaders())
                .names(baseBO.getNames())
                .entityIds(baseBO.getEntityIds())
                .pageNo(baseBO.getPageNo())
                .pageSize(baseBO.getPageSize())
                .orderByCreateTimeDesc(true)
                .build();
        return queryPageList(queryCondition);
    }

    @Override
    public Map<Long, EntityDO> queryGroupById(Integer entityType, Long entityId) {
        EntityQueryCondition queryCondition = EntityQueryCondition.builder()
                .entityType(entityType)
                .entityId(entityId)
                .orderByCreateTimeDesc(true)
                .build();
        List<EntityDO> dataList = queryList(queryCondition);
        return CollectionUtils.isEmpty(dataList) ? Maps.newHashMap() : dataList.stream()
                .collect(Maps::newHashMap, (m, v) -> m.put(v.getId(), v), Map::putAll);
    }

    @Override
    public Map<String, List<EntityDO>> queryGroupByName(Integer entityType, Long entityId) {
        EntityQueryCondition queryCondition = EntityQueryCondition.builder()
                .entityType(entityType)
                .entityId(entityId)
                .orderByCreateTimeDesc(true)
                .build();
        List<EntityDO> dataList = queryList(queryCondition);
        return CollectionUtils.isEmpty(dataList) ? Maps.newHashMap() : dataList.stream()
                .collect(groupingBy(EntityDO::getName));
    }

    @Override
    public List<EntityDO> queryByEntityIds(Integer entityType, Collection<Long> entityIds) {
        EntityQueryCondition queryCondition = EntityQueryCondition.builder()
                .entityType(entityType)
                .entityIds(entityIds)
                .orderByCreateTimeDesc(true)
                .build();
        return queryList(queryCondition);
    }

    @Override
    public Map<Long, List<EntityDO>> queryGroupByEntityId(Integer entityType, Collection<Long> entityIds) {
        List<EntityDO> dataList = queryByEntityIds(entityType, entityIds);
        return CollectionUtils.isEmpty(dataList) ? Maps.newHashMap() : dataList.stream()
                .collect(groupingBy(EntityDO::getEntityId));
    }

    @Override
    public int logicDelete(String operator, Long id) {
        return entityMapper.logicDeleted(operator, id);
    }

    @Override
    public int logicDelete(String operator, Collection<Long> ids) {
        return entityMapper.logicDeletedByIds(operator, ids);
    }

    @Override
    public int logicDelete(String operator, Integer entityType, Long entityId) {
        return entityMapper.logicDeletedByEntity(operator, entityType, entityId);
    }

    @Override
    public int logicBatchDeleted(String operator, Integer entityType, Long entityId, Collection<String> names) {
        return entityMapper.logicBatchDeleted(operator, entityType, entityId, names);
    }

    @Override
    protected void fillQueryCondition(EntityQueryCondition condition, QueryWrapper<EntityDO> queryWrapper) {
        if (condition.getEntityType() != null && condition.getEntityType() > 0) {
            queryWrapper.and(q -> q.eq("entity_type", condition.getEntityType()));
        }
        if (condition.getEntityId() != null && condition.getEntityId() > 0) {
            queryWrapper.and(q -> q.eq("entity_id", condition.getEntityId()));
        }
        if (StringUtils.isNotBlank(condition.getNameEq())) {
            queryWrapper.and(q -> q.eq("name", condition.getNameEq()));
        }
        if (StringUtils.isNotBlank(condition.getNameLike())) {
            queryWrapper.and(q -> q.like("name", condition.getNameLike()));
        }
        if (CollectionUtils.isNotEmpty(condition.getNames())) {
            queryWrapper.and(q -> q.in("name", condition.getNames()));
        }
        if (condition.getStatus() != null && condition.getStatus() > 0) {
            queryWrapper.and(q -> q.eq("status", condition.getStatus()));
        }
        if (CollectionUtils.isNotEmpty(condition.getEntityIds())) {
            queryWrapper.and(q -> q.in("entity_id", condition.getEntityIds()));
        }
        if (StringUtils.isNotBlank(condition.getLeader())) {
            queryWrapper.and(q -> q.eq("extra1", condition.getLeader()));
        }
        if (CollectionUtils.isNotEmpty(condition.getLeaders())) {
            queryWrapper.and(q -> q.in("extra1", condition.getLeaders()));
        }
        if (StringUtils.isNotBlank(condition.getApproveTeamId())) {
            queryWrapper.and(q -> q.eq("extra1", condition.getApproveTeamId()));
        }
    }

    @Override
    protected BaseMapper<EntityDO> getMapper() {
        return entityMapper;
    }
}
