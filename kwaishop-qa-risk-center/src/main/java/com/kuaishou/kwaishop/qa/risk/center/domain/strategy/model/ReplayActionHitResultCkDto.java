package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert.Column;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-15
 */
@Data
public class ReplayActionHitResultCkDto {

    @Column(name = "hitActionList")
    private String hitActionList;

    @Column(name = "oneTotal")
    private Long oneTotal;

    @Column(name = "twoTotal")
    private Long twoTotal;
}
