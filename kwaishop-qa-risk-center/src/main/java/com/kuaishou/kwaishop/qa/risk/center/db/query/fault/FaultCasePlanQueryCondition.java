package com.kuaishou.kwaishop.qa.risk.center.db.query.fault;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2024-03-18
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FaultCasePlanQueryCondition extends BaseQueryCondition {

    private Long centerId;

    private Long teamId;

    private Long caseId;
}
