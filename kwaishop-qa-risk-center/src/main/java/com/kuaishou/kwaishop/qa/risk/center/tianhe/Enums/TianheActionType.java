package com.kuaishou.kwaishop.qa.risk.center.tianhe.Enums;

public enum TianheActionType {
    QUERY("0"),
    CLICK("1");

    private final String value;

    // 构造方法
    TianheActionType(String value) {
        this.value = value;
    }

    // 获取 value（key -> value）
    public String getValue() {
        return value;
    }

    // 通过 value 查找 key（value -> key）
    public static TianheActionType fromValue(String value) {
        for (TianheActionType type : TianheActionType.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }

}
