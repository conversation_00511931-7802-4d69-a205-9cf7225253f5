package com.kuaishou.kwaishop.qa.risk.center.domain.openapi.service;


import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.kuaishou.openapi.ApiClient;

@Configuration
public class OpenApiConfiguration {
    @Bean
    public OpenApi apiClient() {
        String url = "https://is-gateway.corp.kuaishou.com";
        String appKey = "b45b7550-1d3f-4953-a471-cc438f7fa2ee";
        String secretKey = "3a4949b2-1460-4010-974a-c1b16135d73d";
        return new ApiClient().initApiClient(url, appKey, secretKey).createService(OpenApi.class);
    }
}
