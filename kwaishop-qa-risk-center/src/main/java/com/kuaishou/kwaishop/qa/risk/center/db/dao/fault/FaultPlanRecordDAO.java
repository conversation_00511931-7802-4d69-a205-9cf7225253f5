package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault;

import java.util.Collection;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.detail.DetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultPlanRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultPlanRecordGroupByDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultPlanRecordBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-15
 */
public interface FaultPlanRecordDAO extends DetailDAO<FaultPlanRecordDO, CombineQueryParam> {

    List<FaultPlanRecordDO> queryFaultPlanRecordList(FaultPlanRecordBO riskDetailBO);

    PageBO<FaultPlanRecordDO> queryPageFaultPlanRecordList(FaultPlanRecordBO riskDetailBO);

    List<FaultPlanRecordDO> queryFaultPlanRecordList(Long id);

    List<FaultPlanRecordDO> queryFaultPlanRecordByCenterId(Long centerId, Long st, Long et);

    List<FaultPlanRecordDO> queryFaultPlanRecordAllByCenterId(Long centerId, Long st, Long et);

    int batchUpdateFaultPlanRecord(FaultPlanRecordBO faultPlanRecordBO, Integer fromStatus, Integer toStatus);

    List<FaultPlanRecordDO> queryFaultRecordList(Long planId, Collection<Long> ids);

    int batchUpdateFaultPlanRecordByIds(FaultPlanRecordBO faultPlanRecordBO, Integer fromStatus, Integer toStatus);

    List<FaultPlanRecordDO> queryPageFaultPlanRecordList(FaultPlanRecordBO faultPlanRecordBO, int pageNo, int pageSize);

    int countFaultPlanRecord(Long planId);

    List<FaultPlanRecordDO> queryRecordListByScenarioId(Long scenarioId);

    List<FaultPlanRecordDO> queryFaultRecordList(Collection<Long> ids);

    List<FaultPlanRecordGroupByDO> getFaultCountGroupByMember(Long st, Long et);

}
