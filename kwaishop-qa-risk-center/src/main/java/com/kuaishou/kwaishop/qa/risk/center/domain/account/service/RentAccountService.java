package com.kuaishou.kwaishop.qa.risk.center.domain.account.service;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.RentAccountBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-25
 */
public interface RentAccountService {

    void rentAccount(RentAccountBO rentAccountBo);

    void rentAccountV2(RentAccountBO rentAccountBO);

    void returnAccount(RentAccountBO rentAccountBo);

    List<RentAccountDO> queryRentRecords(RentAccountBO rentAccountBo);

    PageBO<RentAccountDO> queryPageRentRecords(RentAccountBO queryBO);

    void extendRent(RentAccountBO rentAccountBO);

    List<String> queryListByOperator(String borrower);
}
