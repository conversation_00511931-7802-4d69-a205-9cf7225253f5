package com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Application;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-28
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tianhe_risk_application")
public class TianheApplicationDO implements Serializable {

    private static final long serialVersionUID = -3738793881091266210L;

    @TableId(value = "id", type = IdType.AUTO)
    private long id;
    /**
     * 应用key
     */
    private String appKey;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 更新时间
     */
    private Long updateTime;
    /**
     * 是否被删除
     */
    private Integer deleted;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 可编辑人群
     */
    private String operators;
    /**
     * 用例集 冗余字段
     */
    private String sampleSets;
    /**
     * 来源平台
     */
    private String platformSource;

}
