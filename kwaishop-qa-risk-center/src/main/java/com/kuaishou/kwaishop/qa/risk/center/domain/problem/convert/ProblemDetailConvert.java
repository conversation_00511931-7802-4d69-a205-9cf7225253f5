package com.kuaishou.kwaishop.qa.risk.center.domain.problem.convert;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.problem.ProblemDetailDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.bo.ProblemDetailBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.CreateProblemRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.ProblemDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.QueryProblemDetailListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.QueryProblemDetailPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.UpdateProblemRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-06
 */
public interface ProblemDetailConvert {

    ProblemDetailDO buildCreateDO(ProblemDetailBO problemDetailBO);

    ProblemDetailDO buildUpdateDO(ProblemDetailDO existDO, ProblemDetailBO problemDetailBO);

    ProblemDetailBO buildCreateBO(CreateProblemRequest request);

    ProblemDetailBO buildUpdateBO(UpdateProblemRequest request);

    ProblemDetailBO buildQueryListBO(QueryProblemDetailListRequest request);

    ProblemDetailBO buildQueryPageListBO(QueryProblemDetailPageListRequest request);

    ProblemDTO buildProblemDTO(ProblemDetailDO problemDetailDO, String centerName, String teamName);
}
