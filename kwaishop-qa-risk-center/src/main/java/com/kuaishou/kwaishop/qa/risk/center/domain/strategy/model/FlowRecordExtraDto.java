package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-19
 */
@Data
public class FlowRecordExtraDto {

    private List<String> pDatePartitionList;

    private List<String> pHourMinPartitionList;

    private List<String> hitStrategyList;

    private List<String> hitActionList;

    //默认0，未命中1，命中2
    private Integer requestHitResult;

    private String whereClause;

    private String queryId;

    private Integer recordSize;
}
