package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-13
 * 流量主表BO
 */
@Data
public class StrategyFlowRecordBo {

    private Long id;

    private String name;

    private String description;

    private List<String> sceneKeyList;

    private Integer total;

    /**
     * @see com.kuaishou.kwaishop.qa.risk.center.common.Enum.FlowType
     */
    private Integer flowType;

    private String flowTypeDesc;

    /**
     * @see com.kuaishou.kwaishop.qa.risk.center.common.Enum.FlowStatus
     */
    private Integer flowStatus;

    private String flowStatusDesc;

    private Long flowStartTime;

    private Long flowEndTime;

    private String createUser;

    private Long createTime;

    private String updateUser;

    private Long updateTime;

    private Integer deleted;
}
