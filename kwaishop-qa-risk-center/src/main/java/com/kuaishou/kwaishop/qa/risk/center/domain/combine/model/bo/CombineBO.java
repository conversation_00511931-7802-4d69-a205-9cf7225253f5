package com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-13
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CombineBO {

    /**
     * 总数
     */
    private Long total;

    /**
     * 实体类型
     */
    private Integer entityType;

    /**
     * 实体id
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 内部实体
     */
    private List<? extends CombineBO> innerData;

}
