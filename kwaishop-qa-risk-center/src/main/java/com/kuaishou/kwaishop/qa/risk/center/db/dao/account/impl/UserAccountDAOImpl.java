package com.kuaishou.kwaishop.qa.risk.center.db.dao.account.impl;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.UserAccountDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.account.UserAccountMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.account.UserAccountQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserAccountTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserDataTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserLoginTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.utils.AESUtil;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-28
 */
@Repository
public class UserAccountDAOImpl extends BaseDAOImpl<UserAccountDO, UserAccountQueryCondition> implements
        UserAccountDAO {

    @Autowired
    private UserAccountMapper userAccountMapper;

    @Override
    protected void fillQueryCondition(UserAccountQueryCondition condition, QueryWrapper<UserAccountDO> queryWrapper) {
        if (condition.getTeamId() != null && condition.getTeamId() > 0L) {
            queryWrapper.and(q -> q.eq("team_id", condition.getTeamId()));
        }
        if (condition.getUserId() != null && condition.getUserId() > 0L) {
            queryWrapper.and(q -> q.eq("user_id", condition.getUserId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getUserIds())) {
            queryWrapper.and(q -> q.in("user_id", condition.getUserIds()));
        }
        if (UserAccountTypeEnum.of(condition.getAccountType()) != null) {
            queryWrapper.and(q -> q.eq("account_type", condition.getAccountType()));
        }
        if (condition.getAccount() != null && !condition.getAccount().isEmpty()) {
            queryWrapper.and(q -> q.eq("account", condition.getAccount()));
        }
        if (CollectionUtils.isNotEmpty(condition.getAccountTypes())) {
            queryWrapper.and(q -> q.in("account_type", condition.getAccountTypes()));
        }
        if (UserDataTypeEnum.of(condition.getDataType()) != null) {
            queryWrapper.and(q -> q.eq("data_type", condition.getDataType()));
        }
        if (CollectionUtils.isNotEmpty(condition.getDataTypes())) {
            queryWrapper.and(q -> q.in("data_type", condition.getDataTypes()));
        }
        if (UserLoginTypeEnum.of(condition.getLoginType()) != null && condition.getLoginType() > 0) {
            queryWrapper.and(q -> q.eq("login_type", condition.getLoginType()));
        }
        if (CollectionUtils.isNotEmpty(condition.getLoginTypes())) {
            queryWrapper.and(q -> q.in("login_type", condition.getLoginTypes()));
        }
        if (condition.getStatus() != null || CollectionUtils.isNotEmpty(condition.getStatuses())) {
            queryWrapper.and(q -> {
                if (condition.getStatus() != null) {
                    q.eq("status", condition.getStatus());
                }
                if (CollectionUtils.isNotEmpty(condition.getStatuses())) {
                    q.or().in("status", condition.getStatuses());
                }
            });
        }
    }

    @Override
    protected BaseMapper<UserAccountDO> getMapper() {
        return userAccountMapper;
    }

    @Override
    public List<UserAccountDO> queryList(UserAccountBO userAccountBO) {
        UserAccountQueryCondition condition = UserAccountQueryCondition.builder()
                .accountType(userAccountBO.getAccountType())
                .accountTypes(userAccountBO.getAccountTypes())
                .dataType(userAccountBO.getDataType())
                .dataTypes(userAccountBO.getDataTypes())
                .loginType(userAccountBO.getLoginType())
                .loginTypes(userAccountBO.getLoginTypes())
                .id(userAccountBO.getId())
                .ids(userAccountBO.getIds())
                .userId(userAccountBO.getUserId())
                .userIds(userAccountBO.getUserIds())
                .status(userAccountBO.getStatus())
                .statuses(Arrays.asList(0, 1))
                .build();
        return queryList(condition);
    }

    @Override
    public PageBO<UserAccountDO> queryPageList(UserAccountBO userAccountBO) {
        UserAccountQueryCondition condition = UserAccountQueryCondition.builder()
                .accountType(userAccountBO.getAccountType())
                .accountTypes(userAccountBO.getAccountTypes())
                .dataType(userAccountBO.getDataType())
                .dataTypes(userAccountBO.getDataTypes())
                .loginType(userAccountBO.getLoginType())
                .loginTypes(userAccountBO.getLoginTypes())
                .id(userAccountBO.getId())
                .ids(userAccountBO.getIds())
                .userId(userAccountBO.getUserId())
                .userIds(userAccountBO.getUserIds())
                .status(userAccountBO.getStatus())
                .statuses(Arrays.asList(0, 1))
                .pageNo(userAccountBO.getPageNo())
                .pageSize(userAccountBO.getPageSize())
                .account(userAccountBO.getAccount())
                .teamId(userAccountBO.getTeamId())
                .build();
        return queryPageList(condition);
    }

    @Override
    public List<UserAccountDO> queryUserAccountByUserId(Long userId) {
        return userAccountMapper.queryUserAccountByUserId(userId);
    }

    @Override
    public int updateRentStatus(Integer status, String modifier, Long userId) {
        return userAccountMapper.updateRentStatus(status, userId);
    }

    @Override
    public int addRentNum(Integer increment, Long userId) {
        return userAccountMapper.addRentNum(increment, userId);
    }

    @Override
    public int reduceRentNum(Integer increment, Long userId) {
        return userAccountMapper.reduceRentNum(increment, userId);
    }

    @Override
    public void updateToken(UserAccountDO userAccountDO) {
        userAccountMapper.updateToken(userAccountDO.getUserId(), userAccountDO.getExt());
    }

    @Override
    public List<UserAccountDO> queryBusinessAccountByRule(Integer from, Integer to) {
        return userAccountMapper.queryBusinessAccountByRule(from, to).stream().map(item -> {
            item.setAccount(AESUtil.decrypt(item.getAccount()));
            item.setPassword(AESUtil.decrypt(item.getPassword()));
            return item;
        }).collect(Collectors.toList());
    }

    @Override
    public UserAccountDO queryByUid(Long uid) {
        UserAccountQueryCondition condition = UserAccountQueryCondition.builder()
                .userId(uid)
                .build();
        UserAccountDO accountDO = queryOne(condition, true);
        return accountDO;
    }
}
