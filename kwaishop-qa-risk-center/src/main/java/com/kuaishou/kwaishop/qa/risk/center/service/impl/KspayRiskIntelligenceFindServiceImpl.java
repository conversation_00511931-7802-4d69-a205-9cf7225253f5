package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz.KspayRiskIntelligenceFindBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KrpcKspayRiskIntelligenceFindServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayQuerySymbolListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayQuerySymbolListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskIntelligenceFindListParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayUpdateMarkedForRiskFieldsResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.PageKspayRiskIntelligenceFindListDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryPageRiskFieldsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryPageRiskFieldsResponse;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class KspayRiskIntelligenceFindServiceImpl extends KrpcKspayRiskIntelligenceFindServiceGrpc.KspayRiskIntelligenceFindServiceImplBaseV2 {
    @Autowired
    private KspayRiskIntelligenceFindBizService kspayRiskIntelligenceFindBizService;

    @Override
    public QueryPageRiskFieldsResponse kspayQueryPageRiskIntelligenceFindList(QueryPageRiskFieldsRequest request) {
        log.info("[KspayRiskIntelligenceFindBizServiceImpl] queryPageRiskIntelligenceFindList request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            PageKspayRiskIntelligenceFindListDTO pageKspayRiskIntelligenceFindListDTO = kspayRiskIntelligenceFindBizService
                    .queryPageRiskIntelligenceFindList(request);
            return QueryPageRiskFieldsResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(pageKspayRiskIntelligenceFindListDTO)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskIntelligenceFindBizServiceImpl] queryPageRiskIntelligenceFindList bizError, exception: ", e);
            return QueryPageRiskFieldsResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskIntelligenceFindBizServiceImpl] queryPageRiskIntelligenceFindList error, exception: ", e);
            return QueryPageRiskFieldsResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayUpdateMarkedForRiskFieldsResponse kspayUpdateRiskLevelForRiskFields(KspayRiskIntelligenceFindListParam request) {
        log.info("[KspayRiskIntelligenceFindBizServiceImpl] kspayUpdateMarkedForRiskFields request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayRiskIntelligenceFindBizService.updateRiskLevelForRiskFields(request);
            return KspayUpdateMarkedForRiskFieldsResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setId(request.getId())
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskIntelligenceFindBizServiceImpl] kspayUpdateMarkedForRiskFields bizError, exception: ", e);
            return KspayUpdateMarkedForRiskFieldsResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskIntelligenceFindBizServiceImpl] kspayUpdateMarkedForRiskFields error, exception: ", e);
            return KspayUpdateMarkedForRiskFieldsResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayQuerySymbolListResponse kspayQuerySymbolList(KspayQuerySymbolListRequest request) {
        log.info("[kspayQuerySymbolList] kspayQuerySymbolList request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            List<String> symbolList = kspayRiskIntelligenceFindBizService
                    .querySymbolList(request);
            return KspayQuerySymbolListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllSymbol(symbolList)
                    .build();
        } catch (BizException e) {
            log.error("[kspayQuerySymbolList] kspayQuerySymbolList bizError, exception: ", e);
            return KspayQuerySymbolListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[kspayQuerySymbolList] kspayQuerySymbolList error, exception: ", e);
            return KspayQuerySymbolListResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }
}
