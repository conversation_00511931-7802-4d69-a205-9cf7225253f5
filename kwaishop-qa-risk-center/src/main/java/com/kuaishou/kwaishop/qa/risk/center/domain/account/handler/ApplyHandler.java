package com.kuaishou.kwaishop.qa.risk.center.domain.account.handler;

import static com.kuaishou.infra.falcon.util.JsonUtils.toJSON;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskStringConfigKey.kspayOpenApiUrl;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.kuaishou.env.util.EnvUtils;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.RentAccountBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.service.ApprovalService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.DPMApprovalParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.ApprovalFlowTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.vo.ApprovalEntityDataVO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.StartApprovalRequest;
import com.kuaishou.kwaishop.qa.risk.center.utils.http.HttpUtils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/7 17:07
 * @注释
 */
@Slf4j
@Service
public class ApplyHandler {

    public static final String TOKEN_GET_URL = "https://is-gateway.corp.kuaishou.com/token/get";

    public static final String PROCESS_KEY = "account_test_key";

    public static final String OLD_PROCESS_KEY = "old_account_test_key";

    public static final String ADD_PROCESS_KEY = "add_account";

    private static final String APP_KEY = "386925f6-03a4-44d1-93a9-c0402f53960d";

    private static final String SECRET_KEY = "36fd168efffeda8e719fde99b95a7473";

    private volatile String accessToken;

    @Autowired
    private ApprovalFactory approvalFactory;

    @Autowired
    private RentAccountBizService rentAccountBizService;

    public void taskStart(StartApprovalRequest request) throws IOException {
        //如果是staging环境，直接不走审批了
        if (EnvUtils.isStaging()) {
            log.info("staging环境，不走审批");
            String bizCode = request.getBizCode();
            ApprovalService service = approvalFactory.getService(bizCode);
            Map<String, Object> map = new HashMap<>();
            map.put("userId", String.valueOf(request.getUserId()));
            map.put("userName", request.getUserName());
            map.put("duration", String.valueOf(request.getDuration()));
            map.put("codeValue", request.getCodeValueList().toString());
            map.put("bizCode", request.getBizCode());

            service.execute(map);
            return;
        }
        //PRT/ONLINE执行审批流
        String url = kspayOpenApiUrl.get() + "/bpm-openapi/api/v1/process/task/start";
        Map<String, String> headers = Maps.newHashMap();
        getAccessToken();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", MessageFormat.format("Bearer {0}", accessToken));
        HttpUtils httpUtils = new HttpUtils();
        ApprovalEntityDataVO body = convert(request);
        try {
            Response response = httpUtils.post(url, headers, toJSON(body));
            log.info("url = {}, param={}, headers={}, response={}", url, body, headers, response);
        } catch (Exception e) {
            log.error("[ApplyHandler] sendCreateDpm error,", e);
        }
    }

    public void handle(Map<String, Object> variables) {
        String bizCode = (String) variables.get("bizCode");
        ApprovalService service = approvalFactory.getService(bizCode);
        service.execute(variables);
    }

    private ApprovalEntityDataVO convert(StartApprovalRequest request) {
        ApprovalEntityDataVO vo = new ApprovalEntityDataVO();
        String businessId = "account_test" + System.currentTimeMillis();
        DPMApprovalParam param = new DPMApprovalParam();
        param.setFlowType(ApprovalFlowTypeEnum.START);
        param.setUserName(request.getUserName());
        param.setTitle("账号池权限审批");
        param.setBizCode(request.getBizCode());
        vo.setBusinessExplain(businessId);
        vo.setBusinessId(businessId);
        ApprovalService service = approvalFactory.getService(request.getBizCode());
        return service.approvalForm(request, request.getBizCode(), param);
    }


    private void getAccessToken() throws IOException {
        //Map<String, Object> map = EshopTestPlatformConfig.getMap();
        Map<String, Object> params = new HashMap<>();
        params.put("appKey", APP_KEY);
        params.put("secretKey", SECRET_KEY);
        HttpUtils httpUtils = new HttpUtils();
        try {
            String url = String.format("%s?%s", TOKEN_GET_URL, toFormString(params));
            Response response = httpUtils.get(url, null);

            assert response.body() != null;
            String responseString = response.body().string();
            Map<String, Object> responseMap = ObjectMapperUtils.fromJson(responseString);
            if ((int) responseMap.get("code") != 0) {
                return;
            }
            Map<String, Object> resultMap = (Map<String, Object>) responseMap.get("result");
            accessToken = (String) resultMap.get("accessToken");
        } catch (IOException ex) {
            throw new IOException("更新token失败");
        }
    }

    private String toFormString(Map<String, Object> map) {
        List<String> nameValuePairs = new LinkedList<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String name = entry.getKey();
            String value = String.valueOf(entry.getValue());
            try {
                value = URLEncoder.encode(value, "UTF-8");
            } catch (UnsupportedEncodingException ignored) {

            }
            String nameValuePair = String.format("%s=%s", name, value);
            nameValuePairs.add(nameValuePair);
        }
        return String.join("&", nameValuePairs);
    }


}
