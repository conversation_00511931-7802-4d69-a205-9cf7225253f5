package com.kuaishou.kwaishop.qa.risk.center.db.query.fault;


import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FaultAlarmRecordQueryCondition extends BaseQueryCondition {


    private Long ruleId;

    private String uic;

    private String ruleName;


}
