package com.kuaishou.kwaishop.qa.risk.center.db.dao.errorcode;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.errorcode.ErrorCodeDO;

public interface ErrorCodeDAO {
    void insert(ErrorCodeDO errorCodeDO);

    void updateById(ErrorCodeDO errorCodeDO);

    void deleteById(Long id);


    IPage<ErrorCodeDO> getErrorCodeList(String creator, Long current, Long size);

    ErrorCodeDO getById(Long id);

    ErrorCodeDO queryByErrorCode(String errorCode);
}
