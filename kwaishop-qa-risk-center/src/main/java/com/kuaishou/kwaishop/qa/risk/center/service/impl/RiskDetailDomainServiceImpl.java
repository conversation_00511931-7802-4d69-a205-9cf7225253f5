package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.RiskDetailBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.CreateRiskDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.CreateRiskDetailResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.DeleteRiskDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.DeleteRiskDetailResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KrpcRiskDetailDomainServiceGrpc.RiskDetailDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.PageRiskDetailDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.QueryPageRiskDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.QueryPageRiskDetailResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.QueryRiskDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.QueryRiskDetailResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.RiskDetailViewDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.UpdateRiskDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.UpdateRiskDetailResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-22
 */

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class RiskDetailDomainServiceImpl extends RiskDetailDomainServiceImplBaseV2 {

    @Autowired
    private RiskDetailBizService riskDetailBizService;
    @Override
    public CreateRiskDetailResponse createRiskDetail(CreateRiskDetailRequest request) {
        log.info("[RiskDetailDomainServiceImpl] createRiskDetail request: {}", protoToJsonString(request));
        try {
            riskDetailBizService.createRiskDetail(request);
            return CreateRiskDetailResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[RiskDetailDomainServiceImpl] createRiskDetail bizError, req: {}, exception: ", protoToJsonString(request), e);
            return CreateRiskDetailResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[RiskDetailDomainServiceImpl] createRiskDetail error, req: {}, exception: ", protoToJsonString(request), e);
            return CreateRiskDetailResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateRiskDetailResponse updateRiskDetail(UpdateRiskDetailRequest request) {
        log.info("[RiskDetailDomainServiceImpl] updateRiskDetail request: {}", protoToJsonString(request));
        try {
            riskDetailBizService.updateRiskDetail(request);
            return UpdateRiskDetailResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[RiskDetailDomainServiceImpl] updateRiskDetail bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateRiskDetailResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[RiskDetailDomainServiceImpl] updateRiskDetail error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateRiskDetailResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public DeleteRiskDetailResponse deleteRiskDetail(DeleteRiskDetailRequest request) {
        log.info("[RiskDetailDomainServiceImpl] deleteRiskDetail request: {}", protoToJsonString(request));
        try {
            riskDetailBizService.deleteRiskDetail(request);
            return DeleteRiskDetailResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[RiskDetailDomainServiceImpl] deleteRiskDetail bizError, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteRiskDetailResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[RiskDetailDomainServiceImpl] deleteRiskDetail error, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteRiskDetailResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryRiskDetailResponse queryRiskDetailList(QueryRiskDetailRequest request) {
        log.info("[RiskDetailDomainServiceImpl] queryRiskDetailList request: {}", protoToJsonString(request));
        try {
            RiskDetailViewDTO res = riskDetailBizService.queryRiskDetailList(request);
            return QueryRiskDetailResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(res)
                    .build();
        } catch (BizException e) {
            log.error("[RiskDetailDomainServiceImpl] queryRiskDetailList bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryRiskDetailResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[RiskDetailDomainServiceImpl] queryRiskDetailList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryRiskDetailResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryPageRiskDetailResponse queryPageRiskDetailList(QueryPageRiskDetailRequest request) {
        log.info("[RiskDetailDomainServiceImpl] queryPageRiskDetailList request: {}", protoToJsonString(request));
        try {
            PageRiskDetailDTO riskDetailDTO = riskDetailBizService.queryPageRiskDetailList(request);
            return QueryPageRiskDetailResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(riskDetailDTO)
                    .build();
        } catch (BizException e) {
            log.error("[RiskDetailDomainServiceImpl] queryPageRiskDetailList bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryPageRiskDetailResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[RiskDetailDomainServiceImpl] queryPageRiskDetailList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryPageRiskDetailResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


}
