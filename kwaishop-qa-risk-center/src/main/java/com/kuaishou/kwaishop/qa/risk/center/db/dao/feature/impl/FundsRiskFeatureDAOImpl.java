package com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.KspayPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.FundsRiskFeatureDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.KspayBaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureRequestDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.feature.FundsRiskFeatureMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundsRiskFeatureQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundsRiskFeatureStatesUpdateCondition;

import lombok.extern.slf4j.Slf4j;

@Repository
@Slf4j
public class FundsRiskFeatureDAOImpl extends KspayBaseDAOImpl<FundsRiskFeatureDO, FundsRiskFeatureQueryCondition> implements FundsRiskFeatureDAO {

    @Autowired
    private FundsRiskFeatureMapper fundsRiskFeatureMapper;

    @Override
    public KspayPageBO<FundsRiskFeatureDO> queryPageRiskFeatureList(FundsRiskFeatureQueryCondition fundsRiskFeatureQueryCondition) {

        return queryPageList(fundsRiskFeatureQueryCondition);
    }

    @Override
    public KspayPageBO<FundsRiskFeatureDO> queryPageRiskFeatureListDescByUpdateTime(FundsRiskFeatureQueryCondition fundsRiskFeatureQueryCondition) {
        return queryPageListDescByUpdateTime(fundsRiskFeatureQueryCondition);
    }

    @Override
    public List<FundsRiskFeatureDO> queryRiskFeatureList(FundsRiskFeatureQueryCondition fundsRiskFeatureQueryCondition) {
        return queryList(fundsRiskFeatureQueryCondition);
    }

    @Override
    public List<FundsRiskFeatureDO> queryFeatureListWithCondition(FundsRiskFeatureQueryCondition featureQueryCondition) {
        return queryListWithCondition(featureQueryCondition);
    }

    @Override
    public void updateFundsRiskFeatureViewState(FundsRiskFeatureStatesUpdateCondition fundsRiskFeatureStatesUpdateCondition) {
        fundsRiskFeatureMapper.updateFundsRiskFeatureViewState(fundsRiskFeatureStatesUpdateCondition.getFeatureId(),
                fundsRiskFeatureStatesUpdateCondition.getStatus(),
                fundsRiskFeatureStatesUpdateCondition.getUpdateTime(),
                fundsRiskFeatureStatesUpdateCondition.getUpdater());
    }

    @Override
    public void updateAllFeaturesWithoutRisk(String featureId) {
        fundsRiskFeatureMapper.updateAllFeaturesWithoutRisk(featureId);
    }

    @Override
    public FundsRiskFeatureDO queryByFeatureId(FundsRiskFeatureDO fundsRiskFeatureDO) {
        return fundsRiskFeatureMapper.queryByFeatureId(fundsRiskFeatureDO.getFeatureId());

    }

    @Override
    public List<FundsRiskFeatureDO> queryNeedSendKimFeature(FundsRiskFeatureDO fundsRiskFeatureDO,
                                                            Long startTime, Long endTime) {
        List<FundsRiskFeatureDO> fundsRiskFeatureDOS = fundsRiskFeatureMapper.queryNeedSendKimFeature(fundsRiskFeatureDO.getDepartment(),
                fundsRiskFeatureDO.getRiskPassStatus(),
                startTime, endTime);
        log.info("queryNeedSendKimFeature fundsRiskFeatureDOS:{}", fundsRiskFeatureDOS);
        return fundsRiskFeatureDOS;

    }


    @Override
    public FundsRiskFeatureDO queryByMainId(String mainId) {
        return queryById(Long.parseLong(mainId.trim()));
    }

    @Override
    public FundsRiskFeatureDO queryByFeatureId(String featureId) {
        FundsRiskFeatureQueryCondition queryCondition = FundsRiskFeatureQueryCondition.builder()
                .featureId(featureId)
                .build();
        return queryOne(queryCondition);
    }

    @Override
    public int updateByFeatureId(FundsRiskFeatureDO fundsRiskFeatureDO) {
        return fundsRiskFeatureMapper.updateFeatureView(fundsRiskFeatureDO.getUpdater(),
                fundsRiskFeatureDO.getRiskPassStatus(), fundsRiskFeatureDO.getFeatureId(), fundsRiskFeatureDO.getExtraData());  // 支持传入RiskPassStatus
    }

    @Override
    public int updateFeatureViewStatusByFeatureId(FundsRiskFeatureDO fundsRiskFeatureDO) {
        return fundsRiskFeatureMapper.updateFeatureViewStatusByFeatureId(fundsRiskFeatureDO.getStatus(), fundsRiskFeatureDO.getFeatureId());
    }

    @Override
    public int updateTestTypeByFeatureId(FundsRiskFeatureDO fundsRiskFeatureDO) {
        return fundsRiskFeatureMapper.updateTestTypeByFeatureId(fundsRiskFeatureDO.getTestType(), fundsRiskFeatureDO.getFeatureId());  // 仅更新此字段
    }

    @Override
    public void updateFeatureViewWithRisk() {
        fundsRiskFeatureMapper.updateFeatureViewWithRisk();
    }

    @Override
    public int isNeedUpdateRiskPassStatus(String featureId) {
        return fundsRiskFeatureMapper.isNeedUpdateRiskPassStatus(featureId);
    }

    @Override
    public int updateRiskPassStatus(String featureId) {
        return fundsRiskFeatureMapper.updateRiskPassStatus(featureId);
    }

    @Override
    public int updateOnlineConfirm(String featureId, Integer onlineConfirm, String operator) {
        return fundsRiskFeatureMapper.updateOnlineConfirm(featureId, onlineConfirm, operator);
    }

    @Override
    public List<FundsRiskFeatureDO> queryListByDepartment(String department) {
        return fundsRiskFeatureMapper.queryListByDepartment(department);
    }

    public Map<String, Object> getFundsRiskFeaturesByPage(FundsRiskFeatureRequestDO request) {
        Long totalCount = fundsRiskFeatureMapper.countFundsRiskFeatures(request);

        // 设置分页参数
        request.setOffset((request.getPageNo() - 1) * request.getPageSize());
        request.setLimit(request.getPageSize());

        List<FundsRiskFeatureDO> allFeatures = fundsRiskFeatureMapper.queryAllFundsRiskFeatures(request);
        Map<String, Object> response = new HashMap<>();
        response.put("totalCount", totalCount);
        response.put("allFeatures", allFeatures);
        return response;
    }

    @Override
    protected void fillLikeQueryCondition(FundsRiskFeatureQueryCondition condition, QueryWrapper<FundsRiskFeatureDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getDepartment())) {
            queryWrapper.and(q -> q.eq("department", condition.getDepartment()));
        }
        if (StringUtils.isNotBlank(condition.getBusinessDomain())) {
            queryWrapper.and(q -> q.eq("business_domain", condition.getBusinessDomain()));
        }
        if (StringUtils.isNotBlank(condition.getFeatureName())) {
            queryWrapper.and(q -> q.like("feature_name", condition.getFeatureName()));
        }
        if (StringUtils.isNotBlank(condition.getFeatureId())) {
            queryWrapper.and(q -> q.like("feature_id", condition.getFeatureId()));
        }
        if (StringUtils.isNotBlank(condition.getStatus())) {
            queryWrapper.and(q -> q.eq("status", condition.getStatus()));
        }
        if (StringUtils.isNotBlank(condition.getTeamWorker())) {
            queryWrapper.and(q -> q.like("team_worker", condition.getTeamWorker()));
        }
        if (StringUtils.isNotBlank(condition.getCreator())) {
            queryWrapper.and(q -> q.like("creator", condition.getCreator()));
        }
        if (condition.getIsRisk() != 0) {
            queryWrapper.and(q -> q.eq("is_risk", condition.getIsRisk()));
        }
        if (condition.getTestType() != 0) {
            queryWrapper.and(q -> q.eq("test_type", condition.getTestType()));
        }
        // 添加时间筛选条件
        if (condition.getStartTime() != null && condition.getStartTime() > 0) {
            if (condition.getEndTime() != null && condition.getEndTime() > 0) {
                // 两个时间都有效，使用 between 查询
                queryWrapper.between("update_time", condition.getStartTime(), condition.getEndTime());
            } else {
                // 只有 startTime 有效，使用 ge (greater than or equal) 查询
                queryWrapper.ge("update_time", condition.getStartTime());
            }
        } else if (condition.getEndTime() != null && condition.getEndTime() > 0) {
            // 只有 endTime 有效，使用 le (less than or equal) 查询
            queryWrapper.le("update_time", condition.getEndTime());
        }
        queryWrapper.and(q -> q.ne("status", 3));  // 过滤需求状态为删除 = 3的数据
    }

    @Override
    protected void fillQueryCondition(FundsRiskFeatureQueryCondition condition, QueryWrapper<FundsRiskFeatureDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getFeatureId())) {
            queryWrapper.and(q -> q.eq("feature_id", condition.getFeatureId()));
        }
        if (StringUtils.isNotBlank(condition.getDepartment())) {
            queryWrapper.and(q -> q.eq("department", condition.getDepartment()));
        }
        if (StringUtils.isNotBlank(condition.getBusinessDomain())) {
            queryWrapper.and(q -> q.eq("business_domain", condition.getBusinessDomain()));
        }
        if (StringUtils.isNotBlank(condition.getFeatureName())) {
            queryWrapper.and(q -> q.eq("feature_name", condition.getFeatureName()));
        }
        if (StringUtils.isNotBlank(condition.getStatus())) {
            queryWrapper.and(q -> q.eq("status", condition.getStatus()));
        }
        if (StringUtils.isNotBlank(condition.getCreator())) {
            queryWrapper.and(q -> q.eq("creator", condition.getCreator()));
        }
        // 添加时间筛选条件
        if (condition.getStartTime() != null && condition.getStartTime() > 0) {
            if (condition.getEndTime() != null && condition.getEndTime() > 0) {
                // 两个时间都有效，使用 between 查询
                queryWrapper.between("update_time", condition.getStartTime(), condition.getEndTime());
            } else {
                // 只有 startTime 有效，使用 ge (greater than or equal) 查询
                queryWrapper.ge("update_time", condition.getStartTime());
            }
        } else if (condition.getEndTime() != null && condition.getEndTime() > 0) {
            // 只有 endTime 有效，使用 le (less than or equal) 查询
            queryWrapper.le("update_time", condition.getEndTime());
        }
    }

    @Override
    protected BaseMapper<FundsRiskFeatureDO> getMapper() {
        return fundsRiskFeatureMapper;
    }
}
