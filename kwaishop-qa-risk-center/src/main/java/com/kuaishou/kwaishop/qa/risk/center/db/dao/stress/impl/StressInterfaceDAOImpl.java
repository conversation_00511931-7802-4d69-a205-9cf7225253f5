package com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressInterfaceDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress.StressInterfaceMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.stress.ScenarioQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.StressInterfaceBO;


@Repository
public class StressInterfaceDAOImpl extends BaseDAOImpl<StressInterfaceDO, ScenarioQueryCondition> implements StressInterfaceDAO {

    @Autowired
    private StressInterfaceMapper stressInterfaceMapper;

    @Override
    protected void fillQueryCondition(ScenarioQueryCondition condition, QueryWrapper<StressInterfaceDO> queryWrapper) {

    }

    @Override
    protected BaseMapper<StressInterfaceDO> getMapper() {
        return stressInterfaceMapper;
    }

    @Override
    public List<StressInterfaceDO> queryStressInterfaceListByScenario(List<Long> scenarioIds) {
        if (scenarioIds == null || scenarioIds.size() == 0) {
            return new ArrayList<>();
        }
        return stressInterfaceMapper.getByInterfaceListByIds(scenarioIds);

    }

    @Override
    public StressInterfaceDO createStressInterfaceOrUpdate(StressInterfaceBO stressInterfaceBO) {
        StressInterfaceDO stressInterfaceDO = stressInterfaceMapper.getByInterfaceName(stressInterfaceBO.getInterfaceName());

        if (stressInterfaceDO != null && stressInterfaceDO.getId() > 0) {
            //update
            stressInterfaceDO.setInterfaceDesc(stressInterfaceBO.getInterfaceDesc());
            stressInterfaceDO.setInterfaceName(stressInterfaceBO.getInterfaceName());
            stressInterfaceDO.setServiceId(stressInterfaceBO.getServiceId());
            stressInterfaceDO.setServiceName(stressInterfaceBO.getServiceName());
            stressInterfaceDO.setDeleted(0);
            stressInterfaceDO.setStatus(1);
            stressInterfaceDO.setCreateTime(System.currentTimeMillis());
            stressInterfaceDO.setUpdateTime(System.currentTimeMillis());
            stressInterfaceMapper.insertInterface(stressInterfaceDO);
        } else {
            //create
            stressInterfaceDO = convertStressInterfaceBOToDO(stressInterfaceBO);
            stressInterfaceMapper.insertInterface(stressInterfaceDO);
        }
        return stressInterfaceDO;
    }

    private StressInterfaceDO convertStressInterfaceBOToDO(StressInterfaceBO stressInterfaceBO) {
        StressInterfaceDO stressInterfaceDO = new StressInterfaceDO();
        stressInterfaceDO.setInterfaceDesc(stressInterfaceBO.getInterfaceDesc());
        stressInterfaceDO.setInterfaceName(stressInterfaceBO.getInterfaceName());
        stressInterfaceDO.setStatus(stressInterfaceBO.getStatus());
        stressInterfaceDO.setDeleted(stressInterfaceBO.getDeleted());
        stressInterfaceDO.setCreateTime(stressInterfaceBO.getCreateTime());
        stressInterfaceDO.setUpdateTime(stressInterfaceBO.getUpdateTime());
        stressInterfaceDO.setId(stressInterfaceBO.getId());
        stressInterfaceDO.setServiceId(stressInterfaceBO.getServiceId());
        stressInterfaceDO.setServiceName(stressInterfaceBO.getServiceName());
        return stressInterfaceDO;
    }

}
