package com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressInterfaceRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress.StressInterfaceRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.stress.ScenarioQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressInterfaceRecordBO;

@Repository
public class StressInterfaceRecordDAOImpl extends BaseDAOImpl<StressInterfaceRecordDO, ScenarioQueryCondition> implements StressInterfaceRecordDAO {

    @Resource
    private StressInterfaceRecordMapper recordMapper;
    @Override
    protected void fillQueryCondition(ScenarioQueryCondition condition, QueryWrapper<StressInterfaceRecordDO> queryWrapper) {

    }

    @Override
    protected BaseMapper<StressInterfaceRecordDO> getMapper() {
        return recordMapper;
    }


    @Override
    public PageBO<StressInterfaceRecordDO> queryStressInterfaceBaseList(QueryStressInterfaceRecordBO bo) {
        return null;
    }

    @Override
    public StressInterfaceRecordDO queryStressInterfaceRecordById(Long recordId) {
        StressInterfaceRecordDO recordDO = recordMapper.queryStressInterfaceRecordById(recordId);
        return recordDO;
    }

    @Override
    public List<StressInterfaceRecordDO> queryStressInterfaceRecords(QueryStressInterfaceRecordBO bo) {
        return recordMapper.queryStressInterfaceRecords(bo);
    }


}
