package com.kuaishou.kwaishop.qa.risk.center.domain.account.service;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TestAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.RentAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UpdateAllUserInfoRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
public interface UserAccountService {

    List<UserAccountDO> queryList(UserAccountBO userAccountBO);

    List<TestAccountDO> queryListV2(UserAccountBO userAccountBO);

    boolean createAccount(UserAccountDO userAccountDO);

    boolean createAccount(TestAccountDO testAccountDO);

    PageBO<UserAccountDO> queryPageList(UserAccountBO userAccountBO);

    List<UserAccountDO> queryAccountByUserId(Long userId);

    List<TestAccountDO> queryAccountByUserIdV2(Long userId, boolean isBid);

    int updateRentStatus(Integer status, String modifier, Long userId);

    void updateRentStatus(RentAccountBO rentAccountBO);

    void updatePassword(UpdateAllUserInfoRequest request);

    void updateToken(UserAccountDO e);

    void updateTeamId(UpdateAllUserInfoRequest request);

    void updateTeamId(Long userId, Integer teamId);

    void accountManagement(long userId, int teamId);

    List<UserAccountDO> queryBusinessAccountByRule(Integer from, Integer to);

    //给主体添加标签tag

    /**
     * @param account isBid为True，就是bid，否则是uid
     */
    void addTagsToAccount(Integer tagId, boolean isBid, Long account);

    void insertAuthInfo(Long userId);

    void fixAccount(Integer from, Integer to);

    void disableAccount(TestAccountDO testAccountDO);

}
