package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.detail.DetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultPlanProblemDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultPlanProblemBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2023-12-28
 */
public interface FaultPlanProblemDAO extends DetailDAO<FaultPlanProblemDO, CombineQueryParam> {
    int logicDelete(Long id, String operator);

    List<FaultPlanProblemDO> queryFaultPlanProblemList(FaultPlanProblemBO faultPlanBO);

    PageBO<FaultPlanProblemDO> queryPageFaultPlanProblemList(FaultPlanProblemBO faultPlanBO);

    List<FaultPlanProblemDO> queryFaultPlanProblemListByTime(Long centerId, Long st, Long et);

    List<FaultPlanProblemDO> queryFaultPlanProblemListByCenterId(Long centerId);


}
