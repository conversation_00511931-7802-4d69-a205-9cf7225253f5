package com.kuaishou.kwaishop.qa.risk.center.domain.datatool.biz;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.kuaishou.intown.json.JSONObject;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.config.constants.CaseExeStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.config.constants.QueryStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.config.constants.QueryTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.config.kwaisql.DpAccessFetch;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataCaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataCaseExecuteDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataQueryExecuteDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataSourceDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.CaseExecuteDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataQueryDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataSourceDo;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.CheckContent;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.CheckOp;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.dto.CaseDiffQueryContent;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.dto.DataCaseExeDTO;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.dto.DataSourceFormat;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.dto.DataSourceResult;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DmQueryRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DmQueryResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryByQueryIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryByQueryIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryBySourceIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryBySourceIdResponse;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-12
 */
@Service
@Slf4j
public class DataCompareBizService {

    @Autowired
    private DataCaseExecuteDAO dataCaseExecuteDAO;
    @Autowired
    private DataCaseDAO dataCaseDAO;
    @Autowired
    private DataSourceDAO dataSourceDAO;
    @Autowired
    private DataQueryExecuteDAO dataQueryExecuteDAO;
    @Autowired
    private KwaiSqlQueryBizService kwaiSqlQueryBizService;
    @Autowired
    private DpAccessFetch dpAccessFetch;


    public Long executeCase(DataCaseExeDTO dataCaseExeDTO) {
        CaseExecuteDO caseExecuteDO = convertDTO2DO(dataCaseExeDTO);
        long caseExeId = dataCaseExecuteDAO.insertCaseExecute(caseExecuteDO);
        //开始查询数据源


        return caseExeId;

    }

    public CaseExecuteDO convertDTO2DO(DataCaseExeDTO dataCaseExeDTO) {
        String exeName = dataCaseExeDTO.getCaseId().toString() + System.currentTimeMillis();


        CaseExecuteDO caseExecuteDO = new CaseExecuteDO();
        caseExecuteDO.setCaseId(dataCaseExeDTO.getCaseId());
        caseExecuteDO.setExecutorName(dataCaseExeDTO.getExecutorName());
        caseExecuteDO.setName(exeName); //caseId + 时间戳
        caseExecuteDO.setStatus(0); //执行状态= 待执行 = 0
        caseExecuteDO.setCreateTime(System.currentTimeMillis());
        caseExecuteDO.setType(dataCaseExeDTO.getCompareType());
        return caseExecuteDO;
    }


    @Async
    public void asyncExecuteByCaseExeId(Long caseExeId) {
        CaseExecuteDO caseExecuteDO = dataCaseExecuteDAO.queryCaseExecuteById(caseExeId);
        // 通过caseExeId 查询到case配置内容
        DataCaseDO dataCaseDO = dataCaseDAO.queryDataCaseById(caseExecuteDO.getCaseId());
        String diffQueryContent = dataCaseDO.getDiffQueryContent();
        CaseDiffQueryContent caseDiffQueryContent =
                JSONObject.parseObject(diffQueryContent, CaseDiffQueryContent.class);

        DataSourceDo dataSourceDoSc = dataSourceDAO.queryDataSourceById(caseDiffQueryContent.getScId());
        DataSourceDo dataSourceDoTarget = dataSourceDAO.queryDataSourceById(caseDiffQueryContent.getTargetId());


        if (CaseExeStatusEnum.WAITING.equals(caseExecuteDO.getStatus())) {
            try {
                caseExecuteDO.setStatus(CaseExeStatusEnum.RUNNING.getValue());
                caseExecuteDO.setStartTime(System.currentTimeMillis());
                log.info(String.format("用例开始执行，caseExeId is %s.", caseExeId));
                // 根据dataSourceID查询数据源返回数据
                String resSc = queryByDataSourceId(dataSourceDoSc.getId(), caseExecuteDO.getExecutorName());
                String resTarget = queryByDataSourceId(dataSourceDoTarget.getId(), caseExecuteDO.getExecutorName());

                // 暂停1秒，获取查询执行状态
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException ignored) {
                }

                DataQueryDO firstQueryDO = dataQueryExecuteDAO.findById(dataSourceDoSc.getId());
                DataQueryDO secondQueryDO = dataQueryExecuteDAO.findById(dataSourceDoTarget.getId());
                Integer firstQueryExeStatus = firstQueryDO.getStatus();
                Integer secondQueryExeStatus = secondQueryDO.getStatus();
                // 每隔1秒轮询一次查询的状态
                while ((QueryStatusEnum.WAITING.getValue().equals(firstQueryExeStatus) || QueryStatusEnum.RUNNING
                        .getValue().equals(firstQueryExeStatus))
                        || (QueryStatusEnum.WAITING.getValue().equals(secondQueryExeStatus) || QueryStatusEnum.RUNNING
                        .getValue()
                        .equals(secondQueryExeStatus))) {
                    try {
                        TimeUnit.SECONDS.sleep(1);
                    } catch (InterruptedException ignored) {
                    }
                    firstQueryDO = dataQueryExecuteDAO.findById(dataSourceDoSc.getId());
                    secondQueryDO = dataQueryExecuteDAO.findById(dataSourceDoTarget.getId());
                    firstQueryExeStatus = firstQueryDO.getStatus();
                    secondQueryExeStatus = secondQueryDO.getStatus();
                }

                //将查询结果 存到case_exe 并转成dataSourceResult格式
                List<DataSourceResult> dataSourceResults = new ArrayList<>();
                DataSourceResult firstDataResult = DataSourceResult.builder().sourceResult(resSc)
                        .sourceQueryId(firstQueryDO.getId()).dataSourceId(dataSourceDoSc.getId())
                        .sourceType(dataSourceDoSc.getType())
                        .build();

                DataSourceResult secondDataResult = DataSourceResult.builder().sourceResult(resTarget)
                        .sourceQueryId(secondQueryDO.getId()).dataSourceId(dataSourceDoTarget.getId())
                        .sourceType(dataSourceDoTarget.getType())
                        .build();

                dataSourceResults.add(firstDataResult);
                dataSourceResults.add(secondDataResult);
                caseExecuteDO.setDataSourceIds(JSONObject.toJSONString(dataSourceResults));
                dataCaseExecuteDAO.updateCaseExecute(caseExecuteDO);
                // 开始进行对比
                List<Map<String, Object>> firstQueryExeFormat = getFormatResult(resSc, dataSourceDoSc.getType());
                List<Map<String, Object>> secondQueryExeFormat =
                        getFormatResult(resTarget, dataSourceDoTarget.getType());



                // 查询join 字段 和 校验算子 转成对象
                CheckContent checkContent = JSONObject.parseObject(dataCaseDO.getCheckContent(), CheckContent.class);
                // join字段以及 校验规则
                List<CheckOp> checkOpList = checkContent.getCheckOpList();
                // join字段支持多个
                List<String> joinKeys = checkContent.getJoinKey();
                //


            } catch (BizException e) {
                log.info("todo");

            }
        }

    }

    /**
     * 对比方法
     * 返回 List<Map<String, Object>> failDetailList  在每一条记录后面加个字段 标记是否一致
     * 定义一个返回类 包含  failDetailList 明细、总diff条数、
     */




    /**
     * 将dm和sql返回jsonString转成 List<Map<String, Object>>
     */
    public List<Map<String, Object>> getFormatResult(String result, Integer queryType) {
        if (queryType.equals(QueryTypeEnum.DM.getValue())) {
            Map<String, Object> formatJson =  JSONObject.parseObject(result);
            List<Map<String, Object>> formatJsonArray = (List<Map<String, Object>>) formatJson.get("result_row");
            log.info(String.format("formatJsonArray %s.", formatJsonArray));
            return formatJsonArray;

        } else {
            List<Map<String, Object>> formatJsonArray = (List<Map<String, Object>>) JSONObject.parseObject(result);
            log.info(String.format("formatJsonArray %s.", formatJsonArray));
            return formatJsonArray;
        }
    }


    @Async
    public String asyncQueryByQueryExeId(Long queryExeId, Integer queryType, DataSourceFormat dataSourceFormat)
            throws Exception {
        DataQueryDO dataQueryDO = dataQueryExecuteDAO.findById(queryExeId);
        if (dataQueryDO.getStatus().equals(QueryStatusEnum.WAITING.getValue())) {
            try {
                //查询执行开始，更新查询任务状态和开始时间
                dataQueryDO.setStatus(QueryStatusEnum.RUNNING.getValue());
                dataQueryDO.setStartTime(System.currentTimeMillis());
                dataQueryExecuteDAO.updateSelectiveById(dataQueryDO);
                log.info(String.format("查询开始执行，queryExeId is %s.", queryExeId));
                QueryTypeEnum queryTypeEnum = QueryTypeEnum.initQueryType(queryType);
                String res = "";
                boolean isSuccess = false;
                switch (queryTypeEnum) {
                    case DM:
                        res = executeDmQuery(dataSourceFormat, queryExeId);
                        if (!res.equals("")) {
                            dataQueryDO.setResult(res);
                            dataQueryDO.setStatus(QueryStatusEnum.SUCCESS.getValue());

                        } else {
                            dataQueryDO.setStatus(QueryStatusEnum.FAIL.getValue());
                        }
                        dataQueryExecuteDAO.updateSelectiveById(dataQueryDO);
                        break;

                    case HIVE:
                        isSuccess = executeKwaiSql(dataSourceFormat, queryExeId, QueryTypeEnum.HIVE.getValue());
                        break;

                    case DRUID:
                        isSuccess = executeKwaiSql(dataSourceFormat, queryExeId, QueryTypeEnum.DRUID.getValue());
                        break;
                    case CLICKHOUSE:
                        isSuccess = executeKwaiSql(dataSourceFormat, queryExeId, QueryTypeEnum.CLICKHOUSE.getValue());
                        break;
                    default:
                        log.info(String.format("asyncQueryByqueryExeId查询类型不匹配，queryTypeEnum is %s.", queryTypeEnum));
                        break;
                }

                // 获取最新的执行记录，判断是否是中止状态
                dataQueryDO = dataQueryExecuteDAO.findById(queryExeId);
                if (QueryStatusEnum.RUNNING.getValue().equals(dataQueryDO.getStatus())) {
                    if (isSuccess) {
                        dataQueryDO.setStatus(QueryStatusEnum.SUCCESS.getValue());
                        dataQueryDO.setEndTime(System.currentTimeMillis());
                        dataQueryExecuteDAO.updateSelectiveById(dataQueryDO);
                        res = dataQueryDO.getResult();
                    } else {
                        dataQueryDO.setStatus(QueryStatusEnum.FAIL.getValue());
                        dataQueryDO.setEndTime(System.currentTimeMillis());
                        dataQueryExecuteDAO.updateSelectiveById(dataQueryDO);
                        res = dataQueryDO.getResult();
                        log.info(String.format("查询执行失败，具体失败原因见上面日志，queryExeId is %s.", queryExeId));
                    }
                }

                //结果更新到case-exe表
                return res;

            } catch (BizException e) {
                log.error("[asyncQueryByqueryExeId]  bizError, exception: ", e);
            }
        } else {
            log.info(String.format("查询任务运行状态不是待执行，当前状态是%s，queryExeId is %s.",
                    dataQueryDO.getStatus(), queryExeId));
        }

        return "";

    }

    public static boolean isNumeric(String str) {
        Pattern pattern = Pattern.compile("[0-9]*");
        return pattern.matcher(str).matches();
    }

    public boolean executeKwaiSql(DataSourceFormat dataSourceFormat, Long queryExeId, Integer queryType)
            throws Exception {
        boolean mapList = dpAccessFetch.fetchDpData(queryExeId, queryType, dataSourceFormat);
        return mapList;

    }

    public String executeDmQuery(DataSourceFormat dataSourceFormat, Long queryExeId) {
        log.info(String.format("查询开始执行，queryExeId is %s，Header is %s,inParam is %s", queryExeId,
                dataSourceFormat.getHeader(), dataSourceFormat.getInParam()));
        if (dataSourceFormat.getHeader() != null && dataSourceFormat.getInParam() != null) {
            //兼容queryID 和 serviceCode 两种查询方式
            if (isNumeric(dataSourceFormat.getHeader())) {

                QueryByQueryIdRequest queryByQueryIdRequest =
                        JSONObject.parseObject(dataSourceFormat.getInParam(), QueryByQueryIdRequest.class);

                QueryByQueryIdResponse queryByQueryIdResponse =
                        kwaiSqlQueryBizService.dmQueryByQueryId(queryByQueryIdRequest);
                //结果更新到 query_exe表 result字段
                log.info(String.format("查询执返回结果，queryExeId is %s，queryByQueryIdResponse is %s", queryExeId,
                        queryByQueryIdResponse.toString()));
                return JSONObject.toJSONString(queryByQueryIdResponse);

                // 结果更新到 case-exe表

            } else {
                DmQueryRequest dmQueryRequest =
                        JSONObject.parseObject(dataSourceFormat.getInParam(), DmQueryRequest.class);
                DmQueryResponse dmQueryResponse = kwaiSqlQueryBizService.dmQuery(dmQueryRequest);
                log.info(String.format("查询执返回结果，queryExeId is %s，dmQueryResponse is %s", queryExeId,
                        dmQueryResponse.toString()));
                return JSONObject.toJSONString(dmQueryResponse);
            }
        }
        return "";
    }

    public String queryByDataSourceId(Long sourceId, String executorName) {
        DataSourceDo dataSourceDo = dataSourceDAO.queryDataSourceById(sourceId);
        DataQueryDO dataQueryDO = new DataQueryDO();
        dataQueryDO.setExecutorName(executorName);
        dataQueryDO.setDataSourceId(sourceId);
        dataQueryDO.setCreateTime(System.currentTimeMillis());
        dataQueryDO.setStatus(QueryStatusEnum.WAITING.getValue());
        long queryExeId = dataQueryExecuteDAO.insertDataQuery(dataQueryDO);

        //将数据源里的content 转成对象
        DataSourceFormat dataSourceFormat = JSONObject.parseObject(dataSourceDo.getContent(), DataSourceFormat.class);

        try {
            String res = asyncQueryByQueryExeId(queryExeId, dataSourceDo.getType(), dataSourceFormat);
            //结果保存到case-exe表
            return res;

        } catch (Exception e) {
            log.error(String.format("asyncQueryByqueryExeId查询异常，queryExeId is %s，queryType is %s", queryExeId,
                    dataSourceDo.getType()));
        }
        return "";
    }



    /**
     * 封装的rpc接口 通过dataSourceID 返回执行内容
     */
    public QueryBySourceIdResponse exeBySourceId(QueryBySourceIdRequest request) {
        if (request.getSourceId() <= 0) {
            return QueryBySourceIdResponse.newBuilder().setResult(BizErrorCode.DATA_SOURCE_ID_ERROR.getCode())
                    .setErrorMsg(BizErrorCode.DATA_SOURCE_ID_ERROR.getMessage()).build();
        }
        if (StringUtils.isBlank(request.getExecutorName())) {
            return QueryBySourceIdResponse.newBuilder().setResult(BizErrorCode.DATA_EXECUTOR_NAME_EMPTY.getCode())
                    .setErrorMsg(BizErrorCode.DATA_EXECUTOR_NAME_EMPTY.getMessage()).build();
        }

        try {

            String res = queryByDataSourceId(request.getSourceId(), request.getExecutorName());
            log.info("[DataCompareBizService] exeBySourceId,  res: ", res);
            return QueryBySourceIdResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(res)
                    .build();

        } catch (BizException e) {
            log.error("[DataCompareBizService] exeBySourceId bizError, exception: ", e);
            return QueryBySourceIdResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[DataCompareBizService] exeBySourceId Error, exception: ", e);
            return QueryBySourceIdResponse.newBuilder().setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

}
