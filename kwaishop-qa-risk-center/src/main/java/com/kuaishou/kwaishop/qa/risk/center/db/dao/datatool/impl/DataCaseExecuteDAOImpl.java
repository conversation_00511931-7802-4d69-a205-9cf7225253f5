package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.DataBaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataCaseExecuteDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.CaseExecuteDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.datatool.DataCaseExeMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.datatool.CaseExecuteCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.CaseExecuteBO;




/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2022-12-11
 */

@Repository
public class DataCaseExecuteDAOImpl extends DataBaseDAO<CaseExecuteDO, CaseExecuteCondition> implements DataCaseExecuteDAO {


    @Autowired
    private DataCaseExeMapper dataCaseExeMapper;

    @Override
    protected void fillQueryCondition(CaseExecuteCondition condition, QueryWrapper<CaseExecuteDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getName())) {
            queryWrapper.like("name", condition.getName());
        }
        if (condition.getCaseId() > 0) {
            queryWrapper.eq("case_id", condition.getCaseId());
        }
        if (StringUtils.isNotBlank(condition.getExecutorName())) {
            queryWrapper.eq("executor_name", condition.getExecutorName());
        }

    }

    @Override
    protected BaseMapper<CaseExecuteDO> getMapper() {
        return dataCaseExeMapper;
    }

    @Override
    public long insertCaseExecute(CaseExecuteDO caseExecuteDO) {
        caseExecuteDO.setCreateTime(System.currentTimeMillis());
        caseExecuteDO.setUpdateTime(System.currentTimeMillis());
        caseExecuteDO.setStartTime(System.currentTimeMillis());
        return dataCaseExeMapper.insert(caseExecuteDO);
    }

    @Override
    public long updateCaseExecute(CaseExecuteDO caseExecuteDO) {
        caseExecuteDO.setUpdateTime(System.currentTimeMillis());

        return dataCaseExeMapper.updateById(caseExecuteDO);
    }

    @Override
    public int logicDeleted(Long id, String operator) {
        return dataCaseExeMapper.logicDeleted(operator, id);
    }

    @Override
    public List<CaseExecuteDO> queryCaseExecuteLikeName(String name) {
        return dataCaseExeMapper.queryCaseExecuteLikeName(name);
    }

    @Override
    public PageBO<CaseExecuteDO> queryPageCaseExecuteList(CaseExecuteBO caseExecuteBO) {
        CaseExecuteCondition queryCondition = CaseExecuteCondition.builder()
                .id(caseExecuteBO.getId())
                .caseId(caseExecuteBO.getCaseId())
                .executorName(caseExecuteBO.getExecutorName())
                .orderByCreateTimeDesc(true)
                .pageNo(caseExecuteBO.getPageNo())
                .pageSize(caseExecuteBO.getPageSize())
                .build();
        return queryPageList(queryCondition);
    }

    @Override
    public List<CaseExecuteDO> queryCaseExecuteList(CaseExecuteDO dataCaseDO) {


        return dataCaseExeMapper.queryAllCaseExe();
    }

    @Override
    public CaseExecuteDO queryCaseExecuteById(Long id) {
        return dataCaseExeMapper.queryCaseExecuteById(id);
    }
}
