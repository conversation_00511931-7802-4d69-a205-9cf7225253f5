package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.impl;

import java.util.Collection;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.FaultPlanDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultPlanDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.fault.FaultPlanMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.fault.FaultPlanQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultPlanBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultPlanStatusEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-08
 */
@Repository
public class FaultPlanDAOImpl extends BaseDAOImpl<FaultPlanDO, FaultPlanQueryCondition> implements FaultPlanDAO {

    @Autowired
    private FaultPlanMapper faultPlanMapper;

    @Override
    public List<FaultPlanDO> queryByCenterIds(Collection<Long> centerIds, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FaultPlanDO> queryByTeamIds(Long centerId, Collection<Long> teamIds, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FaultPlanDO> queryByCenterId(Long centerId, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FaultPlanDO> queryByTeamId(Long centerId, Long teamId, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FaultPlanDO> queryByCenterIdsTeamIds(Collection<Long> centerIds, Collection<Long> teamIds,
            CombineQueryParam queryParam) {
        return null;
    }

    @Override
    protected void fillQueryCondition(FaultPlanQueryCondition condition, QueryWrapper<FaultPlanDO> queryWrapper) {
        if (condition.getCenterId() != null && condition.getCenterId() > 0) {
            queryWrapper.and(q -> q.eq("center_id", condition.getCenterId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getCenterIds())) {
            queryWrapper.and(q -> q.in("center_id", condition.getCenterIds()));
        }
        if (condition.getTeamId() != null && condition.getTeamId() > 0) {
            queryWrapper.and(q -> q.eq("team_id", condition.getTeamId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getTeamIds())) {
            queryWrapper.and(q -> q.in("team_id", condition.getTeamIds()));
        }
        if (StringUtils.isNotBlank(condition.getName())) {
            queryWrapper.and(q -> q.like("name", condition.getName()));
        }
        if (condition.getStatus() != null && FaultPlanStatusEnum.of(condition.getStatus()) != null) {
            queryWrapper.and(q -> q.eq("status", condition.getStatus()));
        }
        if (CollectionUtils.isNotEmpty(condition.getStatusList())) {
            queryWrapper.and(q -> q.in("status", condition.getStatusList()));
        }
        if (condition.getStartTimeGe() != null && condition.getStartTimeGe() > 0) {
            queryWrapper.and(q -> q.ge("start_time", condition.getStartTimeGe()));
        }
        if (condition.getStartTimeLe() != null && condition.getStartTimeLe() > 0) {
            queryWrapper.and(q -> q.le("start_time", condition.getStartTimeLe()));
        }
        if (condition.getEndTimeGe() != null && condition.getEndTimeGe() > 0) {
            queryWrapper.and(q -> q.ge("end_time", condition.getEndTimeGe()));
        }
        if (condition.getEndTimeLe() != null && condition.getEndTimeLe() > 0) {
            queryWrapper.and(q -> q.le("end_time", condition.getEndTimeLe()));
        }
    }

    @Override
    protected BaseMapper<FaultPlanDO> getMapper() {
        return faultPlanMapper;
    }

    @Override
    public List<FaultPlanDO> queryFaultPlanList(FaultPlanBO faultPlanBO) {
        FaultPlanQueryCondition condition = FaultPlanQueryCondition.builder()
                .id(faultPlanBO.getId())
                .status(faultPlanBO.getStatus())
                .name(faultPlanBO.getName())
                .startTimeGe(faultPlanBO.getStartTime())
                .startTimeLe(faultPlanBO.getEndTime())
                .teamId(faultPlanBO.getTeamId())
                .centerId(faultPlanBO.getCenterId())
                .ids(faultPlanBO.getIds())
                .orderByCreateTimeDesc(true)
                .statusList(faultPlanBO.getStatusList())
                .build();
        return queryList(condition);
    }

    @Override
    public List<FaultPlanDO> queryFaultPlanAll() {
        return faultPlanMapper.queryFaultPlanAll();
    }

    @Override
    public PageBO<FaultPlanDO> queryPageFaultPlanList(FaultPlanBO faultPlanBO) {
        FaultPlanQueryCondition condition = FaultPlanQueryCondition.builder()
                .id(faultPlanBO.getId())
                .status(faultPlanBO.getStatus())
                .name(faultPlanBO.getName())
                .teamId(faultPlanBO.getTeamId())
                .centerId(faultPlanBO.getCenterId())
                .startTimeGe(faultPlanBO.getStartTime())
                .startTimeLe(faultPlanBO.getEndTime())
                .pageNo(faultPlanBO.getPageNo())
                .pageSize(faultPlanBO.getPageSize())
                .orderByCreateTimeDesc(true)
                .ids(faultPlanBO.getIds())
                .statusList(faultPlanBO.getStatusList())
                .build();
        return queryPageList(condition);
    }

    @Override
    public List<FaultPlanDO> queryFaultPlanListByCenterId(Long centerId, Long startTime, Long endTime) {
        FaultPlanQueryCondition condition = FaultPlanQueryCondition.builder()
                .centerId(centerId)
                .startTimeGe(startTime)
                .startTimeLe(endTime)
                .build();
        return queryList(condition);
    }

    @Override
    public int logicDelete(Long id, String operator) {
        return faultPlanMapper.logicDeleted(operator, id);
    }
}
