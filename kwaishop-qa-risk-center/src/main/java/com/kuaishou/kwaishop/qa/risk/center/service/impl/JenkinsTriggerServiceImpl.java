package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.domain.jenkins.JenkinsBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.jenkins.JenkinsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.jenkins.JenkinsResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.jenkins.KrpcJenkinsTriggerServiceGrpc.JenkinsTriggerServiceImplBaseV2;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-04
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class JenkinsTriggerServiceImpl extends JenkinsTriggerServiceImplBaseV2 {



    @Autowired
    private JenkinsBizService jenkinsBizService;

    public JenkinsResponse execute(JenkinsRequest jenkinsRequest) {
        try {
            // 构建任务的名称
            String jobName = "your-job-name";
            jenkinsBizService.exec("pay_pc_autotest");
            // 触发构建
        } catch (Exception e) {
            e.printStackTrace();
        }
       return null;
    }


}
