package com.kuaishou.kwaishop.qa.risk.center.domain.account.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_DATA_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ID_NOT_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.UID_NOT_EMPTY_ERROR;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.RentTestAccountRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.TestAccountDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.UserAccountDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.UserAuthDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentTestAccountRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TestAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.account.UserAccountMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.RentAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.RentalStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserDataTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserAccountService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UpdateAllUserInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.utils.AESUtil;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
@Lazy
@Service
@Slf4j
public class UserAccountServiceImpl implements UserAccountService {

    private static final Integer RENT_COUNT = 5;
    @Autowired
    private UserAccountDAO userAccountDAO;
    @Autowired
    private UserAuthDAO userAuthDAO;
    @Autowired
    private TestAccountDAO testAccountDAO;

    @Autowired
    private RentTestAccountRecordDAO rentTestAccountRecordDAO;
    @Autowired
    private UserAccountMapper userAccountMapper;


    @Override
    public List<UserAccountDO> queryList(UserAccountBO userAccountBO) {
        if (userAccountBO == null) {
            throw new BizException(ACCOUNT_DATA_ERROR);
        }
        return userAccountDAO.queryList(userAccountBO);
    }

    @Override
    public List<TestAccountDO> queryListV2(UserAccountBO userAccountBO) {

        return null;
    }

    @Override
    public PageBO<UserAccountDO> queryPageList(UserAccountBO userAccountBO) {
        if (userAccountBO == null) {
            throw new BizException(ACCOUNT_DATA_ERROR);
        }
        return userAccountDAO.queryPageList(userAccountBO);
    }

    @Override
    public List<UserAccountDO> queryAccountByUserId(Long userId) {
        return userAccountDAO.queryUserAccountByUserId(userId);
    }

    @Override
    public List<TestAccountDO> queryAccountByUserIdV2(Long userId, boolean isBid) {
        if (!isBid) {
            return testAccountDAO.queryByUid(userId);
        }
        return new ArrayList<>();
    }

    /**
     * 先不做执行，先不用这个rent_count的判断，改成由每次租借前，进行判断是否超过5个人在租。以及页面展示的如果超过5个人就不展示租借按钮
     */
    @Override
    public int updateRentStatus(Integer status, String modifier, Long userId) {
        // 先判断是什么操作，然后修改对应的租借次数。
        if (Objects.equals(status, UserStatusEnum.CAN_APPLY.getCode())) {
            //想更新成可借用，说明是归还状态
            //            userAccountDAO.reduceRentNum(1, userId);
            //            return userAccountDAO.updateRentStatus(status, modifier, userId);
        } else {
            // 借用状态
            //            userAccountDAO.addRentNum(1, userId);
            //            List<UserAccountDO> userAccountDOS = userAccountDAO.queryUserAccountByUserId(userId);
            //            if (!userAccountDOS.isEmpty() && userAccountDOS.get(0).getRentNum() >= RENT_COUNT) {
            // 改成不可借用状态
            // 先把这个更改可借用的状态给去掉。
            //                return userAccountDAO.updateRentStatus(status, modifier, userId);
            //            }
        }
        return 0;
    }

    @Override
    public void updateRentStatus(RentAccountBO rentAccountBO) {

        List<RentTestAccountRecordDO> rentTestAccountRecordDOS =
                rentTestAccountRecordDAO.queryList(RentAccountBO.builder()
                        .userId(rentAccountBO.getUserId())
                        .rentalStatus(RentalStatusEnum.BORROWED.getCode())
                        .build());
        int num = rentTestAccountRecordDOS.size();

        //testAccountDAO.updateStatus(rentAccountBO.getUserId(), UserStatusEnum.CAN_NOT_APPLY.getCode());


    }

    @Override
    public void updatePassword(UpdateAllUserInfoRequest request) {
        UserAccountDO userAccountDO = userAccountDAO.queryByUid(request.getUserId());
        userAccountDAO.updateSelectiveById(UserAccountDO.builder()
                .userId(userAccountDO.getUserId())
                .password(AESUtil.encrypt(request.getPassword()))
                .id(userAccountDO.getId())
                .modifier(request.getOperator())
                .build());
    }

    @Override
    public void updateToken(UserAccountDO e) {
        userAccountDAO.updateToken(e);
    }

    @Override
    public void updateTeamId(UpdateAllUserInfoRequest request) {

    }

    @Override
    public void updateTeamId(Long userId, Integer teamId) {
        UserAccountDO userAccountDO = userAccountDAO.queryByUid(userId);
        userAccountDAO.updateSelectiveById(UserAccountDO.builder()
                .id(userAccountDO.getId())
                .teamId(Long.valueOf(teamId))
                .build());
    }

    @Override
    public void accountManagement(long userId, int teamId) {

    }

    @Override
    public List<UserAccountDO> queryBusinessAccountByRule(Integer from, Integer to) {
        return userAccountDAO.queryBusinessAccountByRule(from, to);
    }

    @Override
    public void addTagsToAccount(Integer tagId, boolean isBid, Long account) {

    }

    @Override
    public void insertAuthInfo(Long userId) {

    }

    @Override
    public void fixAccount(Integer from, Integer to) {
        for (int i = from; i < to; i++) {
            UserAccountDO userAccountDO = userAccountMapper.selectById(i);
            if (userAccountDO.getPassword() == null) {
                continue;
            }
            String encryptPassword = AESUtil.encrypt(userAccountDO.getPassword());
            userAccountMapper.fixAccount(i, userAccountDO.getAccount(), encryptPassword);

        }
    }

    @Override
    public void disableAccount(TestAccountDO testAccountDO) {
        if (testAccountDO.getKwaiId() != null) {
            testAccountDAO.deleteTestAccount(testAccountDO.getKwaiId());
        }
    }

    @Override
    public boolean createAccount(UserAccountDO userAccountDO) {
        if (userAccountDO.getUserId() == null) {
            throw new BizException(UID_NOT_EMPTY_ERROR);
        }
        if (userAccountDO.getStatus() == null) {
            userAccountDO.setStatus(UserStatusEnum.CAN_APPLY.getCode());
        }
        // 所有导入的账户都是全数据类型
        userAccountDO.setDataType(UserDataTypeEnum.ALL_DATA.getCode());
        //        if (PERSONAL.getCode().equals(userAccountDO.getAccountType())) {
        //            userAccountDO.setStatus(UserStatusEnum.CAN_NOT_APPLY.getCode());
        //        } else {
        //            userAccountDO.setStatus(UserStatusEnum.CAN_APPLY.getCode());
        //        }
        //        if (MathUtil.isNonZeroLong(userAccountDO.getCenterId())
        //                && MathUtil.isNonZeroLong(userAccountDO.getTeamId())
        //                && StringUtils.isNotBlank(userAccountDO.getAccount())
        //                && StringUtils.isNotBlank(userAccountDO.getPassword())) {
        //            userAccountDO.setDataType(UserDataTypeEnum.ALL_DATA.getCode());
        //        } else {
        //            userAccountDO.setDataType(UserDataTypeEnum.SINGLE_UID.getCode());
        //            userAccountDO.setStatus(UserStatusEnum.CAN_NOT_APPLY.getCode());
        //        }
        // 根据uid去重
        UserAccountDO existDO = userAccountDAO.queryByUid(userAccountDO.getUserId());
        if (existDO == null) {
            // 如果账号不存在,可直接插入
            userAccountDO.setAccount(userAccountDO.getAccount());
            userAccountDO.setPassword(AESUtil.encrypt(userAccountDO.getPassword()));
            userAccountDAO.insert(userAccountDO);
            return Boolean.TRUE;
        } else {
            // 如果账户存在则更新
            userAccountDAO.updateSelectiveById(UserAccountDO.builder()
                    .id(existDO.getId())
                    .password(AESUtil.encrypt(userAccountDO.getPassword()))
                    .account(userAccountDO.getAccount())
                    .accountType(userAccountDO.getAccountType())
                    .userId(userAccountDO.getUserId())
                    .loginType(userAccountDO.getLoginType())
                    .teamId(userAccountDO.getTeamId())
                    .dataType(1)
                    .modifier(userAccountDO.getModifier())
                    .status(userAccountDO.getStatus())
                    .updateTime(System.currentTimeMillis())
                    .build());
            //如果账号存在，则抛出异常提示。
            return Boolean.TRUE;
        }
    }

    @Override
    public boolean createAccount(TestAccountDO testAccountDO) {
        if (testAccountDO.getKwaiId() == null && testAccountDO.getBId() == null) {
            throw new BizException(ID_NOT_EMPTY_ERROR);
        }
        // 所有导入的账户都是可借用的状态
        testAccountDO.setStatus(UserStatusEnum.CAN_APPLY.getCode());
        // 所有导入的账户都是全数据类型
        testAccountDO.setDataType(UserDataTypeEnum.ALL_DATA.getCode());

        //查询库里是否已经有了
        boolean isExist = false;
        TestAccountDO tdo = new TestAccountDO();
        if (testAccountDO.getKwaiId() != null) {
            List<TestAccountDO> testAccountDOS = testAccountDAO.queryByUid(testAccountDO.getKwaiId());
            if (testAccountDOS.size() > 0) {
                tdo = testAccountDOS.get(0);
                isExist = true;
            }
        } else if (testAccountDO.getBId() != null) {
            //目前固定位null,后续再调整
            tdo = testAccountDAO.queryByBid(testAccountDO.getBId());
            if (tdo != null) {
                isExist = true;
            }
        }
        if (isExist) {
            //如果存在，更新当前数据
            testAccountDAO.updateSelectiveById(TestAccountDO.builder()
                    .id(tdo.getId())
                    .ownerId(testAccountDO.getOwnerId())
                    .ownerName(testAccountDO.getOwnerName())
                    .bId(testAccountDO.getBId())
                    .password(AESUtil.encrypt(testAccountDO.getPassword()))
                    .account(testAccountDO.getAccount())
                    .accountType(testAccountDO.getAccountType())
                    .kwaiId(testAccountDO.getKwaiId())
                    .loginType(testAccountDO.getLoginType())
                    .modifier(testAccountDO.getModifier())
                    .sellerName(testAccountDO.getSellerName())
                    .storeName(testAccountDO.getStoreName())
                    .storeType(testAccountDO.getStoreType())
                    .celebrityPermission(testAccountDO.getCelebrityPermission())
                    .shopScore(testAccountDO.getShopScore())
                    .promoterScore(testAccountDO.getPromoterScore())
                    .deposit(testAccountDO.getDeposit())
                    .build());
            return Boolean.TRUE;
        } else {
            //直接插入
            testAccountDO.setAccount(testAccountDO.getAccount());
            testAccountDO.setPassword(AESUtil.encrypt(testAccountDO.getPassword()));
            testAccountDO.setId(testAccountDO.getKwaiId());
            testAccountDO.setCreator(testAccountDO.getCreator());
            testAccountDAO.insert(testAccountDO);
        }
        return Boolean.TRUE;
    }
}
