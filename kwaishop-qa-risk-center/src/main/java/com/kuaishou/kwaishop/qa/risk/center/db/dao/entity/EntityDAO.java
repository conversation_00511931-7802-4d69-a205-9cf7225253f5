package com.kuaishou.kwaishop.qa.risk.center.db.dao.entity;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityQueryBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-09
 */
public interface EntityDAO {

    EntityDO queryById(Long id);

    List<EntityDO> queryByIds(Collection<Long> ids);

    Map<Long, EntityDO> batchQueryByIds(Collection<Long> ids);

    int updateSelectiveById(EntityDO domainObject);

    long insert(EntityDO domainObject);

    long saveOrUpdate(EntityDO domainObject);

    EntityDO queryByName(Integer entityType, String name);

    EntityDO queryByName(Integer entityType, String name, Long notEqId);

    EntityDO queryByName(Integer entityType, Long entityId, String name);

    EntityDO queryByName(Integer entityType, Long entityId, String name, Long notEqId);

    List<EntityDO> queryByNames(Integer entityType, Long entityId, Collection<String> names);

    List<EntityDO> queryLikeName(Integer entityType, Long entityId, String name);

    EntityDO queryLikeName(Integer entityType, String name);

    List<EntityDO> queryListByName(Integer entityType, String name);

    List<EntityDO> queryByEntityType(Integer entityType);

    Map<Long, EntityDO> queryGroupByEntityType(Integer entityType);

    List<EntityDO> queryByEntityId(Integer entityType, Long entityId);

    List<EntityDO> queryByEntityIds(Integer entityType, Collection<Long> entityIds);

    List<EntityDO> queryEntityList(EntityQueryBO baseBO);

    PageBO<EntityDO> queryEntityPageList(EntityQueryBO baseBO);

    Map<Long, EntityDO> queryGroupById(Integer entityType, Long entityId);

    Map<String, List<EntityDO>> queryGroupByName(Integer entityType, Long entityId);

    Map<Long, List<EntityDO>> queryGroupByEntityId(Integer entityType, Collection<Long> entityIds);

    int logicDelete(String operator, Long id);

    int logicDelete(String operator, Collection<Long> ids);

    int logicDelete(String operator, Integer entityType, Long entityId);

    int logicBatchDeleted(String operator, Integer entityType, Long entityId, Collection<String> names);

}
