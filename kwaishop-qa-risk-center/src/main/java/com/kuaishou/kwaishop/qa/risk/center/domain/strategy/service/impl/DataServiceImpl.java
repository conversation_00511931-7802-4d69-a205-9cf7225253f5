package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.impl;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.kuaishou.intown.json.JSONArray;
import com.kuaishou.intown.json.JSONObject;
import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.biz.DataQueryExeBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.dao.StrategyTrafficRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.dao.TaskDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecordListStatus;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTestDataPool;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTrafficRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.DataService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryExeResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryExeResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayRequest;
import com.kuaishou.kwaishop.qa.risk.center.utils.JsonUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.http.HttpUtils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;

@Service
@Slf4j
@KrpcService(registry = "custom-registry-config-kess")
public class DataServiceImpl implements DataService {


    @Autowired
    private DataQueryExeBizService dataQueryExeBizService;

    @Autowired
    private StrategyTrafficRecordDAO strategyTrafficRecordDAO;

    @Autowired
    private TaskDAOImpl taskDAO;

    private static final String QUERY_EXE_ID = "2948";

    private static final String POLICY_TREE_EXECUTE_RECORD = "kwaishop_apollo_strategy_policy_tree_execute_record";

    @Override
    public List<StrategyTestDataPool> getDataPoolByRpc() {
        try {
            QueryExeResultResponse queryExeResultResponse = dataQueryExeBizService.queryExeResult(QueryExeResultRequest.newBuilder()
                    .setQueryExeId(Long.parseLong(QUERY_EXE_ID))
                    .build());
            String result = queryExeResultResponse.getData().getResult();

            return parseJson(result);
        } catch (Exception e) {
            log.error("getDataPoolByRpc error", e);
            return null;
        }
    }

    @Override
    public JsonArray mockData() {
        String relativePath = "../../data.csv";

        // 获取绝对路径
        Path absolutePath = Paths.get(relativePath).toAbsolutePath();

        return null;
    }

    public String getDataPoolByHttp() {
        String url = "https://data-qa.test.gifshow.com/gateway/rest/data/qa/platform/datatest/queryExeResult";
        HttpUtils httpUtils = new HttpUtils();

        HashMap<String, String> param = new HashMap<String, String>() {{
            put("queryExeId", QUERY_EXE_ID);
        }};
        Response response = null;
        try {
            response = httpUtils.post(url, null, param);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return response.toString();
    }

    public static void main(String[] args) {
        DataServiceImpl dataServiceImpl = new DataServiceImpl();

    }

    @Override
    public String run() {

        return "";
    }

    /**
     *
     * @param request
     */
    @Override
    public List<StrategyTestDataPool> getDataPoolById(ReplayRequest request) {
        List<StrategyTrafficRecordDO> strategyTrafficRecordDOList = new ArrayList<>();
        if (request.getIdList().isEmpty() && !request.getTaskId().isEmpty()) {
            // 如果没有caseId有taskId，那么就是按照taskId来回放
            strategyTrafficRecordDOList = strategyTrafficRecordDAO.queryAllByTaskId(request.getTaskId());
            taskDAO.updateStatus(request.getTaskId(), RecordListStatus.REPLAYING.getCode());
        } else {
            strategyTrafficRecordDOList = strategyTrafficRecordDAO.queryAllByIds(request.getIdList());
        }
        return buildStrategyTestDataPool(strategyTrafficRecordDOList);
    }

    private List<StrategyTestDataPool> buildStrategyTestDataPool(List<StrategyTrafficRecordDO> strategyTrafficRecordDOList) {
        List<StrategyTestDataPool> res = new ArrayList<>();
        for (StrategyTrafficRecordDO strategyTrafficRecordDO : strategyTrafficRecordDOList) {
            res.add(StrategyTestDataPool.builder()
                            .id(String.valueOf(strategyTrafficRecordDO.getId()))
                            .strategyIds(getListIds(strategyTrafficRecordDO, "strategyIds"))
                            .actionIds(getListIds(strategyTrafficRecordDO, "actionIds"))
                            .featureContext(strategyTrafficRecordDO.getTrafficData())
                            .sceneKey(strategyTrafficRecordDO.getSceneName())
                    .build());
        }
        return res;
    }

    private List<String> getListIds(StrategyTrafficRecordDO strategyTrafficRecordDO, String type) {
        List<String> list = new ArrayList<>();
        JSONObject jsonObject = JSONObject.parseObject(strategyTrafficRecordDO.getExpectedResult());
        JSONArray jsonArray = jsonObject.getJSONArray(type);
        jsonArray.forEach(o -> {
            list.add(o.toString());
        });
        return list;
    }


    /**
     * 用来转换上面拿到的多层嵌套三层转义json
     * @param input
     * @return
     */
    public List<StrategyTestDataPool> parseJson(String input) {
        JsonObject jsonObject = JsonUtils.stringToJson(input, JsonObject.class);
        JsonArray resultRow = jsonObject.getAsJsonArray("result_row");
        List<StrategyTestDataPool> res = new ArrayList<>();
        for (int i = 0; i < resultRow.size(); i++) {
            JsonObject jsonObject1 = resultRow.get(i).getAsJsonObject();
            String featureContext = getFeatureContext(jsonObject1);

            String sceneKey = getSceneKey(jsonObject1);

            res.add(StrategyTestDataPool.builder()
                            .featureContext(featureContext)
                            .actionIds(getExpectIds(jsonObject1, "action_context"))
                            .strategyIds(getExpectIds(jsonObject1, "strategy_context"))
                            .sceneKey(sceneKey)
                            .id(generateUniqueId())
                    .build());
        }
        return res;
    }

    //
    public List<String> getExpectIds(JsonObject jsonObject, String type) {
        //1.先获取actionid
        String actionIdsString = jsonObject.get(POLICY_TREE_EXECUTE_RECORD + "." + type).getAsString();
        String ids = removeFirstAndLastTwoChars(unescapeJsonString(actionIdsString));
        JsonArray jsonArray = JsonUtils.stringToJson(ids, JsonArray.class);
        List<String> res = new ArrayList<>();
        jsonArray.forEach(jsonElement -> {
            if (jsonElement.getAsJsonObject().get("result").getAsBoolean()) {
                res.add(jsonElement.getAsJsonObject().get("nodeId").getAsString());
            }
        });
        return res;
    }

    /**
     * 用来处理获取场景key
     * @param jsonObject
     * @return
     */
    public String getSceneKey(JsonObject jsonObject) {
        String res = unescapeJsonString(jsonObject.get("kwaishop_apollo_strategy_policy_tree_execute_record.scene_key")
                .getAsString());
        return removeFirstAndLastTwoChars(res);
    }

    /**
     * 处理featureContext。
     * @param jsonObject
     * @return
     */
    public String getFeatureContext(JsonObject jsonObject) {
        String featureContext = unescapeJsonString(jsonObject.get("kwaishop_apollo_strategy_policy_tree_execute_record.feature_context")
                .getAsString());
        return coverParmamJsonByDataPool(featureContext);
    }

    /**
     * 处理user_id和替换特殊字符
     * @param featureContext
     * @return
     */
    public String coverParmamJsonByDataPool(String featureContext) {
        JsonObject jsonObject = JsonUtils.stringToJson(removeFirstAndLastTwoChars(featureContext), JsonObject.class);
        jsonObject.addProperty("userId", "3690600021");
        return jsonObject.toString();
    }

    /**
     * 处理，把前后的引号给删除
     * @param input
     * @return
     */
    public String removeFirstAndLastTwoChars(String input) {
        if (input == null || input.length() < 4) {
            throw new IllegalArgumentException("String length must be at least 4");
        }
        return input.substring(1, input.length() - 1);
    }

    /**
     * 生成一个唯一的ID
     * @return 唯一ID字符串
     */
    public static String generateUniqueId() {
        return UUID.randomUUID().toString();
    }


    /**
     * 用于转义，现成的工具类不让用，重新写一个。
     * @param jsonString
     * @return
     */
    public String unescapeJsonString(String jsonString) {
        // 替换常见的 JSON 转义字符
        jsonString = jsonString.replace("\\\"", "\"");
        jsonString = jsonString.replace("\\\\", "\\");
        jsonString = jsonString.replace("\\/", "/");
        jsonString = jsonString.replace("\\b", "\b");
        jsonString = jsonString.replace("\\f", "\f");
        jsonString = jsonString.replace("\\n", "\n");
        jsonString = jsonString.replace("\\r", "\r");
        jsonString = jsonString.replace("\\t", "\t");

        // 处理 Unicode 转义字符
        Pattern unicodePattern = Pattern.compile("\\\\u([0-9a-fA-F]{4})");
        Matcher matcher = unicodePattern.matcher(jsonString);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String unicodeChar = String.valueOf((char) Integer.parseInt(matcher.group(1), 16));
            matcher.appendReplacement(sb, unicodeChar);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }



}
