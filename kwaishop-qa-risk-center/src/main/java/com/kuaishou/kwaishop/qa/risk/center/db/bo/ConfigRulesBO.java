package com.kuaishou.kwaishop.qa.risk.center.db.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-06
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigRulesBO {
    private Long id;
    private String ruleName;
    private String compareRule;
    private String expressionRule;
    private String configType;
    private String effectiveRegion;
    private String data;
    private String scriptRule;
    private Byte status;
    private String createTime;
    private String updateTime;
    private String creator;
    private String updater;
}
