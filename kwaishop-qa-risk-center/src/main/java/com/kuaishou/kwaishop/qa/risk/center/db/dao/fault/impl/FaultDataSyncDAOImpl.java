package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.FaultDataSyncDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultDataSyncDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.fault.FaultDataSyncMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.fault.FaultDataSyncQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultDataSyncBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultDataSyncStatusEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-15
 */
@Repository
public class FaultDataSyncDAOImpl extends BaseDAOImpl<FaultDataSyncDO, FaultDataSyncQueryCondition> implements FaultDataSyncDAO {

    @Autowired
    private FaultDataSyncMapper faultDataSyncMapper;

    @Override
    protected void fillQueryCondition(FaultDataSyncQueryCondition condition,
            QueryWrapper<FaultDataSyncDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getKsn())) {
            queryWrapper.and(q -> q.eq("ksn", condition.getKsn()));
        }
        if (condition.getCenterId() != null && condition.getCenterId() > 0) {
            queryWrapper.and(q -> q.eq("center_id", condition.getCenterId()));
        }
        if (condition.getTeamId() != null && condition.getTeamId() > 0) {
            queryWrapper.and(q -> q.eq("team_id", condition.getTeamId()));
        }
        if (condition.getStatus() != null && FaultDataSyncStatusEnum.of(condition.getStatus()) != null) {
            queryWrapper.and(q -> q.eq("status", condition.getStatus()));
        }
    }

    @Override
    protected BaseMapper<FaultDataSyncDO> getMapper() {
        return faultDataSyncMapper;
    }

    @Override
    public List<FaultDataSyncDO> queryFaultDataSyncList(FaultDataSyncBO faultCaseBO) {
        FaultDataSyncQueryCondition condition = FaultDataSyncQueryCondition.builder()
                .id(faultCaseBO.getId())
                .centerId(faultCaseBO.getCenterId())
                .teamId(faultCaseBO.getTeamId())
                .ksn(faultCaseBO.getKsn())
                .status(faultCaseBO.getStatus())
                .orderByCreateTimeDesc(true)
                .build();
        return queryList(condition);
    }

    @Override
    public PageBO<FaultDataSyncDO> queryPageFaultDataSyncList(FaultDataSyncBO faultCaseBO) {
        FaultDataSyncQueryCondition condition = FaultDataSyncQueryCondition.builder()
                .id(faultCaseBO.getId())
                .centerId(faultCaseBO.getCenterId())
                .teamId(faultCaseBO.getTeamId())
                .ksn(faultCaseBO.getKsn())
                .status(faultCaseBO.getStatus())
                .pageNo(faultCaseBO.getPageNo())
                .pageSize(faultCaseBO.getPageSize())
                .orderByCreateTimeDesc(true)
                .build();
        return queryPageList(condition);
    }

    @Override
    public FaultDataSyncDO queryByUnique(Long centerId, Long teamId, String ksn) {
        FaultDataSyncQueryCondition condition = FaultDataSyncQueryCondition.builder()
                .centerId(centerId)
                .teamId(teamId)
                .ksn(ksn)
                .build();
        return queryOne(condition);
    }
}
