package com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.service;

import java.util.Map;

import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.DPMApprovalParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.vo.ApprovalEntityDataVO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm.StartApprovalRequest;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2025/1/20 14:40
 * @注释
 */
public interface ApprovalService {

    String getCode();

    ApprovalEntityDataVO approvalForm(StartApprovalRequest request, String code, DPMApprovalParam param);

    void execute(Map<String, Object> variables);

}
