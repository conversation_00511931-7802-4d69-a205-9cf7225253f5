package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.kess.service.RpcMetaInfoQueryService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.kess.KrpcKessDomainServiceGrpc.KessDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.kess.QueryMethodListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.kess.QueryMethodListResponse;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class KessDomainServiceImpl extends KessDomainServiceImplBaseV2 {

    @Autowired
    private RpcMetaInfoQueryService rpcMetaInfoQueryService;

    @Override
    public QueryMethodListResponse queryMethodList(QueryMethodListRequest request) {
        log.error("[KessDomainServiceImpl] queryMethodList , req: {} ", protoToJsonString(request));
        try {

            return QueryMethodListResponse.newBuilder()
                    .addAllData(rpcMetaInfoQueryService.queryMethodList(request.getKessName()))
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();

        } catch (BizException e) {
            log.error("[KessDomainServiceImpl] queryMethodList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryMethodListResponse
                    .newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();

        } catch (Exception e) {
            log.error("[KessDomainServiceImpl] queryMethodList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryMethodListResponse
                    .newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }
}
