package com.kuaishou.kwaishop.qa.risk.center.db.query.feature;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FundsRiskFeatureQueryCondition extends BaseQueryCondition {
    private String department;
    private String businessDomain;
    private String featureId;
    private String featureName;
    private String status;
    private String riskPassStatus;
    private String teamWorker;
    private Integer isRisk;
    private Integer testType;
    private Long startTime;
    private Long endTime;
    private boolean isFromPlatformTask;
    private boolean isQueryOrderByCreateTime;
    public boolean isFromPlatformTask() {
        return isFromPlatformTask;
    }

    public void setFromPlatformTask(boolean fromPlatformTask) {
        isFromPlatformTask = fromPlatformTask;
    }

    public boolean isQueryOrderByCreateTime() {
        return isQueryOrderByCreateTime;
    }

    public void setQueryOrderByCreateTime(boolean queryOrderByCreateTime) {
        isQueryOrderByCreateTime = queryOrderByCreateTime;
    }

}
