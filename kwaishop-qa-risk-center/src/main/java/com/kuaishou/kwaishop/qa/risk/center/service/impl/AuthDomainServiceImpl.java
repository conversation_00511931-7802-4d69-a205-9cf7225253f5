package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.auth.biz.AuthBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.auth.AuthDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.auth.AuthRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.auth.AuthResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.auth.KrpcAuthDomainServiceGrpc.AuthDomainServiceImplBaseV2;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-20
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class AuthDomainServiceImpl extends AuthDomainServiceImplBaseV2 {

    @Autowired
    private AuthBizService authBizService;

    @Override
    public AuthResponse checkAuth(AuthRequest request) {
        log.info("[AuthServiceImpl] checkAuth request: {}", protoToJsonString(request));
        try {
            AuthDTO res = authBizService.checkAuth(request);
            return AuthResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(res)
                    .build();
        } catch (BizException e) {
            log.error("[AuthServiceImpl] checkAuth bizError, req: {}, exception: ", protoToJsonString(request), e);
            return AuthResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AuthServiceImpl] checkAuth error, req: {}, exception: ", protoToJsonString(request), e);
            return AuthResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }
}
