package com.kuaishou.kwaishop.qa.risk.center.service.impl;


import static com.google.common.base.Preconditions.checkState;
import static com.kuaishou.blobstore.common.utils.StringUtils.isNotBlank;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.config.constants.ApplicationConstants;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.enums.RiskMeasureEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.service.RiskMeasureFactory;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.GenerateMeasureScoreRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.GenerateMeasureScoreResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.GetMeasureScoreRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.GetMeasureScoreResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KrpcRiskMeasureServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.RiskScore;

import groovy.util.logging.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024/3/21 21:03
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class KspayRiskMeasureServiceImpl extends KrpcRiskMeasureServiceGrpc.RiskMeasureServiceImplBaseV2 {
    @Autowired
    private RiskMeasureFactory riskMeasureFactory;

    @Override
    public GetMeasureScoreResponse getMeasureScore(GetMeasureScoreRequest request) {

        GetMeasureScoreResponse.Builder builder = GetMeasureScoreResponse.newBuilder();
        try {
            checkParams(request);
            List<RiskScore> scoreList = riskMeasureFactory.getMeasureModel(RiskMeasureEnum.of(request.getModel()))
                    .getScoreList(request);
            // 重试一次,增量模型不重试
            if (scoreList == null && RiskMeasureEnum.of(request.getModel()) != RiskMeasureEnum.INCREMENT_MEASURE) {
                scoreList = riskMeasureFactory.getMeasureModel(RiskMeasureEnum.of(request.getModel()))
                        .getScoreList(request);
            }
            return builder.setCode(ApplicationConstants.SUCCESS)
                    .setMsg(ApplicationConstants.SUCCESS)
                    .addAllRiskScoreList(scoreList)
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            return builder.setCode(ApplicationConstants.FAILED)
                    .setMsg(e.getMessage())
                    .addAllRiskScoreList(new ArrayList<>())
                    .build();
        }
    }

    @Override
    public GenerateMeasureScoreResponse generateMeasureScore(GenerateMeasureScoreRequest request) {
        GenerateMeasureScoreResponse.Builder builder = GenerateMeasureScoreResponse.newBuilder();
        try {
            checkState(isNotBlank(request.getModel()), "模型名为空");
            riskMeasureFactory.getMeasureModel(RiskMeasureEnum.of(request.getModel()))
                    .riskScoreCalculator();
        } catch (Exception e) {
            e.printStackTrace();
            return builder.setCode(ApplicationConstants.FAILED)
                    .setMsg(e.getMessage())
                    .build();
        }
        return builder.setCode(ApplicationConstants.SUCCESS)
                .setMsg(ApplicationConstants.SUCCESS)
                .build();
    }

    public void checkParams(GetMeasureScoreRequest request) {
        checkState(isNotBlank(request.getModel()), "模型名为空");
        if (RiskMeasureEnum.of(request.getModel()) == RiskMeasureEnum.INCREMENT_MEASURE) {
            return;
        }
        checkState(isNotBlank(request.getBusiness()), "方向为空");
    }
}
