package com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.Sample;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Sample.TianheSampleCaseDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-18
 */
@Mapper
@DataSourceRouting("kwaishopQaRiskCenter")
public interface TianheSampleCaseMapper extends BaseMapper<TianheSampleCaseDO> {

    @Select("SELECT * FROM tianhe_risk_sample_config"
            + "WHERE JSON_EXTRACT(ext, '$.onlineUrl') = #{onlineUrl}")
    List<TianheSampleCaseDO> selectByOnlineUrl(@Param("onlineUrl") String onlineUrl);


    @Insert("INSERT INTO tianhe_risk_sample_config(name,status,create_time,update_time,deleted,"
            + "creator,operators,domain_code,platform_source,app_key,page_code,modifier,ext,"
            + "prt_url,online_url,action_set)"
            + "VALUES(#{name},#{status},#{createTime},#{updateTime},#{deleted},#{creator},"
            + "#{operators},#{domainCode},#{platformSource},#{appKey},#{pageCode},#{modifier},"
            + "#{ext},#{prtUrl},#{onlineUrl},#{actionSet})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertSample(TianheSampleCaseDO sampleCaseDO);
}