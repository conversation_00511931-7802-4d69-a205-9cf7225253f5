package com.kuaishou.kwaishop.qa.risk.center.db.dao.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.qa.risk.center.common.exception.CommonErrorCode.DATA_QUERY_ERROR;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.kuaishou.infra.framework.datasource.KsMasterVisitedManager;
import com.kuaishou.kwaishop.qa.risk.center.common.constants.SymbolConstants;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.KspayPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.KspayRiskFeatureBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.KspayBaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-15
 */
@Slf4j
public abstract class KspayBaseDAOImpl<T extends KspayBaseDO, Q extends BaseQueryCondition> {

    /**
     * 根据主键ID查询
     *
     * @param id 主键ID
     * @return DO
     */
    public T queryById(Long id) {
        if (id == null) {
            return null;
        }

        return getMapper().selectById(id);
    }
    /**
     * 查询一条记录
     *
     * @param condition 查询条件
     * @param needCheckMany 是否需要检查多条的情况，如果为true多条时会抛异常
     * @return 单条记录
     */
    protected T queryOne(Q condition, boolean needCheckMany) {
        List<T> list = queryList(condition);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        if (needCheckMany && list.size() > 1) {
            throw new BizException(DATA_QUERY_ERROR,
                    "Expected one result (or null) to be returned by queryOne()," + " but found:" + list.size());
        }
        return list.get(0);
    }

    protected T queryOne(Q condition) {
        return queryOne(condition, false);
    }

    /**
     * 查询多条记录
     *
     * @param condition 查询条件
     * @return
     */
    protected List<T> queryList(Q condition) {
        if (condition == null) {
            return Lists.newArrayList();
        }
        QueryWrapper<T> queryWrapper = convertToQueryWrapper(condition);
        return getMapper().selectList(queryWrapper);
    }

    protected List<T> queryListDescByUpdateTime(Q condition) {
        if (condition == null) {
            return Lists.newArrayList();
        }
        QueryWrapper<T> queryWrapper = convertToQueryWrapperDescByUpdateTime(condition);
        return getMapper().selectList(queryWrapper);
    }

    // 修改queryList方法以支持分页
    protected List<T> queryPageListForKspay(Q condition) {
        if (condition == null) {
            return Lists.newArrayList();
        }
        QueryWrapper<T> queryWrapper = convertToQueryWrapper(condition);
        Page<T> page = new Page<>(condition.getPageNo(), condition.getPageSize());
        return getMapper().selectPage(page, queryWrapper).getRecords();
    }

    protected List<T> queryListWithCondition(Q condition) {
        if (condition == null) {
            return getMapper().selectList(null);
        }
        QueryWrapper<T> queryWrapper = convertToQueryWrapper(condition);
        return getMapper().selectList(queryWrapper);
    }

    /**
     * 统计满足指定条件的记录数
     */
    protected Integer count(Q condition) {
        if (condition == null) {
            return 0;
        }
        QueryWrapper<T> queryWrapper = convertToQueryWrapper(condition);
        return getMapper().selectCount(queryWrapper).intValue();
    }

    /**
     * 分页查询，页码默认从1开始
     */
    protected KspayPageBO<T> queryPageList(Q condition) {
        if (condition == null || condition.getPageSize() <= 0) {
            return new KspayPageBO<>(0, 0, 0L, Lists.newArrayList());
        }
        log.info("-----condition is:{} ", toJSON(condition));
        // 对页码进行容错处理
        int pageNo = condition.getPageNo() == null ? 1 : Math.max(condition.getPageNo(), 1);
        QueryWrapper<T> queryWrapper = convertToLikeQueryWrapper(condition);
        log.info("-----queryWrapper is:{} ", toJSON(queryWrapper));
        Page<T> page = new Page<>(pageNo, condition.getPageSize());
        page = getMapper().selectPage(page, queryWrapper);
        return new KspayPageBO<>(pageNo, condition.getPageSize(),
                page.getTotal(), page.getRecords());
    }

    protected KspayPageBO<T> queryPageListDescByUpdateTime(Q condition) {
        if (condition == null || condition.getPageSize() <= 0) {
            return new KspayPageBO<>(0, 0, 0L, Lists.newArrayList());
        }
        log.info("-----condition is:{} ", toJSON(condition));
        // 对页码进行容错处理
        int pageNo = condition.getPageNo() == null ? 1 : Math.max(condition.getPageNo(), 1);
        QueryWrapper<T> queryWrapper = convertToLikeQueryWrapperDescByUpdateTime(condition);
        log.info("-----queryWrapper is:{} ", toJSON(queryWrapper));
        Page<T> page = new Page<>(pageNo, condition.getPageSize());
        page = getMapper().selectPage(page, queryWrapper);
        return new KspayPageBO<>(pageNo, condition.getPageSize(),
                page.getTotal(), page.getRecords());
    }

    protected KspayRiskFeatureBO<T> queryRiskPageListByUpdateTime(Q condition) {
        if (condition == null || condition.getPageSize() <= 0) {
            return new KspayRiskFeatureBO<>(0, 0, 0, Lists.newArrayList());
        }
        log.info("-----condition is:{} ", toJSON(condition));
        // 对页码进行容错处理
        int pageNo = condition.getPageNo() == null ? 1 : Math.max(condition.getPageNo(), 1);
        QueryWrapper<T> queryWrapper = convertToLikeQueryWrapperDescByUpdateTime(condition);
        log.info("-----queryWrapper is:{} ", toJSON(queryWrapper));
        Page<T> page = new Page<>(pageNo, condition.getPageSize());
        page = getMapper().selectPage(page, queryWrapper);
        return new KspayRiskFeatureBO<>(pageNo, condition.getPageSize(),
                page.getTotal(), page.getRecords());
    }

    /**
     * 新增记录
     *
     * @param domainObject 领域对象
     * @return 影响行数
     */
    public long insert(T domainObject) {
        if (domainObject == null) {
            return 0;
        }
        domainObject.setUpdateTime(System.currentTimeMillis());
        domainObject.setCreateTime(System.currentTimeMillis());
        // 后面接入SSO后这里需要换成从登录上下文中获取
        if (StringUtils.isBlank(domainObject.getCreator())) {
            domainObject.setCreator(SymbolConstants.DEFAULT_USER_SYMBOL);
        }
        getMapper().insert(domainObject);
        return domainObject.getId();
    }

    /**
     * 根据主键ID更新传入的非空对象
     *
     * @param domainObject 更新对象
     * @return 主键ID
     */
    public int updateSelectiveById(T domainObject) {
        if (domainObject == null) {
            return 0;
        }
        if (domainObject.getId() == null) {
            throw new IllegalArgumentException("Id can't null!");
        }
        domainObject.setUpdateTime(System.currentTimeMillis());
        if (StringUtils.isBlank(domainObject.getUpdater())) {
            domainObject.setUpdater(SymbolConstants.DEFAULT_USER_SYMBOL);
        }
        return getMapper().updateById(domainObject);
    }

    /**
     * 根据主键ID更新传入的非空对象-不会更新时间
     *
     * @param domainObject 更新对象
     * @return 主键ID
     */
    public int updateSelectiveByIdNoTime(T domainObject) {
        if (domainObject == null) {
            return 0;
        }
        if (domainObject.getId() == null) {
            throw new IllegalArgumentException("Id can't null!");
        }
        if (StringUtils.isBlank(domainObject.getUpdater())) {
            domainObject.setUpdater(SymbolConstants.DEFAULT_USER_SYMBOL);
        }
        return getMapper().updateById(domainObject);
    }

    /**
     * 逻辑删除
     *
     * @param id 主键
     * @return 影响行数
     */
    public int logicDelete(Long id) {
        if (id == null) {
            return 0;
        }
        return getMapper().deleteById(id);
    }

    /**
     * 将原始查询条件转换为查询wrapper
     *
     * @param condition 原始查询条件
     * @return 查询wrapper
     */
    private QueryWrapper<T> convertToQueryWrapper(Q condition) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        // 子类扩展
        fillQueryCondition(condition, queryWrapper);
        if (condition.getOrderByCreateTimeDesc() != null) {
            queryWrapper.orderByDesc("create_time");
        }
        // 强制读主库
        if (condition.getReadMaster() != null && condition.getReadMaster()) {
            KsMasterVisitedManager.setMasterVisited();
        }
        return queryWrapper;
    }

    private QueryWrapper<T> convertToQueryWrapperDescByUpdateTime(Q condition) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        // 子类扩展
        fillQueryCondition(condition, queryWrapper);

        queryWrapper.orderByDesc("update_time");

        // 强制读主库
        if (condition.getReadMaster() != null && condition.getReadMaster()) {
            KsMasterVisitedManager.setMasterVisited();
        }
        return queryWrapper;
    }


    private QueryWrapper<T> convertToLikeQueryWrapper(Q condition) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        // 子类扩展
        fillLikeQueryCondition(condition, queryWrapper);
        queryWrapper.orderByDesc("create_time");
        // 强制读主库
        if (condition.getReadMaster() != null && condition.getReadMaster()) {
            KsMasterVisitedManager.setMasterVisited();
        }
        return queryWrapper;
    }

    private QueryWrapper<T> convertToLikeQueryWrapperDescByUpdateTime(Q condition) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        // 子类扩展
        fillLikeQueryCondition(condition, queryWrapper);
        queryWrapper.orderByDesc("update_time");
        // 强制读主库
        if (condition.getReadMaster() != null && condition.getReadMaster()) {
            KsMasterVisitedManager.setMasterVisited();
        }
        return queryWrapper;
    }

    protected abstract void fillLikeQueryCondition(Q condition, QueryWrapper<T> queryWrapper);

    private QueryWrapper<T> convertToQueryAllWrapper(Q condition) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        // 子类扩展
        if (condition.getOrderByIdAsc() != null) {
            queryWrapper.orderByAsc("id");
        }
        if (condition.getOrderByIdDesc() != null) {
            queryWrapper.orderByDesc("id");
        }
        if (condition.getOrderByCreateTimeDesc() != null) {
            queryWrapper.orderByDesc("create_time");
        }
        // 强制读主库
        if (condition.getReadMaster() != null && condition.getReadMaster()) {
            KsMasterVisitedManager.setMasterVisited();
        }
        return queryWrapper;
    }

    /**
     * 开放给子类，用于扩展子类特有的查询条件
     *
     * @param condition 查询条件
     * @param queryWrapper 特定条件拼接在该wrapper上即可
     */
    protected abstract void fillQueryCondition(Q condition, QueryWrapper<T> queryWrapper);

    protected abstract BaseMapper<T> getMapper();

}
