package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.ScenarioExecuteDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.ScenarioExecuteBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2023-03-15
 */
public interface ScenarioExecuteDAO {


    long insertScenarioExecute(ScenarioExecuteDO scenarioExecuteDO);

    long updateScenarioExecute(ScenarioExecuteDO scenarioExecuteDO);

    int logicDeleted(Long id, String operator);

    /**
     * 根据name模糊查询
     * @param name
     * @return
     */
    List<ScenarioExecuteDO> queryScenarioExecuteLikeName(String name);

    PageBO<ScenarioExecuteDO> queryPageScenarioExecuteList(ScenarioExecuteBO scenarioExecuteBO);

    List<ScenarioExecuteDO> queryScenarioExecuteList(ScenarioExecuteDO scenarioExecuteDO);
    ScenarioExecuteDO queryScenarioExecuteById(Long id);

}
