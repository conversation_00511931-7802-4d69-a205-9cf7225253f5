package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.utils.ProtobufUtil.protoToJsonString;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.biz.FaultTrafficService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.ApiAutoTestCallRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.ApiAutoTestCallResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.KrpcFaultAutoDomainServiceGrpc.FaultAutoDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.RpcKessCallRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.RpcKessCallResponse;


import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class FaultAutoDomainServiceImpl extends FaultAutoDomainServiceImplBaseV2 {

    @Autowired
    private FaultTrafficService faultTrafficService;

    @Override
    public ApiAutoTestCallResponse apiAutoTestCall(ApiAutoTestCallRequest request) {
        log.info("[FaultAutoDomainServiceImpl] apiAutoTestCall request: {}", protoToJsonString(request));
        try {
            return ApiAutoTestCallResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(faultTrafficService.apiAutoTestCall(
                            request.getCasePath(), request.getLaneId(), request.getExecuteUser()))
                    .build();
        } catch (BizException e) {
            log.error("[FaultAutoDomainServiceImpl] apiAutoTestCall bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ApiAutoTestCallResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultAutoDomainServiceImpl] apiAutoTestCall error, req: {}, exception: ", protoToJsonString(request), e);
            return ApiAutoTestCallResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    @Override
    public RpcKessCallResponse rpcKessCall(RpcKessCallRequest request) {
        log.info("[FaultAutoDomainServiceImpl] rpcKessCall request: {}", protoToJsonString(request));
        try {
            return RpcKessCallResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(faultTrafficService.rpcKessCall(request.getKsn(),
                            request.getFullMethod(), request.getJsonParam(), request.getLaneId(), request.getExecuteUser()))
                    .build();
        } catch (BizException e) {
            log.error("[FaultAutoDomainServiceImpl] rpcKessCall bizError, req: {}, exception: ", protoToJsonString(request), e);
            return RpcKessCallResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultAutoDomainServiceImpl] rpcKessCall error, req: {}, exception: ", protoToJsonString(request), e);
            return RpcKessCallResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


}
