package com.kuaishou.kwaishop.qa.risk.center.domain.combine.convert.impl;

import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.qa.risk.center.domain.combine.convert.FaultCaseCombineConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCombineBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCombineInfo;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCombineProblemInfo;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCombineQueryBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseCombineDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseCombineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseProblemCombineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCombineInfoDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCombineProblemInfoDTO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-13
 */
@Component
public class FaultCaseCombineConvertImpl implements FaultCaseCombineConvert {

    @Override
    public FaultCombineQueryBO buildQueryBO(FaultCaseCombineRequest request) {
        return FaultCombineQueryBO.builder()
                .centerId(request.getCenterId())
                .startTime(request.getStartTime())
                .endTime(request.getEndTime())
                .operator(request.getOperator())
                .build();
    }

    @Override
    public FaultCombineQueryBO buildQueryBO(FaultCaseProblemCombineRequest request) {
        return FaultCombineQueryBO.builder()
                .centerId(request.getCenterId())
                .startTime(request.getStartTime())
                .endTime(request.getEndTime())
                .operator(request.getOperator())
                .build();
    }

    @Override
    public FaultCaseCombineDTO buildCaseCombineDTO(FaultCombineBO faultCombineBO) {
        return FaultCaseCombineDTO.newBuilder()
                .setCaseRecordSum(faultCombineBO.getCaseRecordSum())
                .setCaseSum(faultCombineBO.getCaseSum())
                .addAllCombineInfo(faultCombineBO.getFaultCombineInfos()
                        .stream().map(this::buildCombineInfoDTO)
                        .collect(Collectors.toList()))
                .build();
    }

    @Override
    public FaultCombineInfoDTO buildCombineInfoDTO(FaultCombineInfo faultCombineInfo) {
        return FaultCombineInfoDTO.newBuilder()
                .setCaseCount(faultCombineInfo.getCaseCount())
                .setCaseRecordCount(faultCombineInfo.getCaseRecordCount())
                .setEntityId(faultCombineInfo.getEntityId())
                .setEntityName(faultCombineInfo.getEntityName())
                .setEntityType(faultCombineInfo.getEntityType())
                .setCaseProblemCount(faultCombineInfo.getCaseProblemCount())
                .setCaseProblemSolvedCount(faultCombineInfo.getCaseProblemSolvedCount())
                .setCaseProblemUnsolvedCount(faultCombineInfo.getCaseProblemUnsolvedCount())
                .build();
    }

    @Override
    public FaultCombineProblemInfoDTO buildProblemInfoDTO(FaultCombineProblemInfo faultCombineProblemInfo) {
        return FaultCombineProblemInfoDTO.newBuilder()
                .setProblemCount(faultCombineProblemInfo.getProblemCount())
                .setProblemType(faultCombineProblemInfo.getProblemType())
                .build();
    }

}
