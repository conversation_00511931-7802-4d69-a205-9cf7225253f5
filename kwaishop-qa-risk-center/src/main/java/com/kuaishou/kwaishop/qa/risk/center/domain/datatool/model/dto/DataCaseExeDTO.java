package com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.dto;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.CheckContent;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-14
 */
@Data
@Builder
public class DataCaseExeDTO {

    /**
     * 用例执行id
     */
    private Long caseExeId;

    /**
     * 用例id
     */
    private Long caseId;

    /**
     * 用例执行名称
     */
    private String dataCaseName;


    /**
     * 执行人中文名称
     */
    private String executorName;

    /**
     * 执行状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 执行开始时间
     */
    private Long startTime;

    /**
     * 执行结束时间
     */
    private Long endTime;

    /**
     * 执行耗时
     */
    private String costTime;

    /**
     * 对比类型
     */
    private Integer compareType; // dm vs 表 =1; 表vs表 = 2 dm vs dm = 3 ; 接口 vs dm =4；接口 vs 表 = 5


    /**
     * 用例配置信息-数据源id 列表 以及 数据结果 data_source_ids
     */
    private List<DataSourceResult> dataSourceResults;

    /**
     * 存储校验diff结果   data_query_exe_ids
     */
    private List<DataDiffResult> dataDiffResults;
    //private String  data_query_exe_ids;

//    /**
//     * 源数据集条数
//     */
//    private Long sourceCnt;
//
//    /**
//     * 目标数据集条数
//     */
//    private Long targetCnt;

    /**
     * dataCase表配置信息-checkContent 校验规则
     */
    private CheckContent checkContent;


}
