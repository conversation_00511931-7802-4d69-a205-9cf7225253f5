package com.kuaishou.kwaishop.qa.risk.center.domain.customer.service;

import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.ExportDataBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.ExportParam;

public interface EvaluateService {

    void exportEvaluateDataLogistic(long startTime, long endTime, int pageIndex);

    void exportEvaluateDataPlan(long startTime, long endTime, int pageIndex);

    void upLoadFile(ExportDataBO data, ExportParam exportParam);

    void selectHive(long pDate, long limit, long offset);
}
