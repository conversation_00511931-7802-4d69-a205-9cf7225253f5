package com.kuaishou.kwaishop.qa.risk.center.db.bo;


import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryChainsByIdData {
    private String chainKey;
    private String chainName;
    private String ruleContent;
    private Integer state;
    private String newState;
    private Integer type;
    private Long updateTime;
    private String changeTime;
    private Map<String, String> remark;
    private Long ruleId;
}
