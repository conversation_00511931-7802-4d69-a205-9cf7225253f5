package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.DiffResult;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.ReportService;
import com.kuaishou.kwaishop.qa.risk.center.utils.kim.KimUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-29
 */
@Service
@Slf4j
public class ReportServiceImpl implements ReportService {

    private static final String KIM_ROBOT_KEY = "http://kim-aliyun.internal/kim-api/api/robot/send?key=bbadb740-9bcb-4c3d-930e-a9d0078448c";

    @Override
    public void report(List<DiffResult> diffResults, String diffRate) {
        log.info("report");
        List<String> headers = Arrays.asList("场景名", "请求参数", "预期结果", "实际结果", "diff原因");

        String message = generateMarkdownTable(headers, covertDiffResultToRows(diffResults), diffRate);
        //发送报告
        sendKim(message);
    }

    public List<List<String>> covertDiffResultToRows(List<DiffResult> diffResults) {
        List<List<String>> rows = new ArrayList<>();
        diffResults.forEach(diffResult -> {
            List<String> row = new ArrayList<>();
            row.add(diffResult.getSceneKey());
            row.add(diffResult.getParamJson());

            row.add(diffResult.getExpect());
            row.add(diffResult.getActual());

            row.add(diffResult.getReason());
            rows.add(row);
        });
        return rows;
    }


    /**
     * 根据报告模板，生成markdown格式的报告。
     */
    public String generateMarkdownTable(List<String> headers, List<List<String>> rows, String diffRate) {
        StringBuilder markdown = new StringBuilder();

        // 添加标题
        markdown.append("# 策略平台自动化测试报告\n\n");
        markdown.append("\n## 报告结论\n\n");
        markdown.append("**diff率**：").append(diffRate).append("\n");

        // 添加表格标题
        markdown.append("## diff结果\n\n");

        // 添加表格头
        markdown.append("| ");
        for (String header : headers) {
            markdown.append(header).append(" | ");
        }
        markdown.append("\n");

        // 添加表格头分隔符
        markdown.append("| ");
        for (int i = 0; i < headers.size(); i++) {
            markdown.append("----- | ");
        }
        markdown.append("\n");

        // 添加表格内容
        for (List<String> row : rows) {
            markdown.append("| ");
            for (String cell : row) {
                markdown.append(cell != null ? cell : "null").append(" | ");
            }
            markdown.append("\n");
        }

        return markdown.toString();
    }


    /**
     * 发送kim机器人
     * @param message
     */
    public void sendKim(String message) {
        try {
            KimUtils.sendMarkdownGroupBotMessage(KIM_ROBOT_KEY, message);
        } catch (Exception e) {
            log.error("sendKim error", e);
        }
        log.info("sendKim success");
    }
}
