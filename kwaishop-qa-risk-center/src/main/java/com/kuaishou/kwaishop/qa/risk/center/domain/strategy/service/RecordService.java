package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service;

import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordStartEndPDate;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.PageResult;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecodeDetailClickHouseDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowRecordBo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordAddRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordDetailListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordVoResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetAllInstantsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.InstantInfo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-19
 */

public interface RecordService {

    FlowRecordStartEndPDate getFlowRecordId(Long id);

    /**
     * 记录创建
     */
    void flowRecordAdd(FlowRecordAddRequest request);

    /**
     * 记录主列表查询
     */
    PageResult<StrategyFlowRecordBo> flowRecordList(FlowRecordListRequest request);

    FlowRecordVoResponse flowRecordDetail(Long id);

    /**
     * 记录创建，查询hive并发送kafka到ck
     */
    void recordTaskHandler(Long id);

    /**
     * 记录明细详情，查询ck
     */
    PageResult<RecodeDetailClickHouseDto> flowRecordDetailList(FlowRecordDetailListRequest request);

    RecodeDetailClickHouseDto getRecodeDetail(String id, Long flowRecordId);

    void getRecordDetailInfo(String id, String pDate, Long flowRecordId);

    // 获取实例
    Iterable<InstantInfo> getAllInstants(GetAllInstantsRequest request);

    void testOneService(String pdate);

    void setRedis(String key, String value);

    String getRedis(String key);

    String getFlowRecordAnalysisResult(Long id);

    String getFlowRecordAnalysisResultWithMd(Long id);

    void recordFinalMessage(Long id);
}
