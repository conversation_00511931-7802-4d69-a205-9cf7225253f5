package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.dao;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper.StrategyRecordListMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyRecordListDO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.QueryPageRecordTaskRequest;

@Repository
public class TaskDAOImpl {

    @Autowired
    private StrategyRecordListMapper strategyRecordListMapper;

    public Page<StrategyRecordListDO> queryPage(QueryPageRecordTaskRequest request) {
        QueryWrapper<StrategyRecordListDO> queryWrapper = new QueryWrapper<>();

        if (!request.getSceneKey().isEmpty()) {
            queryWrapper.eq("scene_name", request.getSceneKey());
        }
        if (!request.getCreator().isEmpty()) {
            queryWrapper.eq("creator", request.getCreator());
        }
        if (!request.getRecordPartition().isEmpty()) {
            queryWrapper.eq("record_partition", request.getRecordPartition());
        }
        if (request.getStatus() != 0) {
            queryWrapper.eq("status", request.getStatus());
        }
        if (request.getStartTime() != 0L) {
            queryWrapper.ge("update_time", request.getStartTime());
        }
        if (request.getEndTime() != 0L) {
            queryWrapper.le("update_time", request.getEndTime());
        }
        queryWrapper.orderByDesc("update_time");

        Page<StrategyRecordListDO> page = new Page<>(request.getPageNo(), request.getPageSize());
        return strategyRecordListMapper.selectPage(page, queryWrapper);
    }

    public int updateStatus(String taskId, int status) {
        return strategyRecordListMapper.updateStatus(taskId, status);
    }

    public String queryTaskSql(String taskId) {
        String res = strategyRecordListMapper.queryTaskSql(taskId);
        return res;
    }

    public String queryIdByTaskId(String taskId) {
        return strategyRecordListMapper.queryIdByTaskId(taskId);
    }

    public void updateConclusion(String taskId, String diffValue) {
        strategyRecordListMapper.updateConclusion(taskId, diffValue);
    }
}