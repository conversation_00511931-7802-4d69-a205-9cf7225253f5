package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import com.kuaishou.kwaishop.qa.risk.center.db.query.detail.DetailBaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StrategyTrafficRecorQueryCondition extends DetailBaseQueryCondition {
    private String taskId;

    private String sceneKey;

    private Integer diffConclusion;
}