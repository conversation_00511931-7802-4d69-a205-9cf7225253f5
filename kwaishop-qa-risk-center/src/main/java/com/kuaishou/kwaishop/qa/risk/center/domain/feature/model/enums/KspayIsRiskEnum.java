package com.kuaishou.kwaishop.qa.risk.center.domain.feature.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

public enum KspayIsRiskEnum {
    UNKNOWN(0, "未知状态"),
    HAS_RISK(1, "有资损"),
    NO_RISK(0, "无资损"),
    ;

    private final Integer code;

    private final String desc;

    KspayIsRiskEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static KspayIsRiskEnum of(Integer code) {
        for (KspayIsRiskEnum kspayIsRiskEnum: values()) {
            if (kspayIsRiskEnum.getCode().equals(code) && kspayIsRiskEnum.getCode() > 0) {
                return kspayIsRiskEnum;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo() {
        List<EnumInfo> res = new ArrayList<>();
        for (KspayIsRiskEnum typeEnum: values()) {
            if (typeEnum.getCode() > 0) {
                EnumInfo enumInfo = EnumInfo.newBuilder()
                        .setValue(typeEnum.getCode())
                        .setDesc(typeEnum.getDesc())
                        .build();
                res.add(enumInfo);
            }
        }
        return res;
    }
}
