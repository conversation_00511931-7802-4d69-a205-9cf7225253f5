package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz.impl.KspayRiskFeatureBizServiceImpl.convertToFundsRiskFeatureRequestDO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kspay.util.GsonUtils;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.config.constants.ApplicationConstants;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.ChainKeyBO;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.QueryCheckBO;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.RiskFieldBO;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.RiskInfoBO;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.RiskInfoQueryBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.FundsRiskFeatureBranchDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.impl.FundsRiskFeatureDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.impl.RiskFeatureDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureBranchDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureRequestDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundsRiskFeatureQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz.KspayCheckInfoResourceService;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz.KspayRiskFeatureBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.convert.KspayRiskFeatureConvert;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.ChainDetails;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.CheckChainsList;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.FeatureStatisticInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.GetRiskEntryAndFieldsStatisticRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.GetRiskEntryAndFieldsStatisticResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KdevAccuracyMsgSendRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KdevAccuracyMsgSendResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KrpcKspayRiskFeatureDetailServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayAuditRiskEventResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayBranchAndViewIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayQueryCheckChainsInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayQueryCheckInfoListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayQueryCheckInfoResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayQueryRiskByRepoAndBranchResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayQueryRiskByRepoAndBranchResponseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRelateRiskEventRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRelateRiskEventResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetail;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetailSimple;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetailSimpleV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureViewParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspaySimpleRelateRiskEventResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.MarkIfChainsValid;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.MarkIfChainsValidResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.PageKspayRiskFeatureListDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.PageQueryRiskFeatureStatisticRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryCheckChainsById;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryCheckChainsByIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryCheckChainsStatisticRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryCheckChainsStatisticResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryPageRiskFeatureListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryPageRiskFeatureListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryRiskFeatureDetailByIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryRiskFeatureDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryRiskFeatureDetailResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.RiskFeatureStatisticResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.UpdateFeatureViewBranchResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.UpdateFeatureViewResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayBranchRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayBranchRequestV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayRiskDetailParam;
import com.kuaishou.kwaishop.qa.risk.center.ral.client.QueryCheckAndRulesClient;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class KspayRiskFeatureServiceImpl extends KrpcKspayRiskFeatureDetailServiceGrpc.KspayRiskFeatureDetailServiceImplBaseV2 {

    @Autowired
    private KspayRiskFeatureBizService kspayRiskFeatureBizService;
    @Autowired
    private KspayCheckInfoResourceService kspayCheckInfoResourceService;
    @Autowired
    private FundsRiskFeatureBranchDAO fundsRiskFeatureBranchDAO;
    @Autowired
    private FundsRiskFeatureDAOImpl fundsRiskFeatureDaoImpl;
    @Autowired
    private KspayRiskFeatureConvert kspayRiskFeatureConvert;
    @Autowired
    private RiskFeatureDAO riskFeatureDAO;

    private final NamedParameterJdbcTemplate jdbcTemplate;

    public KspayRiskFeatureServiceImpl(NamedParameterJdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }
    private static final int OFFSET = 0;
    private static final int LIMIT = 1000000;
    private static final int DEFAULT_PAGE_SIZE = 1000000;

    @Override
    public QueryPageRiskFeatureListResponse kspayQueryPageRiskFeatureList(QueryPageRiskFeatureListRequest request) {
        log.info("[KspayRiskFeatureServiceImpl] queryPageRiskFeatureList request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            PageKspayRiskFeatureListDTO pageKspayRiskFeatureListDTO = kspayRiskFeatureBizService.queryPageRiskFeatureList(request);
            return QueryPageRiskFeatureListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(pageKspayRiskFeatureListDTO)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] queryPageRiskFeatureList bizError, exception: ", e);
            return QueryPageRiskFeatureListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] queryPageRiskFeatureList error, exception: ", e);
            return QueryPageRiskFeatureListResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryRiskFeatureDetailResponse kspayQueryRiskFeatureDetail(QueryRiskFeatureDetailRequest request) {
        log.info("[KspayRiskFeatureServiceImpl] queryRiskFeatureDetail request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            List<KspayRiskFeatureDetail> response =  kspayRiskFeatureBizService.queryRiskFeatureDetail(request);
            return QueryRiskFeatureDetailResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(response)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] queryRiskFeatureDetail bizError, exception: ", e);
            return QueryRiskFeatureDetailResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] queryRiskFeatureDetail error, exception: ", e);
            return QueryRiskFeatureDetailResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryRiskFeatureDetailResponse kspayQueryRiskFeatureDetailById(QueryRiskFeatureDetailByIdRequest request) {
        log.info("[KspayRiskFeatureServiceImpl] kspayQueryRiskFeatureDetailById request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            KspayRiskFeatureDetail response =  kspayRiskFeatureBizService.queryRiskFeatureDetailById(request);
            return QueryRiskFeatureDetailResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addData(response)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayQueryRiskFeatureDetailById bizError, exception: ", e);
            return QueryRiskFeatureDetailResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayQueryRiskFeatureDetailById error, exception: ", e);
            return QueryRiskFeatureDetailResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryRiskFeatureDetailResponse kspayQueryRiskByRepoAndBranchAndViewId(KspayBranchAndViewIdRequest request) {
        log.info("[KspayRiskFeatureServiceImpl] queryRiskByRepoAndBranchAndViewId request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            KspayRiskFeatureDetail response = kspayRiskFeatureBizService.queryRiskByRepoAndBranchAndViewId(request);
            return QueryRiskFeatureDetailResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg(ErrorCode.BasicErrorCode.SUCCESS.getMessage())
                    .addData(response)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] queryRiskByRepoAndBranchAndViewId bizError, exception: ", e);
            return QueryRiskFeatureDetailResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] queryRiskByRepoAndBranchAndViewId error, exception: ", e);
            return QueryRiskFeatureDetailResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateFeatureViewResponse kspayUpdateFeatureView(KspayRiskFeatureViewParam request) {
        log.info("[KspayRiskFeatureServiceImpl] updateRiskFeatureView request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayRiskFeatureBizService.updateRiskFeatureView(request);
            return UpdateFeatureViewResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] updateRiskFeatureView bizError, exception: ", e);
            return UpdateFeatureViewResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] updateRiskFeatureView error, exception: ", e);
            return UpdateFeatureViewResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    /**
     * 仅提供给platform api调用
     * */
    @Override
    public UpdateFeatureViewResponse kspayUpdateFeatureViewTestType(KspayRiskFeatureViewParam request) {
        log.info("[KspayRiskFeatureServiceImpl] kspayUpdateFeatureViewTestType request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayRiskFeatureBizService.updateRiskFeatureViewTestType(request);
            return UpdateFeatureViewResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayUpdateFeatureViewTestType bizError, exception: ", e);
            return UpdateFeatureViewResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayUpdateFeatureViewTestType error, exception: ", e);
            return UpdateFeatureViewResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateFeatureViewBranchResponse kspayUpdateFeatureViewBranch(KspayRiskFeatureDetail request) {
        log.info("[KspayRiskFeatureServiceImpl] updateRiskFeatureViewBranch request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayRiskFeatureBizService.updateRiskFeatureViewBranch(request);
            return UpdateFeatureViewBranchResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setFeatureId(request.getFeatureId())
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] updateRiskFeatureViewBranch bizError, exception: ", e);
            return UpdateFeatureViewBranchResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] updateRiskFeatureViewBranch error, exception: ", e);
            return UpdateFeatureViewBranchResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateFeatureViewBranchResponse kspayUpdateFeatureViewBranchAllStatus(KspayRiskFeatureDetail request) {
        log.info("[KspayRiskFeatureServiceImpl] kspayUpdateFeatureViewBranchAllStatus request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayRiskFeatureBizService.updateFeaturesViewBranchAllStatus(request);
            return UpdateFeatureViewBranchResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setFeatureId(request.getFeatureId())
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayUpdateFeatureViewBranchAllStatus bizError, exception: ", e);
            return UpdateFeatureViewBranchResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayUpdateFeatureViewBranchAllStatus error, exception: ", e);
            return UpdateFeatureViewBranchResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateFeatureViewBranchResponse kspayUpdateBranchAllStatusByFeatureId(KspayRiskFeatureDetail request) {
        log.info("[KspayRiskFeatureServiceImpl] kspayUpdateBranchAllStatusByFeatureId request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayRiskFeatureBizService.updateBranchAllStatusByFeatureId(request);
            return UpdateFeatureViewBranchResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setFeatureId(request.getFeatureId())
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayUpdateBranchAllStatusByFeatureId bizError, exception: ", e);
            return UpdateFeatureViewBranchResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayUpdateBranchAllStatusByFeatureId error, exception: ", e);
            return UpdateFeatureViewBranchResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayQueryRiskByRepoAndBranchResponse kspayQueryRiskByRepoAndBranchName(KspayBranchRequest request) {
        log.info("[KspayRiskFeatureServiceImpl] queryRiskByRepoAndBranch request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            List<KspayRiskFeatureDetailSimple> response = kspayRiskFeatureBizService.queryRiskByRepoAndBranch(request);
            return KspayQueryRiskByRepoAndBranchResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg(ErrorCode.BasicErrorCode.SUCCESS.getMessage())
                    .addAllData(response)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] queryRiskByRepoAndBranch bizError, exception: ", e);
            return KspayQueryRiskByRepoAndBranchResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] queryRiskByRepoAndBranch error, exception: ", e);
            return KspayQueryRiskByRepoAndBranchResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    public static final int STATUS = 200;

    @Override
    public KspayQueryRiskByRepoAndBranchResponseV2 kspayQueryRiskByRepoAndBranchNameV2(KspayBranchRequestV2 request) {
        log.info("[KspayRiskFeatureServiceImpl] queryRiskByRepoAndBranch request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            List<KspayRiskFeatureDetailSimpleV2>  response = kspayRiskFeatureBizService.queryRiskByRepoAndBranchV2(request);
            return KspayQueryRiskByRepoAndBranchResponseV2.newBuilder()
                    .setStatus(STATUS)
                    .setMessage(ErrorCode.BasicErrorCode.SUCCESS.getMessage())
                    .setData(CollectionUtils.isEmpty(response) ? KspayRiskFeatureDetailSimpleV2.getDefaultInstance() : response.get(0))
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] queryRiskByRepoAndBranch bizError, exception: ", e);
            return KspayQueryRiskByRepoAndBranchResponseV2.newBuilder()
                    .setStatus(e.getCode())
                    .setMessage(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] queryRiskByRepoAndBranch error, exception: ", e);
            return KspayQueryRiskByRepoAndBranchResponseV2.newBuilder()
                    .setStatus(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setMessage(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayRelateRiskEventResponse kspayRelateHistoryRiskEvent(KspayRelateRiskEventRequest request) {
        log.info("[KspayRiskFeatureServiceImpl] kspayRelateHistoryRiskEvent request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            List<KspayRiskDetailParam> response = kspayCheckInfoResourceService.queryRelateHistoryRiskEvent(request);

            if (CollectionUtils.isEmpty(response)) {
                log.info("[KspayRiskFeatureServiceImpl] No related history risk events found.");
                return KspayRelateRiskEventResponse.newBuilder()
                        .setResult(BaseResultCode.SUCCESS_VALUE)
                        .setErrorMsg(ErrorCode.BasicErrorCode.SUCCESS.getMessage()) // 或者使用一个表示“无结果”的错误码
                        .build(); // 不添加子风险列表
            }

            return KspayRelateRiskEventResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg(ErrorCode.BasicErrorCode.SUCCESS.getMessage())
                    .addAllSubRiskList(response)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayRelateHistoryRiskEvent bizError, exception: ", e);
            return KspayRelateRiskEventResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayRelateHistoryRiskEvent error, exception: ", e);
            return KspayRelateRiskEventResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspaySimpleRelateRiskEventResponse kspaySimpleRelateHistoryRiskEvent(KspayRelateRiskEventRequest request) {
        log.info("[KspayRiskFeatureServiceImpl] KspaySimpleRelateRiskEventResponse request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            String response = kspayCheckInfoResourceService.querySimpleRelateHistoryRiskEvent(request);
            response = StringUtils.defaultIfBlank(response, ""); // 如果response为空，则设置为默认值：空字符串

            return KspaySimpleRelateRiskEventResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg(ErrorCode.BasicErrorCode.SUCCESS.getMessage())
                    .setSubRiskName(response)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] KspaySimpleRelateRiskEventResponse bizError, exception: ", e);
            return KspaySimpleRelateRiskEventResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] KspaySimpleRelateRiskEventResponse error, exception: ", e);
            return KspaySimpleRelateRiskEventResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayAuditRiskEventResponse kspayAuditRiskEvent(KspayRelateRiskEventRequest request) {
        log.info("[KspayRiskFeatureServiceImpl] KspayRelateRiskEventRequest request: {}", ProtobufUtil.protoToJsonString(request));
        try {

            return kspayCheckInfoResourceService.getKspayAuditRiskEvent(request);
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] KspayRelateRiskEventRequest bizError, exception: ", e);
            return KspayAuditRiskEventResponse.newBuilder()
                    .setResult(e.getCode())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] KspayRelateRiskEventRequest error, exception: ", e);
            return KspayAuditRiskEventResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .build();
        }
    }

    @Override
    public GetRiskEntryAndFieldsStatisticResponse getKspayRiskStatistic(GetRiskEntryAndFieldsStatisticRequest request) {
        log.info("[getKspayRiskStatistic] getKspayRiskStatistic request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            return kspayRiskFeatureBizService.getKspayRiskStatistic(request);
        } catch (BizException e) {
            log.error("[getKspayRiskStatistic] getKspayRiskStatistic bizError, exception: ", e);
            return GetRiskEntryAndFieldsStatisticResponse.newBuilder()
                    .setResult(e.getCode())
                    .build();
        } catch (Exception e) {
            log.error("[getKspayRiskStatistic] getKspayRiskStatistic error, exception: ", e);
            return GetRiskEntryAndFieldsStatisticResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .build();
        }
    }

    @Override
    public KdevAccuracyMsgSendResponse kspayKdevAccuracyMsgSend(KdevAccuracyMsgSendRequest request) {
        log.info("[KspayRiskFeatureServiceImpl] kdevAccuracyMsgSend request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayRiskFeatureBizService.kdevAccuracyMsgSend(request);
            return KdevAccuracyMsgSendResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg(ErrorCode.BasicErrorCode.SUCCESS.getMessage())
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] kdevAccuracyMsgSend bizError, exception: ", e);
            return KdevAccuracyMsgSendResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] kdevAccuracyMsgSend error, exception: ", e);
            return KdevAccuracyMsgSendResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public RiskFeatureStatisticResponse kspayPageQueryRiskFeatureStatistic(PageQueryRiskFeatureStatisticRequest request) {
        log.info("[KspayRiskFeatureServiceImpl] kspayPageQueryRiskFeatureStatistic request: {}",
                ProtobufUtil.protoToJsonString(request));
        try {
            List<FeatureStatisticInfo> featureStatisticInfos = kspayRiskFeatureBizService.kspayPageQueryRiskFeatureStatistic(request);

            // 获取总记录数
            FundsRiskFeatureRequestDO fundsRiskFeatureRequestDO = convertToFundsRiskFeatureRequestDO(request);
            Map<String, Object> fundsRiskFeatures = fundsRiskFeatureDaoImpl.getFundsRiskFeaturesByPage(fundsRiskFeatureRequestDO);
            Long totalCount = (Long) fundsRiskFeatures.get("totalCount");

            return RiskFeatureStatisticResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrMsg(ErrorCode.BasicErrorCode.SUCCESS.getMessage())
                    .addAllFeatureStatisticInfo(featureStatisticInfos)
                    .setTotalCount(totalCount)
                    .setPageNo(request.getPageNo())
                    .setPageSize(request.getPageSize())
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayPageQueryRiskFeatureStatistic bizError, exception: ", e);
            return RiskFeatureStatisticResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayPageQueryRiskFeatureStatistic error, exception: ", e);
            return RiskFeatureStatisticResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryCheckChainsByIdResponse kspayQueryCheckChainsById(QueryCheckChainsById request) {
        log.info("[KspayRiskFeatureServiceImpl] kspayQueryCheckChainsById request: {}",
                ProtobufUtil.protoToJsonString(request));
        try {
            return kspayRiskFeatureBizService.queryChainsById(request);
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayQueryCheckChainsById bizError, exception: ", e);
            return QueryCheckChainsByIdResponse.newBuilder()
                    .setStatus(e.getCode())
                    .setMessage(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayQueryCheckChainsById error, exception: ", e);
            return QueryCheckChainsByIdResponse.newBuilder()
                    .setStatus(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setMessage(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    @Override
    public MarkIfChainsValidResponse kspayMarkIfChainsValid(MarkIfChainsValid request) {
        log.info("[KspayRiskFeatureServiceImpl] kspayMarkIfChainsValid request: {}",
                ProtobufUtil.protoToJsonString(request));
        try {
            return kspayRiskFeatureBizService.markIfChainsValid(request);
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayMarkIfChainsValid bizError, exception: ", e);
            return MarkIfChainsValidResponse.newBuilder()
                    .setStatus(e.getCode())
                    .setMessage(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayMarkIfChainsValid error, exception: ", e);
            return MarkIfChainsValidResponse.newBuilder()
                    .setStatus(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setMessage(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    private final ExecutorService executorService = Executors.newFixedThreadPool(10); // 创建固定大小的线程池


    // 获取区间时间内推荐规则数据
    @Override
    public QueryCheckChainsStatisticResponse kspayQueryCheckChainsStatistic(QueryCheckChainsStatisticRequest request) {
        log.info("[KspayRiskFeatureServiceImpl] queryPageRiskFeatureList request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            FundsRiskFeatureQueryCondition queryCondition = FundsRiskFeatureQueryCondition.builder()
                    .department(StringUtils.isNotBlank(request.getDepartment()) ? request.getDepartment() : "")
                    .status(StringUtils.isNotBlank(request.getStatus()) ? request.getStatus() : "")
                    .isRisk(request.getIsRisk())
                    .startTime(request.getStartTime() != 0 ? request.getStartTime() : 0)
                    .endTime(request.getEndTime() != 0 ? request.getEndTime() : System.currentTimeMillis())
                    .build();

            List<FundsRiskFeatureDO> fundsRiskFeatureDOList = riskFeatureDAO.queryFeatureList(false,
                    queryCondition, OFFSET, LIMIT, DEFAULT_PAGE_SIZE, jdbcTemplate);

            CompletableFuture<List<CheckChainsList>> future = CompletableFuture.supplyAsync(() -> {
                List<CheckChainsList> allCheckChainsLists = new ArrayList<>();

                for (FundsRiskFeatureDO fundsRiskFeatureDO : fundsRiskFeatureDOList) {
                    List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS = fundsRiskFeatureBranchDAO.queryFundRiskFeatureBranch(
                            kspayRiskFeatureConvert.buildFundRiskFeatureBranchQueryCondition(fundsRiskFeatureDO.getFeatureId()));

                    // 构建请求参数
                    List<KspayQueryCheckChainsInfo> checkChainsInfoList = fundsRiskFeatureBranchDOS.stream()
                            .map(branch -> KspayQueryCheckChainsInfo.newBuilder()
                                    .setRepoName(branch.getRepoName())
                                    .setBranchName(branch.getBranchName())
                                    .setFeatureViewId(branch.getFeatureViewId())
                                    .setRiskTableFields(branch.getRiskTableFields())
                                    .build())
                            .collect(Collectors.toList());

                    KspayQueryCheckInfoListRequest queryCheckInfoListRequest = KspayQueryCheckInfoListRequest.newBuilder()
                            .addAllCheckChainsInfo(checkChainsInfoList)
                            .build();

                    // 调用平台服务
                    KspayQueryCheckInfoResponse kspayResponse = kspayQueryCheckInfos(queryCheckInfoListRequest);

                    // 处理响应数据
                    if (kspayResponse != null && kspayResponse.getCheckChainsMapList() != null) {
                        allCheckChainsLists.addAll(kspayResponse.getCheckChainsMapList());
                    }
                }

                // 打印 allCheckChainsLists 的结果
                log.info("[KspayRiskFeatureServiceImpl] 核对规则统计结果: {}", allCheckChainsLists.size());

                // 根据 chain_id 去重
                Set<Long> uniqueChainIds = new HashSet<>();
                List<CheckChainsList> uniqueCheckChainsLists = allCheckChainsLists.stream()
                        .filter(list -> list.getCheckChainsList().stream().anyMatch(chain -> uniqueChainIds.add(chain.getChainId())))
                        .collect(Collectors.toList());

                // 打印去重后的结果
                log.info("[KspayRiskFeatureServiceImpl] 去重后的核对规则统计结果: {}", uniqueCheckChainsLists.size());

                return uniqueCheckChainsLists;
            }, executorService);

            List<CheckChainsList> result = future.join(); // 等待异步任务完成

            return QueryCheckChainsStatisticResponse.newBuilder()
                    .setCode(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(result)
                    .setTotalCount(result.size())
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayQueryCheckChainsStatistic bizError, exception: ", e);
            return QueryCheckChainsStatisticResponse.newBuilder()
                    .setCode(e.getCode())
                    .setMessage(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskFeatureServiceImpl] kspayQueryCheckChainsStatistic error, exception: ", e);
            return QueryCheckChainsStatisticResponse.newBuilder()
                    .setCode(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setMessage(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayQueryCheckInfoResponse kspayQueryCheckInfos(KspayQueryCheckInfoListRequest request) {
        log.info("[KspayRiskFeatureServiceImpl] queryCheckInfos request: {}", ProtobufUtil.protoToJsonString(request));
        List<RiskInfoBO> requestBody = request.getCheckChainsInfoList().stream().map(req ->
                        RiskInfoBO.builder()
                                .warehouse(req.getRepoName())
                                .riskFields(GsonUtils.fromJSON(req.getRiskTableFields(), List.class, RiskFieldBO.class))
                                .build()
                )
                .collect(Collectors.toList());
        List<RiskInfoQueryBO> checkRequest = requestBody.stream().map(body -> RiskInfoQueryBO.builder()
                        .warehouse(body.getWarehouse())
                        .riskFields(body.getRiskFields().stream().map(field -> QueryCheckBO.builder()
                                        .riskElements(Arrays.asList(field.getRiskElements().split(",")))
                                        .tableName(field.getTableName())
                                        .build())
                                .collect(Collectors.toList()))
                        .build())
                .collect(Collectors.toList());
        Map<String, List<ChainKeyBO>> checkChain = QueryCheckAndRulesClient.queryCheckChain(checkRequest);
        if (checkChain == null || checkChain.keySet() == null || checkChain.keySet().size() == 0) {
            log.warn("[KspayRiskFeatureServiceImpl] queryCheckInfos is null");
            return KspayQueryCheckInfoResponse.newBuilder()
                    .setResult(ApplicationConstants.FAILED)
                    .setErrorMsg("查询核对链为空")
                    .build();
        }
        List<CheckChainsList> chainsList = new ArrayList<>();
        //根据request遍历对应的库名，查询库里的rule字段，如果为空，直接规则待确认，如果某个ruleid不存在规则待确认(两层循环 外层request 内层rule)
        request.getCheckChainsInfoList().forEach(req -> {
            String ruleIsValid = fundsRiskFeatureBranchDAO.queryRuleIsValid(req.getRepoName(), req.getBranchName(), req.getFeatureViewId());
            log.info("[KspayRiskFeatureServiceImpl] queryCheckInfos ruleIsValid: {}", ruleIsValid);
            List<ChainKeyBO> chainDetailBOS = checkChain.get(req.getRepoName());
            if (ruleIsValid == null) {
                if (chainDetailBOS != null) {
                    List<ChainDetails> chainDetails = chainDetailBOS.stream().map(chainKeyBO ->
                            ChainDetails.newBuilder()
                                    .setChainId(chainKeyBO.getChainId())
                                    .setChainKey(chainKeyBO.getChainKey())
                                    .setChainName(chainKeyBO.getChainName())
                                    .setChainState(chainKeyBO.getState())
                                    .setRuleIsValid("规则待确认")
                                    .build()
                    ).collect(Collectors.toList());
                    chainsList.add(CheckChainsList.newBuilder()
                            .addAllCheckChains(chainDetails)
                            .setRepoName(req.getRepoName())
                            .build());
                }
            } else {
                Map<Long, Integer> ruleMap = GsonUtils.fromJSON(ruleIsValid, Map.class);
                List<ChainDetails> chainDetails = chainDetailBOS.stream().map(chainKeyBO ->
                        ChainDetails.newBuilder()
                                .setChainId(chainKeyBO.getChainId())
                                .setChainKey(chainKeyBO.getChainKey())
                                .setChainName(chainKeyBO.getChainName())
                                .setChainState(chainKeyBO.getState())
                                .setRuleIsValid(ruleMap.containsKey(chainKeyBO.getChainId()) ? "规则已确认" : "规则待确认")
                                .build()
                ).collect(Collectors.toList());
                chainsList.add(CheckChainsList.newBuilder()
                        .addAllCheckChains(chainDetails)
                        .setRepoName(req.getRepoName())
                        .build());
            }
        });
        log.info("[KspayRiskFeatureServiceImpl] queryCheckInfos chainsList: {}", chainsList);
        return KspayQueryCheckInfoResponse.newBuilder()
                .setResult(ApplicationConstants.SUCCESS)
                .addAllCheckChainsMap(chainsList)
                .build();
    }
}
