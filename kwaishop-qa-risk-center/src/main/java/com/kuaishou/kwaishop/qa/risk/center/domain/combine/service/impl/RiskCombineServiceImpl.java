package com.kuaishou.kwaishop.qa.risk.center.domain.combine.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.enums.CoverTypeEnum.COVER;
import static com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.enums.EffectiveEnum.ON;
import static com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.enums.LevelEnum.HIGH;
import static com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.enums.LevelEnum.LOW;
import static com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.enums.LevelEnum.MEDIUM;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.detail.DetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.RiskDetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.RiskDetailDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.RiskInfoCombineBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.enums.DateTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.service.CombineAbstract;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.service.RiskInfoCombineService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-26
 */
@Service
@Lazy
@Slf4j
public class RiskCombineServiceImpl extends CombineAbstract<RiskDetailDO, RiskInfoCombineBO,
        CombineQueryParam> implements RiskInfoCombineService {

    @Autowired
    private RiskDetailDAO riskDetailDAO;


    @Override
    protected DetailDAO<RiskDetailDO, CombineQueryParam> getDAO() {
        return riskDetailDAO;
    }

    @Override
    protected List<RiskDetailDO> fillFilter(CombineQueryParam queryParam, List<RiskDetailDO> dataList) {
        return dataList;
    }

    @Override
    protected RiskInfoCombineBO buildCombineBO(List<RiskDetailDO> dataList, EntityTypeEnum entityTypeEnum,
            Long id, DateTypeEnum dateTypeEnum) {
        RiskInfoCombineBO res = new RiskInfoCombineBO();
        switch (entityTypeEnum) {
            case TEAM_TYPE:
                EntityDO teamDO = getTeamMaps().get(id);
                res.setId(teamDO.getId());
                res.setEntityType(teamDO.getEntityType());
                res.setName(teamDO.getName());
                break;
            case CENTER_TYPE:
                EntityDO centerDO = getCenterMaps().get(id);
                res.setId(centerDO.getId());
                res.setEntityType(centerDO.getEntityType());
                res.setName(centerDO.getName());
            default:
        }
        long verifyEffectiveCount = 0L;
        long planEffectiveCount = 0L;
        long highLevelCount = 0L;
        long mediumLevelCount = 0L;
        long lowLevelCount = 0L;
        long riskCoverCount = 0L;
        long planCoverCount = 0L;
        long fundExpTotal = 0L;
        if (CollectionUtils.isEmpty(dataList)) {
            res.setVerifyEffectiveCount(verifyEffectiveCount);
            res.setPlanEffectiveCount(planEffectiveCount);
            res.setHighLevelCount(highLevelCount);
            res.setMediumLevelCount(mediumLevelCount);
            res.setLowLevelCount(lowLevelCount);
            res.setRiskCoverCount(riskCoverCount);
            res.setPlanCoverCount(planCoverCount);
            res.setFundExpTotal(fundExpTotal);
            res.setVerifyEffectiveRate(0L);
            res.setPlanEffectiveRate(0L);
            res.setHighLevelRate(0L);
            res.setMediumLevelRate(0L);
            res.setLowLevelRate(0L);
            res.setNewRiskCount(0L);
            res.setRiskCoverRate(0L);
            res.setPlanCoverRate(0L);
            res.setTotal(0L);
            return res;
        }
        long total = dataList.size();
        res.setTotal(total);
        res.setNewRiskCount(getDateSize(dataList, dateTypeEnum));
        for (RiskDetailDO riskDetailDO: dataList) {
            if (riskDetailDO.getVerifyEffective().equals(ON.getCode())) {
                verifyEffectiveCount++;
            }
            if (riskDetailDO.getPlanEffective().equals(ON.getCode())) {
                planEffectiveCount++;
            }
            if (riskDetailDO.getLevel().equals(HIGH.getCode())) {
                highLevelCount++;
            }
            if (riskDetailDO.getLevel().equals(MEDIUM.getCode())) {
                mediumLevelCount++;
            }
            if (riskDetailDO.getLevel().equals(LOW.getCode())) {
                lowLevelCount++;
            }
            if (riskDetailDO.getPlanCover().equals(COVER.getCode())) {
                planCoverCount++;
            }
            if (riskDetailDO.getRiskCover().equals(COVER.getCode())) {
                riskCoverCount++;
            }
            fundExpTotal = fundExpTotal + riskDetailDO.getFundExp();
        }
        res.setVerifyEffectiveCount(verifyEffectiveCount);
        res.setPlanEffectiveCount(planEffectiveCount);
        res.setHighLevelCount(highLevelCount);
        res.setMediumLevelCount(mediumLevelCount);
        res.setLowLevelCount(lowLevelCount);
        res.setRiskCoverCount(riskCoverCount);
        res.setPlanCoverCount(planCoverCount);
        res.setFundExpTotal(fundExpTotal);
        res.setVerifyEffectiveRate(getRate(verifyEffectiveCount, total));
        res.setPlanEffectiveRate(getRate(planEffectiveCount, total));
        res.setHighLevelRate(getRate(highLevelCount, total));
        res.setMediumLevelRate(getRate(mediumLevelCount, total));
        res.setLowLevelRate(getRate(lowLevelCount, total));
        res.setRiskCoverRate(getRate(riskCoverCount, total));
        res.setPlanCoverRate(getRate(planCoverCount, total));
        return res;
    }

}
