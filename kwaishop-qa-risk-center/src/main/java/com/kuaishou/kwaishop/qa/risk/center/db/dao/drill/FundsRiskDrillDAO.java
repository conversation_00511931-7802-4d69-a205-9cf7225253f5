package com.kuaishou.kwaishop.qa.risk.center.db.dao.drill;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.drill.FundsRiskDrillDO;

public interface FundsRiskDrillDAO {

    long insert(FundsRiskDrillDO fundsRiskDrillDO);

    int updateSelectiveById(FundsRiskDrillDO fundsRiskDrillDO);

    FundsRiskDrillDO queryById(Long id);

    PageBO<FundsRiskDrillDO> queryFundsRiskDrillList(FundsRiskDrillDO fundsRiskDrillDO);
}
