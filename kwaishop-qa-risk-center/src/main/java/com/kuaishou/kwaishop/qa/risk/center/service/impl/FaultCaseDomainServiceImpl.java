package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.biz.FaultCaseBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.BatchUpdateCaseBusinessTagRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.BatchUpdateCaseBusinessTagResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.BatchUpdateCaseTagRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.BatchUpdateCaseTagResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.CreateCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.CreateCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.DeleteCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.DeleteCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.KrpcFaultCaseDomainServiceGrpc.FaultCaseDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.PageFaultCaseDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryPageCaseListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryPageCaseListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateCaseResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-01-05
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class FaultCaseDomainServiceImpl extends FaultCaseDomainServiceImplBaseV2 {

    @Autowired
    private FaultCaseBizService faultCaseBizService;

    @Override
    public CreateCaseResponse createFaultCase(CreateCaseRequest request) {
        log.info("[FaultCaseDomainServiceImpl] createFaultCase request: {}", protoToJsonString(request));
        try {
            faultCaseBizService.createFaultCase(request);
            return CreateCaseResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultCaseDomainServiceImpl] createFaultCase bizError, req: {}, exception: ", protoToJsonString(request), e);
            return CreateCaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultCaseDomainServiceImpl] createFaultCase error, req: {}, exception: ", protoToJsonString(request), e);
            return CreateCaseResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateCaseResponse updateFaultCase(UpdateCaseRequest request) {
        log.info("[FaultCaseDomainServiceImpl] updateFaultCase request: {}", protoToJsonString(request));
        try {
            faultCaseBizService.updateFaultCase(request);
            return UpdateCaseResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultCaseDomainServiceImpl] updateFaultCase bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateCaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultCaseDomainServiceImpl] updateFaultCase error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateCaseResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public BatchUpdateCaseTagResponse batchUpdateCaseTag(BatchUpdateCaseTagRequest request) {
        log.info("[FaultCaseDomainServiceImpl] batchUpdateCaseTag request: {}", protoToJsonString(request));
        try {
            faultCaseBizService.batchUpdateCaseTag(request);
            return BatchUpdateCaseTagResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultCaseDomainServiceImpl] batchUpdateCaseTag bizError, req: {}, exception: ", protoToJsonString(request), e);
            return BatchUpdateCaseTagResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultCaseDomainServiceImpl] batchUpdateCaseTag error, req: {}, exception: ", protoToJsonString(request), e);
            return BatchUpdateCaseTagResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateCaseResponse batchUpdateById(UpdateCaseRequest request) {
        log.info("[FaultCaseDomainServiceImpl] batchUpdateById request: {}", protoToJsonString(request));
        try {
            faultCaseBizService.batchUpdateById(request);
            return UpdateCaseResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultCaseDomainServiceImpl] batchUpdateById bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateCaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultCaseDomainServiceImpl] batchUpdateById error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateCaseResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public BatchUpdateCaseBusinessTagResponse batchUpdateCaseBusinessTag(BatchUpdateCaseBusinessTagRequest request) {
        log.info("[FaultCaseDomainServiceImpl] batchUpdateCaseBusinessTag request: {}", protoToJsonString(request));
        try {
            faultCaseBizService.batchUpdateBusinessTag(request);
            return BatchUpdateCaseBusinessTagResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultCaseDomainServiceImpl] batchUpdateCaseBusinessTag bizError, req: {}, exception: ", protoToJsonString(request), e);
            return BatchUpdateCaseBusinessTagResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultCaseDomainServiceImpl] batchUpdateCaseBusinessTag error, req: {}, exception: ", protoToJsonString(request), e);
            return BatchUpdateCaseBusinessTagResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public DeleteCaseResponse deleteFaultCase(DeleteCaseRequest request) {
        log.info("[FaultCaseDomainServiceImpl] deleteFaultCase request: {}", protoToJsonString(request));
        try {
            faultCaseBizService.deleteFaultCase(request);
            return DeleteCaseResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultCaseDomainServiceImpl] deleteFaultCase bizError, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteCaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultCaseDomainServiceImpl] deleteFaultCase error, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteCaseResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryPageCaseListResponse queryFaultCasePageList(QueryPageCaseListRequest request) {
        log.info("[FaultCaseDomainServiceImpl] queryFaultCasePageList request: {}", protoToJsonString(request));
        try {
            PageFaultCaseDTO res = faultCaseBizService.queryFaultCasePageList(request);
            return QueryPageCaseListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(res)
                    .build();
        } catch (BizException e) {
            log.error("[FaultCaseDomainServiceImpl] queryFaultCasePageList bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryPageCaseListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultCaseDomainServiceImpl] queryFaultCasePageList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryPageCaseListResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }
}