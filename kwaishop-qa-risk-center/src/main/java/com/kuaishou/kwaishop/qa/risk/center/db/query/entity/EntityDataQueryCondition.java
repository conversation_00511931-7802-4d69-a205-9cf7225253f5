package com.kuaishou.kwaishop.qa.risk.center.db.query.entity;

import java.util.Collection;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-29
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EntityDataQueryCondition extends BaseQueryCondition {

    /**
     * 实体类型
     */
    private Integer entityType;

    /**
     * 实体id
     */
    private Long entityId;

    /**
     * 数据类型
     */
    private Integer dataType;

    /**
     * 日期 20230329
     */
    private String dt;

    /**
     * 实体ids
     */
    private Collection<Long> entityIds;
}
