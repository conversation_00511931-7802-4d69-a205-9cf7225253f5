package com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <zhang<PERSON><EMAIL>>
 * Create on 2022-12-11
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaseExecuteBO {

    private  Long id;

    private Long caseId;

    private String name;

    private String executorName;


    /**
     * 页码
     */
    private Integer pageNo;
    /**
     * 页容量
     */
    private Integer pageSize;


}
