package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.phantomthief.tuple.ThreeTuple;
import com.kuaishou.infra.patronum.traffic.storage.dto.ResultCode;
import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz.KspayGitToDepartmentMappingService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.FirstDepartmentInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.GitToDepartmentMappingParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.GitToDepartmentMappingResult;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.GitToMappingInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KrpcKspayGitToDepartmentMappingServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryGitToMappingRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryGitToMappingResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.SecondDepartmentInfo;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class KspayGitDepartmentMappingServiceImpl extends KrpcKspayGitToDepartmentMappingServiceGrpc.KspayGitToDepartmentMappingServiceImplBaseV2 {
    @Autowired
    private KspayGitToDepartmentMappingService kspayGitToDepartmentMappingService;
    /**
     * 仅提供给platform api调用
     * */
    @Override
    public GitToDepartmentMappingResult kspayInsertOrUpdateGitToDepartmentMapping(GitToDepartmentMappingParam param) {
        log.info("[KspayGitDepartmentMappingServiceImpl] kspayInsertOrUpdateGitToDepartmentMapping request: {}",
                ProtobufUtil.protoToJsonString(param));
        try {
            kspayGitToDepartmentMappingService.insertOrUpdateGitToDepartmentMapping(param);
            return GitToDepartmentMappingResult.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg("成功")
                    .build();
        } catch (BizException e) {
            log.error("[KspayGitDepartmentMappingServiceImpl] "
                    + "kspayInsertOrUpdateGitToDepartmentMapping bizError, exception: ", e);
            return GitToDepartmentMappingResult.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayGitDepartmentMappingServiceImpl] "
                    + "kspayInsertOrUpdateGitToDepartmentMapping error, exception: ", e);
            return GitToDepartmentMappingResult.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    // 仓库部门映射查询接口（调qa-platform api）
    @Override
    public QueryGitToMappingResponse queryGitMappingToDepartment(QueryGitToMappingRequest request) {
        log.info("[KspayGitDepartmentMappingServiceImpl] queryGitMappingToDepartment request: {}",
                ProtobufUtil.protoToJsonString(request));
        try {
//            return kspayGitToDepartmentMappingService.queryGitToMapping(request);  // 请求有问题，先不走这里
            ThreeTuple<List<GitToMappingInfo>, List<FirstDepartmentInfo>, List<SecondDepartmentInfo>> gitToMappings
                    = kspayGitToDepartmentMappingService.queryGitToMappings(request);
            List<GitToMappingInfo> gitToMappingInfoList = gitToMappings.getFirst();
            List<FirstDepartmentInfo> firstDepartmentInfoList = gitToMappings.getSecond();
            List<SecondDepartmentInfo> secondDepartmentInfoList = gitToMappings.getThird();
            return QueryGitToMappingResponse.newBuilder()
                    .setResult(ResultCode.SUCCESS.getCode())
                    .setErrMsg(ResultCode.SUCCESS.getMessage())
                    .addAllGitToMappingInfo(gitToMappingInfoList)
                    .addAllSecondDepartmentInfo(secondDepartmentInfoList)
                    .addAllFirstDepartmentInfo(firstDepartmentInfoList)
                    .setTotalCount(gitToMappingInfoList.size())
                    .build();
        } catch (BizException e) {
            log.error("[KspayGitDepartmentMappingServiceImpl] queryGitMappingToDepartment bizError, exception: ", e);
            return QueryGitToMappingResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayGitDepartmentMappingServiceImpl] queryGitMappingToDepartment error, exception: ", e);
            return QueryGitToMappingResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }
}