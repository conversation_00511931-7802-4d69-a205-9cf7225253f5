package com.kuaishou.kwaishop.qa.risk.center.db.dao;

import java.util.Collection;
import java.util.Map;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.BaseDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-24
 */
public interface BaseDAO<T extends BaseDO> {

    T queryById(Long id);

    Map<Long, T> batchQueryByIds(Collection<Long> ids);

    int updateSelectiveById(T domainObject);

    long insert(T domainObject);

}
