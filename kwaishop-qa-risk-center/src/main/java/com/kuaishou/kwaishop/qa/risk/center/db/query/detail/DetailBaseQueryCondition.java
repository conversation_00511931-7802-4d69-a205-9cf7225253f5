package com.kuaishou.kwaishop.qa.risk.center.db.query.detail;

import java.util.Collection;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-11
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DetailBaseQueryCondition extends BaseQueryCondition {

    /**
     * 质量中心
     */
    private Long centerId;

    /**
     * 团队
     */
    private Long teamId;

    /**
     * 质量中心s
     */
    private Collection<Long> centerIds;

    /**
     * 团队s
     */
    private Collection<Long> teamIds;
}
