package com.kuaishou.kwaishop.qa.risk.center.domain.combine.convert.impl;

import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.qa.risk.center.domain.combine.convert.RiskCombineConvertHandler;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.RiskInfoCombineBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.QueryGroupByRiskSummaryDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.QueryRiskSummaryDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.RiskSummaryDataDTO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-26
 */
@Component
public class RiskCombineConvertConvertHandlerImpl implements RiskCombineConvertHandler {

    @Override
    public CombineQueryParam buildSummaryQueryParam(QueryRiskSummaryDataRequest request) {
        return CombineQueryParam.builder()
                .dateType(request.getDateType())
                .build();
    }

    @Override
    public CombineQueryParam buildGroupingByQueryParam(QueryGroupByRiskSummaryDataRequest request) {
        return CombineQueryParam.builder()
                .teamId(request.getTeamId())
                .centerId(request.getCenterId())
                .dateType(request.getDateType())
                .build();
    }

    @Override
    public RiskSummaryDataDTO buildSummaryDTO(CombineBO combineBO) {
        RiskInfoCombineBO riskInfoCombineBO = (RiskInfoCombineBO) combineBO;
        RiskSummaryDataDTO.Builder res = RiskSummaryDataDTO.newBuilder();
        if (CollectionUtils.isNotEmpty(riskInfoCombineBO.getInnerData())) {
            res.addAllInnerData(riskInfoCombineBO.getInnerData().stream()
                    .map(this::buildSummaryDTO).collect(Collectors.toList()));
        }
        if (riskInfoCombineBO.getEntityType() != null) {
            res.setEntityType(riskInfoCombineBO.getEntityType());
        }
        if (riskInfoCombineBO.getId() != null) {
            res.setId(riskInfoCombineBO.getId());
        }
        if (riskInfoCombineBO.getName() != null) {
            res.setName(riskInfoCombineBO.getName());
        }
        res.setRiskDetailTotal(riskInfoCombineBO.getTotal());
        res.setVerifyEffectiveCount(riskInfoCombineBO.getVerifyEffectiveCount());
        res.setPlanEffectiveCount(riskInfoCombineBO.getPlanEffectiveCount());
        res.setHighLevelCount(riskInfoCombineBO.getHighLevelCount());
        res.setMediumLevelCount(riskInfoCombineBO.getMediumLevelCount());
        res.setLowLevelCount(riskInfoCombineBO.getLowLevelCount());
        res.setRiskCoverCount(riskInfoCombineBO.getRiskCoverCount());
        res.setRiskCoverRate(riskInfoCombineBO.getRiskCoverRate());
        res.setVerifyEffectiveRate(riskInfoCombineBO.getVerifyEffectiveRate());
        res.setPlanEffectiveRate(riskInfoCombineBO.getPlanEffectiveRate());
        res.setHighLevelRate(riskInfoCombineBO.getHighLevelRate());
        res.setMediumLevelRate(riskInfoCombineBO.getMediumLevelRate());
        res.setLowLevelRate(riskInfoCombineBO.getLowLevelRate());
        res.setNewRiskCount(riskInfoCombineBO.getNewRiskCount());
        res.setPlanCoverCount(riskInfoCombineBO.getPlanCoverCount());
        res.setPlanCoverRate(riskInfoCombineBO.getPlanCoverRate());
        res.setFundExpTotal(riskInfoCombineBO.getFundExpTotal());
        return res.build();
    }
}
