package com.kuaishou.kwaishop.qa.risk.center.db.query.stress;


import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 *
 * Created on 2022-09-16
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ScenarioQueryCondition extends BaseQueryCondition {
    /**
     * 组织名
     */
    private String name;

    private Long id;
}
