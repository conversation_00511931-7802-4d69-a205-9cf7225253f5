package com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressServiceBaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceBaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress.StressServiceBaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.stress.ScenarioQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressServiceBaseBO;

@Repository
public class StressServiceBaseDAOImpl extends BaseDAOImpl<StressServiceBaseDO, ScenarioQueryCondition> implements StressServiceBaseDAO {

    @Autowired
    private StressServiceBaseMapper stressServiceBaseMapper;

    @Override
    protected void fillQueryCondition(ScenarioQueryCondition condition, QueryWrapper<StressServiceBaseDO> queryWrapper) {
    }

    @Override
    protected BaseMapper<StressServiceBaseDO> getMapper() {
        return stressServiceBaseMapper;
    }


    @Override
    public PageBO<StressServiceBaseDO> queryStressServiceBaseList(QueryStressServiceBaseBO bo) {
        Integer pageNo = 0;
        Integer pageSize = 10;
        if (bo.getPageNo() >= 1) {
            pageNo = bo.getPageNo() - 1;
        }
        if (bo.getPageSize() > 100 || bo.getPageSize() <= 0) {
            pageSize = 100;
        }
        Integer startNo = pageNo * bo.getPageSize();
        Long count = stressServiceBaseMapper.countStressServiceList(bo.getServiceId(), bo.getServiceName());
        List<StressServiceBaseDO> stressServiceBaseList  = stressServiceBaseMapper
                .queryStressServiceLists(bo.getServiceId(), bo.getServiceName(), startNo, pageSize);
        return new PageBO<StressServiceBaseDO>(pageNo, pageSize,
                count, stressServiceBaseList);
    }

    @Override
    public StressServiceBaseDO queryStressServiceBaseByServiceId(Long id) {
        return stressServiceBaseMapper.queryStressServiceBaseByServiceId(id);
    }
}
