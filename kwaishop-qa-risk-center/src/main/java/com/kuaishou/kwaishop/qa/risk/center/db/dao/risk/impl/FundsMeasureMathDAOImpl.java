package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.FundsMeasureMathDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsMeasureMathDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.FundsMeasureMathMapper;

import groovy.util.logging.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024/3/7 16:15
 */
@Repository
@Slf4j
public class FundsMeasureMathDAOImpl implements FundsMeasureMathDAO {
    @Autowired
    private FundsMeasureMathMapper fundsMeasureMathMapper;
    @Override
    public List<FundsMeasureMathDO> getMathList(String moduleName) {
        return fundsMeasureMathMapper.getMathList(moduleName);
    }

}
