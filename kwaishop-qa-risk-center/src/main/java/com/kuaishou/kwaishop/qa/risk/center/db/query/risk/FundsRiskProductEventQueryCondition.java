package com.kuaishou.kwaishop.qa.risk.center.db.query.risk;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-01-05
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FundsRiskProductEventQueryCondition extends BaseQueryCondition {

    private String name;
    private String riskProduct;
    private String riskEvent;
    private String businessDomain;
    private String department;
}
