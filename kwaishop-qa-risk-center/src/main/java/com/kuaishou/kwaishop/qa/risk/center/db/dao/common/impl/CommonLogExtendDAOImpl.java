package com.kuaishou.kwaishop.qa.risk.center.db.dao.common.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kuaishou.infra.boot.jdbc.datasource.TransactionalDataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.common.CommonLogExtendDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.common.CommonLogDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.common.CommonLogMapper;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-27
 */
@Service
@TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
public class CommonLogExtendDAOImpl extends ServiceImpl<CommonLogMapper, CommonLogDO> implements CommonLogExtendDAO {
}
