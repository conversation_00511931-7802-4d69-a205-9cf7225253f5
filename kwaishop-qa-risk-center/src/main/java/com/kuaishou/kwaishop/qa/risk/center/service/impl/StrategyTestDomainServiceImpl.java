package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.phantomthief.scope.Scope;
import com.kuaishou.framework.scope.TraceContextUtil;
import com.kuaishou.infra.framework.kafka.KafkaProducers;
import com.kuaishou.infra.framework.mq.MqMessage;
import com.kuaishou.infra.framework.mq.MqSyncSendResult;
import com.kuaishou.infra.framework.mq.MsgProducer;
import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.biz.StrategyCenterAutoTestBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.ReplayService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.impl.StrategyTrafficRecordImpl;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.KrpcStrategyTestDomainServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.QueryPageRecordTaskRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.QueryPageRecordTaskResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.QueryPageReplayDetailResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.SendKafkaRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.SendKafkaResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.SimpleQueryRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.SimpleQueryResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StartRecordingRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StartRecordingResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StrategyTestRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StrategyTestResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestAiOpenAiRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestAiOpenAiResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestMsg;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestRealTimeCacheRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.TestRealTimeCacheResponse;
import com.kuaishou.protobuf.trace.TraceContext;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-20
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class StrategyTestDomainServiceImpl extends KrpcStrategyTestDomainServiceGrpc.StrategyTestDomainServiceImplBaseV2 {

    @Autowired
    private StrategyCenterAutoTestBizService strategyCenterAutoTestBizService;

    @Autowired
    private StrategyTrafficRecordImpl strategyTrafficRecord;

    @Autowired
    private ReplayService replayService;

    @Override
    public TestAiOpenAiResponse testAiOpenAi(TestAiOpenAiRequest request) {
        log.info("治理策略测试开始启动, request = ");
        try {
            return strategyCenterAutoTestBizService.testAiOpenAi1(request);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 老的回放入口，已废弃
     * @param request
     * @return
     */
    @Override
    public StrategyTestResponse startTest(StrategyTestRequest request) {
        log.info("治理策略测试开始启动, request = {}", protoToJsonString(request));
        try {
            strategyCenterAutoTestBizService.startTest(request);
            return StrategyTestResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg("触发成功")
                    .build();
        } catch (BizException e) {
            log.error("治理策略测试, 业务错误, request = {}, exception = ", protoToJsonString(request), e);
            return StrategyTestResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("治理策略测试出现异常报错, request = {}, exception = ", protoToJsonString(request), e);
            return StrategyTestResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    /**
     * 最新的回放的入口，通过传递caseId or taskId来回放
     * @param request
     * @return
     */
    @Override
    public StrategyTestResponse startReplay(ReplayRequest request) {
        log.info("治理策略测试开始启动, request = {}", protoToJsonString(request));
        try {
            strategyCenterAutoTestBizService.replayRequest(request);
            return StrategyTestResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg("触发成功")
                    .build();
        } catch (BizException e) {
            log.error("治理策略测试, 业务错误, request = {}, exception = ", protoToJsonString(request), e);
            return StrategyTestResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("治理策略测试出现异常报错, request = {}, exception = ", protoToJsonString(request), e);
            return StrategyTestResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    /**
     * 创建任务录制入口
     * @param request
     * @return
     */
    @Override
    public StartRecordingResponse startRecording(StartRecordingRequest request) {
        log.info("策略测试数据开始录制, request = {}", protoToJsonString(request));
        try {
            // 录制是异步的，先去执行，先返回结果
            strategyCenterAutoTestBizService.startRecording(request);
            return StartRecordingResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg("触发成功，异步任务正在执行，请等待数据录制完成")
                    .build();
        } catch (BizException e) {
            log.error("策略测试数据录制错误, 业务错误, request = {}, exception = ", protoToJsonString(request), e);
            return StartRecordingResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("策略测试数据录制错误, request = {}, exception = ", protoToJsonString(request), e);
            return StartRecordingResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


//    @Override
//    public QueryTrafficResponse queryTraffic(QueryTrafficRequest request) {
//        log.info("查询录制数据, request = {}", protoToJsonString(request));
//        try {
//            strategyCenterAutoTestBizService.queryTraffic(request);
//            return QueryTrafficResponse.newBuilder()
//                    .setResult(BaseResultCode.SUCCESS_VALUE)
//                    .setErrorMsg("触发成功")
//                    .build();
//        } catch (BizException e) {
//            log.error("治理策略测试, 业务错误, request = {}, exception = ", protoToJsonString(request), e);
//            return QueryTrafficResponse.newBuilder()
//                    .setResult(e.getCode())
//                    .setErrorMsg(e.getMessage())
//                    .build();
//        } catch (Exception e) {
//            log.error("治理策略测试出现异常报错, request = {}, exception = ", protoToJsonString(request), e);
//            return QueryTrafficResponse.newBuilder()
//                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
//                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
//                    .build();
//        }
//    }

    /**
     * 回放详情接口
     * @param request
     * @return
     */
    @Override
    public QueryPageReplayDetailResponse replayDetail(ReplayDetailRequest request) {
        log.info("策略测试查询录制数据, request = {}", protoToJsonString(request));
        try {
            return QueryPageReplayDetailResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(strategyCenterAutoTestBizService.replayDetail(request))
                    .build();
        } catch (BizException e) {
            log.error("[策略测试查询录制数据] replayDetail bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryPageReplayDetailResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[策略测试查询录制数据] replayDetail error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryPageReplayDetailResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }


    /**
     * 用于分页查询录制任务
     * @param request
     * @return
     */
    @Override
    public QueryPageRecordTaskResponse queryRecordTask(QueryPageRecordTaskRequest request) {
        log.info("策略测试查询录制任务, request = {}", protoToJsonString(request));
        try {
            return QueryPageRecordTaskResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(strategyTrafficRecord.queryPage(request))
                    .build();
        } catch (BizException e) {
            log.error("[策略测试查询录制任务] queryRecordTask bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryPageRecordTaskResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[策略测试查询录制任务] queryRecordTask error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryPageRecordTaskResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    /**
     * 用于查询录制任务的sql和caseid的请求参数
     * @param request
     * @return
     */
    @Override
    public SimpleQueryResponse simpleQuery(SimpleQueryRequest request) {
        log.info("策略测试简单查询, request = {}", protoToJsonString(request));
        try {
            return SimpleQueryResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(strategyCenterAutoTestBizService.simpleQuery(request) == null
                            ?
                            ""
                            :
                            strategyCenterAutoTestBizService.simpleQuery(request))
                    .build();
        } catch (BizException e) {
            log.error("[策略测试查询录制任务] queryRecordTask bizError, req: {}, exception: ", protoToJsonString(request), e);
            return SimpleQueryResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[策略测试查询录制任务] queryRecordTask error, req: {}, exception: ", protoToJsonString(request), e);
            return SimpleQueryResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    @Override
    public SendKafkaResponse sendKafka(SendKafkaRequest request) {
        log.info("开始发送kafka, {}", protoToJsonString(request));
        if (request.getType().equals("kafka")) {
            Scope.runWithExistScope(new Scope(), () -> {
                TraceContext traceContext = TraceContext.newBuilder().setLaneId("PRT.ad_inspire").build();
                TraceContextUtil.initTraceContext(traceContext);
                // 在这个 scope 内里调用业务逻辑就发到泳道上了
                KafkaProducers.sendString(request.getTopic(), request.getMessage());
            });
            log.info("发送kafka成功");
        } else {
            Scope.runWithExistScope(new Scope(), () -> {
                TraceContext traceContext = TraceContext.newBuilder().setLaneId("PRT.ad_inspire").build();
                TraceContextUtil.initTraceContext(traceContext);
                // 在这里发送mq
                log.info("request = {}", request.getMessage());
                for (int i = 0; i < 5; i++) {
                    sendMqMessage(request.getMessage());
                }
            });
            log.info("发送mq成功");
        }

        return SendKafkaResponse.newBuilder()
                .setResult(BaseResultCode.SUCCESS_VALUE)
                .setErrorMsg("触发成功")
                .build();
    }

    @Resource(name = "testmqProducer")
    private MsgProducer testmqProducer;

    public void sendMqMessage(String msg) {
        // do something
        TestMsg messageBody = TestMsg.newBuilder().setMsgId(1).setMsgText(msg).build();
        MqMessage message = testmqProducer.createMsgBuilder(messageBody).build();
        MqSyncSendResult result = testmqProducer.sendSync(message);
        log.info("mq发送消息, {}, 参数 {}", result.toString(), protoToJsonString(messageBody));
    }

    @Override
    public TestRealTimeCacheResponse testRealTimeCache(TestRealTimeCacheRequest request) {
        String result = replayService.getRealTimeCache(request.getSceneKey());
        return TestRealTimeCacheResponse.newBuilder()
                .setData(result)
                .build();
    }
}
