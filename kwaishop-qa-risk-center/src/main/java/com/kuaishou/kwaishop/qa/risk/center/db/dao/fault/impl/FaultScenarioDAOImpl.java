package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.impl;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.FaultScenarioDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultScenarioDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.fault.FaultScenarioMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.fault.FaultScenarioQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultScenarioBO;

import lombok.extern.slf4j.Slf4j;

@Repository
@Slf4j
public class FaultScenarioDAOImpl extends BaseDAOImpl<FaultScenarioDO, FaultScenarioQueryCondition> implements FaultScenarioDAO {

    @Autowired
    private FaultScenarioMapper faultScenarioMapper;

    @Override
    public PageBO<FaultScenarioDO> queryPageFaultScenario(FaultScenarioBO faultScenarioBO) {
        FaultScenarioQueryCondition condition = FaultScenarioQueryCondition.builder()
                .scenario(faultScenarioBO.getScenario())
                .teamId(faultScenarioBO.getTeamId())
                .pageNo(faultScenarioBO.getPageNo())
                .pageSize(faultScenarioBO.getPageSize())
                .level(faultScenarioBO.getLevel())
                .status(faultScenarioBO.getStatusList())
                .id(faultScenarioBO.getId())
                .orderByCreateTimeDesc(true)
                .entry(faultScenarioBO.getEntry())
                .build();
        return queryPageList(condition);
    }

    @Override
    public List<FaultScenarioDO> queryListScenario(FaultScenarioBO faultScenarioBO) {
        FaultScenarioQueryCondition condition = FaultScenarioQueryCondition.builder()
                .scenario(faultScenarioBO.getScenario())
                .teamId(faultScenarioBO.getTeamId())
                .entry(faultScenarioBO.getEntry())
                .ksn(faultScenarioBO.getKsn())
                .orderByCreateTimeDesc(true)
                .build();
        return queryList(condition);
    }

    @Override
    public FaultScenarioDO queryFaultScenario(FaultScenarioBO faultScenarioBO) {
        FaultScenarioQueryCondition condition = FaultScenarioQueryCondition.builder()
                .scenario(faultScenarioBO.getScenario())
                .teamId(faultScenarioBO.getTeamId())
                .id(faultScenarioBO.getId())
                .entry(faultScenarioBO.getEntry())
                .ksn(faultScenarioBO.getKsn())
                .build();
        List<FaultScenarioDO> res = queryList(condition);
        if (CollectionUtils.isEmpty(res)) {
            return null;
        }
        return res.get(0);
    }

    @Override
    public FaultScenarioDO queryFaultScenarioById(Long id) {
        return queryById(id);
    }

    @Override
    public long deleteFaultScenario(long id) {
        return logicDelete(id);
    }

    @Override
    public long insertOrUpdateFaultScenario(FaultScenarioDO faultScenarioDO) {
        try {
            return saveOrUpdate(faultScenarioDO);
        } catch (Exception ex) {
            return 0;
        }
    }

    @Override
    protected void fillQueryCondition(FaultScenarioQueryCondition condition,
                                      QueryWrapper<FaultScenarioDO> queryWrapper) {
        if (condition.getScenario() != null && StringUtils.isNotBlank(condition.getScenario())) {
            queryWrapper.and(q -> q.like("business_scenario", condition.getScenario()));
        }
        if (condition.getEntry() != null && StringUtils.isNotBlank(condition.getEntry())) {
            queryWrapper.and(q -> q.likeLeft("business_entry", condition.getEntry()));
        }
        if (condition.getKsn() != null && StringUtils.isNotBlank(condition.getKsn())) {
            queryWrapper.and(q -> q.eq("entry_ksn", condition.getKsn()));
        }
        if (condition.getTeamId() != null && condition.getTeamId() > 0) {
            queryWrapper.and(q -> q.eq("team_id", condition.getTeamId()));
        }
        if (condition.getLevel() != null && StringUtils.isNotBlank(condition.getLevel())) {
            queryWrapper.and(q -> q.eq("scenario_level", condition.getLevel()));
        }
        if (condition.getStatus() != null && CollectionUtils.isNotEmpty(condition.getStatus())) {
            queryWrapper.and(q -> q.in("status", condition.getStatus()));
        }
    }

    @Override
    protected BaseMapper<FaultScenarioDO> getMapper() {
        return faultScenarioMapper;
    }

}
