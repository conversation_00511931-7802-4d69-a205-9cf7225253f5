package com.kuaishou.kwaishop.qa.risk.center.domain.account.convert;

import java.util.Map;

import com.google.gson.JsonArray;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TestAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAuthDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.AccountTokenBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportAccountExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportTokenExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportAccountExcelRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportAccountTokenRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryPageUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UserAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.CreateTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryTestAccountsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.ImportKlinkTokenRequest;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.BaseExcelModel;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
public interface UserAccountConvert {

    UserAccountBO buildQueryBO(QueryUserAccountRequest request);

    UserAccountBO buildQueryBOV2(QueryTestAccountsRequest request);

    UserAccountBO buildQueryPageBO(QueryPageUserAccountRequest request);

    UserAccountBO buildCreateBO(ImportUserAccountRequest request);

    TestAccountDO buildCreateDO(CreateTestAccountRequest request);

    UserAccountDO buildCreateDO(UserAccountBO accountBO);

    UserAccountDTO buildDTO(UserAccountDO userAccountDO, String centerName, String teamName, String op);

    ImportAccountExcelBO buildExcelBO(ImportAccountExcelRequest request);

    UserAccountDO phoneExcelAccountDO(BaseExcelModel baseExcelModel, ImportAccountExcelBO excelBO);

    UserAccountDO emailExcelAccountDO(BaseExcelModel baseExcelModel, ImportAccountExcelBO excelBO);

    UserAccountDO singleUidExcelAccountDO(BaseExcelModel baseExcelModel, ImportAccountExcelBO excelBO);

    AccountTokenBO buildAccountTokenBO(UserAccountDO userAccountDO);

    UserAccountDTO buildUserAuthDTO(UserAccountDTO userAccountDTO, Map<Long, UserAuthDO> map);

    ImportTokenExcelBO buildTokenExcelBO(ImportAccountTokenRequest request);

    UserAccountDO tokenExcelAccountDO(BaseExcelModel e, ImportTokenExcelBO excelBO);

    ImportTokenExcelBO buildTokenExcelBO(ImportKlinkTokenRequest request);

    UserAccountDO parseUserAccount(JsonArray jsonArray);

}
