package com.kuaishou.kwaishop.qa.risk.center.db.dao.detail;

import java.util.Collection;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.KspayBaseDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-13
 */

public interface KspayDetailDAO<T extends KspayBaseDO, P extends CombineQueryParam> {

    List<T> queryByCenterIds(Collection<Long> centerIds, P queryParam);

    List<T> queryByTeamIds(Long centerId, Collection<Long> teamIds, P queryParam);

    List<T> queryByCenterId(Long centerId, P queryParam);

    List<T> queryByTeamId(Long centerId, Long teamId, P queryParam);

    List<T> queryByCenterIdsTeamIds(Collection<Long> centerIds, Collection<Long> teamIds, P queryParam);
}
