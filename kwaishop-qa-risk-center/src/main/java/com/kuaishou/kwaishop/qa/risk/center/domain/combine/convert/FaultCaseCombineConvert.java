package com.kuaishou.kwaishop.qa.risk.center.domain.combine.convert;

import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCombineBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCombineInfo;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCombineProblemInfo;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCombineQueryBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseCombineDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseCombineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseProblemCombineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCombineInfoDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCombineProblemInfoDTO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-13
 */
public interface FaultCaseCombineConvert {

    FaultCombineQueryBO buildQueryBO(FaultCaseCombineRequest request);

    FaultCombineQueryBO buildQueryBO(FaultCaseProblemCombineRequest request);

    FaultCaseCombineDTO buildCaseCombineDTO(FaultCombineBO faultCombineBO);

    FaultCombineInfoDTO buildCombineInfoDTO(FaultCombineInfo faultCombineInfo);

    FaultCombineProblemInfoDTO buildProblemInfoDTO(FaultCombineProblemInfo faultCombineProblemInfo);
}
