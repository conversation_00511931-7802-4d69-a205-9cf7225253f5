package com.kuaishou.kwaishop.qa.risk.center.db.dao.feature;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.KspayPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundsRiskFeatureQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundsRiskFeatureStatesUpdateCondition;

public interface FundsRiskFeatureDAO {
    KspayPageBO<FundsRiskFeatureDO> queryPageRiskFeatureList(FundsRiskFeatureQueryCondition fundsRiskFeatureQueryCondition);
    KspayPageBO<FundsRiskFeatureDO> queryPageRiskFeatureListDescByUpdateTime(FundsRiskFeatureQueryCondition fundsRiskFeatureQueryCondition);
    List<FundsRiskFeatureDO> queryRiskFeatureList(FundsRiskFeatureQueryCondition fundsRiskFeatureQueryCondition);
    List<FundsRiskFeatureDO> queryFeatureListWithCondition(FundsRiskFeatureQueryCondition featureQueryCondition);
    void updateFundsRiskFeatureViewState(FundsRiskFeatureStatesUpdateCondition fundsRiskFeatureStatesUpdateCondition);
    void updateAllFeaturesWithoutRisk(String featureId);
    FundsRiskFeatureDO queryByFeatureId(FundsRiskFeatureDO fundsRiskFeatureDO);

    FundsRiskFeatureDO queryByMainId(String mainId);

    FundsRiskFeatureDO queryByFeatureId(String featureId);
    List<FundsRiskFeatureDO> queryNeedSendKimFeature(FundsRiskFeatureDO fundsRiskFeatureDO, Long startTime, Long endTime);

    int updateSelectiveById(FundsRiskFeatureDO fundsRiskFeatureDO);

    void updateFeatureViewWithRisk();

    int updateByFeatureId(FundsRiskFeatureDO fundsRiskFeatureDO);
    int updateFeatureViewStatusByFeatureId(FundsRiskFeatureDO fundsRiskFeatureDO);
    int updateTestTypeByFeatureId(FundsRiskFeatureDO fundsRiskFeatureDO);
    int isNeedUpdateRiskPassStatus(String featureId);
    int updateOnlineConfirm(String featureId, Integer onlineConfirm, String operator);
    int updateRiskPassStatus(String featureId);
    List<FundsRiskFeatureDO> queryListByDepartment(String department);
}
