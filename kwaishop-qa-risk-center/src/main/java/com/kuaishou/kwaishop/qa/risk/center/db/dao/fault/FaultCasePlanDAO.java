package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.detail.DetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultCasePlanDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCasePlanBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2024-03-18
 */
public interface FaultCasePlanDAO extends DetailDAO<FaultCasePlanDO, CombineQueryParam> {
    PageBO<FaultCasePlanDO> queryFaultPlanIdsByCaseId(FaultCasePlanBO faultCasePlanBO);

    void deleteFaultCasePlanByPlanId(String operator, Long planId);
}
