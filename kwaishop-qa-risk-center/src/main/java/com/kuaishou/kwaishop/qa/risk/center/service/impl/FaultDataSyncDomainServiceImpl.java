package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.biz.FaultDataSyncBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.CreateFaultDataSyncRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.CreateFaultDataSyncResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.KrpcFaultDataSyncDomainServiceGrpc.FaultDataSyncDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.PageFaultDataSyncDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultDataSyncPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultDataSyncPageListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateFaultDataSyncRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateFaultDataSyncResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-01-06
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class FaultDataSyncDomainServiceImpl extends FaultDataSyncDomainServiceImplBaseV2 {

    @Autowired
    private FaultDataSyncBizService faultDataSyncBizService;

    @Override
    public CreateFaultDataSyncResponse createFaultDataSync(CreateFaultDataSyncRequest request) {
        log.info("[FaultDataSyncDomainServiceImpl] createFaultDataSync request: {}", protoToJsonString(request));
        try {
            faultDataSyncBizService.createSync(request);
            return CreateFaultDataSyncResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultDataSyncDomainServiceImpl] createFaultDataSync bizError, req: {}, exception: ", protoToJsonString(request), e);
            return CreateFaultDataSyncResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultDataSyncDomainServiceImpl] createFaultDataSync error, req: {}, exception: ", protoToJsonString(request), e);
            return CreateFaultDataSyncResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateFaultDataSyncResponse updateFaultDataSync(UpdateFaultDataSyncRequest request) {
        log.info("[FaultDataSyncDomainServiceImpl] updateFaultDataSync request: {}", protoToJsonString(request));
        try {
            faultDataSyncBizService.updateSync(request);
            return UpdateFaultDataSyncResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultDataSyncDomainServiceImpl] updateFaultDataSync bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateFaultDataSyncResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultDataSyncDomainServiceImpl] updateFaultDataSync error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateFaultDataSyncResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryFaultDataSyncPageListResponse queryFaultDataSyncPageList(QueryFaultDataSyncPageListRequest request) {
        log.info("[FaultDataSyncDomainServiceImpl] queryFaultDataSyncPageList request: {}", protoToJsonString(request));
        try {
            PageFaultDataSyncDTO res = faultDataSyncBizService.querySyncPageList(request);
            return QueryFaultDataSyncPageListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(res)
                    .build();
        } catch (BizException e) {
            log.error("[FaultDataSyncDomainServiceImpl] queryFaultDataSyncPageList bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultDataSyncPageListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultDataSyncDomainServiceImpl] queryFaultDataSyncPageList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultDataSyncPageListResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }
}
