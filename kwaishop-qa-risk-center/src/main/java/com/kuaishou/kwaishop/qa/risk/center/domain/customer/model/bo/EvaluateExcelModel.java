package com.kuaishou.kwaishop.qa.risk.center.domain.customer.model.bo;


import com.alibaba.excel.annotation.ExcelProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluateExcelModel {

    @ExcelProperty("scene")
    private String scene;

    @ExcelProperty("question")
    private String question;

    @ExcelProperty("question_context")
    private String questionContext;

    @ExcelProperty("prompt")
    private String prompt;

    @ExcelProperty("answer")
    private String answer;

    @ExcelProperty("answer_origin")
    private String answerOrigin;

    @ExcelProperty("tools")
    private String tools;

    @ExcelProperty("traceid")
    private String traceid;


}
