package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataSourceDo;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.DataSourceBo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-12
 */
public interface DataCompareDAO {


    PageBO<DataSourceDo> queryPageDataSourceList(DataSourceBo dataSourceBo);
}
