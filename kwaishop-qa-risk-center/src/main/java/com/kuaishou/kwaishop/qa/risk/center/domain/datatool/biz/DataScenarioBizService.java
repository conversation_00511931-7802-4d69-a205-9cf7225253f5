package com.kuaishou.kwaishop.qa.risk.center.domain.datatool.biz;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DATA_SCEANRIO_CREATE_PARAM_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DATA_SCEANRIO_UPDATE_PARAM_ERROR;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.config.constants.QueryStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataCaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataCaseExecuteDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataScenarioDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.ScenarioCaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.ScenarioCaseExeDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.ScenarioExecuteDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.CaseExecuteDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataScenarioDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.ScenarioCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.ScenarioCaseExeDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.ScenarioExecuteDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.DataScenarioBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.ScenarioExecuteBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CreateDataScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CreateDataScenarioResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DataScenarioDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DeleteCaseFromScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.DeleteCaseFromScenarioResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.InsertCaseIntoScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.InsertCaseIntoScenarioResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.PageDataScenarioDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.PageScenarioExe;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryCaseExeByScenarioExeIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryCaseExeByScenarioExeIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseByScenarioIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryDataCaseByScenarioIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryPageDataScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryPageDataScenarioResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.RunDataScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.RunDataScenarioResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.ScenarioCaseDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.ScenarioCaseExeDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.ScenarioExeDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.ScenarioExeResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.ScenarioExeResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.UpdateDataScenarioRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.UpdateDataScenarioResponse;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2023-03-15
 */
@Service
@Slf4j
public class DataScenarioBizService {
    @Autowired
    private DataScenarioDAO dataScenarioDAO;

    @Autowired
    private ScenarioCaseDAO scenarioCaseDAO;

    @Autowired
    private DataCaseDAO dataCaseDAO;

    @Autowired
    private DataCaseExecuteDAO dataCaseExecuteDAO;

    @Autowired
    private ScenarioExecuteDAO scenarioExecuteDAO;

    @Autowired
    private ScenarioCaseExeDAO scenarioCaseExeDAO;

    @Autowired
    private DataCaseExeBizService dataCaseExeBizService;

    public CreateDataScenarioResponse createDataScenario(CreateDataScenarioRequest request) {

        if (StringUtils.isBlank(request.getName()) || StringUtils.isBlank(request.getCreatorName())) {
            throw new BizException(DATA_SCEANRIO_CREATE_PARAM_ERROR);
        }
        try {
            DataScenarioDO dataScenarioDO = DataScenarioDO.builder()
                    .creatorName(request.getCreatorName())
                    .name(request.getName())
                    .build();
            List<Long> caseIds = request.getCaseIdListList();

            dataScenarioDAO.insertDataScenario(dataScenarioDO);
            for (Long caseId : caseIds) {
                ScenarioCaseDO scenarioCaseDO = ScenarioCaseDO.builder()
                        .caseId(caseId)
                        .scenarioId(dataScenarioDO.getId())
                        .creatorName(dataScenarioDO.getCreatorName())
                        .build();

                scenarioCaseDAO.insertScenarioCase(scenarioCaseDO);
            }
            return CreateDataScenarioResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] createDataSource bizError, exception: ", e);
            return CreateDataScenarioResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[DataSourceBizService] createDataSource Error, exception: ", e);
            return CreateDataScenarioResponse.newBuilder().setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    public UpdateDataScenarioResponse updateDataScenario(UpdateDataScenarioRequest request) {

        if (request.getId() <= 0) {
            throw new BizException(DATA_SCEANRIO_UPDATE_PARAM_ERROR);
        }
        if (StringUtils.isBlank(request.getName())) {
            return UpdateDataScenarioResponse.newBuilder().setResult(DATA_SCEANRIO_UPDATE_PARAM_ERROR.getCode())
                    .setErrorMsg(DATA_SCEANRIO_UPDATE_PARAM_ERROR.getMessage()).build();
        }
        try {
            DataScenarioDO dataScenarioDO = dataScenarioDAO.queryDataScenarioById(request.getId());
            if (dataScenarioDO == null) {
                throw new BizException(DATA_SCEANRIO_UPDATE_PARAM_ERROR);
            }
            dataScenarioDO.setName(request.getName());
            dataScenarioDO.setAutoTrigger(request.getAutoTrigger());
            dataScenarioDAO.updateDataScenario(dataScenarioDO);
            return UpdateDataScenarioResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] updateDataSource bizError, exception: ", e);
            return UpdateDataScenarioResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        }

    }

    public InsertCaseIntoScenarioResponse insertCaseIntoScenario(InsertCaseIntoScenarioRequest request) {
        if (request.getScenarioId() <= 0) {
            throw new BizException(DATA_SCEANRIO_UPDATE_PARAM_ERROR);
        }
        try {
            List<Long> caseIds = request.getCaseIdListList();
            for (Long caseId : caseIds) {
                ScenarioCaseDO scenarioCaseDO = ScenarioCaseDO.builder()
                        .caseId(caseId)
                        .scenarioId(request.getScenarioId())
                        .creatorName(request.getCreatorName())
                        .build();

                scenarioCaseDAO.insertScenarioCase(scenarioCaseDO);
            }

            return InsertCaseIntoScenarioResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] updateDataSource bizError, exception: ", e);
            return InsertCaseIntoScenarioResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        }

    }

    public DeleteCaseFromScenarioResponse deleteCaseFromScenario(DeleteCaseFromScenarioRequest request) {
        if (request.getScenarioId() <= 0) {
            throw new BizException(DATA_SCEANRIO_UPDATE_PARAM_ERROR);
        }
        try {
            List<Long> caseIds = request.getCaseIdListList();
            Long scenarioId = request.getScenarioId();
            for (Long caseId : caseIds) {
                Long scenarioCaseId = scenarioCaseDAO.queryIdByScenarioIdCaseId(scenarioId, caseId);
                scenarioCaseDAO.logicDeleted(scenarioCaseId, request.getCreatorName());
            }

            return DeleteCaseFromScenarioResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] updateDataSource bizError, exception: ", e);
            return DeleteCaseFromScenarioResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        }

    }

    public QueryDataCaseByScenarioIdResponse queryDataCaseByScenarioId(QueryDataCaseByScenarioIdRequest request) {
        if (request.getScenarioId() <= 0) {
            throw new BizException(DATA_SCEANRIO_UPDATE_PARAM_ERROR);
        }
        try {
            List<Long> caseIds = scenarioCaseDAO.queryCaseIdList(request.getScenarioId());
            List<ScenarioCaseDTO> scenarioCaseDTOList = new ArrayList<>();
            for (Long caseId : caseIds) {
                DataCaseDO dataCaseDO = dataCaseDAO.queryDataCaseById(caseId);
                ScenarioCaseDTO scenarioCaseDTO = ScenarioCaseDTO.newBuilder()
                        .setId(dataCaseDO.getId())
                        .setName(dataCaseDO.getName())
                        .setCreateTime(dataCaseDO.getCreateTime())
                        .setUpdateTime(dataCaseDO.getUpdateTime())
                        .setCreatorName(dataCaseDO.getCreatorName())
                        .build();
                scenarioCaseDTOList.add(scenarioCaseDTO);
            }



            return QueryDataCaseByScenarioIdResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(scenarioCaseDTOList)
                    .build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] updateDataSource bizError, exception: ", e);
            return QueryDataCaseByScenarioIdResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        }


    }

    public QueryDataCaseByScenarioIdResponse queryDataCaseWithoutScenario(QueryDataCaseByScenarioIdRequest request) {
        if (request.getScenarioId() <= 0) {
            throw new BizException(DATA_SCEANRIO_UPDATE_PARAM_ERROR);
        }
        try {
            List<Long> caseIds = scenarioCaseDAO.queryCaseIdList(request.getScenarioId());

            List<DataCaseDO>  dataCaseDOListWithoutScenario = dataCaseDAO.queryDataCaseList(null);
            List<ScenarioCaseDTO> scenarioCaseDTOList = new ArrayList<>();
            for (DataCaseDO dataCaseDO : dataCaseDOListWithoutScenario) {
                if (!caseIds.contains(dataCaseDO.getId())) {
                    ScenarioCaseDTO scenarioCaseDTO = ScenarioCaseDTO.newBuilder()
                            .setId(dataCaseDO.getId())
                            .setName(dataCaseDO.getName())
                            .setCreateTime(dataCaseDO.getCreateTime())
                            .setUpdateTime(dataCaseDO.getUpdateTime())
                            .setCreatorName(dataCaseDO.getCreatorName())
                            .build();
                    scenarioCaseDTOList.add(scenarioCaseDTO);
                }
            }

            return QueryDataCaseByScenarioIdResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(scenarioCaseDTOList)
                    .build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] updateDataSource bizError, exception: ", e);
            return QueryDataCaseByScenarioIdResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        }


    }

    public RunDataScenarioResponse runDataScenario(RunDataScenarioRequest request) throws Exception {

        if (request.getScenarioId() <= 0) {
            throw new BizException(DATA_SCEANRIO_UPDATE_PARAM_ERROR);
        }
        try {
            DataScenarioDO dataScenarioDO = dataScenarioDAO.queryDataScenarioById(request.getScenarioId());
            ScenarioExecuteDO scenarioExecuteDO = ScenarioExecuteDO.builder()
                    .scenarioId(request.getScenarioId())
                    .name(dataScenarioDO.getName())
                    .executorName(request.getOperator())
                    .type(1)
                    .status(QueryStatusEnum.RUNNING.getValue())
                    .build();
            scenarioExecuteDAO.insertScenarioExecute(scenarioExecuteDO);
            List<Long> caseIds = scenarioCaseDAO.queryCaseIdList(request.getScenarioId());
            for (Long caseId : caseIds) {
                Long caseExeId = dataCaseExeBizService.runDataCase(caseId, request.getOperator());
                ScenarioCaseExeDO scenarioCaseExeDO = ScenarioCaseExeDO.builder()
                        .caseExeId(caseExeId)
                        .scenarioExeId(scenarioExecuteDO.getId())
                        .build();
                scenarioCaseExeDAO.insertScenarioCase(scenarioCaseExeDO);
            }

            return RunDataScenarioResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE).build();
        } catch (BizException e) {
            log.error("[DataSourceBizService] updateDataSource bizError, exception: ", e);
            return RunDataScenarioResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        }
    }

    public QueryPageDataScenarioResponse queryPageDataScenario(QueryPageDataScenarioRequest request) {
        try {
            DataScenarioBO dataScenarioBO = DataScenarioBO.builder()
                    .name(request.getName())
                    .creatorName(request.getCreatorName())
                    .pageNo(request.getPageNo())
                    .pageSize(request.getPageSize())
                    .build();
            PageBO<DataScenarioDO> dataScenarioDOPageBO =
                    dataScenarioDAO.queryPageDataScenarioList(dataScenarioBO);

            List<DataScenarioDTO> dataScenarioDTOList = dataScenarioDOPageBO.getData().stream()
                    .map(this::convertToDTO).collect(Collectors.toList());
            PageDataScenarioDTO pageDataScenarioDTO = PageDataScenarioDTO.newBuilder().addAllDataScenarioList(dataScenarioDTOList)
                    .setPageNo(dataScenarioDOPageBO.getPageNo())
                    .setPageSize(dataScenarioDOPageBO.getPageSize())
                    .setTotal(dataScenarioDOPageBO.getTotal())
                    .build();
            return QueryPageDataScenarioResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(pageDataScenarioDTO).build();
        } catch (BizException e) {
            return QueryPageDataScenarioResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        }

    }

    public ScenarioExeResultResponse queryPageScenarioExeResult(ScenarioExeResultRequest request) {
        try {
            ScenarioExecuteBO scenarioExecuteBO = ScenarioExecuteBO.builder()
                    .scenarioId(request.getScenarioId())
                    .executorName(request.getOperator())
                    .pageNo(request.getPageNo())
                    .pageSize(request.getPageSize())
                    .build();

            PageBO<ScenarioExecuteDO> scenarioExecuteDOPageBO =
                    scenarioExecuteDAO.queryPageScenarioExecuteList(scenarioExecuteBO);

            List<ScenarioExeDTO> scenarioExeDTOList = scenarioExecuteDOPageBO.getData().stream()
                    .map(this::convertScenarioExeToDTO).collect(Collectors.toList());
            PageScenarioExe pageScenarioExe = PageScenarioExe.newBuilder()
                    .addAllScenarioReportList(scenarioExeDTOList)
                    .setPageNo(scenarioExecuteDOPageBO.getPageNo())
                    .setPageSize(scenarioExecuteDOPageBO.getPageSize())
                    .setTotal(scenarioExecuteDOPageBO.getTotal())
                    .build();
            return ScenarioExeResultResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(pageScenarioExe).build();
        } catch (BizException e) {
            return ScenarioExeResultResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        }
    }

    public QueryCaseExeByScenarioExeIdResponse queryCaseExeByScenarioExeId(QueryCaseExeByScenarioExeIdRequest request) {
        if (request.getScenarioExeId() <= 0) {
            throw new BizException(DATA_SCEANRIO_CREATE_PARAM_ERROR);
        }
        try {
            List<Long> caseExeList = scenarioCaseExeDAO.queryCaseExeList(request.getScenarioExeId());
            List<ScenarioCaseExeDTO> caseReportList = caseExeList.stream()
                    .map(this::convertCaseExeToDTO).collect(Collectors.toList());
            return QueryCaseExeByScenarioExeIdResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllCaseReportList(caseReportList)
                    .build();

        } catch (BizException e) {
            return QueryCaseExeByScenarioExeIdResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        }
    }

    private DataScenarioDTO convertToDTO(DataScenarioDO dataScenarioDO) {
        return DataScenarioDTO.newBuilder()
                .setCreateTime(dataScenarioDO.getCreateTime())
                .setUpdateTime(dataScenarioDO.getUpdateTime())
                .setId(dataScenarioDO.getId())
                .setName(dataScenarioDO.getName())
                .setCreatorName(dataScenarioDO.getCreatorName())
                .setAutoTrigger(dataScenarioDO.getAutoTrigger())
                .build();
    }

    private ScenarioExeDTO convertScenarioExeToDTO(ScenarioExecuteDO scenarioExecuteDO) {
        return ScenarioExeDTO.newBuilder()
                .setId(scenarioExecuteDO.getId())
                .setScenarioId(scenarioExecuteDO.getScenarioId())
                .setName(scenarioExecuteDO.getName())
                .setExecutorName(scenarioExecuteDO.getExecutorName())
                .setStatus(scenarioExecuteDO.getStatus())
                .setStartTime(scenarioExecuteDO.getStartTime())
                .build();
    }

    private ScenarioCaseExeDTO convertCaseExeToDTO(Long caseExecuteId) {
        CaseExecuteDO caseExecuteDO = dataCaseExecuteDAO.queryCaseExecuteById(caseExecuteId);
        return ScenarioCaseExeDTO.newBuilder()
                .setId(caseExecuteDO.getId())
                .setCaseId(caseExecuteDO.getCaseId())
                .setName(caseExecuteDO.getName())
                .setExecutorName(caseExecuteDO.getExecutorName())
                .setStatus(caseExecuteDO.getStatus())
                .setStartTime(caseExecuteDO.getStartTime())
                .setEndTime(caseExecuteDO.getEndTime())
                .setDataQueryExeIds(caseExecuteDO.getDataQueryExeIds())
                .build();

    }



}
