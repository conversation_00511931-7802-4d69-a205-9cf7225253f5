package com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.FeatureStatisticInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.GetRiskEntryAndFieldsStatisticRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.GetRiskEntryAndFieldsStatisticResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KdevAccuracyMsgSendRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayBranchAndViewIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetail;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetailSimple;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetailSimpleV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureViewParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.MarkIfChainsValid;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.MarkIfChainsValidResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.PageKspayRiskFeatureListDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.PageQueryRiskFeatureStatisticRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryCheckChainsById;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryCheckChainsByIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryPageRiskFeatureListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryRiskFeatureDetailByIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryRiskFeatureDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayBranchRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayBranchRequestV2;

public interface KspayRiskFeatureBizService {
    PageKspayRiskFeatureListDTO queryPageRiskFeatureList(QueryPageRiskFeatureListRequest request);

    // 下面的queryPageRiskFeatureListOld废弃
    PageKspayRiskFeatureListDTO queryPageRiskFeatureListOld(QueryPageRiskFeatureListRequest request);

    List<KspayRiskFeatureDetail> queryRiskFeatureDetail(QueryRiskFeatureDetailRequest request);

    KspayRiskFeatureDetail queryRiskFeatureDetailById(QueryRiskFeatureDetailByIdRequest request);

    void updateRiskFeatureView(KspayRiskFeatureViewParam request);
    void updateRiskFeatureViewTestType(KspayRiskFeatureViewParam request);

    String updateRiskFeatureViewBranch(KspayRiskFeatureDetail request);

    String updateFeaturesViewBranchAllStatus(KspayRiskFeatureDetail request);

    String updateBranchAllStatusByFeatureId(KspayRiskFeatureDetail request);

    List<KspayRiskFeatureDetailSimple> queryRiskByRepoAndBranch(KspayBranchRequest request);
    List<KspayRiskFeatureDetailSimple> queryRiskByRepoAndBranchSimple(KspayBranchRequest request);
    KspayRiskFeatureDetail queryRiskByRepoAndBranchAndViewId(KspayBranchAndViewIdRequest request);

    List<KspayRiskFeatureDetailSimpleV2> queryRiskByRepoAndBranchV2(KspayBranchRequestV2 request);
    List<KspayRiskFeatureDetailSimpleV2> queryRiskByRepoAndBranchV2Simple(KspayBranchRequestV2 request, ArrayList<String> qaManagerList);

    GetRiskEntryAndFieldsStatisticResponse getKspayRiskStatistic(GetRiskEntryAndFieldsStatisticRequest request);

    // 看板统计数据接口
    List<FeatureStatisticInfo> kspayPageQueryRiskFeatureStatistic(PageQueryRiskFeatureStatisticRequest request);

    void kdevAccuracyMsgSend(KdevAccuracyMsgSendRequest request);

    //根据chainId查询核对链规则
    QueryCheckChainsByIdResponse queryChainsById(QueryCheckChainsById request);

    MarkIfChainsValidResponse markIfChainsValid(MarkIfChainsValid request);
}
