package com.kuaishou.kwaishop.qa.risk.center.domain.combine.service;

import static com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum.CENTER_TYPE;
import static com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum.DEFAULT;
import static com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum.TEAM_TYPE;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.detail.DetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.detail.DetailBaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.enums.DateTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.cache.EntityCacheService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.utils.date.LocalDateUtil;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-13
 */
public abstract class CombineAbstract<T extends DetailBaseDO, B extends CombineBO,
        P extends CombineQueryParam> {

    @Autowired
    private EntityCacheService entityCacheService;

    private Map<Long, EntityDO> centerMaps;

    private Map<Long, EntityDO> teamMaps;

    private Map<Long, List<Long>> centerTeamMaps;

    protected Map<Long, EntityDO> getCenterMaps() {
        return this.centerMaps;
    }

    protected Map<Long, EntityDO> getTeamMaps() {
        return this.teamMaps;
    }

    protected Map<Long, List<Long>> getCenterTeamMaps() {
        return this.centerTeamMaps;
    }

    protected abstract DetailDAO<T, P> getDAO();

    protected List<T> getAllSummaryData(P queryParam) {
        // 查全部的质量中心
        centerMaps = Maps.newHashMap();
        teamMaps = Maps.newHashMap();
        centerTeamMaps = Maps.newHashMap();
        List<EntityDO> centerDOS = entityCacheService.queryByEntityType(CENTER_TYPE.getCode());
        if (queryParam.getCenterId() != null && queryParam.getCenterId() > 0) {
            centerDOS = centerDOS.stream()
                    .filter(e -> e.getId().equals(queryParam.getCenterId()))
                    .collect(Collectors.toList());
        }
        // 查询全部团队
        List<EntityDO> teamDOS = entityCacheService.queryByEntityType(TEAM_TYPE.getCode());
        if (queryParam.getTeamId() != null && queryParam.getTeamId() > 0) {
            teamDOS = teamDOS.stream()
                    .filter(e -> e.getId().equals(queryParam.getTeamId()))
                    .collect(toList());
        }
        centerMaps = centerDOS.stream().collect(Maps::newHashMap, (m, v) -> m.put(v.getId(), v), Map::putAll);
        teamMaps = teamDOS.stream().collect(Maps::newHashMap, (m, v) -> m.put(v.getId(), v), Map::putAll);
        Map<Long, List<EntityDO>> teamCenterMaps = teamDOS.stream().collect(groupingBy(EntityDO::getEntityId));
        for (EntityDO centerDO: centerDOS) {
            if (teamCenterMaps.containsKey(centerDO.getId())) {
                List<Long> teamIds = teamCenterMaps.get(centerDO.getId()).stream().map(EntityDO::getId).collect(toList());
                centerTeamMaps.put(centerDO.getId(), teamIds);
                continue;
            }
            centerTeamMaps.put(centerDO.getId(), new ArrayList<>());
        }
        return getDAO().queryByCenterIdsTeamIds(centerMaps.keySet(), teamMaps.keySet(), queryParam);
    }

    public B getSummaryData(P param) {
        Integer dateType = param.getDateType();
        if (dateType == null || DateTypeEnum.of(dateType) == null) {
            dateType = DateTypeEnum.WEEK.getCode();
        }
        DateTypeEnum dateTypeEnum = DateTypeEnum.of(dateType);
        List<T> dataList = getAllSummaryData(param);
        return buildCombineBO(dataList, DEFAULT, null, dateTypeEnum);
    }

    protected Map<Long, Map<Long, List<T>>> getGroupingSummaryData(P queryParam) {
        List<T> dataList = getAllSummaryData(queryParam);
        Map<Long, Map<Long, List<T>>> dataMap = Maps.newHashMap();
        if (queryParam.getCenterId() != null && queryParam.getCenterId() > 0
                && centerMaps.containsKey(queryParam.getCenterId())) {
            dataList = dataList.stream().filter(e -> e.getCenterId().equals(queryParam.getCenterId()))
                    .collect(Collectors.toList());

            if (queryParam.getTeamId() != null && queryParam.getTeamId() > 0
                    && teamMaps.containsKey(queryParam.getTeamId())) {
                dataList = dataList.stream().filter(e -> e.getTeamId().equals(queryParam.getTeamId()))
                        .collect(Collectors.toList());
            }
        }
        List<T> resList = fillFilter(queryParam, dataList);

        // 组装结果
        for (Long centerId: centerMaps.keySet()) {
            List<Long> teamIds = centerTeamMaps.get(centerId);
            if (CollectionUtils.isEmpty(teamIds)) {
                dataMap.put(centerId, Maps.newHashMap());
                continue;
            }
            Map<Long, List<T>> teamDataMap = new HashMap<>();
            for (Long teamId: teamIds) {
                List<T> teamDataList = resList.stream().filter(
                        e -> e.getCenterId().equals(centerId)
                                && e.getTeamId().equals(teamId))
                        .collect(toList());
                teamDataMap.put(teamId, teamDataList);
            }
            dataMap.put(centerId, teamDataMap);
        }
        return dataMap;
    }

    public List<B> getGroupingByData(P param) {
        List<B> res = new ArrayList<>();
        Integer dateType = param.getDateType();
        if (dateType == null || DateTypeEnum.of(dateType) == null) {
            dateType = DateTypeEnum.WEEK.getCode();
        }
        DateTypeEnum dateTypeEnum = DateTypeEnum.of(dateType);
        Map<Long, Map<Long, List<T>>> groupMap = getGroupingSummaryData(param);
        for (Entry<Long, Map<Long, List<T>>> entry: groupMap.entrySet()) {
            Long centerId = entry.getKey();
            Map<Long, List<T>> teamInfoMap = entry.getValue();
            List<T> centerInfo = new ArrayList<>();
            List<B> teamBOS = new ArrayList<>();
            for (Entry<Long, List<T>> teamEntry: teamInfoMap.entrySet()) {
                Long teamId = teamEntry.getKey();
                List<T> teamInfo = teamEntry.getValue();
                centerInfo.addAll(teamInfo);
                B teamBO = buildCombineBO(teamInfo, TEAM_TYPE, teamId, dateTypeEnum);
                teamBOS.add(teamBO);
            }
            B centerBO = buildCombineBO(centerInfo, CENTER_TYPE, centerId, dateTypeEnum);
            centerBO.setInnerData(teamBOS);
            res.add(centerBO);
        }
        return res;
    }

    protected long getDateSize(List<T> dataList, DateTypeEnum dateTypeEnum) {
        switch (dateTypeEnum) {
            case DAY:
                dataList = dataList.stream()
                        .filter(e -> e.getCreateTime() >= LocalDateUtil.getTodayStartMilli())
                        .collect(Collectors.toList());
                break;
            case WEEK:
                dataList = dataList.stream()
                        .filter(e -> e.getCreateTime() >= LocalDateUtil.getCurrentWeekStartMilli())
                        .collect(toList());
                break;
            case MONTH:
                dataList = dataList.stream()
                        .filter(e -> e.getCreateTime() >= LocalDateUtil.getCurrentMonthStartMill())
                        .collect(toList());
                break;
            default:
        }
        return dataList.size();
    }

    protected long getRate(Long molecular, Long denominator) {
        return new BigDecimal(String.valueOf(molecular))
                .multiply(new BigDecimal("100"))
                .divide(new BigDecimal(String.valueOf(denominator)), 0, RoundingMode.DOWN)
                .longValue();
    }

    protected abstract List<T> fillFilter(P queryParam, List<T> dataList);

    protected abstract B buildCombineBO(List<T> dataList, EntityTypeEnum entityTypeEnum,
            Long id, DateTypeEnum dateTypeEnum);
}