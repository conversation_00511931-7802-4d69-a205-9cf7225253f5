package com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayQuerySymbolListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskIntelligenceFindListParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.PageKspayRiskIntelligenceFindListDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryPageRiskFieldsRequest;

public interface KspayRiskIntelligenceFindBizService {
    PageKspayRiskIntelligenceFindListDTO queryPageRiskIntelligenceFindList(QueryPageRiskFieldsRequest request);

    String updateRiskLevelForRiskFields(KspayRiskIntelligenceFindListParam request);

    List<String> querySymbolList(KspayQuerySymbolListRequest request);

}
