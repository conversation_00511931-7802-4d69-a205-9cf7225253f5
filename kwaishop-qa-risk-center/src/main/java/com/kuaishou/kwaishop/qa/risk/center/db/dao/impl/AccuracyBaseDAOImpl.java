package com.kuaishou.kwaishop.qa.risk.center.db.dao.impl;

import static com.kuaishou.kwaishop.qa.risk.center.common.exception.CommonErrorCode.DATA_QUERY_ERROR;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.infra.framework.datasource.KsMasterVisitedManager;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.DataPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.AccuracyBaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.extern.slf4j.Slf4j;


@Slf4j
public abstract class AccuracyBaseDAOImpl<T extends AccuracyBaseDO, Q extends BaseQueryCondition> {

    /**
     * 根据主键ID查询
     *
     * @param id 主键ID
     * @return DO
     */
    public T queryById(Long id) {
        if (id == null) {
            return null;
        }

        return getMapper().selectById(id);
    }

    /**
     * 根据主键ID更新传入的非空对象
     *
     * @param domainObject 更新对象
     * @return 主键ID
     */
    public int updateSelectiveById(T domainObject) {
        if (domainObject == null) {
            return 0;
        }
        if (domainObject.getId() == null) {
            throw new IllegalArgumentException("Id can't null!");
        }
        return getMapper().updateById(domainObject);
    }

    public long insert(T domainObject) {
        if (domainObject == null) {
            return 0;
        }
        // 后面接入SSO后这里需要换成从登录上下文中获取
        getMapper().insert(domainObject);
        return domainObject.getId();
    }

    public Map<Long, T> batchQueryByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Maps.newHashMap();
        }
        List<T> dataList = getMapper().selectBatchIds(ids);
        return CollectionUtils.isEmpty(dataList) ? Maps.newHashMap() : dataList.stream()
                .collect(Maps::newHashMap, (m, v) -> m.put(v.getId(), v), Map::putAll);
    }

    public long saveOrUpdate(T domainObject) {
        if (domainObject == null) {
            return 0;
        }
        if (domainObject.getId() == null || domainObject.getId() <= 0) {
            return insert(domainObject);
        }
        int cnt = updateSelectiveById(domainObject);
        return cnt <= 0 ? 0 : domainObject.getId();
    }

    protected T queryOne(Q condition) {
        return queryOne(condition, false);
    }

    /**
     * 查询一条记录
     *
     * @param condition     查询条件
     * @param needCheckMany 是否需要检查多条的情况，如果为true多条时会抛异常
     * @return 单条记录
     */
    protected T queryOne(Q condition, boolean needCheckMany) {
        List<T> list = queryList(condition);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        if (needCheckMany && list.size() > 1) {
            throw new BizException(DATA_QUERY_ERROR,
                    "Expected one result (or null) to be returned by queryOne()," + " but found:" + list.size());
        }
        return list.get(0);
    }

    /**
     * 查询多条记录
     *
     * @param condition 查询条件
     * @return 单条记录
     */
    protected List<T> queryList(Q condition) {
        if (condition == null) {
            return Lists.newArrayList();
        }
        QueryWrapper<T> queryWrapper = convertToQueryWrapper(condition);
        return getMapper().selectList(queryWrapper);
    }


    /**
     * 分页查询，页码默认从1开始
     */
    protected DataPageBO<T> queryPageList(Q condition) {
        if (condition == null || condition.getPageSize() <= 0) {
            return new DataPageBO<>(0, 0, 0L, Lists.newArrayList());
        }
        // 对页码进行容错处理
        int pageNo = condition.getPageNo() == null ? 1 : Math.max(condition.getPageNo(), 1);
        QueryWrapper<T> queryWrapper = convertToQueryWrapper(condition);
        log.info("sql = {}", queryWrapper.getSqlSelect());
        String sqlpart = queryWrapper.getCustomSqlSegment();
        log.info("customerSqlSegment = {}", sqlpart);
        Page<T> page = new Page<>(pageNo, condition.getPageSize());
        page = getMapper().selectPage(page, queryWrapper);
        return new DataPageBO<>(pageNo, condition.getPageSize(),
                page.getTotal(), page.getRecords());
    }

    /**
     * 统计满足指定条件的记录数
     */
    protected Integer count(Q condition) {
        if (condition == null) {
            return 0;
        }
        QueryWrapper<T> queryWrapper = convertToQueryWrapper(condition);
        return getMapper().selectCount(queryWrapper).intValue();
    }

    /**
     * 逻辑删除
     *
     * @param id 主键
     * @return 影响行数
     */
    public int logicDelete(Long id) {
        if (id == null) {
            return 0;
        }
        return getMapper().deleteById(id);
    }


    /**
     * 将原始查询条件转换为查询wrapper
     *
     * @param condition 原始查询条件
     * @return 查询wrapper
     */
    private QueryWrapper<T> convertToQueryWrapper(Q condition) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        if (condition.getIdNotEqual() != null && condition.getIdNotEqual() > 0) {
            queryWrapper.and(q -> q.ne("id", condition.getIdNotEqual()));
        }
        if (condition.getId() != null && condition.getId() > 0) {
            queryWrapper.and(q -> q.eq("id", condition.getId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getIds())) {
            queryWrapper.and(q -> q.in("id", condition.getIds()));
        }
        // 子类扩展
        fillQueryCondition(condition, queryWrapper);
        if (condition.getOrderByIdAsc() != null) {
            queryWrapper.orderByAsc("id");
        }
        if (condition.getOrderByIdDesc() != null) {
            queryWrapper.orderByDesc("id");
        }
        if (condition.getOrderByCreateTimeDesc() != null) {
            queryWrapper.orderByDesc("gmt_created");
        }
        // 强制读主库
        if (condition.getReadMaster() != null && condition.getReadMaster()) {
            KsMasterVisitedManager.setMasterVisited();
        }
        return queryWrapper;
    }

    /**
     * 开放给子类，用于扩展子类特有的查询条件
     *
     * @param condition    查询条件
     * @param queryWrapper 特定条件拼接在该wrapper上即可
     */
    protected abstract void fillQueryCondition(Q condition, QueryWrapper<T> queryWrapper);

    protected abstract BaseMapper<T> getMapper();

}
