package com.kuaishou.kwaishop.qa.risk.center.domain.datatool.biz;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.PropertyPlaceholderHelper;

import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.config.constants.CaseExeStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.config.constants.QueryStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.config.constants.QueryTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataCaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataCaseExecuteDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataQueryExecuteDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.DataSourceDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.CaseExecuteDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataQueryDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataSourceDo;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model.CaseExecuteBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CaseExeDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CaseExeResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.CaseExeResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.PageCaseExe;
import com.kuaishou.kwaishop.qa.risk.center.utils.kim.KimUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.kim.model.KimBotInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-23
 */
@Slf4j
@Service
public class DataCaseExeBizService {

    @Autowired
    private DataCaseExecuteDAO dataCaseExecuteDAO;

    @Autowired
    private DataCaseDAO dataCaseDAO;

    @Autowired
    private DataSourceDAO dataSourceDAO;

    @Autowired
    private DataQueryExecuteDAO dataQueryExecuteDAO;

    @Autowired
    private KwaiSqlQueryBizService kwaiSqlQueryBizService;

    @Autowired
    private DataQueryExeBizService dataQueryExeBizService;

    private static final String KIM_BOT_KEY = "data_test_message";

    public Long runDataCase(Long caseId, String operator) throws Exception {

        DataCaseDO dataCaseDO = dataCaseDAO.queryDataCaseById(caseId);

        DataQueryDO dataQueryDO = DataQueryDO.builder()
                .dataSourceId(dataCaseDO.getDataSourceId())
                .name(dataCaseDO.getName())
                .executorName(operator)
                .status(QueryStatusEnum.RUNNING.getValue())
                .build();
        dataQueryExecuteDAO.insertDataQuery(dataQueryDO);
        Long dataQueryId = dataQueryDO.getId();

        CaseExecuteDO caseExecuteDO = CaseExecuteDO.builder()
                .caseId(dataCaseDO.getId())
                .name(dataCaseDO.getName())
                .executorName(operator)
                .type(1)
                .status(CaseExeStatusEnum.RUNNING.getValue())
                .dataSourceIds(dataCaseDO.getDataSourceId().toString())
                .dataQueryExeIds(dataQueryId.toString())
                .build();

        dataCaseExecuteDAO.insertCaseExecute(caseExecuteDO);

        executeDataSource(dataQueryDO, caseExecuteDO, dataCaseDO);

        JSONObject diffQueryContent = new JSONObject(dataCaseDO.getDiffQueryContent());
        boolean isDiff = diffQueryContent.getBoolean("isDiff"); //是否对比数据源case
        if (isDiff) {
            Long diffDataSourceId = diffQueryContent.getLong("diffDataSourceId");
            JSONObject diffConfig = diffQueryContent.getJSONObject("diffConfig");
            JSONArray joinConfig = diffQueryContent.getJSONArray("joinConfig");
            DataQueryDO diffDataQueryDO = DataQueryDO.builder()
                    .dataSourceId(diffDataSourceId)
                    .name(dataCaseDO.getName())
                    .executorName(operator)
                    .status(QueryStatusEnum.RUNNING.getValue())
                    .build();
            dataQueryExecuteDAO.insertDataQuery(diffDataQueryDO);
            Long diffDataQueryId = diffDataQueryDO.getId();
            caseExecuteDO.setDataSourceIds(
                    dataCaseDO.getDataSourceId().toString() + "," + diffDataQueryDO.getDataSourceId().toString());
            caseExecuteDO.setDataQueryExeIds(dataQueryId.toString() + "," + diffDataQueryId.toString());
            dataCaseExecuteDAO.updateCaseExecute(caseExecuteDO);
            diffDataSource(dataQueryDO, caseExecuteDO, diffDataQueryDO, diffConfig, joinConfig);

        }
        if (caseExecuteDO.getStatus().equals(CaseExeStatusEnum.FAILED.getValue())) {
            KimBotInfo kimBotInfo = KimUtils.getKimBotInfo(KIM_BOT_KEY);
            Properties properties = new Properties();
            properties.setProperty("caseName", caseExecuteDO.getName());
            properties.setProperty("resultId", String.valueOf(caseExecuteDO.getId()));
            properties.setProperty("operator", KimUtils.getAtUserString(operator));
            PropertyPlaceholderHelper helper = new PropertyPlaceholderHelper("${", "}");
            String message = helper.replacePlaceholders(kimBotInfo.getMessage(), properties);
            KimUtils.sendMessageByKimMapConfig(KIM_BOT_KEY, message);
        }
        return caseExecuteDO.getId();


    }

    @Async
    public void executeDataSource(DataQueryDO dataQueryDO, CaseExecuteDO caseExecuteDO, DataCaseDO dataCaseDO) {

        DataSourceDo dataSourceDo = dataSourceDAO.queryDataSourceById(dataQueryDO.getDataSourceId());
        int queryType = dataSourceDo.getType();
        log.info(String.format("查询开始执行，queryExeId is %s.", dataQueryDO.getDataSourceId()));
        QueryTypeEnum queryTypeEnum = QueryTypeEnum.initQueryType(queryType);
        boolean sourceQueryStatus = false;
        switch (queryTypeEnum) {
            case GRPC:
                break;
            case MYSQL:
                break;
            case HIVE:
                sourceQueryStatus = kwaiSqlQueryBizService.doFetchDpData(dataSourceDo, dataQueryDO.getId());
                break;
            case CLICKHOUSE:
                sourceQueryStatus = kwaiSqlQueryBizService.doFetchDpData(dataSourceDo, dataQueryDO.getId());
                break;
            case KTABLE:
                break;
            case DRUID:
                sourceQueryStatus = kwaiSqlQueryBizService.doFetchDpData(dataSourceDo, dataQueryDO.getId());
                break;
            case DM:
                sourceQueryStatus = kwaiSqlQueryBizService.doQueryByQueryId(dataSourceDo, dataQueryDO.getId());
                break;
            case DM20:
                sourceQueryStatus = kwaiSqlQueryBizService.doDm20Query(dataSourceDo, dataQueryDO.getId());
            default:
                break;
        }
        if (!sourceQueryStatus) {
            caseExecuteDO.setStatus(CaseExeStatusEnum.FAILED.getValue());
            caseExecuteDO.setEndTime(System.currentTimeMillis());
            dataCaseExecuteDAO.updateCaseExecute(caseExecuteDO);
            return;
        }
        boolean assertResult = runAssert(dataCaseDO, dataQueryExecuteDAO.findById(dataQueryDO.getId()));
        if (assertResult) {
            caseExecuteDO.setStatus(CaseExeStatusEnum.SUCCESS.getValue());
        } else {
            caseExecuteDO.setStatus(CaseExeStatusEnum.FAILED.getValue());
        }
        caseExecuteDO.setEndTime(System.currentTimeMillis());
        dataCaseExecuteDAO.updateCaseExecute(caseExecuteDO);
    }

    @Async
    public void diffDataSource(DataQueryDO originDataQueryDO, CaseExecuteDO caseExecuteDO, DataQueryDO diffDataQueryDO,
                               JSONObject diffConfig, JSONArray joinConfig
    ) {

        DataSourceDo dataSourceDo = dataSourceDAO.queryDataSourceById(diffDataQueryDO.getDataSourceId());
        int queryType = dataSourceDo.getType();
        log.info(String.format("查询开始执行，queryExeId is %s.", diffDataQueryDO.getDataSourceId()));
        QueryTypeEnum queryTypeEnum = QueryTypeEnum.initQueryType(queryType);
        boolean sourceQueryStatus = false;
        switch (queryTypeEnum) {
            case GRPC:
                break;
            case MYSQL:
                break;
            case HIVE:
                sourceQueryStatus = kwaiSqlQueryBizService.doFetchDpData(dataSourceDo, diffDataQueryDO.getId());
                break;
            case CLICKHOUSE:
                sourceQueryStatus = kwaiSqlQueryBizService.doFetchDpData(dataSourceDo, diffDataQueryDO.getId());
                break;
            case KTABLE:
                break;
            case DRUID:
                sourceQueryStatus = kwaiSqlQueryBizService.doFetchDpData(dataSourceDo, diffDataQueryDO.getId());
                break;
            case DM:
                sourceQueryStatus = kwaiSqlQueryBizService.doQueryByQueryId(dataSourceDo, diffDataQueryDO.getId());
                break;
            case DM20:
                sourceQueryStatus = kwaiSqlQueryBizService.doDm20Query(dataSourceDo, diffDataQueryDO.getId());
            default:
                break;
        }
        Integer originQueryExeStatus = originDataQueryDO.getStatus();
        Integer diffQueryExeStatus = diffDataQueryDO.getStatus();
        Integer maxWaitingTime = 0;
        while (((QueryStatusEnum.WAITING.getValue().equals(originQueryExeStatus) || QueryStatusEnum.RUNNING
                .getValue().equals(originQueryExeStatus))
                || (QueryStatusEnum.WAITING.getValue().equals(diffQueryExeStatus) || QueryStatusEnum.RUNNING
                .getValue()
                .equals(diffQueryExeStatus))) && maxWaitingTime < 60 * 20) {
            try {
                TimeUnit.SECONDS.sleep(1);
                maxWaitingTime++;
            } catch (InterruptedException ignored) {
            }
            originDataQueryDO = dataQueryExecuteDAO.findById(originDataQueryDO.getId());
            diffDataQueryDO = dataQueryExecuteDAO.findById(diffDataQueryDO.getId());
            originQueryExeStatus = originDataQueryDO.getStatus();
            diffQueryExeStatus = diffDataQueryDO.getStatus();
        }
        if (!sourceQueryStatus) {
            caseExecuteDO.setStatus(CaseExeStatusEnum.FAILED.getValue());
            caseExecuteDO.setEndTime(System.currentTimeMillis());
            dataCaseExecuteDAO.updateCaseExecute(caseExecuteDO);
            return;
        }
        if (maxWaitingTime <= 0) {
            caseExecuteDO.setStatus(QueryStatusEnum.INTERRUPT.getValue());
        } else {
            originDataQueryDO.getResult();
            Boolean diffResult = runDiff(diffConfig, joinConfig, originDataQueryDO, diffDataQueryDO);
            if (!diffResult) {
                caseExecuteDO.setStatus(CaseExeStatusEnum.FAILED.getValue());
            }
        }
        caseExecuteDO.setEndTime(System.currentTimeMillis());
        dataCaseExecuteDAO.updateCaseExecute(caseExecuteDO);
    }

    public boolean runAssert(DataCaseDO dataCase, DataQueryDO dataQuery) {

        if (dataQuery.getResult() == null) {
            return false;
        }
        boolean assertResult = true;
        try {
            JSONObject checkContent = new JSONObject(dataCase.getCheckContent());
            JSONObject queryResult = new JSONObject(dataQuery.getResult());
            JSONArray resultRow = queryResult.getJSONArray("result_row");
            JSONArray checkPoints = checkContent.getJSONArray("checkPoint");

            JSONArray checkedResultRow = new JSONArray();
            for (int index = 0; index < resultRow.length(); index++) {
                JSONObject checkPointResult = checkAssert(resultRow.getJSONObject(index), checkPoints);
                checkedResultRow.put(checkPointResult);
                if (!checkPointResult.getBoolean("assertType")) {
                    assertResult = false;
                }
            }
            queryResult.put("assertResult", checkedResultRow);
            dataQueryExecuteDAO.updateResult(dataQuery.getId(), queryResult.toString());
        } catch (Exception e) {
            return false;
        }

        return assertResult;
    }

    public JSONObject checkAssert(JSONObject rowData, JSONArray checkPoints) throws Exception {
        List<String> faultItem = new ArrayList<>();
        boolean isPassed = true;
        for (int index = 0; index < checkPoints.length(); index++) {
            JSONObject checkPoint = checkPoints.getJSONObject(index);
            JSONArray params = checkPoint.getJSONArray("params");
            String function = checkPoint.getString("function");
            Double assertValue = checkPoint.getDouble("assertValue");
            switch (function) {
                case "isMore":
                    for (int indexParam = 0; indexParam < params.length(); indexParam++) {
                        String param = params.getString(indexParam);
                        String res = rowData.getString(param).replaceAll("\"", "");
                        Double result = Double.valueOf(res);
                        if (result < assertValue) {
                            isPassed = false;
                            faultItem.add(param);
                        }
                    }
                    break;
                case "isLess":
                    for (int indexParam = 0; indexParam < params.length(); indexParam++) {
                        String param = params.getString(indexParam);
                        Long result = rowData.getLong(param);
                        if (result > assertValue) {
                            isPassed = false;
                            faultItem.add(param);
                        }
                    }
                    break;
                default:
                    break;
            }
        }
        rowData.put("assertType", isPassed);
        rowData.put("faultItem", faultItem);
        return rowData;
    }

    public boolean runDiff(JSONObject diffConfig, JSONArray joinConfig, DataQueryDO originDataQueryDO,
                           DataQueryDO diffDataQueryDO) {
        if (originDataQueryDO.getResult() == null || diffDataQueryDO.getResult() == null) {
            return false;
        }

        if (joinConfig.length() <= 0) {
            return false;
        }
        try {
            JSONObject originQueryResult = new JSONObject(originDataQueryDO.getResult());
            JSONArray originDataResultRow = originQueryResult.getJSONArray("result_row");
            JSONObject diffQueryResult = new JSONObject(diffDataQueryDO.getResult());
            JSONArray diffDataResultRow = diffQueryResult.getJSONArray("result_row");

            List<String> originJoinItems = new ArrayList<>();
            List<String> diffJoinItems = new ArrayList<>();

            for (int index = 0; index < joinConfig.length(); index++) {
                JSONObject joinItem = joinConfig.getJSONObject(index);
                originJoinItems.add(joinItem.getString("origin"));
                diffJoinItems.add(joinItem.getString("diff"));
            }

            List<String> originDiffItems = new ArrayList<>();
            List<String> diffDiffItems = new ArrayList<>();

            for (Iterator<String> it = diffConfig.keys(); it.hasNext();) {
                String diffItem = it.next();
                if (diffConfig.getString(diffItem).length() > 0) {
                    originDiffItems.add(diffItem);
                    diffDiffItems.add(diffConfig.getString(diffItem));
                }
            }
            log.info("[DataCaseExeBizService] diff字段信息:", originDiffItems.toString(), diffDiffItems.toString());
            Boolean caseDiffResult = true;
            Map<List<String>, JSONObject> diffMap = new HashMap<>();
            for (int index = 0; index < originDataResultRow.length(); index++) {
                JSONObject originDataResult = originDataResultRow.getJSONObject(index);
                List<String> joinResult = new ArrayList<>();
                JSONObject diffResullt = new JSONObject();
                for (String originJoinItem : originJoinItems) {
                    joinResult.add(originDataResult.getString(originJoinItem));
                }
                for (String originDiffItem : originDiffItems) {
                    diffResullt.put(originDiffItem, originDataResult.getString(originDiffItem));
                }
                JSONObject diffResultObj = new JSONObject();
                diffResultObj.put("result", diffResullt);
                diffMap.put(joinResult, diffResultObj);
            }
            for (int index = 0; index < diffDataResultRow.length(); index++) {
                JSONObject diffDataResult = diffDataResultRow.getJSONObject(index);

                List<String> joinResult = new ArrayList<>();
                for (String diffJoinItem : diffJoinItems) {
                    joinResult.add(diffDataResult.getString(diffJoinItem));
                }
                if (diffMap.containsKey(joinResult)) {
                    JSONObject originData = diffMap.get(joinResult);
                    JSONObject diffResult = new JSONObject();
                    List<String> failItems = new ArrayList<>();
                    for (int i = 0; i < diffDiffItems.size(); i++) {
                        if (!diffDataResult.getString(diffDiffItems.get(i))
                                .equals(originData.getJSONObject("result").getString(originDiffItems.get(i)))) {
                            failItems.add(originDiffItems.get(i));
                            caseDiffResult = false;
                        }
                        diffResult.put(diffDiffItems.get(i), diffDataResult.getString(diffDiffItems.get(i)));
                    }
                    originData.put("failItems", failItems);
                    originData.put("diffResult", diffResult);
                }
            }
            log.info("[DataCaseExeBizService]对比详情", diffMap.toString());
            diffQueryResult.put("diffDetail", diffMap);
            dataQueryExecuteDAO.updateResult(diffDataQueryDO.getId(), diffQueryResult.toString());
            return caseDiffResult;
        } catch (Exception e) {

            return false;
        }
    }

    public boolean queryExecute(DataQueryDO dataQuery) {
        //        DataSourceDo dataSourceDo = dataSourceDAO.queryDataSourceById(dataQuery.getDataSourceId());
        //        Integer queryType = dataSourceDo.getType();
        //        switch (queryType) {
        //            case 1:
        //
        //                break;
        //            case 2:
        //
        //                break;
        //            case 7:
        //                DmQueryRequest dmQueryRequest = DmQueryRequest.newBuilder()
        //                        .build();
        //                DmQueryResponse dmQueryResponse = kwaiSqlQueryBizService.dmQuery(dmQueryRequest);
        //                List<DataMeta> dataMetaList = dmQueryResponse.getDataMetaList();
        //                List<DataResultRow> dataResultRowList = dmQueryResponse.getResultRowList();
        //                List<String> headers = new ArrayList<>();
        //                for (DataMeta dataMeta : dataMetaList) {
        //                    headers.add(dataMeta.getName());
        //                }
        //                List<Map<String, String>> resultData = new ArrayList<>();
        //
        //                for (DataResultRow dataResultRow : dataResultRowList) {
        //                    List<ResultField> resultFieldList = dataResultRow.getResultFieldList();
        //                    Map<String, String> rs = new HashMap<>();
        //                    for (int index = 0; index < resultFieldList.size(); index++) {
        //                        rs.put(headers.get(index), resultFieldList.get(index).getValue());
        //                    }
        //                    resultData.add(rs);
        //                }
        //                dataQuery.setResult(resultData.toString());
        //                dataQueryExecuteDAO.updateSelectiveById(dataQuery);
        //                break;
        //            default:
        //                break;
        //        }
        return false;
    }

    public CaseExeResultResponse queryPageCaseReports(CaseExeResultRequest request) {
        try {
            CaseExecuteBO caseExecuteBO;
            if (request.getId() <= 0) {
                caseExecuteBO = CaseExecuteBO.builder()
                        .executorName(request.getOperator())
                        .name(request.getName())
                        .caseId(request.getCaseId())
                        .pageNo(request.getPageNo())
                        .pageSize(request.getPageSize())
                        .build();
            } else {
                caseExecuteBO = CaseExecuteBO.builder()
                        .id(request.getId())
                        .executorName(request.getOperator())
                        .name(request.getName())
                        .caseId(request.getCaseId())
                        .pageNo(request.getPageNo())
                        .pageSize(request.getPageSize())
                        .build();
            }

            PageBO<CaseExecuteDO> caseExecuteDOPageBO =
                    dataCaseExecuteDAO.queryPageCaseExecuteList(caseExecuteBO);
            List<CaseExeDTO> caseExeDTOList = caseExecuteDOPageBO.getData().stream()
                    .map(this::convertToDTO).collect(Collectors.toList());
            PageCaseExe pageCaseExe = PageCaseExe.newBuilder().addAllCaseReportList(caseExeDTOList)
                    .setPageNo(caseExecuteDOPageBO.getPageNo())
                    .setPageSize(caseExecuteDOPageBO.getPageSize())
                    .setTotal(caseExecuteDOPageBO.getTotal())
                    .build();
            return CaseExeResultResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(pageCaseExe).build();

        } catch (BizException e) {
            return CaseExeResultResponse.newBuilder().setResult(e.getCode())
                    .setErrorMsg(e.getMessage()).build();
        }

    }

    private CaseExeDTO convertToDTO(CaseExecuteDO caseExecuteDO) {
        if (caseExecuteDO.getStartTime() == null) {
            caseExecuteDO.setStartTime(0L);
        }
        if (caseExecuteDO.getEndTime() == null) {
            caseExecuteDO.setEndTime(0L);
        }
        if (caseExecuteDO.getDataQueryExeIds() == null) {
            caseExecuteDO.setDataQueryExeIds("");
        }
        return CaseExeDTO.newBuilder()
                .setStartTime(caseExecuteDO.getStartTime())
                .setEndTime(caseExecuteDO.getEndTime())
                .setId(caseExecuteDO.getId())
                .setCaseId(caseExecuteDO.getCaseId())
                .setName(caseExecuteDO.getName())
                .setExecutorName(caseExecuteDO.getExecutorName())
                .setStatus(caseExecuteDO.getStatus())
                .setDataQueryExeIds(caseExecuteDO.getDataQueryExeIds())
                .build();
    }


}
