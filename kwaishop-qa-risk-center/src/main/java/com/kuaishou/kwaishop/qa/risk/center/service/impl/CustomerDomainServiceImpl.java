package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.Enum.EvaluateSceneEnum;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.UserAccountBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.EvaluateService;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.MsgService;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.SearchInfoService;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.SessionService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.BatchMsgSendC2BRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.CloseSessionRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.CloseSessionResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.ExportEvaluateDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.ExportEvaluateDataResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.ImportKlinkTokenRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.ImportKlinkTokenResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.KrpcCustomerDomainServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.MsgSendB2CRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.MsgSendB2CResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.MsgSendC2BRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.MsgSendC2BResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.PageUserKlinkLoginDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.PageUserKlinkLoginListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.PageUserKlinkLoginListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.SelectHiveRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.SelectHiveResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.UpdateKlinkLoginInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.UpdateKlinkLoginInfoResponse;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class CustomerDomainServiceImpl extends KrpcCustomerDomainServiceGrpc.CustomerDomainServiceImplBaseV2 {


    @Autowired
    private MsgService msgService;

    @Autowired
    private SearchInfoService searchInfoService;

    @Autowired
    private SessionService sessionService;

    @Autowired
    private UserAccountBizService accountBizService;

    @Autowired
    private EvaluateService evaluateService;

    @Override
    public MsgSendC2BResponse msgSendC2B(MsgSendC2BRequest request) {
        log.info("[CustomerDomainServiceImpl] msgSendC2B request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            return msgService.msgSendC2B(request);
        } catch (BizException e) {
            log.error("[CustomerDomainServiceImpl] msgSendC2B bizError, exception: ", e);
            return MsgSendC2BResponse.newBuilder().setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[CustomerDomainServiceImpl] msgSendC2B error, exception: ", e);
            return MsgSendC2BResponse.newBuilder().setErrorMsg(e.getMessage()).build();
        }
    }


    @Override
    public MsgSendC2BResponse batchMsgSendC2B(BatchMsgSendC2BRequest request) {
        log.info("[CustomerDomainServiceImpl] batchMsgSendC2B request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            return msgService.batchMsgSendC2B(request);
        } catch (BizException e) {
            log.error("[CustomerDomainServiceImpl] batchMsgSendC2B bizError, exception: ", e);
            return MsgSendC2BResponse.newBuilder().setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[CustomerDomainServiceImpl] batchMsgSendC2B error, exception: ", e);
            return MsgSendC2BResponse.newBuilder().setErrorMsg(e.getMessage()).build();
        }
    }

    @Override
    public MsgSendB2CResponse msgSendB2C(MsgSendB2CRequest request) {
        log.info("[CustomerDomainServiceImpl] msgSendB2C request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            return msgService.msgSendB2C(request);
        } catch (BizException e) {
            log.error("[CustomerDomainServiceImpl] msgSendB2C bizError, exception: ", e);
            return MsgSendB2CResponse.newBuilder().setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[CustomerDomainServiceImpl] msgSendB2C error, exception: ", e);
            return MsgSendB2CResponse.newBuilder().setErrorMsg(e.getMessage()).build();
        }
    }

    @Override
    public CloseSessionResponse closeSession(CloseSessionRequest request) {
        log.info("[CustomerDomainServiceImpl] sessionClose request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            return sessionService.closeSession(request);
        } catch (BizException e) {
            log.error("[CustomerDomainServiceImpl] sessionClose bizError, exception: ", e);
            return CloseSessionResponse.newBuilder().setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[CustomerDomainServiceImpl] sessionClose error, exception: ", e);
            return CloseSessionResponse.newBuilder().setErrorMsg(e.getMessage()).build();
        }

    }

    @Override
    public PageUserKlinkLoginListResponse queryUserKlinkLoginInfo(PageUserKlinkLoginListRequest request) {
        log.info("[CustomerDomainServiceImpl] queryUserKlinkLoginInfo request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            PageUserKlinkLoginDTO res = searchInfoService.queryUserKlinkLoginInfo(request);
            return PageUserKlinkLoginListResponse.newBuilder()
                    .setData(res)
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[CustomerDomainServiceImpl] queryUserKlinkLoginInfo bizError ,exception:", e);
            return PageUserKlinkLoginListResponse.newBuilder().setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[CustomerDomainServiceImpl] queryUserKlinkLoginInfo error ,exception:", e);
            return PageUserKlinkLoginListResponse.newBuilder().setErrorMsg(e.getMessage()).build();
        }

    }

    @Override
    public UpdateKlinkLoginInfoResponse updateUserKlinkLoginInfo(UpdateKlinkLoginInfoRequest request) {
        log.info("[CustomerDomainServiceImpl] updateKlinkLoginInfo request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            searchInfoService.updateUserKlinkLoginInfo(request);
            return UpdateKlinkLoginInfoResponse.newBuilder().setResult(BaseResultCode.SUCCESS_VALUE).build();
        } catch (BizException e) {
            log.error("[CustomerDomainServiceImpl] updateKlinkLoginInfo bizError ,exception:", e);
            return UpdateKlinkLoginInfoResponse.newBuilder().setErrorMsg(e.getMessage()).build();
        } catch (Exception e) {
            log.error("[CustomerDomainServiceImpl] updateKlinkLoginInfo error ,exception:", e);
            return UpdateKlinkLoginInfoResponse.newBuilder().setErrorMsg(e.getMessage()).build();
        }

    }

    @Override
    public ImportKlinkTokenResponse importKlinkToken(ImportKlinkTokenRequest request) {
        log.info("[AccountDomainServiceImpl] ImportKlinkToken request: {}", protoToJsonString(request));
        try {
            accountBizService.importKlinkToken(request);
            return ImportKlinkTokenResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .buildPartial();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] ImportKlinkToken bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ImportKlinkTokenResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] ImportKlinkToken error, req: {}, exception: ", protoToJsonString(request), e);
            return ImportKlinkTokenResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    @Override
    public ExportEvaluateDataResponse exportEvaluateData(ExportEvaluateDataRequest request) {
        log.info("[CustomerDomainServiceImpl] exportEvaluateData request: {}", protoToJsonString(request));
        try {
            if (request.getScene() == EvaluateSceneEnum.LOGISTIC.getCode()) {
                evaluateService.exportEvaluateDataLogistic(request.getStartTime(), request.getEndTime(), request.getPageIndex());
            } else if (request.getScene() == EvaluateSceneEnum.PLAN.getCode()) {
                evaluateService.exportEvaluateDataPlan(request.getStartTime(), request.getEndTime(), request.getPageIndex());
            }
            return ExportEvaluateDataResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .buildPartial();
        } catch (BizException e) {
            log.error("[CustomerDomainServiceImpl] exportEvaluateData bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ExportEvaluateDataResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[CustomerDomainServiceImpl] exportEvaluateData error, req: {}, exception: ", protoToJsonString(request), e);
            return ExportEvaluateDataResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public SelectHiveResponse selectHive(SelectHiveRequest request) {
        log.info("[CustomerDomainServiceImpl] selectHive request: {}", protoToJsonString(request));
        try {
            evaluateService.selectHive(request.getPDate(), request.getLimit(), request.getOffset());
            return SelectHiveResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .buildPartial();
        } catch (BizException e) {
            log.error("[CustomerDomainServiceImpl] selectHive bizError, req: {}, exception: ", protoToJsonString(request), e);
            return SelectHiveResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[CustomerDomainServiceImpl] selectHive error, req: {}, exception: ", protoToJsonString(request), e);
            return SelectHiveResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

}
