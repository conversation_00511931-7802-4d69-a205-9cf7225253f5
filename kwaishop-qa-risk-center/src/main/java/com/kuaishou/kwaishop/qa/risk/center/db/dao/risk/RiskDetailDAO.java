package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.detail.DetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.RiskDetailDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.bo.RiskDetailBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-20
 */
public interface RiskDetailDAO extends DetailDAO<RiskDetailDO, CombineQueryParam> {

    long insert(RiskDetailDO riskDetailDO);

    RiskDetailDO queryById(Long id);

    int updateSelectiveById(RiskDetailDO riskDetailDO);

    int logicDeleted(Long id, String operator);

    List<RiskDetailDO> queryRiskDetailList(RiskDetailBO riskDetailBO);

    PageBO<RiskDetailDO> queryPageRiskDetailList(RiskDetailBO riskDetailBO);
}
