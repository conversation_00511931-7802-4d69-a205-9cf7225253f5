package com.kuaishou.kwaishop.qa.risk.center.domain.account.convert;

import java.util.Map;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAuthDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAuthBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.AccountRentalDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryPageUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UserAuthDTO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-23
 */
public interface UserAuthConvert {
    UserAuthDO buildUserAuthDOByLego(UserAccountDO userAccountDO);


    UserAuthDO parseUserAuth(String userAuthResultsString);

    AccountRentalDTO buildRentalDTOByUserAuthDTO(AccountRentalDTO rentalDTO, Map<Long, UserAuthDO> map, Map<Long, UserAccountDO> tokenMap);

    UserAuthDTO buildUserAuthDTO(UserAuthDO userAuthDO);

    UserAuthBO buildUserAuthBOByRequest(QueryPageUserAccountRequest request);
}
