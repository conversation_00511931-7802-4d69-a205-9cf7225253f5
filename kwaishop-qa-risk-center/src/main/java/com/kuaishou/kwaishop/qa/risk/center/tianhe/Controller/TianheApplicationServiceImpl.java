package com.kuaishou.kwaishop.qa.risk.center.tianhe.Controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AddApplicationRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AddApplicationResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteApplicationRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteApplicationResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.KrpcTianheApplicationServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListApplicationRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListApplicationResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateApplicationRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateApplicationResponse;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Service.TianheApplicationService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-19
 */
@Slf4j
@Service
@KrpcService(server = "custom-servers-config", serviceName = "kwaishop-qa-risk-center")
public class TianheApplicationServiceImpl extends KrpcTianheApplicationServiceGrpc.TianheApplicationServiceImplBaseV2 {
    @Autowired
    private TianheApplicationService tianheApplicationService;

    @Override
    public AddApplicationResponse addApplication(AddApplicationRequest request) {
        return tianheApplicationService.addApplication(request);
    }

    @Override
    public ListApplicationResponse listApplication(ListApplicationRequest request) {
        return tianheApplicationService.listApplication(request);
    }

    @Override
    public UpdateApplicationResponse updateApplication(UpdateApplicationRequest request) {
        return tianheApplicationService.updateApplication(request);
    }

    @Override
    public DeleteApplicationResponse deleteApplication(DeleteApplicationRequest request) {
        return tianheApplicationService.deleteApplication(request);
    }

}
