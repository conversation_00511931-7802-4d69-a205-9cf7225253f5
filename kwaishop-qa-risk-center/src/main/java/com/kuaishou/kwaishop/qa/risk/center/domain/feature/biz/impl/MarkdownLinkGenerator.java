package com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz.impl;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

public class MarkdownLinkGenerator {

    public static String generateMarkdownLink(String featureName, String cleanedKimWorker, String cleanedHasRisk,
                                              String cleanedConvertedStatus, String cleanedUpdateTime) {
        try {
            // URL编码
            String encodedFeatureName = URLEncoder.encode(featureName, StandardCharsets.UTF_8.toString());

            // 创建Markdown链接
            String markdownLink = String.format("| [%s](https://kwaishop-risk.staging.kuaishou.com/kspay/kspayRiskFeatureQuery?featureName=%s)"
                            + "|%s|%s|%s|%s| \n",
                    encodedFeatureName, encodedFeatureName, cleanedKimWorker, cleanedHasRisk, cleanedConvertedStatus, cleanedUpdateTime);

            // 检查是否需要转义Markdown特殊字符
            if (isValidMarkdownLink(markdownLink)) {
                return markdownLink;
            } else {
                // 转义Markdown特殊字符
                encodedFeatureName = escapeMarkdownSpecialChars(encodedFeatureName);
                return String.format("| [%s](https://kwaishop-risk.staging.kuaishou.com/kspay/kspayRiskFeatureQuery?featureName=%s)"
                                + "|%s|%s|%s|%s| \n",
                        encodedFeatureName, encodedFeatureName, cleanedKimWorker, cleanedHasRisk, cleanedConvertedStatus, cleanedUpdateTime);
            }
        } catch (Exception e) {
            throw new RuntimeException("Error generating markdown link", e);
        }
    }

    private static boolean isValidMarkdownLink(String markdownLink) {
        // 简单检查：如果链接中包含未转义的方括号，则认为无效
        return !markdownLink.contains("[") && !markdownLink.contains("]");
    }

    private static String escapeMarkdownSpecialChars(String text) {
        // 转义Markdown特殊字符：方括号、星号、下划线、反引号
        return text.replace("[", "\\[").replace("]", "\\]")
                .replace("*", "\\*").replace("_", "\\_")
                .replace("`", "\\`");
    }
}