package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-29
 */
@Data
@Builder
public class DiffResult {

    private String paramJson; // 这个放请求参数

    private String expect; //这里放预期结果

    private String actual; //这里放实际结果

    private String sceneKey; // 这个放场景key

    private String reason; // 这个放原因
}
