package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.DataBaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool.ScenarioCaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.ScenarioCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.datatool.ScenarioCaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.datatool.ScenarioCaseQueryCondition;

/**
 * <AUTHOR> <zhang<PERSON><EMAIL>>
 * Create on 2023-03-15
 */
@Repository
public class ScenarioCaseDAOImpl extends DataBaseDAO<ScenarioCaseDO, ScenarioCaseQueryCondition> implements ScenarioCaseDAO {

    @Autowired
    private ScenarioCaseMapper scenarioCaseMapper;



    @Override
    protected void fillQueryCondition(ScenarioCaseQueryCondition condition, QueryWrapper<ScenarioCaseDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getCreatorName())) {
            queryWrapper.eq("creator_name", condition.getCreatorName());
        }
    }

    @Override
    protected BaseMapper<ScenarioCaseDO> getMapper() {
        return scenarioCaseMapper;
    }

    @Override
    public long insertScenarioCase(ScenarioCaseDO scenarioCaseDO) {
        scenarioCaseDO.setCreateTime(System.currentTimeMillis());
        scenarioCaseDO.setUpdateTime(System.currentTimeMillis());
        return scenarioCaseMapper.insert(scenarioCaseDO);
    }

    @Override
    public long updateScenarioCase(ScenarioCaseDO scenarioCaseDO) {
        return scenarioCaseMapper.updateById(scenarioCaseDO);
    }

    @Override
    public int logicDeleted(Long id, String operator) {
        return scenarioCaseMapper.logicDeleted(operator, id);
    }

    @Override
    public List<Long> queryCaseIdList(Long scenarioId) {
        return scenarioCaseMapper.queryCaseIdsByScenarioId(scenarioId);
    }

    @Override
    public List<ScenarioCaseDO> queryScenarioCaseList(ScenarioCaseDO scenarioCaseDO) {
        return null;
    }

    @Override
    public Long queryIdByScenarioIdCaseId(Long scenarioId, Long caseId) {
        return scenarioCaseMapper.queryIdByScenarioIdCaseId(scenarioId, caseId);
    }

    @Override
    public ScenarioCaseDO queryScenarioCaseById(Long id) {
        return null;
    }
}
