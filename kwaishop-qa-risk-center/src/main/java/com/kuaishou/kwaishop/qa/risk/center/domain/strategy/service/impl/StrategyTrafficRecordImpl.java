package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kuaishou.intown.json.JSON;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.dao.StrategyTrafficRecordDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.dao.TaskDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecordListStatus;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyRecordListDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTestDataPool;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTrafficRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.StrategyTrafficRecordService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.PageRecordTaskDetailDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.QueryPageRecordTaskRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StrategyRecordListDTO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-26
 */
@Service
public class StrategyTrafficRecordImpl implements StrategyTrafficRecordService {

    @Autowired
    private StrategyTrafficRecordDAOImpl strategyTrafficRecordDAOImpl;

   @Autowired
   private TaskDAOImpl taskDAO;

    @Override
    public void insertData(List<StrategyTrafficRecordDO> strategyTrafficRecordDOS) {
        strategyTrafficRecordDAOImpl.batchInsert(strategyTrafficRecordDOS);
    }

    @Override
    public void insert(StrategyTrafficRecordDO strategyTrafficRecordDO) {
        strategyTrafficRecordDAOImpl.insert(strategyTrafficRecordDO);
    }

    @Override
    public void updateStatus(String taskId, int status) {
        strategyTrafficRecordDAOImpl.updateStatus(StrategyRecordListDO.builder()
                        .taskId(Long.valueOf(taskId))
                        .updateTime(System.currentTimeMillis())
                        .status(status)
                .build());
    }

    @Override
    public void insertRecord(StrategyRecordListDO record) {
        strategyTrafficRecordDAOImpl.insertRecord(record);
    }

    @Override
    public PageBO<StrategyTrafficRecordDO> queryReplayDetail(ReplayDetailRequest request) {
        return strategyTrafficRecordDAOImpl.queryPageList(request);
    }

    @Override
    public void updateDiffInfo(List<StrategyTestDataPool> dataPoolParams) {
        strategyTrafficRecordDAOImpl.updateDiffInfo(buildStrategyTrafficRecordDO(dataPoolParams));
    }

    @Override
    public String queryTrafficData(String caseId) {
        return strategyTrafficRecordDAOImpl.queryTrafficData(caseId);
    }

    @Override
    public String queryTaskIdByCaseId(Long caseId) {
        return strategyTrafficRecordDAOImpl.queryTaskIdByCaseId(caseId);
    }

    @Override
    public List<StrategyTrafficRecordDO> queryAllCaseByTaskId(String taskId) {
        return strategyTrafficRecordDAOImpl.queryAllCaseByTaskId(taskId);
    }


    @Autowired
    private TaskDAOImpl strategyRecordListDAO;

    public PageRecordTaskDetailDTO queryPage(QueryPageRecordTaskRequest request) {
        Page<StrategyRecordListDO> page = strategyRecordListDAO.queryPage(request);
        List<StrategyRecordListDTO> collect = page.getRecords().stream().map(this::convertToDTO).collect(Collectors.toList());

        return PageRecordTaskDetailDTO.newBuilder()
                .setTotal(page.getTotal())
                .setPageNo(request.getPageNo())
                .setPageSize(request.getPageSize())
                .addAllDetails(collect)
                .build();
    }

    private StrategyRecordListDTO convertToDTO(StrategyRecordListDO doObj) {

        return StrategyRecordListDTO.newBuilder()
                .setId(doObj.getId())
                .setCreator(doObj.getCreator())
                .setSceneName(doObj.getSceneName())
                .setRecordPartition(doObj.getRecordPartition())
                .setStatus(RecordListStatus.fromCode(doObj.getStatus()).getDescription())
                .setCreateTime(doObj.getCreateTime())
                .setUpdateTime(doObj.getUpdateTime())
                .setTaskId(doObj.getTaskId())
                .setRecordSql(doObj.getRecordSql() == null ? "" : doObj.getRecordSql())
                .build();
    }

    public List<StrategyTrafficRecordDO> buildStrategyTrafficRecordDO(List<StrategyTestDataPool> dataPoolParams) {
        List<StrategyTrafficRecordDO> strategyTrafficRecordDOS = new ArrayList<>();
        for (StrategyTestDataPool dataPoolParam : dataPoolParams) {
            strategyTrafficRecordDOS.add(StrategyTrafficRecordDO.builder()
                            .actualResult(listsToJsonString("actionIds", dataPoolParam.getActualActionIds(),
                                    "strategyIds", dataPoolParam.getActualStrategyIds()))
                            .diffConclusion(dataPoolParam.getDiffConclusion())
                            .replayTime(dataPoolParam.getReplayTime())
                            .id(Long.valueOf(dataPoolParam.getId()))
                            .replayLaneId(dataPoolParam.getReplayLaneId())
                    .build());
        }
        return strategyTrafficRecordDOS;
    }

    public String listsToJsonString(String key1, List<String> list1, String key2, List<String> list2) {
        // 创建一个包含这两个列表的Map
        Map<String, Object> map = new HashMap<>();
        map.put(key1, list1);
        map.put(key2, list2);

        // 使用Fastjson将Map转换为JSON字符串

        return JSON.toJSONString(map);
    }

}
