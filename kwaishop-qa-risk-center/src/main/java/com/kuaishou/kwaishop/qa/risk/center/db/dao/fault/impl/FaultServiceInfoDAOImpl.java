package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.DataPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.FaultServiceInfoDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.AccuracyBaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultServiceInfoDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.fault.FaultServiceInfoMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.fault.FaultServiceInfoQueryCondition;

import lombok.extern.slf4j.Slf4j;


@Repository
@Slf4j
public class FaultServiceInfoDAOImpl extends AccuracyBaseDAOImpl<FaultServiceInfoDO, FaultServiceInfoQueryCondition> implements FaultServiceInfoDAO {

    @Autowired
    private FaultServiceInfoMapper faultServiceInfoMapper;

    @Override
    public List<FaultServiceInfoDO> getBaseSrcMethod(String srcService) {
        FaultServiceInfoQueryCondition queryCondition = FaultServiceInfoQueryCondition
                .builder()
                .srcService(srcService)
                .build();
        return queryList(queryCondition);

    }

    @Override
    public FaultServiceInfoDO queryFaultServiceInfo(FaultServiceInfoQueryCondition condition) {
        return queryOne(condition);
    }

    @Override
    public long insertOrUpdateServiceInfo(FaultServiceInfoDO infoDO) {
        try {
            return saveOrUpdate(infoDO);
        } catch (Exception ex) {
            log.error("saveOrUpdate error", ex);
            return 0;
        }
    }

    @Override
    public FaultServiceInfoDO queryFaultServiceInfoById(long id) {
        try {
            return queryById(id);
        } catch (Exception ex) {
            log.error("queryFaultServiceInfoById error", ex);
            return null;
        }

    }

    @Override
    public DataPageBO<FaultServiceInfoDO> queryRecommendSrcMethod(FaultServiceInfoQueryCondition condition) {
        return queryPageList(condition);
    }

    @Override
    protected void fillQueryCondition(FaultServiceInfoQueryCondition condition,
                                      QueryWrapper<FaultServiceInfoDO> queryWrapper) {
        if (condition.getSrcService() != null && StringUtils.isNotBlank(condition.getSrcService())) {
            queryWrapper.and(q -> q.eq("src_service", condition.getSrcService()));
        }
        if (condition.getSrcMethod() != null && StringUtils.isNotBlank(condition.getSrcMethod())) {
            queryWrapper.and(q -> q.eq("src_method", condition.getSrcMethod()));
        }
        if (condition.getType() != null) {
            queryWrapper.and(q -> q.eq("type", condition.getType()));
        }
    }

    @Override
    protected BaseMapper<FaultServiceInfoDO> getMapper() {
        return faultServiceInfoMapper;
    }
}
