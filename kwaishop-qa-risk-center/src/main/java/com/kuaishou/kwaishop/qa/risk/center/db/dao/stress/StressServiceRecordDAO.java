package com.kuaishou.kwaishop.qa.risk.center.db.dao.stress;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressServiceRecordBO;

public interface StressServiceRecordDAO {
    PageBO<StressServiceRecordDO> queryStressSerRecordList(QueryStressServiceRecordBO bo);
    StressServiceRecordDO queryStressSerRecordById(Long id);
    List<StressServiceRecordDO> queryStressSerRecords(QueryStressServiceRecordBO queryBO);
}
