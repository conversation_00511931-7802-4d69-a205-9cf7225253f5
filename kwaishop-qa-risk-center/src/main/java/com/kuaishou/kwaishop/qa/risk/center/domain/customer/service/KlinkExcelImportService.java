package com.kuaishou.kwaishop.qa.risk.center.domain.customer.service;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.klink.UserKlinkLoginDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportTokenExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.BaseExcelModel;

public interface KlinkExcelImportService {

    List<UserKlinkLoginDO> getKlinkLoginDOList(ImportTokenExcelBO excelBO);

    List<UserKlinkLoginDO> buildKlinkLoginDOList(List<BaseExcelModel> excelModels, ImportTokenExcelBO excelBO);

}
