package com.kuaishou.kwaishop.qa.risk.center.domain.customer.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.old.exception.ServiceException;

/**
 * <AUTHOR> <<EMAIL>>
 * <p>
 * 接口规范：https://docs.corp.kuaishou.com/d/home/<USER>
 *
 * ResultCodeUtil会打error日志,在他们完成变更
 */
public class Result {

    private static final String DEFAULT_ERROR_MESSAGE = "服务器繁忙，请稍后再试";

    private long result;

    @JsonProperty(value = "error_msg")
    private String errorMsg;

    private Object data;

    public Result() {

    }

    public static Result ok() {
        Result result = new Result();
        result.setResult(BaseResultCode.SUCCESS_VALUE);
        return result;
    }

    public static Result ok(Object data) {
        Result result = ok();
        result.setData(data);
        return result;
    }

    public static Result error(BaseResultCode resultCode) {
        Result result = new Result();
        result.setResult(resultCode.getNumber());
        return result;
    }

    public static Result error(BaseResultCode resultCode, String msg) {
        Result result = error(resultCode);
        result.setErrorMsg(msg);
        return result;
    }

    public static Result error(BaseResultCode resultCode, String msg, Object data) {
        Result result = error(resultCode);
        result.setErrorMsg(msg);
        result.setData(data);
        return result;
    }

    public static Result error(ResultCode resultCode) {
        Result result = new Result();
        result.setResult(resultCode.getCode());
        result.setErrorMsg(resultCode.getMessage());
        return result;
    }

    public static Result error(ResultCode resultCode, String msg) {
        Result result = error(resultCode);
        result.setErrorMsg(msg);
        return result;
    }

    public static Result error(ResultCode resultCode, String msg, Object data) {
        Result result = error(resultCode);
        result.setErrorMsg(msg);
        result.setData(data);
        return result;
    }

    public static Result error(int resultCode, String msg) {
        Result result = new Result();
        result.setResult(resultCode);
        result.setErrorMsg(msg);
        return result;
    }

    public static Result error(Exception e) {
        if (e instanceof ServiceException) {
            ServiceException serviceException = (ServiceException) e;
            return Result.error(serviceException.getCode(), serviceException.getOverrideMessage());
        }
        return Result.error(BaseResultCode.SERVER_ERROR, DEFAULT_ERROR_MESSAGE);
    }

    public long getResult() {
        return result;
    }

    public void setResult(long result) {
        this.result = result;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

}
