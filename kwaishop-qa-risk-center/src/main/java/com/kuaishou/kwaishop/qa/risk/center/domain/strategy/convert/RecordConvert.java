package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert;

import java.sql.SQLException;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import com.google.api.client.util.Lists;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.kuaishou.kconf.common.json.JsonMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.common.Enum.DeletedEnum;
import com.kuaishou.kwaishop.qa.risk.center.common.Enum.FlowStatus;
import com.kuaishou.kwaishop.qa.risk.center.common.Enum.FlowType;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordExtraDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordListRequestPojo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordStartEndPDate;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.RecordKafkaDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowRecordBo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowRecordDo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.util.StringEscapeUtils;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordAddRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordDetailListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowRecordListRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-19
 */
@Slf4j
public class RecordConvert {

    public static StrategyFlowRecordDo addRequest2Do(FlowRecordAddRequest request) {
        long currentTimeMillis = System.currentTimeMillis();
        StrategyFlowRecordDo recordDo = new StrategyFlowRecordDo();
        recordDo.setName(request.getName());
        recordDo.setDescription(request.getDescription());
        if (FlowType.STRATEGY_LOG.getCode() == request.getFlowType()) {
            recordDo.setFlowType(request.getFlowType());
            FlowRecordExtraDto extraDto = new FlowRecordExtraDto();
            extraDto.setPDatePartitionList(request.getStrategyLog().getPDatePartitionList());
            extraDto.setPHourMinPartitionList(request.getStrategyLog().getPHourMinPartitionList());
            extraDto.setHitStrategyList(request.getStrategyLog().getHitStrategyList());
            extraDto.setHitActionList(request.getStrategyLog().getHitActionList());
            extraDto.setRequestHitResult(request.getStrategyLog().getRequestHitResult());
            extraDto.setWhereClause(request.getStrategyLog().getWhereClause());

            recordDo.setSceneKeyList(Joiner.on(",").join(request.getStrategyLog().getSceneKeyListList()));
            recordDo.setTotal(request.getStrategyLog().getTotal());
            recordDo.setExtra(JsonMapperUtils.toJson(extraDto));
        }
        recordDo.setFlowType(request.getFlowType());
        recordDo.setFlowStatus(FlowStatus.PROCESSING.getCode());
        recordDo.setFlowStartTime(currentTimeMillis);
        recordDo.setCreateTime(currentTimeMillis);
        recordDo.setUpdateTime(currentTimeMillis);
        recordDo.setCreateUser(request.getOperator());
        recordDo.setUpdateUser(request.getOperator());
        recordDo.setDeleted(DeletedEnum.VALID.getCode());
        if (FlowType.REAL_TIME.getCode() == recordDo.getFlowType()) {
            recordDo.setSceneKeyList(Joiner.on(",").join(request.getStrategyLog().getSceneKeyListList()));
        }

        recordDo.setTotal(request.getStrategyLog().getTotal());
        return recordDo;
    }

    public static FlowRecordListRequestPojo listRequest2Pojo(FlowRecordListRequest request) {
        FlowRecordListRequestPojo requestPojo = new FlowRecordListRequestPojo();
        int limit = request.getPageSize();
        int offset = request.getPageSize() * (request.getPageNum() - 1);
        requestPojo.setLimit(limit);
        requestPojo.setOffset(offset);
        if (request.getId() != NumberUtils.LONG_ZERO) {
            requestPojo.setId(request.getId());
        }
        if (StringUtils.isNotBlank(request.getName())) {
            requestPojo.setName(request.getName());
        }
        if (StringUtils.isNotBlank(request.getSceneKey())) {
            requestPojo.setSceneKey(request.getSceneKey());
        }
        if (request.getFlowStatus() != 0) {
            requestPojo.setFlowStatus(request.getFlowStatus());
        }
        if (request.getFlowType() != 0) {
            requestPojo.setFlowType(request.getFlowType());
        }
        if (StringUtils.isNotBlank(request.getCreateUser())) {
            requestPojo.setCreateUser(request.getCreateUser());
        }

        return requestPojo;
    }

    public static List<StrategyFlowRecordBo> doList2BoList(List<StrategyFlowRecordDo> recordDoList) {
        return recordDoList.stream().map(RecordConvert::do2Bo).collect(java.util.stream.Collectors.toList());
    }

    public static StrategyFlowRecordBo do2Bo(StrategyFlowRecordDo recordDo) {
        StrategyFlowRecordBo recordBo = new StrategyFlowRecordBo();
        log.info("recordDo = {}", JsonMapperUtils.toJson(recordDo));
        recordBo.setId(recordDo.getId());
        recordBo.setName(recordDo.getName());
        recordBo.setDescription(recordDo.getDescription());
        recordBo.setFlowType(recordDo.getFlowType());
        recordBo.setFlowTypeDesc(FlowType.getEnumByCode(recordDo.getFlowType()).getDesc());
        recordBo.setSceneKeyList(
                Optional.ofNullable(recordDo.getSceneKeyList())
                        .map(sceneKeyList -> Splitter.on(",").splitToList(sceneKeyList))
                        .orElse(Collections.emptyList())
        );
        if (FlowType.REAL_TIME.getCode() == recordDo.getFlowType()) {
            recordBo.setTotal(-1);
        } else {
            recordBo.setTotal(recordDo.getTotal());
        }
        recordBo.setFlowStatus(recordDo.getFlowStatus());
        if (FlowStatus.PROCESSING.getCode() == recordDo.getFlowStatus()) {
            if (StringUtils.isNotBlank(recordDo.getExtra())) {
                recordBo.setFlowStatusDesc("录制中");
            }
        } else if (FlowStatus.FINISHED.getCode() == recordDo.getFlowStatus() && (
                FlowType.STRATEGY_LOG.getCode() == recordDo.getFlowType()
                        || FlowType.CUSTOM_HIVE.getCode() == recordDo.getFlowType())) {
            String extra = recordDo.getExtra();
            FlowRecordExtraDto extraDto = Optional.ofNullable(extra)
                    .filter(e -> !e.isEmpty())
                    .map(e -> JsonMapperUtils.fromJson(e, FlowRecordExtraDto.class))
                    .orElse(new FlowRecordExtraDto());
            String desc;
            if (Objects.isNull(extraDto.getRecordSize())) {
                desc = "录制完成";
            } else {
                desc = "录制完成(" + extraDto.getRecordSize() + "/" + recordDo.getTotal() + ")";
            }
            recordBo.setFlowStatusDesc(desc);
        } else if (FlowStatus.FINISHED.getCode() == recordDo.getFlowStatus()
                && FlowType.REAL_TIME.getCode() == recordDo.getFlowType()) {
            recordBo.setFlowStatusDesc("录制完成");
        }
        recordBo.setFlowStartTime(recordDo.getFlowStartTime());
        recordBo.setFlowEndTime(recordDo.getFlowEndTime());
        recordBo.setCreateUser(recordDo.getCreateUser());
        recordBo.setCreateTime(recordDo.getCreateTime());
        recordBo.setUpdateUser(recordDo.getUpdateUser());
        recordBo.setUpdateTime(recordDo.getUpdateTime());
        recordBo.setDeleted(recordDo.getDeleted());
        return recordBo;
    }

    public static List<RecordKafkaDto> hiveResultList2KafkaDtos(List<Map<String, Object>> list) {
        return list.stream().map(RecordConvert::hiveResult2KafkaDto).collect(java.util.stream.Collectors.toList());
    }

    public static RecordKafkaDto hiveResult2KafkaDto(Map<String, Object> result) {
        RecordKafkaDto dto = new RecordKafkaDto();
        dto.setId(UUID.randomUUID().toString());
        dto.setFlowRecordId(Long.parseLong((String) result.get("flow_record_id")));
        dto.setTraceId((String) result.get("trace_id"));
        dto.setSceneKey((String) result.get("scene_key"));
        dto.setSceneType((String) result.get("scene_type"));
        dto.setFeatureKeyMap((String) result.get("feature_key_map"));
        dto.setRequestHitResult(Boolean.valueOf((String) result.get("request_hit_result")));

        List<String> hitActionList = Lists.newArrayList();
        List<String> hitStrategyList = Lists.newArrayList();
        String dagNodeMapStr = (String) result.get("dag_node_map");
        Map<String, Object> dagNodeMap = JsonMapperUtils.fromJson(dagNodeMapStr);
        Map<String, Boolean> actionNodeMap = (Map<String, Boolean>) dagNodeMap.get("ACTION_NODE");
        Map<String, Boolean> strategyNodeMap = (Map<String, Boolean>) dagNodeMap.get("STRATEGY_NODE");
        if (MapUtils.isNotEmpty(actionNodeMap)) {
            for (Entry<String, Boolean> entry : actionNodeMap.entrySet()) {
                if (entry.getValue()) {
                    hitActionList.add(entry.getKey());
                }
            }
        }
        if (MapUtils.isNotEmpty(strategyNodeMap)) {
            for (Entry<String, Boolean> entry : strategyNodeMap.entrySet()) {
                if (entry.getValue()) {
                    hitStrategyList.add(entry.getKey());
                }
            }
        }
        dto.setHitActionList(hitActionList);
        dto.setHitStrategyList(hitStrategyList);

        dto.setConditionExecuteDetailInfo((String) result.get("condition_exec_detail_info"));
        dto.setRuleExecuteDetailInfo((String) result.get("rule_exec_detail_info"));

        dto.setBizKeyInfo((String) result.get("biz_key_info"));
        dto.setRecordCreateTime((String) result.get("create_time"));
        dto.setRecordEndTime((String) result.get("end_time"));
        dto.setCreateTime(String.valueOf(System.currentTimeMillis()));
        dto.setRecordPDate((String) result.get("p_date"));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = sdf.format(new Date(System.currentTimeMillis()));
        dto.setPDate(formattedDate);

        return dto;
    }

    public static List<RecordKafkaDto> hiveResultList2KafkaDtos(List<Map<String, Object>> list, long flowRecordId) {
        return list.stream().map(x -> RecordConvert.hiveResult2KafkaDto(x, flowRecordId))
                .collect(java.util.stream.Collectors.toList());
    }

    public static RecordKafkaDto hiveResult2KafkaDto(Map<String, Object> result, long flowRecordId) {
        RecordKafkaDto dto = new RecordKafkaDto();
        dto.setId(UUID.randomUUID().toString());
        dto.setFlowRecordId(flowRecordId);
        dto.setTraceId((String) result.get("trace_id"));
        dto.setSceneKey((String) result.get("scene_key"));
        dto.setSceneType((String) result.get("scene_type"));

        Map<String, Object> featureKeyMap = JsonMapperUtils.fromJson((String) result.get("feature_key_map"));
        featureKeyMap.entrySet().removeIf(
                entry -> entry.getKey().startsWith("dictFeature") || entry.getKey().equals("POLICY_PERF_START_TIME_KEY")
                        || entry.getKey().equals("POLICY_PERF_START_TIME_FEATURE_KEY"));
        dto.setFeatureKeyMap(JsonMapperUtils.toJson(featureKeyMap));

        dto.setRequestHitResult(Boolean.valueOf((String) result.get("request_hit_result")));

        List<String> hitActionList = Lists.newArrayList();
        List<String> hitStrategyList = Lists.newArrayList();
        if ("TREE".equals(dto.getSceneType())) {
            String dagNodeMapStr = (String) result.get("dag_node_map");
            Map<String, Object> dagNodeMap = JsonMapperUtils.fromJson(dagNodeMapStr);
            Map<String, Boolean> actionNodeMap = (Map<String, Boolean>) dagNodeMap.get("ACTION_NODE");
            Map<String, Boolean> strategyNodeMap = (Map<String, Boolean>) dagNodeMap.get("STRATEGY_NODE");
            if (MapUtils.isNotEmpty(actionNodeMap)) {
                for (Entry<String, Boolean> entry : actionNodeMap.entrySet()) {
                    if (entry.getValue()) {
                        hitActionList.add(entry.getKey());
                    }
                }
            }
            if (MapUtils.isNotEmpty(strategyNodeMap)) {
                for (Entry<String, Boolean> entry : strategyNodeMap.entrySet()) {
                    if (entry.getValue()) {
                        hitStrategyList.add(entry.getKey());
                    }
                }
            }
            dto.setHitActionList(hitActionList);
            dto.setHitStrategyList(hitStrategyList);
        } else if ("LIST".equals(dto.getSceneType())) {
            List<String> actionHitStrategyPolicyCodes =
                    JsonMapperUtils.ofJsonCollection((String) result.get("action_hit_strategy_policy_codes"),
                            List.class, String.class);

            List<String> actionHitPolicyCodes =
                    JsonMapperUtils.ofJsonCollection((String) result.get("action_hit_policy_codes"), List.class,
                            String.class);

            dto.setHitActionList(actionHitPolicyCodes);
            dto.setHitStrategyList(Collections.singletonList(actionHitStrategyPolicyCodes.get(0).split("_")[0]));
        }

        dto.setConditionExecuteDetailInfo((String) result.get("condition_exec_detail_info"));
        dto.setRuleExecuteDetailInfo((String) result.get("rule_exec_detail_info"));

        dto.setBizKeyInfo((String) result.get("biz_key_info"));
        dto.setRecordCreateTime((String) result.get("create_time"));
        dto.setRecordEndTime((String) result.get("end_time"));
        dto.setCreateTime(String.valueOf(System.currentTimeMillis()));
        dto.setRecordPDate((String) result.get("p_date"));

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = sdf.format(new Date(System.currentTimeMillis()));
        dto.setPDate(formattedDate);

        return dto;
    }

    public static String detailListRequest2ListSql(FlowRecordDetailListRequest request,
            FlowRecordStartEndPDate flowRecordStartEndPDate, String pDate) throws SQLException {
//        @SuppressWarnings("checkstyle:LineLength") String sql =
//                "SELECT pdate,flowRecordId,sceneKey,recordCreateTime,hitStrategyList,recordPDate,id,"
//                        + "recordEndTime,createTime,bizKeyInfo,traceId,requestHitResult,hitActionList,sceneType FROM "
//                        + "kwaishop_sellerdata.strategy_flow_record where pdate ";
//        if (StringUtils.isNotBlank(pDate)) {
//            sql = sql.concat(" = ").concat("'" + pDate + "'");
//        } else {
//            sql = sql.concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'")
//                    .concat(" and pdate <= ").concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");
//        }
//        if (StringUtils.isNotBlank(request.getId())) {
//            sql = sql.concat(" and id = '").concat(request.getId()).concat("'");
//        }
//        if (StringUtils.isNotBlank(request.getBizKeyInfo())) {
//            sql = sql.concat(" and bizKeyInfo = '").concat(request.getBizKeyInfo().concat("'"));
//        }
//        if (request.getFlowRecordId() != NumberUtils.LONG_ZERO) {
//            sql = sql.concat(" and flowRecordId = ").concat(String.valueOf(request.getFlowRecordId()));
//        }
//        if (StringUtils.isNotBlank(request.getSceneKey())) {
//            sql = sql.concat(" and sceneKey = '").concat(request.getSceneKey()).concat("'");
//        }
//        if (request.getRequestHitResult() != NumberUtils.INTEGER_ZERO) {
//            if (request.getRequestHitResult() == NumberUtils.INTEGER_ONE) {
//                sql = sql.concat(" and requestHitResult = true");
//            } else {
//                sql = sql.concat(" and requestHitResult = false");
//            }
//        }
//        if (StringUtils.isNotBlank(request.getBizKeyInfo())) {
//            sql = sql.concat(" and bizKeyInfo like '%").concat(request.getBizKeyInfo()).concat("%'");
//        }
//        sql = sql.concat(" order by createTime desc");
//        int offset = (request.getPageNum() - 1) * request.getPageSize();
//        int limit = request.getPageSize();
//        sql = sql.concat(" limit ").concat(String.valueOf(limit)).concat(" ").concat(" offset ")
//                .concat(String.valueOf(offset));
//        return sql;

        String sql =
                "SELECT pdate,flowRecordId,sceneKey,recordCreateTime,hitStrategyList,recordPDate,id,"
                        + "recordEndTime,createTime,bizKeyInfo,traceId,requestHitResult,hitActionList,sceneType FROM "
                        + "kwaishop_sellerdata.strategy_flow_record where pdate ";
        if (StringUtils.isNotBlank(pDate)) {
            sql = sql.concat(" = ").concat("'" + StringEscapeUtils.escapeSql(pDate) + "'");
        } else {
            sql = sql.concat(" >= ").concat("'" + StringEscapeUtils.escapeSql(flowRecordStartEndPDate.getStartPDate()) + "'")
                    .concat(" and pdate <= ").concat("'" + StringEscapeUtils.escapeSql(flowRecordStartEndPDate.getEndPDate()) + "'");
        }
        if (StringUtils.isNotBlank(request.getId())) {
            sql = sql.concat(" and id = '").concat(StringEscapeUtils.escapeSql(request.getId())).concat("'");
        }
//        if (StringUtils.isNotBlank(request.getBizKeyInfo())) {
//            sql = sql.concat(" and bizKeyInfo = '").concat(StringEscapeUtils.escapeSql(request.getBizKeyInfo().concat("'")));
//        }
        if (request.getFlowRecordId() != NumberUtils.LONG_ZERO) {
            sql = sql.concat(" and flowRecordId = ").concat(String.valueOf(request.getFlowRecordId()));
        }
        if (StringUtils.isNotBlank(request.getSceneKey())) {
            sql = sql.concat(" and sceneKey = '").concat(StringEscapeUtils.escapeSql(request.getSceneKey())).concat("'");
        }
        if (request.getRequestHitResult() != NumberUtils.INTEGER_ZERO) {
            if (request.getRequestHitResult() == NumberUtils.INTEGER_ONE) {
                sql = sql.concat(" and requestHitResult = true");
            } else {
                sql = sql.concat(" and requestHitResult = false");
            }
        }
        if (StringUtils.isNotBlank(request.getBizKeyInfo())) {
            sql = sql.concat(" and bizKeyInfo like '%").concat(StringEscapeUtils.escapeSql(request.getBizKeyInfo())).concat("%'");
        }
        sql = sql.concat(" order by createTime desc");
        int offset = (request.getPageNum() - 1) * request.getPageSize();
        int limit = request.getPageSize();
        sql = sql.concat(" limit ").concat(String.valueOf(limit)).concat(" ").concat(" offset ")
                .concat(String.valueOf(offset));
        return sql;
    }

    public static String detailRequest2Sql(String id, FlowRecordStartEndPDate flowRecordStartEndPDate) {
//        @SuppressWarnings("checkstyle:LineLength") String sql =
//                "SELECT pdate,flowRecordId,sceneKey,recordCreateTime,hitStrategyList,recordPDate,id,featureKeyMap,"
//                        + "recordEndTime,createTime,bizKeyInfo,traceId,requestHitResult,hitActionList,sceneType FROM "
//                        + "kwaishop_sellerdata.strategy_flow_record where pdate ";
//
//        sql = sql.concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'").concat(" and pdate <= ")
//                .concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");
//
//        if (StringUtils.isNotBlank(id)) {
//            sql = sql.concat(" and id = '").concat(id).concat("'");
//        }
//        return sql;
        String sql =
                "SELECT pdate,flowRecordId,sceneKey,recordCreateTime,hitStrategyList,recordPDate,id,featureKeyMap,"
                        + "recordEndTime,createTime,bizKeyInfo,traceId,requestHitResult,hitActionList,sceneType FROM "
                        + "kwaishop_sellerdata.strategy_flow_record where pdate ";

        String startPDate = StringEscapeUtils.escapeSql(flowRecordStartEndPDate.getStartPDate());
        String endPDate = StringEscapeUtils.escapeSql(flowRecordStartEndPDate.getEndPDate());

        sql = sql.concat(" >= ").concat("'").concat(startPDate).concat("'").concat(" and pdate <= ")
                .concat("'").concat(endPDate).concat("'");

        if (StringUtils.isNotBlank(id)) {
            String escapedId = StringEscapeUtils.escapeSql(id);
            sql = sql.concat(" and id = '").concat(escapedId).concat("'");
        }
        return sql;
    }

    public static String detailListRequest2CountSql(FlowRecordDetailListRequest request,
            FlowRecordStartEndPDate flowRecordStartEndPDate, String pDate) {
//        String sql = "SELECT count(*) FROM kwaishop_sellerdata.strategy_flow_record where pdate ";
//        if (StringUtils.isNotBlank(pDate)) {
//            sql = sql.concat(" = ").concat("'" + pDate + "'");
//        } else {
//            sql = sql.concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'")
//                    .concat(" and pdate <= ").concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");
//        }
//        if (StringUtils.isNotBlank(request.getId())) {
//            sql = sql.concat(" and id = '").concat(request.getId()).concat("'");
//        }
//        if (request.getFlowRecordId() != NumberUtils.LONG_ZERO) {
//            sql = sql.concat(" and flowRecordId = ").concat(String.valueOf(request.getFlowRecordId()));
//        }
//        if (StringUtils.isNotBlank(request.getSceneKey())) {
//            sql = sql.concat(" and sceneKey = '").concat(request.getSceneKey()).concat("'");
//        }
//        return sql;
        String sql = "SELECT count(*) FROM kwaishop_sellerdata.strategy_flow_record where pdate ";

        if (StringUtils.isNotBlank(pDate)) {
            String escapedPDate = StringEscapeUtils.escapeSql(pDate);
            sql = sql.concat(" = ").concat("'").concat(escapedPDate).concat("'");
        } else {
            String startPDate = StringEscapeUtils.escapeSql(flowRecordStartEndPDate.getStartPDate());
            String endPDate = StringEscapeUtils.escapeSql(flowRecordStartEndPDate.getEndPDate());

            sql = sql.concat(" >= ").concat("'").concat(startPDate).concat("'")
                    .concat(" and pdate <= ").concat("'").concat(endPDate).concat("'");
        }

        if (StringUtils.isNotBlank(request.getId())) {
            String escapedId = StringEscapeUtils.escapeSql(request.getId());
            sql = sql.concat(" and id = '").concat(escapedId).concat("'");
        }

        if (request.getFlowRecordId() != NumberUtils.LONG_ZERO) {
            // Assuming flowRecordId is a safe integer that doesn't need escaping.
            sql = sql.concat(" and flowRecordId = ").concat(String.valueOf(request.getFlowRecordId()));
        }

        if (StringUtils.isNotBlank(request.getSceneKey())) {
            String escapedSceneKey = StringEscapeUtils.escapeSql(request.getSceneKey());
            sql = sql.concat(" and sceneKey = '").concat(escapedSceneKey).concat("'");
        }

        return sql;
    }

    public static String analysisRecordTotalCount(Long flowRecordId, FlowRecordStartEndPDate flowRecordStartEndPDate) {
        @SuppressWarnings("checkstyle:LineLength") String sql =
                "SELECT count(*) FROM kwaishop_sellerdata.strategy_flow_record where flowRecordId = " + flowRecordId
                        + " and pdate ";

        sql = sql.concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'").concat(" and pdate <= ")
                .concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");
        return sql;
    }

    public static String analysisRecordRequestHitResult(Long flowRecordId,
            FlowRecordStartEndPDate flowRecordStartEndPDate) {
        @SuppressWarnings("checkstyle:LineLength") String sql =
                "SELECT requestHitResult,count(*) as total FROM kwaishop_sellerdata.strategy_flow_record where "
                        + "flowRecordId = " + flowRecordId + " and pdate ";

        sql = sql.concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'").concat(" and pdate <= ")
                .concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");

        sql = sql.concat(" group by requestHitResult");
        return sql;
    }

    public static String analysisRecordStrategyHitResult(Long flowRecordId,
            FlowRecordStartEndPDate flowRecordStartEndPDate) {
        @SuppressWarnings("checkstyle:LineLength") String sql =
                "SELECT hitStrategyList,count(*) as total FROM kwaishop_sellerdata.strategy_flow_record where "
                        + "flowRecordId = " + flowRecordId + " and pdate ";

        sql = sql.concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'").concat(" and pdate <= ")
                .concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");

        sql = sql.concat(" group by hitStrategyList order by total desc");
        return sql;
    }

    public static String analysisRecordActionHitResult(Long flowRecordId,
            FlowRecordStartEndPDate flowRecordStartEndPDate) {
        @SuppressWarnings("checkstyle:LineLength") String sql =
                "SELECT hitActionList,count(*) as total FROM kwaishop_sellerdata.strategy_flow_record where "
                        + "flowRecordId = " + flowRecordId + " and pdate ";
        sql = sql.concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'").concat(" and pdate <= ")
                .concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");
        sql = sql.concat(" group by hitActionList order by total desc");
        return sql;
    }


    public static String analysisRecordStrategyHitDiff(Long flowRecordId,
            FlowRecordStartEndPDate flowRecordStartEndPDate) {
        String sql =
                "select"
                + "     t.hitStrategyList,t1.total,t2.total"
                + " from"
                + " ("
                + "     select"
                + "         distinct hitStrategyList1 as hitStrategyList"
                + " from"
                + "     kwaishop_sellerdata.strategy_flow_replay"
                + " where"
                + "     {0}"
                + " and flowReplayId = {1}"
                + " union all"
                + "     select"
                + "         distinct hitStrategyList2 as hitStrategyList"
                + " from"
                + "     kwaishop_sellerdata.strategy_flow_replay"
                + " where"
                + "     {0}"
                + " and flowReplayId = {1}"
                + " ) t"
                + " left join"
                + " ("
                + "     select"
                + "         hitStrategyList1 as hitStrategyList, count(1) as total"
                + "     from"
                + "         kwaishop_sellerdata.strategy_flow_replay"
                + "     where"
                + "         {0}"
                + "         and flowReplayId = {1}"
                + "     group by hitStrategyList1"
                + " ) t1 on t.hitStrategyList = t1.hitStrategyList"
                + " left join"
                + " ("
                + "     select"
                + "         hitStrategyList2 as hitStrategyList, count(1) as total"
                + "     from"
                + "         kwaishop_sellerdata.strategy_flow_replay"
                + "     where"
                + "         {0}"
                + "         and flowReplayId = {1}"
                + "     group by hitStrategyList2"
                + " ) t2 on t.hitStrategyList = t2.hitStrategyList"
                + " order by abs(t1.total - t2.total) desc";

        String pdateSelect = "pdate".concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'").concat(" and pdate <= ")
                .concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");
        return MessageFormat.format(sql, pdateSelect, flowRecordId);
    }

    public static String analysisRecordActionHitDiff(Long flowRecordId,
            FlowRecordStartEndPDate flowRecordStartEndPDate) {
        String sql =
                "select"
                        + "     t.hitActionList,t1.total,t2.total"
                        + " from"
                        + " ("
                        + "     select"
                        + "         distinct hitActionList1 as hitActionList"
                        + " from"
                        + "     kwaishop_sellerdata.strategy_flow_replay"
                        + " where"
                        + "     {0}"
                        + " and flowReplayId = {1}"
                        + " union all"
                        + "     select"
                        + "         distinct hitActionList2 as hitActionList"
                        + " from"
                        + "     kwaishop_sellerdata.strategy_flow_replay"
                        + " where"
                        + "     {0}"
                        + " and flowReplayId = {1}"
                        + " ) t"
                        + " left join"
                        + " ("
                        + "     select"
                        + "         hitActionList1 as hitActionList, count(1) as total"
                        + "     from"
                        + "         kwaishop_sellerdata.strategy_flow_replay"
                        + "     where"
                        + "         {0}"
                        + "         and flowReplayId = {1}"
                        + "     group by hitActionList1"
                        + " ) t1 on t.hitActionList = t1.hitActionList"
                        + " left join"
                        + " ("
                        + "     select"
                        + "         hitActionList2 as hitActionList, count(1) as total"
                        + "     from"
                        + "         kwaishop_sellerdata.strategy_flow_replay"
                        + "     where"
                        + "         {0}"
                        + "         and flowReplayId = {1}"
                        + "     group by hitActionList2"
                        + " ) t2 on t.hitActionList = t2.hitActionList"
                        + " order by abs(t1.total - t2.total) desc";

        String pdateSelect = "pdate".concat(" >= ").concat("'" + flowRecordStartEndPDate.getStartPDate() + "'").concat(" and pdate <= ")
                .concat("'" + flowRecordStartEndPDate.getEndPDate() + "'");
        return MessageFormat.format(sql, pdateSelect, flowRecordId);
    }


}
