package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.impl;

import java.util.List;
import java.util.stream.StreamSupport;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.intown.json.JSON;
import com.kuaishou.intown.json.JSONObject;
import com.kuaishou.kconf.common.json.JsonMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.common.Enum.ReplayStatus;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper.StrategyFlowReplayMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowReplayListRequestPojo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowReplayDo;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.PipeLineService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.RecordService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.ReplayService;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.util.KdevResponseUtils;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.CheckReplayStatusByPipelineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.CheckReplayStatusByPipelineResponseFixed;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.FlowReplayAddRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.GetAllInstantsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.InstantInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StartReplayByPipelineRequest;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j//@Lazy
public class PipeLineServiceImpl implements PipeLineService {

    @Autowired
    private ReplayService replayService;

    @Autowired
    private RecordService recordService;

    @Autowired
    private StrategyFlowReplayMapper strategyFlowReplayMapper;

    private static final long MOCK_ID = 97L;

    private static final Long FLOW_MOCK_ID = 258L;

    private static final Integer SLEEP_TIME = 50000;
    @Override
    public String startReplayByPipeline(StartReplayByPipelineRequest request) {
        // 0. 前置参数校验、选择用哪个录制id
        // 1. 第一步创建回放任务
        log.info("startReplayByPipeline第一步创建回放任务:{}", JsonMapperUtils.toJson(request));
        // 2.在那之前，先取一个线上的实例做对比
        Iterable<InstantInfo> instants = recordService.getAllInstants(GetAllInstantsRequest.newBuilder()
                .setServiceName("kwaishop-apollo-strategy-center")
                .setStage("PROD")
                .build());

        String prodInfo = StreamSupport.stream(instants.spliterator(), false)
                .filter(instantInfo -> instantInfo.getHostName().contains("PROD"))
                .map(JsonMapperUtils::toJson)
                .findFirst()
                .orElse("");


        log.info("流水线回放查到的数据: {}", prodInfo);
        // 3. 第三步启动回放任务
        FlowReplayAddRequest build = null;
        try {
            build = FlowReplayAddRequest.newBuilder()
                    .setDescription("test.lxd.noMock")
                    .setName("流水线回放" + request.getKdevJobLogId())
                    .setReplayConfigOne(com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayConfig.newBuilder()
                            .setReplayType(4)
                            .addPodInfoList(convertJsonToTargetFormat(prodInfo))
                            .build())
                    .setReplayConfigTwo(com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayConfig.newBuilder()
                            .setReplayType(2)
                            .setLandId(request.getLaneId())
                            .build())
                    .setReplayQps(10)
                    .setOperator(request.getExecuteUser())
                    .setFlowRecordId(FLOW_MOCK_ID) // 这边先写死一个
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
        }

        log.info("流水线创建任务参数: {}", JsonMapperUtils.toJson(build));
        replayService.flowReplayAdd(build);
        // 先等待一段时间，去查这个任务有没有新建成功，目前是15s
        try {
            log.info("开始等待10s");
            Thread.sleep(SLEEP_TIME);
        } catch (Exception e) {
            e.printStackTrace();
        }
        FlowReplayListRequestPojo pojo = new FlowReplayListRequestPojo();
        pojo.setName("流水线回放" + request.getKdevJobLogId());
        List<StrategyFlowReplayDo> strategyFlowReplayDos = strategyFlowReplayMapper.strategyFlowReplayList(pojo);
        log.info("流水线回放查到的数据size是" + strategyFlowReplayDos.size());
        log.info("流水线数据list：{}", JsonMapperUtils.toJson(strategyFlowReplayDos));
        return String.valueOf(strategyFlowReplayDos.get(0).getId());
    }

    @Override
    public CheckReplayStatusByPipelineResponseFixed checkReplayStatusByPipeline(CheckReplayStatusByPipelineRequest request) {

        log.info("checkReplayStatusByPipeline:{}", JsonMapperUtils.toJson(request));
        String replayTaskId = request.getPipelineId();
        // 查询是否回放完成
        FlowReplayListRequestPojo pojo = new FlowReplayListRequestPojo();
        pojo.setId(Long.parseLong(replayTaskId));
        List<StrategyFlowReplayDo> strategyFlowReplayDos = strategyFlowReplayMapper.strategyFlowReplayList(pojo);
        if (strategyFlowReplayDos.get(0).getReplayStatus() != ReplayStatus.FINISHED.getCode()) {
            log.info("test.lxd 查询到回放未完成");
            // 没完成，先返回未完成的
            return CheckReplayStatusByPipelineResponseFixed.newBuilder()
                    .setStatus(KdevResponseUtils.SUCCESS_STATUS)
                    .setMessage(KdevResponseUtils.SUCCESS_MESSAGE)
                    .setData(KdevResponseUtils.setCheckReplayStatusDataFixed("null", "null", replayTaskId))
                    .build();
        }
        String replayResultAnalysis = "";
        // 已经完成的，就去获取结果
//        if (strategyFlowReplayDos.get(0).getDescription().equals("test.lxd")) {
//            log.info("进入mock测试流程 test.lxd");
//            replayResultAnalysis = replayService.getReplayResultAnalysis(Long.parseLong(request.getPipelineId()));
//        } else {
        // 直接获取结果
        replayResultAnalysis = replayService.getReplayResultAnalysis(Long.parseLong(replayTaskId));


        // 在这里做卡点，目前全部不卡点，只返回通过
        return CheckReplayStatusByPipelineResponseFixed.newBuilder()
                .setStatus(KdevResponseUtils.SUCCESS_STATUS)
                .setMessage(KdevResponseUtils.SUCCESS_MESSAGE)
                .setData(KdevResponseUtils.setCheckReplayStatusDataFixed("true", replayResultAnalysis, replayTaskId))
                .build();
    }


    public String convertJsonToTargetFormat(String jsonString) {
        try {
            // 使用 Fastjson 解析 JSON 字符串
            JSONObject jsonObject = JSON.parseObject(jsonString);

            // 提取字段
            String hostName = jsonObject.getString("hostName");
            String ip = jsonObject.getString("ip");
            String port = jsonObject.getString("port");

            // 拼接成目标格式
            String result = String.format("%s:%s_%s", hostName, ip, port);

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

}