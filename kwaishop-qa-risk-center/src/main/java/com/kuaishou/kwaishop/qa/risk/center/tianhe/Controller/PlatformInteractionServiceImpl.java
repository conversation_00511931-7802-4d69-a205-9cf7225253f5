package com.kuaishou.kwaishop.qa.risk.center.tianhe.Controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.CheckSubmitTestRequest;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.CheckSubmitTestResponse;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.CheckTestExitRequest;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.CheckTestExitResponse;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.GetDeployControlInfoRequest;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.GetDeployControlInfoResponse;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.KrpcChangeOrderTestSpiServiceGrpc;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.QuerySubmitTestCheckListRequest;
import com.kuaishou.kwaishop.lingzhu.platform.spi.service.protobuf.QuerySubmitTestCheckListResponse;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Service.PlatformInteractionService;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-12-16
 */
@Slf4j
@Service
@KrpcService(server = "custom-servers-config", serviceName = "kwaishop-qa-risk-center")
public class PlatformInteractionServiceImpl extends KrpcChangeOrderTestSpiServiceGrpc.ChangeOrderTestSpiServiceImplBaseV2 {

    @Autowired
    private  PlatformInteractionService platformInteractionService;

    @Override
    public GetDeployControlInfoResponse getDeployControlInfo(GetDeployControlInfoRequest request) {
        return platformInteractionService.getDeployControlInfoResponse(request);
    }

    @Override
    public CheckSubmitTestResponse checkSubmitTest(CheckSubmitTestRequest request) {
        return platformInteractionService.checkSubmitTestResponse(request);
    }

    @Override
    public CheckTestExitResponse checkTestExit(CheckTestExitRequest request) {
        return platformInteractionService.checkTestExitResponse(request);
    }

    @Override
    public QuerySubmitTestCheckListResponse querySubmitTestCheckList(QuerySubmitTestCheckListRequest request) {
        return platformInteractionService.querySubmitTestCheckListResponse(request);
    }

}
