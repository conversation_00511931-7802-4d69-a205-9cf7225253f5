package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.IpPortDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.PolicyTestResponse;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTestDataPool;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-05-28
 */
public interface PolicyRpcService {

    @Deprecated
    PolicyTestResponse policyTree(StrategyTestDataPool strategyTestDataPool, String laneId);

    @Deprecated
    String policy(StrategyTestDataPool strategyTestDataPool);

    @Deprecated
    String strategyTreeDetail(String sceneKey);

    String policyTrafficByLandId(String laneId, String sceneKey, String paramJson);

    String policyTrafficByGroup(String group, String sceneKey, String paramJson);

    String policyTrafficByIpPort(String ip, int port, String sceneKey, String paramJson);

    String policyTrafficByIpPort(List<IpPortDto> list, String sceneKey, String paramJson);
}
