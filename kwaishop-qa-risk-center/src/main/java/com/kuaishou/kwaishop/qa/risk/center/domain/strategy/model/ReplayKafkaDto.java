package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-17
 */
@Data
public class ReplayKafkaDto {

    private String id;

    private Long flowReplayId;

    private String pDate;

    private String replayPDate;

    private String recordPDate;

    private String sceneKey;

    private String featureKeyMap;

    private Boolean diffResult;

    private Boolean requestHitResult1;

    private List<String> hitActionList1;

    private List<String> hitStrategyList1;

    private Boolean requestHitResult2;

    private List<String> hitActionList2;

    private List<String> hitStrategyList2;

    private String bizKeyInfo;

    private String createTime;
}
