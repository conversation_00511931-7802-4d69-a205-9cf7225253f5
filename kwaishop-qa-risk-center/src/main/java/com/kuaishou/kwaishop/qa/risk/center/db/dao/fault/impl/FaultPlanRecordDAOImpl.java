package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.impl;

import java.util.Collection;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.FaultPlanRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultPlanRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultPlanRecordGroupByDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.fault.FaultPlanRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.fault.FaultPlanRecordQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultPlanRecordBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultPlanConformTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultPlanStatusEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-15
 */
@Repository
public class FaultPlanRecordDAOImpl extends BaseDAOImpl<FaultPlanRecordDO, FaultPlanRecordQueryCondition> implements FaultPlanRecordDAO {

    @Autowired
    private FaultPlanRecordMapper faultPlanRecordMapper;


    @Override
    public List<FaultPlanRecordDO> queryByCenterIds(Collection<Long> centerIds, CombineQueryParam queryParam) {
        FaultPlanRecordQueryCondition condition = FaultPlanRecordQueryCondition.builder()
                .centerIds(centerIds)
                .orderByCreateTimeDesc(true)
                .build();
        return queryList(condition);
    }

    @Override
    public List<FaultPlanRecordDO> queryByTeamIds(Long centerId, Collection<Long> teamIds,
                                                  CombineQueryParam queryParam) {
        FaultPlanRecordQueryCondition condition = FaultPlanRecordQueryCondition.builder()
                .centerId(centerId)
                .teamIds(teamIds)
                .orderByCreateTimeDesc(true)
                .build();
        return queryList(condition);
    }

    @Override
    public List<FaultPlanRecordDO> queryByCenterId(Long centerId, CombineQueryParam queryParam) {
        FaultPlanRecordQueryCondition condition = FaultPlanRecordQueryCondition.builder()
                .centerId(centerId)
                .orderByCreateTimeDesc(true)
                .build();
        return queryList(condition);
    }

    @Override
    public List<FaultPlanRecordDO> queryByTeamId(Long centerId, Long teamId, CombineQueryParam queryParam) {
        FaultPlanRecordQueryCondition condition = FaultPlanRecordQueryCondition.builder()
                .centerId(centerId)
                .teamId(teamId)
                .orderByCreateTimeDesc(true)
                .build();
        return queryList(condition);
    }

    @Override
    public List<FaultPlanRecordDO> queryByCenterIdsTeamIds(Collection<Long> centerIds, Collection<Long> teamIds,
                                                           CombineQueryParam queryParam) {
        FaultPlanRecordQueryCondition condition = FaultPlanRecordQueryCondition.builder()
                .centerIds(centerIds)
                .teamIds(teamIds)
                .orderByCreateTimeDesc(true)
                .build();
        return queryList(condition);
    }

    @Override
    protected void fillQueryCondition(FaultPlanRecordQueryCondition condition,
                                      QueryWrapper<FaultPlanRecordDO> queryWrapper) {
        if (condition.getCenterId() != null && condition.getCenterId() > 0) {
            queryWrapper.and(q -> q.eq("center_id", condition.getCenterId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getCenterIds())) {
            queryWrapper.and(q -> q.in("center_id", condition.getCenterIds()));
        }
        if (condition.getTeamId() != null && condition.getTeamId() > 0) {
            queryWrapper.and(q -> q.eq("team_id", condition.getTeamId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getTeamIds())) {
            queryWrapper.and(q -> q.in("team_id", condition.getTeamIds()));
        }
        if (condition.getPlanId() != null && condition.getPlanId() > 0) {
            queryWrapper.and(q -> q.eq("plan_id", condition.getPlanId()));
        }
        if (condition.getCaseId() != null && condition.getCaseId() > 0) {
            queryWrapper.and(q -> q.eq("case_id", condition.getCaseId()));
        }
        if (condition.getStatus() != null && FaultPlanStatusEnum.of(condition.getStatus()) != null) {
            queryWrapper.and(q -> q.eq("status", condition.getStatus()));
        }
        if (CollectionUtils.isNotEmpty(condition.getStatusList())) {
            queryWrapper.and(q -> q.in("status", condition.getStatusList()));
        }
        if (condition.getConformType() != null && FaultPlanConformTypeEnum.of(condition.getConformType()) != null) {
            queryWrapper.and(q -> q.eq("conform_type", condition.getConformType()));
        }
        if (condition.getStartTimeGe() != null && condition.getStartTimeGe() > 0) {
            queryWrapper.and(q -> q.ge("create_time", condition.getStartTimeGe()));
        }
        if (condition.getEndTimeLe() != null && condition.getEndTimeLe() > 0) {
            queryWrapper.and(q -> q.le("create_time", condition.getEndTimeLe()));
        }
        if (condition.getQueryReport() != null && condition.getQueryReport()) {
            queryWrapper.and(q -> q.isNotNull("report"));
        }
        if (condition.getUpdateTimeGe() != null && condition.getUpdateTimeGe() > 0) {
            queryWrapper.and(q -> q.ge("update_time", condition.getUpdateTimeGe()));
        }
        if (condition.getUpdateTimeLe() != null && condition.getUpdateTimeLe() > 0) {
            queryWrapper.and(q -> q.le("update_time", condition.getUpdateTimeLe()));
        }
        if (condition.getScenarioId() != null && condition.getScenarioId() > 0) {
            queryWrapper.and(q -> q.eq("scenario_id", condition.getScenarioId()));
        }
    }

    @Override
    protected BaseMapper<FaultPlanRecordDO> getMapper() {
        return faultPlanRecordMapper;
    }

    @Override
    public List<FaultPlanRecordDO> queryFaultPlanRecordList(FaultPlanRecordBO faultPlanRecordBO) {
        FaultPlanRecordQueryCondition condition = FaultPlanRecordQueryCondition.builder()
                .id(faultPlanRecordBO.getId())
                .centerId(faultPlanRecordBO.getCenterId())
                .teamId(faultPlanRecordBO.getTeamId())
                .planId(faultPlanRecordBO.getPlanId())
                .caseId(faultPlanRecordBO.getCaseId())
                .conformType(faultPlanRecordBO.getConformType())
                .status(faultPlanRecordBO.getStatus())
                .queryReport(faultPlanRecordBO.getQueryReport())
                .startTimeGe(faultPlanRecordBO.getCreateTimeGe())
                .endTimeLe(faultPlanRecordBO.getCreateTimeLe())
                .updateTimeGe(faultPlanRecordBO.getUpdateTimeGe())
                .updateTimeLe(faultPlanRecordBO.getUpdateTimeLe())
                .statusList(faultPlanRecordBO.getStatusList())
                .orderByCreateTimeDesc(true)
                .build();
        return queryList(condition);
    }

    @Override
    public PageBO<FaultPlanRecordDO> queryPageFaultPlanRecordList(FaultPlanRecordBO riskDetailBO) {
        return null;
    }

    @Override
    public List<FaultPlanRecordDO> queryFaultPlanRecordAllByCenterId(Long centerId, Long st, Long et) {
        FaultPlanRecordQueryCondition condition = FaultPlanRecordQueryCondition.builder()
                .centerId(centerId)
                .startTimeGe(st)
                .endTimeLe(et)
                .build();
        return queryList(condition);
    }

    @Override
    public int batchUpdateFaultPlanRecord(FaultPlanRecordBO faultPlanRecordBO, Integer fromStatus, Integer toStatus) {
        return faultPlanRecordMapper.batchUpdateFaultPlanRecord(faultPlanRecordBO,
                fromStatus, toStatus, System.currentTimeMillis());
    }

    @Override
    public int batchUpdateFaultPlanRecordByIds(FaultPlanRecordBO faultPlanRecordBO, Integer fromStatus, Integer toStatus) {
        return faultPlanRecordMapper.batchUpdateFaultPlanRecordByIds(faultPlanRecordBO,
                fromStatus, toStatus, System.currentTimeMillis());
    }

    @Override
    public List<FaultPlanRecordDO> queryFaultPlanRecordByCenterId(Long centerId, Long st, Long et) {
        FaultPlanRecordQueryCondition condition = FaultPlanRecordQueryCondition.builder()
                .centerId(centerId)
                .startTimeGe(st)
                .endTimeLe(et)
                .status(FaultPlanStatusEnum.FINISH.getCode())
                .build();
        return queryList(condition);
    }

    @Override
    public List<FaultPlanRecordDO> queryPageFaultPlanRecordList(FaultPlanRecordBO faultPlanRecordBO, int offset, int pageSize) {
        if (faultPlanRecordBO.getId() != 0) {
            return faultPlanRecordMapper.findFaultPlanRecordByPageById(faultPlanRecordBO.getId(), offset, pageSize);
        }
        return faultPlanRecordMapper.findFaultPlanRecordByPage(faultPlanRecordBO.getPlanId(), offset, pageSize);
    }

    @Override
    public int countFaultPlanRecord(Long planId) {
        return faultPlanRecordMapper.countFaultPlanRecord(planId);
    }

    @Override
    public List<FaultPlanRecordDO> queryFaultPlanRecordList(Long planId) {
        FaultPlanRecordQueryCondition condition = FaultPlanRecordQueryCondition.builder()
                .planId(planId)
                .readMaster(true)
                .build();
        return queryList(condition);
    }

    @Override
    public List<FaultPlanRecordDO> queryFaultRecordList(Long planId, Collection<Long> ids) {
        FaultPlanRecordQueryCondition condition = FaultPlanRecordQueryCondition.builder()
                .planId(planId)
                .ids(ids)
                .build();
        return queryList(condition);
    }

    @Override
    public List<FaultPlanRecordDO> queryFaultRecordList(Collection<Long> ids) {
        FaultPlanRecordQueryCondition condition = FaultPlanRecordQueryCondition.builder()
                .ids(ids)
                .build();
        return queryList(condition);
    }

    @Override
    public List<FaultPlanRecordGroupByDO> getFaultCountGroupByMember(Long st, Long et) {

        return faultPlanRecordMapper.getFaultCountGroupByMember(st, et);
    }

    @Override
    public List<FaultPlanRecordDO> queryRecordListByScenarioId(Long scenarioId) {
        FaultPlanRecordQueryCondition condition = FaultPlanRecordQueryCondition.builder()
                .scenarioId(scenarioId)
                .build();
        return queryList(condition);
    }
}
