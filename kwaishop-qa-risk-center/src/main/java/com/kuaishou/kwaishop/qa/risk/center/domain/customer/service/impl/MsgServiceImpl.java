package com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.domain.customer.constants.Numbers.APP_ID;

import java.util.HashSet;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.gson.JsonObject;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.infra.passport.agent.InfraPassportAgent;
import com.kuaishou.infra.passport.internal.sdk.service.PassportTokenService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskObjectConfigKey;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.klink.UserKlinkLoginDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.klink.UserKlinkLoginDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.config.KlinkConfig;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.model.KlinkLoginExtBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.model.enums.BusinessTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.MsgService;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.utils.CsSendUtil;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.BatchMsgSendC2BRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.MsgSendB2CRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.MsgSendB2CResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.MsgSendC2BRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.MsgSendC2BResponse;
import com.kuaishou.kwaishop.qa.risk.center.utils.JsonUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.kess.KessUtil;
import com.kuaishou.kwaishop.qa.risk.center.utils.kess.dto.KessRun;
import com.kwai.link.KlinkPressureTestClient;
import com.kwai.link.log.KwaiLinkLog;

import lombok.extern.slf4j.Slf4j;

@Service
@Lazy
@Slf4j
public class MsgServiceImpl implements MsgService {

    private static final String KWAISHOP_CS_SCENE_SERVICE = "kwaishop-cs-scene-service";

    private static final String GET_USER_TOKEN = "solution.login.KwaishopCsSolutionLoginProduceService/GetUserToken";

    private static final String DISTRIBUTE_GET_USER_TOKEN
            = "kuaishou.kwaishop.cs.distribute.coop.KwaishopDistributeCoopSessionService/GetUserToken";

    @Autowired
    private CsSendUtil csSendUtil;

    @Autowired
    private UserKlinkLoginDAO userKlinkLoginDAO;

    @Autowired
    private PassportTokenService passportTokenService;


    @Override
    public MsgSendC2BResponse msgSendC2B(MsgSendC2BRequest request) {
        try {
            MsgSendC2BResponse.Builder res = MsgSendC2BResponse.newBuilder();
            KwaiLinkLog.setLevel(KwaiLinkLog.Level.DEBUG);
            if (request.getBusinessType() == BusinessTypeEnum.SERVICEMARKET.getCode()) {
                msgSendC2B4ServiceMarket(request);
            } else if (request.getBusinessType() == BusinessTypeEnum.DISTRIBUTE.getCode()) {
                msgSendC2B4Distribute(request.getSenderId(), request.getTargetId(),
                        request.getText(), request.getLaneId());
            } else {
                msgSendC2B4XiaoDian(request);
            }
            return res.setErrorMsg(request.getSenderId() + "|" + request.getTargetId() + "|" + request.getText()
                    + "|" + request.getBusinessType() + "|" + request.getLaneId())
                    .setResult(1)
                    .build();
        } catch (Exception ex) {
            log.info("异常｜" + ex);
            return MsgSendC2BResponse.newBuilder().setErrorMsg("异常｜" + ex).build();
        }
    }

    public void msgSendC2B4XiaoDian(MsgSendC2BRequest request) throws Exception {
        if (StringUtils.isNotBlank(request.getSenderId()) && StringUtils.isNotBlank(request.getTargetId())) {
            KlinkPressureTestClient.init(APP_ID, "KUAISHOU", "ANDROID_PHONE", request.getLaneId());
            KlinkConfig klinkConfig = QaRiskObjectConfigKey.klinkSwitch.getObject();
            String targetId = String.valueOf(csSendUtil.uid2oid(Long.parseLong(request.getTargetId())));
            InfraPassportAgent.NewZtTokenResult tokenResult = getToken(Long.parseLong(request.getSenderId()), "kuaishou.klink.h5",
                    "web_7d9f5c6bcaae49d1af731c76c6ad520f", "KUAISHOU");
            String tokenValue = tokenResult.getTokenValue();
            String ssecurity = tokenResult.getSsecurity();
            csSendUtil.buyerSend(klinkConfig.getIp(), klinkConfig.getPort(), tokenValue, ssecurity, targetId, request);
        } else {
            throw new Exception("发送者和接收者id不能未空|" + request.getSenderId() + "｜" + request.getTargetId());
        }
    }

    public void msgSendC2B4ServiceMarket(MsgSendC2BRequest request) throws Exception {
        if (StringUtils.isNotBlank(request.getSenderId()) && StringUtils.isNotBlank(request.getTargetId())) {
            KlinkPressureTestClient.init(APP_ID, "KUAISHOU", "PC_WEB", request.getLaneId());
            KlinkConfig klinkConfig = QaRiskObjectConfigKey.klinkSwitch.getObject();
            JsonObject response = queryUserToken("f79501da-48e0-4f27-964a-ab811eb3b9f9",
                    Long.parseLong(request.getTargetId()), "kshop.servicemarket.im",
                    "web_7d9f5c6bcaae49d1af731c76c6ad520f");
            if (!"SUCCESS".equals(response.get("error_message").getAsString())) {
                throw new Exception("获取token失败|" + response.get("error_message"));
            }
            String targetId = response.getAsJsonObject("data").get("owner_id").getAsString();
            InfraPassportAgent.CreateVirtualIdTokenResult tokenResult = getToken(Long.parseLong(request.getSenderId()), "kuaishou.servicemarket.im",
                    "web_service_market_did");
            String tokenValue = tokenResult.getTokenValue();
            String ssecurity = tokenResult.getSsecurity();
            csSendUtil.serviceMarketbuyerSend(klinkConfig.getIp(), klinkConfig.getPort(), tokenValue,
                    ssecurity, request.getSenderId(), request.getText(), targetId);
        } else {
            throw new Exception("发送者和接收者id不能未空|" + request.getSenderId() + "｜" + request.getTargetId());
        }
    }


    @Override
    public MsgSendC2BResponse batchMsgSendC2B(BatchMsgSendC2BRequest request) {
        MsgSendC2BResponse.Builder res = MsgSendC2BResponse.newBuilder();
        try {
            if (StringUtils.isBlank(request.getTargetId())) {
                return res.setErrorMsg("目标id为空").build();
            }
            if (CollectionUtils.isEmpty(request.getSenderIdList())) {
                return res.setErrorMsg("发送者为空").build();
            }
            KlinkPressureTestClient.init(APP_ID, "KUAISHOU", "ANDROID_PHONE", request.getLaneId());
            KlinkConfig klinkConfig = QaRiskObjectConfigKey.klinkSwitch.getObject();
            Set<String> senderIdSet = new HashSet<>(request.getSenderIdList());
            senderIdSet.forEach(
                    e -> {
                        if (request.getBusinessType() == 2) {
                            try {
                                msgSendC2B4Distribute(e, request.getTargetId(), request.getText(), request.getLaneId());
                            } catch (Exception exception) {
                                log.info("异常｜" + exception.getMessage());
                            }
                        } else {
                            UserKlinkLoginDO userKlinkLoginDO = userKlinkLoginDAO.queryUserLoginInfoByUserId(Long.parseLong(e));
                            if (userKlinkLoginDO != null) {
                                KlinkLoginExtBO extBO = ObjectMapperUtils.fromJSON(userKlinkLoginDO.getExt(), KlinkLoginExtBO.class);
                                csSendUtil.buyerSend(e, request.getTargetId(), request.getText(), klinkConfig, extBO);
                            } else {
                                log.info("发送者不存在｜userId=" + e);
                            }
                        }
                    }
            );
            return res.setResult(BaseResultCode.SUCCESS_VALUE).build();
        } catch (Exception ex) {
            log.info("异常｜" + ex);
            return res.setErrorMsg(ex.getMessage()).build();
        }
    }

    @Override
    public MsgSendB2CResponse msgSendB2C(MsgSendB2CRequest request) {
        MsgSendB2CResponse.Builder res = MsgSendB2CResponse.newBuilder();
        try {
            if (request.getBusinessType() == 2) {
                msgSendB2C4ServiceMarket(request);
            } else {
                msgSendB2C4XiaoDian(request);
            }
            return res.setErrorMsg(request.getSenderId() + "|" + request.getTargetId() + "|" + request.getText()
                    + "|" + request.getLaneId() + "|" + request.getBusinessType())
                    .setResult(1)
                    .build();
        } catch (Exception ex) {
            log.info("异常｜" + ex);
            return res.setErrorMsg(ex.getMessage()).build();
        }
    }

    public void msgSendB2C4XiaoDian(MsgSendB2CRequest request) throws Exception {
        if (StringUtils.isNotBlank(request.getSenderId()) && StringUtils.isNotBlank(request.getTargetId())) {
            KlinkPressureTestClient.init(APP_ID, "KUAISHOU", "ANDROID_PHONE", request.getLaneId());
            KlinkConfig klinkConfig = QaRiskObjectConfigKey.klinkSwitch.getObject();
            JsonObject response = queryUserToken("00635321-8464-4815-8d15-45929c610aff",
                    Long.parseLong(request.getSenderId()), "kuaishou.shop.im",
                    "web_7d9f5c6bcaae49d1af731c76c6ad520f");
            if (!"SUCCESS".equals(response.get("error_message").getAsString())) {
                throw new Exception("获取token失败|" + response.get("error_message"));
            }
            String tokenValue = response.getAsJsonObject("data").get("token").getAsString();
            String ssecurity = response.getAsJsonObject("data").get("security").getAsString();
            boolean isStaff = response.getAsJsonObject("data").get("owner_id").getAsLong()
                    != response.getAsJsonObject("data").get("user_id").getAsLong();
            csSendUtil.merchantSend(klinkConfig.getIp(), klinkConfig.getPort(), tokenValue,
                    ssecurity, request.getTargetId(), request.getText(), request.getSenderId(), isStaff);
        } else {
            throw new Exception("发送者和接收者id不能未空|" + request.getSenderId() + "｜" + request.getTargetId());
        }
    }


    public void msgSendB2C4ServiceMarket(MsgSendB2CRequest request) throws Exception {
        if (StringUtils.isNotBlank(request.getSenderId()) && StringUtils.isNotBlank(request.getTargetId())) {
            KlinkPressureTestClient.init(APP_ID, "KUAISHOU", "PC_WEB", request.getLaneId());
            KlinkConfig klinkConfig = QaRiskObjectConfigKey.klinkSwitch.getObject();
            JsonObject response = queryUserToken("f79501da-48e0-4f27-964a-ab811eb3b9f9",
                    Long.parseLong(request.getSenderId()), "kshop.servicemarket.im",
                    "web_7d9f5c6bcaae49d1af731c76c6ad520f");
            if (!"SUCCESS".equals(response.get("error_message").getAsString())) {
                throw new Exception("获取token失败|" + response.get("error_message"));
            }
            String tokenValue = response.getAsJsonObject("data").get("token").getAsString();
            String ssecurity = response.getAsJsonObject("data").get("security").getAsString();
            boolean isStaff = response.getAsJsonObject("data").get("owner_id").getAsLong()
                    != response.getAsJsonObject("data").get("user_id").getAsLong();
            csSendUtil.serviceMarketMerchantSend(klinkConfig.getIp(), klinkConfig.getPort(), tokenValue,
                    ssecurity, request.getSenderId(), request.getText(), request.getTargetId(), isStaff);

        } else {
            throw new Exception("发送者和接收者id不能未空|" + request.getSenderId() + "｜" + request.getTargetId());
        }
    }

    public void msgSendC2B4Distribute(String userId, String targetId, String text, String laneId) throws Exception {
        if (StringUtils.isNotBlank(userId) && StringUtils.isNotBlank(targetId)) {
            KlinkPressureTestClient.init(APP_ID, "KUAISHOU", "PC_WEB", laneId);
            KlinkConfig klinkConfig = QaRiskObjectConfigKey.klinkSwitch.getObject();
            JsonObject response = queryUserToken4distribute(Long.parseLong(userId), "kuaishou.shop.im",
                    "web_7d9f5c6bcaae49d1af731c76c6ad520f", laneId);
            if (response.get("result").getAsInt() != 1) {
                throw new Exception("获取token失败|" + response.get("error_message"));
            }
            String tokenValue = response.getAsJsonObject("data").get("token").getAsString();
            String ssecurity = response.getAsJsonObject("data").get("security").getAsString();
            csSendUtil.distributeSend(klinkConfig.getIp(), klinkConfig.getPort(), tokenValue,
                    ssecurity, userId, text, targetId);

        } else {
            throw new Exception("发送者和接收者id不能未空|" + userId + "｜" + targetId);
        }
    }

    public JsonObject queryUserToken4distribute(long userId, String sid, String did, String laneId) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("user_id", userId);
        jsonObject.addProperty("sid", sid);
        jsonObject.addProperty("did", did);
        KessRun kessrun = KessRun.builder()
                .jsonData(JsonUtils.jsonObjectToMap(jsonObject))
                .rpcName(KWAISHOP_CS_SCENE_SERVICE)
                .fullRrcMethod(DISTRIBUTE_GET_USER_TOKEN)
                .laneId(laneId)
                .build();
        try {
            String res = KessUtil.run(kessrun);
            log.info("response = {}", res);
            return JsonUtils.stringToJson(res, JsonObject.class);
        } catch (Exception e) {
            log.error("GetUserToken 调用异常，{}", e.getMessage());
            throw new BizException(SERVER_ERROR);
        }
    }


    public JsonObject queryUserToken(String appKey, long userId, String sid, String did) {
        JsonObject jsonObject = new JsonObject();
        JsonObject loginInfo = new JsonObject();
        loginInfo.addProperty("app_key", appKey);
        loginInfo.addProperty("login_user_id", userId);
        loginInfo.addProperty("did", did);
        jsonObject.addProperty("sid", sid);
        jsonObject.add("login_info", loginInfo);
        KessRun kessrun = KessRun.builder()
                .jsonData(JsonUtils.jsonObjectToMap(jsonObject))
                .rpcName(KWAISHOP_CS_SCENE_SERVICE)
                .fullRrcMethod(GET_USER_TOKEN)
                .build();
        try {
            String res = KessUtil.run(kessrun);
            log.info("response = {}", res);
            return JsonUtils.stringToJson(res, JsonObject.class);
        } catch (Exception e) {
            log.error("GetUserToken 调用异常，{}", e.getMessage());
            throw new BizException(SERVER_ERROR);
        }
    }


    public InfraPassportAgent.NewZtTokenResult getToken(long userId, String sid, String did, String kpn) {
        try {
            InfraPassportAgent.NewZtTokenResult newZtTokenResult = passportTokenService.createZtToken(kpn, sid, userId, did, "");
            if (!newZtTokenResult.getResult()) {
                throw new BizException(SERVER_ERROR);
            }
            return newZtTokenResult;
        } catch (Exception ex) {
            log.error("getToken 调用异常，{}", ex.getMessage());
            throw new BizException(SERVER_ERROR);
        }

    }

    public InfraPassportAgent.CreateVirtualIdTokenResult getToken(long userId, String sid, String did) {
        try {
            InfraPassportAgent.CreateVirtualIdTokenResult newZtTokenResult = passportTokenService.createVirtualIdToken(sid, did, userId, userId);
            if (!newZtTokenResult.getResult()) {
                throw new BizException(SERVER_ERROR);
            }
            return newZtTokenResult;
        } catch (Exception ex) {
            log.error("getToken 调用异常，{}", ex.getMessage());
            throw new BizException(SERVER_ERROR);
        }

    }


}
