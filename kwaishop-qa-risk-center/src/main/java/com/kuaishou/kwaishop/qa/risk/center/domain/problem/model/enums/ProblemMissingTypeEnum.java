package com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-05
 */
public enum ProblemMissingTypeEnum {

    UNKNOWN(0, "未知类型"),
    MISSING(1, "漏测"),
    NOT_MISSING(2, "非漏测"),
    ;

    private final Integer code;

    private final String desc;

    ProblemMissingTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ProblemMissingTypeEnum of(Integer code) {
        for (ProblemMissingTypeEnum entityTypeEnum: values()) {
            if (entityTypeEnum.getCode().equals(code) && entityTypeEnum.getCode() > 0) {
                return entityTypeEnum;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo() {
        List<EnumInfo> res = new ArrayList<>();
        for (ProblemMissingTypeEnum typeEnum: values()) {
            if (typeEnum.getCode() > 0) {
                EnumInfo enumInfo = EnumInfo.newBuilder()
                        .setValue(typeEnum.getCode())
                        .setDesc(typeEnum.getDesc())
                        .build();
                res.add(enumInfo);
            }
        }
        return res;
    }
}
