package com.kuaishou.kwaishop.qa.risk.center.tianhe.Service;

import java.util.List;
import java.util.UUID;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AddSampleSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AddSampleSetResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteCaseFromSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteCaseFromSetResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteSampleSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteSampleSetResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ExecuteSampleSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ExecuteSampleSetResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.InsertCaseIntoSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.InsertCaseIntoSetResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.QuerySampleBySetIdData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.QuerySampleBySetIdRecord;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.QuerySampleBySetIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.QuerySampleBySetIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.QuerySampleSetExecuteListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.QuerySampleSetExecuteListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.SampleExecuteListData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.SampleSetDebugRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.SampleSetDebugResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.SampleSetExecuteListData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.TianheDebugRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.TianheDebugResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateSampleSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateSampleSetResponse;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Sample.TianheExecuteSampleDO;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Sample.TianheSampleCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.SampleSet.TianheExecuteSampleSetDO;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.SampleSet.TianheSampleSetDO;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Enums.TianheErrCodeEnum;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.Sample.TianheExecuteSampleMapper;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.Sample.TianheSampleCaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.SampleSet.TianheExecuteSampleSetMapper;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.SampleSet.TianheSampleSetMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-03-19
 */
@Service
@Slf4j
public class TianheSampleSetService {

    private final TianheSampleSetMapper tianheSampleSetMapper;

    private final TianheExecuteSampleSetMapper tianheExecuteSampleSetMapper;

    private final TianheSampleCaseService tianheSampleCaseService;

    private final TianheSampleCaseMapper tianheSampleCaseMapper;

    private final TianheExecuteSampleMapper tianheExecuteSampleMapper;

    public TianheSampleSetService(TianheSampleSetMapper tianheSampleSetMapper, TianheExecuteSampleSetMapper tianheExecuteSampleSetMapper,
                                  TianheSampleCaseService tianheSampleCaseService, TianheSampleCaseMapper tianheSampleCaseMapper,
                                  TianheExecuteSampleMapper tianheExecuteSampleMapper) {
        this.tianheExecuteSampleSetMapper = tianheExecuteSampleSetMapper;
        this.tianheSampleCaseService = tianheSampleCaseService;
        this.tianheSampleCaseMapper = tianheSampleCaseMapper;
        this.tianheExecuteSampleMapper = tianheExecuteSampleMapper;
        this.tianheSampleSetMapper = tianheSampleSetMapper;
    }


    public QuerySampleBySetIdResponse querySampleBySetIdResponse(QuerySampleBySetIdRequest request) {
        log.info("QuerySampleBySetId request {}", ObjectMapperUtils.toJSON(request));
        QuerySampleBySetIdResponse.Builder builder = QuerySampleBySetIdResponse.newBuilder();
        try {
            QuerySampleBySetIdData.Builder tempData = QuerySampleBySetIdData.newBuilder();
            // 校验入参
            if (request.getSampleSetId() <= 0) {
                return builder.setResult(1) // 250509，前端要求这里返回1
                        .setErrorMsg("参数不合法")
                        .setSuccess(true) // 250509修改，前端要求这里返回个true
                        .setData(QuerySampleBySetIdData.newBuilder().build())
                        .build();
            }
            TianheSampleSetDO sampleSet = tianheSampleSetMapper.selectById(request.getSampleSetId());
            // 校验用例集是否被删除
            if (sampleSet.getDeleted() == 1) {
                return builder.setResult(TianheErrCodeEnum.RECORD_DELETED.getCode())
                        .setSuccess(false)
                        .setErrorMsg("用例集已删除")
                        .setData(QuerySampleBySetIdData.newBuilder().build())
                        .build();
            }
            // samples存用例ID
            List<Integer> sampleList = ObjectMapperUtils.fromJSON(sampleSet.getSamples(), List.class, Integer.class);
            tempData.setTotal(sampleList.size());
            for (int sample : sampleList) {
                TianheSampleCaseDO sampleDetail = tianheSampleCaseMapper.selectById(sample);
                tempData.addList(QuerySampleBySetIdRecord.newBuilder()
                        .setId(sample).setName(sampleDetail.getName()).setAppKey(sampleDetail.getAppKey())
                        .setPageCode(sampleDetail.getPageCode()).setPlatformSource(sampleDetail.getPlatformSource()).build());
            }
            return builder.setResult(TianheErrCodeEnum.SUCCESS.getCode())
                    .setSuccess(true)
                    .setErrorMsg("success")
                    .setData(tempData.build())
                    .build();
        } catch (Exception e) {
            log.error("QuerySampleBySetId error ", e);
            return builder.setResult(TianheErrCodeEnum.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("获取用例列表异常")
                    .build();
        }

    }

    public AddSampleSetResponse addSampleSetResponse(AddSampleSetRequest request) {
        return handleAddSampleSetResponse(request);
    }

    private AddSampleSetResponse handleAddSampleSetResponse(AddSampleSetRequest request) {
        log.info("AddSampleSet request {}", ObjectMapperUtils.toJSON(request));
        AddSampleSetResponse.Builder builder = AddSampleSetResponse.newBuilder();
        try {
            // 校验入参
            if (((request.getIsInspectionRequired() == 1) && request.getInspectionFrequency().equals(""))
                    || request.getName().equals("") || request.getCreator().equals("") || request.getAppId() <= 0) {
                return builder
                        .setResult(TianheErrCodeEnum.PARAM_INVALID.getCode())
                        .setSuccess(false)
                        .setErrorMsg("参数不合法")
                        .build();
            }
            long timestamp = System.currentTimeMillis();
            TianheSampleSetDO data = new TianheSampleSetDO();
            data.setSetName(request.getName());
            data.setCreateTime(timestamp);
            data.setUpdateTime(timestamp);
            data.setDeleted(0);
            data.setCreator(request.getCreator());
            data.setIsInspectionRequired(request.getIsInspectionRequired());
            data.setInspectionFrequency(request.getInspectionFrequency());
            data.setAppId((long) request.getAppId());
            // 塞个空的json列表
            data.setSamples("[]");

            int insertCount = tianheSampleSetMapper.insert(data);
            boolean flag = insertCount > 0;
            int result = flag ? TianheErrCodeEnum.SUCCESS.getCode() : TianheErrCodeEnum.INSERT_FAIL.getCode();
            String errMsg = flag ? "success" : "创建用例集失败";
            return builder.setResult(result)
                    .setSuccess(flag)
                    .setErrorMsg(errMsg)
                    .build();

        } catch (Exception e) {
            log.error("AddSampleSet error ", e);
            return builder
                    .setResult(TianheErrCodeEnum.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("新增用例集异常")
                    .build();
        }
    }

    public DeleteSampleSetResponse deleteSampleSetResponse(DeleteSampleSetRequest request) {
        return handleDeleteSampleSetResponse(request);
    }

    private DeleteSampleSetResponse handleDeleteSampleSetResponse(DeleteSampleSetRequest request) {
        log.info("DeleteSampleSet request {}", ObjectMapperUtils.toJSON(request));
        DeleteSampleSetResponse.Builder builder = DeleteSampleSetResponse.newBuilder();
        try {
            // 校验入参
            if (request.getId() <= 0 || request.getOperator().equals("")) {
                return builder.setResult(TianheErrCodeEnum.PARAM_INVALID.getCode())
                        .setSuccess(false)
                        .setErrorMsg("参数不合法")
                        .build();
            }
            // 判断下是否是系统自动生成的用例集
            TianheSampleSetDO sampleSet = tianheSampleSetMapper.selectById(request.getId());
            if (sampleSet.getCreator().equals("System")) {
                return builder.setResult(TianheErrCodeEnum.NO_PERMISSION.getCode())
                        .setSuccess(false)
                        .setErrorMsg("系统自动生成的用例集不允许删除")
                        .build();
            }
            // 校验下是否已被删除
            if (sampleSet.getDeleted() == 1) {
                return builder.setResult(TianheErrCodeEnum.RECORD_DELETED.getCode())
                        .setSuccess(false)
                        .setErrorMsg("用例集已删除")
                        .build();
            }
            TianheSampleSetDO data = new TianheSampleSetDO();
            UpdateWrapper<TianheSampleSetDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", request.getId());
            data.setDeleted(1);
            data.setUpdateTime(System.currentTimeMillis());
            data.setSamples("");

            int updateCount = tianheSampleSetMapper.update(data, updateWrapper);
            boolean flag = updateCount > 0;
            int result = flag ? TianheErrCodeEnum.SUCCESS.getCode() : TianheErrCodeEnum.UPDATE_FAIL.getCode();
            String errMsg = flag ? "success" : "删除用例集失败";
            return builder.setResult(result)
                    .setSuccess(flag)
                    .setErrorMsg(errMsg)
                    .build();

        } catch (Exception e) {
            log.error("DeleteSampleSet error ", e);
            return builder.setResult(TianheErrCodeEnum.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("删除用例集异常")
                    .build();
        }
    }

    public UpdateSampleSetResponse updateSampleSetResponse(UpdateSampleSetRequest request) {
        return handleUpdateSampleSetResponse(request);
    }

    private UpdateSampleSetResponse handleUpdateSampleSetResponse(UpdateSampleSetRequest request) {
        log.info("UpdateSampleSet request {}", ObjectMapperUtils.toJSON(request));
        UpdateSampleSetResponse.Builder builder = UpdateSampleSetResponse.newBuilder();
        try {
            // 校验入参
            if (request.getId() <= 0 || request.getName().equals("")
                    || (request.getIsInspectionRequired() == 1 && request.getInspectionFrequency().equals(""))) {
                return builder.setResult(TianheErrCodeEnum.PARAM_INVALID.getCode())
                        .setSuccess(false)
                        .setErrorMsg("参数不合法")
                        .build();
            }
            // 判断下是否是系统自动生成的用例集
            TianheSampleSetDO sampleSet = tianheSampleSetMapper.selectById(request.getId());
            if (sampleSet.getCreator().equals("System")) {
                return builder.setResult(TianheErrCodeEnum.NO_PERMISSION.getCode())
                        .setSuccess(false)
                        .setErrorMsg("系统自动生成的用例集不允许修改")
                        .build();
            }
            // 校验下是否已被删除
            if (sampleSet.getDeleted() == 1) {
                return builder.setResult(TianheErrCodeEnum.RECORD_DELETED.getCode())
                        .setSuccess(false)
                        .setErrorMsg("用例集已被删除，不允许修改")
                        .build();
            }
            TianheSampleSetDO data = new TianheSampleSetDO();
            UpdateWrapper<TianheSampleSetDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", request.getId());
            data.setSetName(request.getName());
            data.setIsInspectionRequired(request.getIsInspectionRequired());
            data.setInspectionFrequency(request.getInspectionFrequency());
            data.setUpdateTime(System.currentTimeMillis());

            int updateCount = tianheSampleSetMapper.update(data, updateWrapper);
            boolean flag = updateCount > 0;
            int result = flag ? TianheErrCodeEnum.SUCCESS.getCode() : TianheErrCodeEnum.UPDATE_FAIL.getCode();
            String errMsg = flag ? "success" : "更新用例集失败";

            return builder
                    .setResult(result)
                    .setSuccess(flag)
                    .setErrorMsg(errMsg)
                    .build();

        } catch (Exception e) {
            log.error("UpdateSampleSet error ", e);
            return builder
                    .setResult(TianheErrCodeEnum.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("更新用例集异常")
                    .build();
        }
    }

    public InsertCaseIntoSetResponse insertCaseIntoSetResponse(InsertCaseIntoSetRequest request) {
        return handleInsertCaseIntoSetResponse(request);
    }

    // TODO 校验用例是否被删除是否要做?
    private InsertCaseIntoSetResponse handleInsertCaseIntoSetResponse(InsertCaseIntoSetRequest request) {
        log.info("insertCaseIntoSet request {}", ObjectMapperUtils.toJSON(request));
        InsertCaseIntoSetResponse.Builder builder = InsertCaseIntoSetResponse.newBuilder();
        try {
            TianheSampleSetDO data = tianheSampleSetMapper.selectById(request.getSetId());
            // 判断用例集是否存在
            if (data.getDeleted().equals(1)) {
                return builder.setResult(TianheErrCodeEnum.RECORD_DELETED.getCode())
                        .setSuccess(false)
                        .setErrorMsg("该用例集已删除")
                        .build();
            }
            List<Integer> samples = ObjectMapperUtils.fromJSON(data.getSamples(), List.class, Integer.class);
            // 判断用例是否已经在用例集里
            if (samples.contains(request.getCaseId())) {
                return builder.setResult(TianheErrCodeEnum.UPDATE_FAIL.getCode())
                        .setSuccess(false)
                        .setErrorMsg("该用例已存在！")
                        .build();
            }
            samples.add(request.getCaseId());
            TianheSampleSetDO newData = new TianheSampleSetDO();
            UpdateWrapper<TianheSampleSetDO> updateWrapper = new UpdateWrapper<>();
            newData.setSamples(ObjectMapperUtils.toJSON(samples));
            updateWrapper.eq("id", request.getSetId());

            int updateCount = tianheSampleSetMapper.update(newData, updateWrapper);
            boolean flag = updateCount > 0;
            int result = flag ? TianheErrCodeEnum.SUCCESS.getCode() : TianheErrCodeEnum.UPDATE_FAIL.getCode();
            String errMsg = flag ? "success" : "插入用例失败";

            return builder.setResult(result)
                    .setSuccess(flag)
                    .setErrorMsg(errMsg)
                    .build();

        } catch (Exception e) {
            log.error("insertCaseIntoSet error ", e);
            return builder.setResult(TianheErrCodeEnum.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("插入用例异常")
                    .build();
        }
    }

    public DeleteCaseFromSetResponse deleteCaseFromSetResponse(DeleteCaseFromSetRequest request) {
        return handleDeleteCaseFromSetResponse(request);
    }

    private DeleteCaseFromSetResponse handleDeleteCaseFromSetResponse(DeleteCaseFromSetRequest request) {
        log.info("DeleteCaseFromSet request {}", ObjectMapperUtils.toJSON(request));
        DeleteCaseFromSetResponse.Builder builder = DeleteCaseFromSetResponse.newBuilder();
        try {
            TianheSampleSetDO data = tianheSampleSetMapper.selectById(request.getSetId());
            // 判断用例集是否存在
            if (data.getDeleted().equals(1)) {
                return builder.setResult(TianheErrCodeEnum.RECORD_DELETED.getCode())
                        .setSuccess(false)
                        .setErrorMsg("该用例集已删除")
                        .build();
            }
            List<Integer> samples = ObjectMapperUtils.fromJSON(data.getSamples(), List.class, Integer.class);
            // 判断用例是否已经在用例集里
            if (!samples.contains(request.getCaseId())) {
                return builder.setResult(TianheErrCodeEnum.UPDATE_FAIL.getCode())
                        .setSuccess(false)
                        .setErrorMsg("该用例不存在！")
                        .build();
            }
            samples.remove(Integer.valueOf(request.getCaseId()));
            TianheSampleSetDO newData = new TianheSampleSetDO();
            UpdateWrapper<TianheSampleSetDO> updateWrapper = new UpdateWrapper<>();
            newData.setSamples(ObjectMapperUtils.toJSON(samples));
            updateWrapper.eq("id", request.getSetId());

            int updateCount = tianheSampleSetMapper.update(newData, updateWrapper);
            boolean flag = updateCount > 0;
            int result = flag ? TianheErrCodeEnum.SUCCESS.getCode() : TianheErrCodeEnum.UPDATE_FAIL.getCode();
            String errMsg = flag ? "success" : "删除用例失败";

            return builder.setResult(result)
                    .setSuccess(flag)
                    .setErrorMsg(errMsg)
                    .build();

        } catch (Exception e) {
            log.error("DeleteCaseFromSet error ", e);
            return builder.setResult(TianheErrCodeEnum.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("删除用例异常")
                    .build();
        }
    }

    public SampleSetDebugResponse sampleSetDebugResponse(SampleSetDebugRequest request) {
        TianheSampleSetDO data = tianheSampleSetMapper.selectById(request.getSetId());
        data.setAppId((long) request.getAppId());
        data.setSamples("[]");
        UpdateWrapper<TianheSampleSetDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", request.getSetId());
        tianheSampleSetMapper.update(data, updateWrapper);

        return SampleSetDebugResponse.newBuilder()
                .setResult(1)
                .setSuccess(true)
                .setErrorMsg("success")
                .build();
    }

    public ExecuteSampleSetResponse executeSampleSetResponse(ExecuteSampleSetRequest request) {
        return handleExecuteSampleSetResponse(request);
    }

    // TODO 执行人校验 前端做? 后端搞有点复杂
    private ExecuteSampleSetResponse handleExecuteSampleSetResponse(ExecuteSampleSetRequest request) {
        log.info("ExecuteSampleSet request {}", ObjectMapperUtils.toJSON(request));
        ExecuteSampleSetResponse.Builder builder = ExecuteSampleSetResponse.newBuilder();
        try {
            String executeId = UUID.randomUUID().toString();
            TianheSampleSetDO sampleSet = tianheSampleSetMapper.selectById(request.getSetId());
            // 校验下用例集是否被删除
            if (sampleSet.getDeleted() == 1) {
                return builder.setResult(TianheErrCodeEnum.RECORD_DELETED.getCode())
                        .setSuccess(false)
                        .setErrorMsg("用例集已被删除")
                        .build();
            }
            List<Integer> samples = ObjectMapperUtils.fromJSON(sampleSet.getSamples(), List.class, Integer.class);
            if (samples.isEmpty()) {
                return builder.setResult(TianheErrCodeEnum.INSERT_FAIL.getCode())
                        .setSuccess(false)
                        .setErrorMsg("该用例集下无可执行用例")
                        .build();
            }
            if (!tianheSampleCaseService.batchExecuteSampleResponse(samples, executeId, "")) {
                // 校验下批量执行用例是否成功
                return builder.setResult(TianheErrCodeEnum.INSERT_FAIL.getCode())
                        .setSuccess(false)
                        .setErrorMsg("执行用例集失败")
                        .build();
            }
            long timestamp = System.currentTimeMillis();
            TianheExecuteSampleSetDO data = new TianheExecuteSampleSetDO();
            data.setSetId((long) request.getSetId());
            data.setSetName(sampleSet.getSetName());
            data.setExecuteId(executeId);
            data.setCreateTime(timestamp);
            data.setUpdateTime(timestamp);

            boolean flag = tianheExecuteSampleSetMapper.insert(data) > 0;
            int result = flag ? TianheErrCodeEnum.SUCCESS.getCode() : TianheErrCodeEnum.INSERT_FAIL.getCode();
            String errMsg = flag ? "success" : "执行用例集失败";
            return builder.setResult(result)
                    .setSuccess(flag)
                    .setErrorMsg(errMsg)
                    .setExecuteId(executeId)
                    .build();
        } catch (Exception e) {
            log.error("ExecuteSampleSet error ", e);
            return builder
                    .setResult(TianheErrCodeEnum.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("执行用例集异常")
                    .build();
        }
    }

    public QuerySampleSetExecuteListResponse querySampleSetExecuteListResponse(QuerySampleSetExecuteListRequest request) {
        return handleQuerySampleSetExecuteListResponse(request);
    }

    private QuerySampleSetExecuteListResponse handleQuerySampleSetExecuteListResponse(QuerySampleSetExecuteListRequest request) {
        log.info("QuerySampleSetExecuteList request {}", ObjectMapperUtils.toJSON(request));
        QuerySampleSetExecuteListResponse.Builder builder = QuerySampleSetExecuteListResponse.newBuilder();
        try {
            // 这里不校验用例集是否删除吧
            QueryWrapper<TianheExecuteSampleSetDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("set_id", request.getSetId());
            queryWrapper.orderByDesc("create_time");
            List<TianheExecuteSampleSetDO> list = tianheExecuteSampleSetMapper.selectList(queryWrapper);
            builder.setTotal(list.size());
            // 遍历用例集执行记录
            for (TianheExecuteSampleSetDO executeSampleSet : list) {
                SampleSetExecuteListData.Builder tempSetList = SampleSetExecuteListData.newBuilder();
                QueryWrapper<TianheExecuteSampleDO> sampleDOQueryWrapper = new QueryWrapper<>();
                sampleDOQueryWrapper.eq("execute_id", executeSampleSet.getExecuteId());
                sampleDOQueryWrapper.orderByDesc("create_time");
                // 根据用例集的执行id查对应的用例执行记录
                List<TianheExecuteSampleDO> executeSampleDOList = tianheExecuteSampleMapper.selectList(sampleDOQueryWrapper);
                tempSetList.setExecuteId(executeSampleSet.getExecuteId()).setExecuteCount(executeSampleDOList.size());
                for (TianheExecuteSampleDO executeSample : executeSampleDOList) {
                    SampleExecuteListData.Builder tempSampleList = SampleExecuteListData.newBuilder();
                    tempSetList.addSampleExecuteList(tempSampleList.setSampleId(Math.toIntExact(executeSample.getSampleId()))
                            .setSampleName(executeSample.getName()).setCreateTime(executeSample.getCreateTime())
                            .setExecuteStatus(executeSample.getExecuteStatus()).setActionType(executeSample.getActionType())
                            .setExt(executeSample.getExt()).setXpath(executeSample.getXpath()));
                }
                builder.addSetExecuteList(tempSetList);
            }
            return builder.setResult(TianheErrCodeEnum.SUCCESS.getCode())
                    .setSuccess(true)
                    .setErrorMsg("success")
                    .build();
        } catch (Exception e) {
            log.error("QuerySampleSetExecuteList error ", e);
            return builder.setResult(TianheErrCodeEnum.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("查询用例集执行记录异常")
                    .build();
        }
    }

    public TianheDebugResponse debugResponse(TianheDebugRequest request) {
        log.info("debugResponse request {}", ObjectMapperUtils.toJSON(request));
        try {
//            long startId = 21L;
//            long endId = 832L;
            TianheExecuteSampleDO tianheExecuteSampleDO = new TianheExecuteSampleDO();
            tianheExecuteSampleDO.setActionType(1);
            QueryWrapper<TianheExecuteSampleDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("actionType", 1);

            return TianheDebugResponse.newBuilder()
                    .setResult(TianheErrCodeEnum.SUCCESS.getCode())
                    .setSuccess(true)
                    .setErrorMsg("success")
                    .build();
        } catch (Exception e) {
            log.error("debugResponse error ", e);
            return TianheDebugResponse.newBuilder()
                    .setResult(TianheErrCodeEnum.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("debug异常")
                    .build();
        }
    }
}
