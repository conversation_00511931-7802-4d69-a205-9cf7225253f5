package com.kuaishou.kwaishop.qa.risk.center.domain.account.handler;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.APPROVE_TYPE_ERROR;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.service.ApprovalService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.ApprovalBizCodeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2025/1/20 14:12
 * @注释
 */
@Service
@Lazy
@Slf4j
public class ApprovalFactory {

    @Autowired
    private List<ApprovalService> approvalServices;


    private Map<String, ApprovalService> approvalServiceMap;

    @PostConstruct
    private void init() {
        approvalServiceMap = new HashMap<>();
        approvalServices.forEach(p -> approvalServiceMap.put(p.getCode(), p));
    }

    public ApprovalService getService(String code) {
        if (ApprovalBizCodeEnum.fromBizCode(code) != null) {
            if (approvalServiceMap.containsKey(code)) {
                return approvalServiceMap.get(code);
            }
        }
        throw new BizException(APPROVE_TYPE_ERROR);
    }
}
