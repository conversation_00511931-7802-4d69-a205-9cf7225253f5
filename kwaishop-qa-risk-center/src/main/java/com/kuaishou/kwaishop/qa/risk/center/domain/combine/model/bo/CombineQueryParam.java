package com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-14
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CombineQueryParam {

    private Long centerId;

    private Long teamId;

    private Integer dateType;
}
