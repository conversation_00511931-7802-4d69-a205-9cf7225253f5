package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.KspayRiskDetailBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KrpcKspayRiskDetailDomainServiceGrpc.KspayRiskDetailDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayCreateRiskDefenseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayCreateRiskDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayCreateRiskDetailResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayRiskDefenseDetail;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayRiskDefenseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayRiskListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayRiskResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspaySubRiskDetailParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayUpdateDefenseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayUpdateSubRiskResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.PageKspayRiskDetailDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.QueryPageKspayRiskDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.QueryPageKspayRiskDetailResponse;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-22
 */

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class KspayRiskDetailDomainServiceImpl extends KspayRiskDetailDomainServiceImplBaseV2 {


    @Autowired
    private KspayRiskDetailBizService kspayRiskDetailBizService;


    @Override
    public KspayCreateRiskDetailResponse kspayCreateRiskDetail(KspayCreateRiskDetailRequest request) {
        log.info("[KspayRiskDetailDomainServiceImpl] kspayCreateRiskDetail request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayRiskDetailBizService.kspayCreateRiskDetail(request);
            return KspayCreateRiskDetailResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskDetailDomainServiceImpl] kspayCreateRiskDetail bizError, exception: ", e);
            return KspayCreateRiskDetailResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskDetailDomainServiceImpl] kspayCreateRiskDetail error, exception: ", e);
            return KspayCreateRiskDetailResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }



    @Override
    public QueryPageKspayRiskDetailResponse kspayQueryPageRiskDetailList(QueryPageKspayRiskDetailRequest request) {
        log.info("[KspayRiskDetailDomainServiceImpl] queryPageKspayRiskDetailList request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            PageKspayRiskDetailDTO pageKspayRiskDetailDTO = kspayRiskDetailBizService.queryPageRiskDetailList(request);
            return QueryPageKspayRiskDetailResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(pageKspayRiskDetailDTO)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskDetailDomainServiceImpl] queryPageKspayRiskDetailList bizError, exception: ", e);
            return QueryPageKspayRiskDetailResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskDetailDomainServiceImpl] queryPageKspayRiskDetailList error, exception: ", e);
            return QueryPageKspayRiskDetailResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayCreateRiskDefenseResponse kspayCreateRiskDefense(KspayRiskDefenseDetail request) {
        log.info("[KspayRiskDetailDomainServiceImpl] kspayCreateRiskDefense request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayRiskDetailBizService.createRiskDefense(request);
            return KspayCreateRiskDefenseResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskDetailDomainServiceImpl] kspayCreateRiskDefense bizError, exception: ", e);
            return KspayCreateRiskDefenseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskDetailDomainServiceImpl] kspayCreateRiskDefense error, exception: ", e);
            return KspayCreateRiskDefenseResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayRiskListResponse kspayQueryRiskList(KspayRiskDefenseRequest request) {
        log.info("[KspayRiskDetailDomainServiceImpl] kspayQueryRiskList request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            List<KspayRiskResponse> response =  kspayRiskDetailBizService.queryRiskList(request);
            return KspayRiskListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllKspayRiskList(response)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskDetailDomainServiceImpl] kspayQueryRiskList bizError, exception: ", e);
            return KspayRiskListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskDetailDomainServiceImpl] kspayQueryRiskList error, exception: ", e);
            return KspayRiskListResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayUpdateSubRiskResponse kspayUpdateSubRisk(KspaySubRiskDetailParam request) {
        log.info("[KspayRiskDetailDomainServiceImpl] kspayUpdateSubRisk request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayRiskDetailBizService.updateSubRisk(request);
            return KspayUpdateSubRiskResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskDetailDomainServiceImpl] kspayUpdateSubRisk bizError, exception: ", e);
            return KspayUpdateSubRiskResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskDetailDomainServiceImpl] kspayUpdateSubRisk error, exception: ", e);
            return KspayUpdateSubRiskResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayUpdateDefenseResponse kspayUpdateDefense(KspayRiskDefenseDetail request) {
        log.info("[KspayRiskDetailDomainServiceImpl] kspayUpdateDefense request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayRiskDetailBizService.updateDefenseInfo(request);
            return KspayUpdateDefenseResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[KspayRiskDetailDomainServiceImpl] kspayUpdateDefense bizError, exception: ", e);
            return KspayUpdateDefenseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayRiskDetailDomainServiceImpl] kspayUpdateDefense error, exception: ", e);
            return KspayUpdateDefenseResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }
}
