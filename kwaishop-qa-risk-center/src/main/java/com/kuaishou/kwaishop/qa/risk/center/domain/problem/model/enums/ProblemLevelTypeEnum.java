package com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-02
 */
public enum ProblemLevelTypeEnum {

    UNKNOWN(0, "未知类型", ProblemDetailTypeEnum.ACCIDENT.getCode()),
    P0(1, "P0级故障", ProblemDetailTypeEnum.ACCIDENT.getCode()),
    P1(2, "P1级故障", ProblemDetailTypeEnum.ACCIDENT.getCode()),
    P2(3, "P2级故障", ProblemDetailTypeEnum.ACCIDENT.getCode()),
    P3(4, "P3级故障", ProblemDetailTypeEnum.ACCIDENT.getCode()),
    P4(5, "P4级故障", ProblemDetailTypeEnum.ACCIDENT.getCode()),
    P5(6, "P5级故障", ProblemDetailTypeEnum.ACCIDENT.getCode()),


    UNKNOWN1(0, "未知类型", ProblemDetailTypeEnum.ONLINE_PROBLEM.getCode()),
    HIGH(1, "高优问题", ProblemDetailTypeEnum.ONLINE_PROBLEM.getCode()),
    MED(2, "中优问题", ProblemDetailTypeEnum.ONLINE_PROBLEM.getCode()),
    LOW(3, "低优问题", ProblemDetailTypeEnum.ONLINE_PROBLEM.getCode()),

    ;

    private final Integer code;

    private final String desc;

    private final Integer problemType;

    ProblemLevelTypeEnum(Integer code, String desc, Integer problemType) {
        this.code = code;
        this.desc = desc;
        this.problemType = problemType;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getProblemType() {
        return problemType;
    }

    public static ProblemLevelTypeEnum of(Integer code, Integer problemType) {
        for (ProblemLevelTypeEnum entityTypeEnum: values()) {
            if (entityTypeEnum.getCode().equals(code) && entityTypeEnum.getCode() > 0
                    && entityTypeEnum.getProblemType().equals(problemType)) {
                return entityTypeEnum;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo(Integer problemType) {
        List<EnumInfo> res = new ArrayList<>();
        for (ProblemLevelTypeEnum typeEnum: values()) {
            if (typeEnum.getCode() > 0 && typeEnum.getProblemType().equals(problemType)) {
                EnumInfo enumInfo = EnumInfo.newBuilder()
                        .setValue(typeEnum.getCode())
                        .setDesc(typeEnum.getDesc())
                        .build();
                res.add(enumInfo);
            }
        }
        return res;
    }
}
