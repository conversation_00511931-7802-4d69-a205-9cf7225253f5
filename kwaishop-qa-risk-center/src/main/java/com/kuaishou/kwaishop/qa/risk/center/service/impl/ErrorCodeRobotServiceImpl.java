package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCodeError;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.errorcode.BizAreaDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.errorcode.ErrorCodeDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.errorcode.BizAreaService;
import com.kuaishou.kwaishop.qa.risk.center.domain.errorcode.ErrorCodeService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.AddBizAreaRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.AddBizAreaResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.AddErrorCodeRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.AddErrorCodeResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.BizAreaListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.BizAreaListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.DeleteBizAreaRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.DeleteBizAreaResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.DeleteErrorCodeRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.DeleteErrorCodeResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.ErrorCodeInfoResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.ErrorCodeListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.ErrorCodeListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.ErrorInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.KrpcErrorCodeRobotServiceGrpc.ErrorCodeRobotServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.Result;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.UpdateBizAreaRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.UpdateBizAreaResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.UpdateErrorCodeRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode.UpdateErrorCodeResponse;
import com.kuaishou.kwaishop.qa.risk.center.utils.ResultHandler;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class ErrorCodeRobotServiceImpl extends ErrorCodeRobotServiceImplBaseV2 {

//    private static Map<String, Object> gatewayErrorCode = JsonMapperUtils.fromJson("{\n"
//            + "    \"G-802000-1023\": \"流量限制\",\n"
//            + "    \"G-802000-1024\": \"流量限制\",\n"
//            + "    \"G-802000-1022\": \"流量限制\",\n"
//            + "    \"G-802000-1018\": \"流量限制\",\n"
//            + "    \"G-802000-1016\": \"流量限制-限流\",\n"
//            + "    \"G-802000-1017\": \"流量限制-限额\",\n"
//            + "    \"G-803000-1002\": \"鉴权失败\",\n"
//            + "    \"G-803000-1031\": \"鉴权失败\",\n"
//            + "    \"G-804000-1009\": \"请求参数不合法\",\n"
//            + "    \"G-804000-12\": \"请求参数不合法\",\n"
//            + "    \"G-809000-1007\": \"获取RPC反射信息时异常\",\n"
//            + "    \"S-805000-1010\": \"服务异常，调grpc异常\",\n"
//            + "    \"G-809000-1030\": \"服务异常，参数映射转换异常\",\n"
//            + "    \"S-806000-1011\": \"服务超时，调用grpc超时\",\n"
//            + "    \"G-804000-1034\": \"HTTP Method请求方式不合法\",\n"
//            + "    \"G-804000-1035\": \"media type 不合法\",\n"
//            + "    \"G-808000-2\": \"请求过于频繁\",\n"
//            + "    \"G-809000-1026\": \"URL不存在，请检查API路径和域名路由配置\"\n"
//            + "}");



    @Autowired
    private ErrorCodeService errorCodeService;
    @Autowired
    private BizAreaService bizAreaService;

    @Override
    public AddBizAreaResponse addBizArea(AddBizAreaRequest request) {
        AddBizAreaResponse.Builder builder = AddBizAreaResponse.newBuilder();
        try {
            if (request == null
                    || StringUtils.isBlank(request.getOwner())
                    || StringUtils.isBlank(request.getCurUser())
                    || StringUtils.isBlank(request.getAreaCode())
                    || StringUtils.isBlank(request.getAreaCodeDesc())) {
                log.error("addBizArea error, param invalid, request {}", request);
                throw new BizException(BasicErrorCode.PARAM_INVALID);
            }

            bizAreaService.addBizArea(request);
            Result result = ResultHandler.buildSuccessResult();
            return builder.setResult(result).build();
        } catch (Exception e) {
            log.error("addBizArea Exception", e);
            Result result = ResultHandler.buildExceptioResult(e);
            return builder.setResult(result).build();
        }
    }

    @Override
    public UpdateBizAreaResponse updateBizArea(UpdateBizAreaRequest request) {
        UpdateBizAreaResponse.Builder builder = UpdateBizAreaResponse.newBuilder();
        try {
            if (request == null
                    || (request.getId() <= 0)
                    || StringUtils.isBlank(request.getOwner())
                    || StringUtils.isBlank(request.getCurUser())
                    || StringUtils.isBlank(request.getAreaCode())
                    || StringUtils.isBlank(request.getAreaCodeDesc())) {
                log.error("updateBizArea error, param invalid, request {}", request);
                throw new BizException(BasicErrorCode.PARAM_INVALID);
            }
            BizAreaDO bizArea = bizAreaService.getBizAreaById(request.getId());
            if (!bizArea.getCreator().equals(request.getCurUser())) {
                throw new BizException(ErrorCodeError.NOT_PERMIT);
            }
            bizAreaService.updateBizArea(request);
            return builder.setResult(ResultHandler.buildSuccessResult()).build();
        } catch (Exception e) {
            log.error("updateBizArea Exception", e);
            return builder.setResult(ResultHandler.buildExceptioResult(e)).build();
        }
    }

    @Override
    public DeleteBizAreaResponse deleteBizArea(DeleteBizAreaRequest request) {
        DeleteBizAreaResponse.Builder builder = DeleteBizAreaResponse.newBuilder();
        try {
            if (request == null
                    || (request.getId() <= 0)) {
                log.error("deleteBizArea error, param invalid, request {}", request);
                throw new BizException(BasicErrorCode.PARAM_INVALID);
            }
            bizAreaService.deleteBizArea(request);
            return builder.setResult(ResultHandler.buildSuccessResult()).build();
        } catch (Exception e) {
            log.error("deleteBizArea Exception", e);
            return builder.setResult(ResultHandler.buildExceptioResult(e)).build();
        }
    }

    @Override
    public BizAreaListResponse queryBizAreaList(BizAreaListRequest request) {
        BizAreaListResponse.Builder builder = BizAreaListResponse.newBuilder();
        try {
            if (request == null) {
                log.error("queryBizAreaList error, param invalid, request {}", request);
                throw new BizException(BasicErrorCode.PARAM_INVALID);
            }
            BizAreaListResponse bizAreaList = bizAreaService.getBizAreaList(request);
            builder.setResult(ResultHandler.buildSuccessResult());
            builder.addAllBizAreaEntity(bizAreaList.getBizAreaEntityList());
            builder.setTotalPage(bizAreaList.getTotalPage());
            builder.setPageNum(bizAreaList.getPageNum());
            return builder.build();
        } catch (Exception e) {
            log.error("queryBizAreaList Exception", e);
            return builder.setResult(ResultHandler.buildExceptioResult(e)).build();
        }
    }

    @Override
    public AddErrorCodeResponse addErrorCode(AddErrorCodeRequest request) {
        AddErrorCodeResponse.Builder builder = AddErrorCodeResponse.newBuilder();
        try {
            if (request == null
                    || StringUtils.isBlank(request.getCurUser())
                    || StringUtils.isBlank(request.getErrorCode())
                    || StringUtils.isBlank(request.getErrorCodeDesc())) {
                log.error("addErrorCode error, param invalid, request {}", request);
                throw new BizException(BasicErrorCode.PARAM_INVALID);
            }
            errorCodeService.addErrorCode(request);
            return builder.setResult(ResultHandler.buildSuccessResult()).build();
        } catch (Exception e) {
            log.error("addErrorCode Exception", e);
            return builder.setResult(ResultHandler.buildExceptioResult(e)).build();
        }
    }

    @Override
    public UpdateErrorCodeResponse updateErrorCode(UpdateErrorCodeRequest request) {

        UpdateErrorCodeResponse.Builder builder = UpdateErrorCodeResponse.newBuilder();
        try {
            if (request == null
                    || request.getId() <= 0
                    || StringUtils.isBlank(request.getCurUser())
                    || StringUtils.isBlank(request.getErrorCode())
                    || StringUtils.isBlank(request.getErrorCodeDesc())) {
                log.error("updateErrorCode error, param invalid, request {}", request);
                throw new BizException(BasicErrorCode.PARAM_INVALID);
            }
            errorCodeService.updateErrorCode(request);
            return builder.setResult(ResultHandler.buildSuccessResult()).build();
        } catch (Exception e) {
            log.error("updateErrorCode Exception", e);
            return builder.setResult(ResultHandler.buildExceptioResult(e)).build();
        }
    }

    @Override
    public DeleteErrorCodeResponse deleteErrorCode(DeleteErrorCodeRequest request) {
        DeleteErrorCodeResponse.Builder builder = DeleteErrorCodeResponse.newBuilder();
        try {
            if (request == null
                    || request.getId() <= 0
                    || StringUtils.isBlank(request.getCurUser())) {
                log.error("deleteErrorCode error, param invalid, request {}", request);
                throw new BizException(BasicErrorCode.PARAM_INVALID);
            }
            errorCodeService.deleteErrorCode(request);
            return builder.setResult(ResultHandler.buildSuccessResult()).build();
        } catch (Exception e) {
            log.error("deleteErrorCode Exception", e);
            return builder.setResult(ResultHandler.buildExceptioResult(e)).build();
        }
    }

    @Override
    public ErrorCodeListResponse getErrorCodeList(ErrorCodeListRequest request) {
        ErrorCodeListResponse.Builder builder = ErrorCodeListResponse.newBuilder();
        try {
            if (request == null) {
                log.error("getErrorCodeList error, param invalid, request {}", request);
                throw new BizException(BasicErrorCode.PARAM_INVALID);
            }
            ErrorCodeListResponse codeListResponse = errorCodeService.getErrorCodeList(request);
            return builder.setResult(ResultHandler.buildSuccessResult())
                    .addAllErrorCodeEntity(codeListResponse.getErrorCodeEntityList())
                    .setTotalPage(codeListResponse.getTotalPage())
                    .setPageNum(codeListResponse.getPageNum()).build();
        } catch (Exception e) {
            log.error("getErrorCodeList Exception", e);
            return builder.setResult(ResultHandler.buildExceptioResult(e)).build();
        }
    }


    @Override
    public ErrorCodeInfoResponse getErrorCodeInfo(ErrorInfoRequest request) {
        ErrorCodeInfoResponse.Builder builder = ErrorCodeInfoResponse.newBuilder();
        try {
            if (request == null || StringUtils.isBlank(request.getErrorCode())) {
                log.error("getErrorCodeInfo error, param invalid, request {}", request);
                throw new BizException(BasicErrorCode.PARAM_INVALID);
            }
            String[] split = request.getErrorCode().split("-");
            if (!(split.length == 3)
                    || !"SGF".contains(split[0])) {
                log.error("getErrorCodeInfo error, param invalid, request {}", request);
                throw new BizException(ErrorCodeError.ERROR_CODE_NOT_RIGHT);
            }
            BizAreaDO bizAreaDO = bizAreaService.queryByAreaCode(split[0] + "-" + split[1]);
            if (bizAreaDO == null) {
                throw new BizException(ErrorCodeError.CAN_NOT_FIND);
            }
            builder.setResult(ResultHandler.buildSuccessResult());
            builder.setErrorCode(request.getErrorCode());
            builder.setOwner(bizAreaDO.getOwner());
            ErrorCodeDO errorCodeDO = errorCodeService.queryByErrorCode(request);
            if (errorCodeDO != null) {
                builder.setDesc(errorCodeDO.getErrorCodeDesc());
            }
            return builder.build();
        } catch (Exception e) {
            log.error("getErrorCodeList Exception", e);
            return builder.setResult(ResultHandler.buildExceptioResult(e)).build();
        }
    }


//    @Override
//    public ErrorCodeQueryResponse queryErrorCodeInfo(ErrorCodeQueryRequest request) {
//        ErrorCodeQueryResponse.Builder builder = ErrorCodeQueryResponse.newBuilder();
//        try {
//            String errorCode = request.getErrorCode();
//            if (request == null || StringUtils.isBlank(errorCode)) {
//                log.error("queryErrorCodeInfo error, param invalid, request {}", request);
//                throw new BizException(BasicErrorCode.PARAM_INVALID);
//            }
//            String[] split = errorCode.split("-");
//            if (!(split.length == 3)
//                    || !"SGF".contains(split[0])) {
//                log.error("getErrorCodeInfo error, param invalid, request {}", request);
//                throw new BizException(ErrorCodeError.ERROR_CODE_NOT_RIGHT);
//            }
//            if("S-806000-1011".equals(errorCode)
//                || "S-805000-1010".equals(errorCode)
//                    ||  split[0].equals("G")){
//                gatewayErrorCode.get()
//
//            }
//
//            BizAreaDO bizAreaDO = bizAreaService.queryByAreaCode(split[0] + "-" + split[1]);
//            builder.setResult(ResultHandler.buildSuccessResult());
//            builder.setErrorCode(errorCode);
//            builder.setOwner(bizAreaDO.getOwner());
//            ErrorCodeDO errorCodeDO = errorCodeService.queryByErrorCode(request);
//            if (errorCodeDO != null) {
//                builder.setDesc(bizAreaDO.getAreaCodeDesc());
//            }
//            return builder.build();
//        } catch (Exception e) {
//            log.error("getErrorCodeList Exception", e);
//            return builder.setResult(ResultHandler.buildExceptioResult(e)).build();
//        }

}
