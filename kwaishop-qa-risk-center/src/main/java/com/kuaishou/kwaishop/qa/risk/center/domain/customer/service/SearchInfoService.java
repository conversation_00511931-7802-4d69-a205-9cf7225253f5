package com.kuaishou.kwaishop.qa.risk.center.domain.customer.service;


import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.klink.UserKlinkLoginDO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.PageUserKlinkLoginDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.PageUserKlinkLoginListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.UpdateKlinkLoginInfoRequest;


public interface SearchInfoService {


    PageUserKlinkLoginDTO queryUserKlinkLoginInfo(PageUserKlinkLoginListRequest request);

    long updateUserKlinkLoginInfo(UpdateKlinkLoginInfoRequest request);

    long updateUserKlinkLoginInfo(UserKlinkLoginDO userKlinkLoginDO);

}
