package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.common.constants.RentalCode.BasicRentalCode.SUCCESSFUL_EXTEND;
import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.constants.RentalCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserDbForDumpDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.account.UserAccountMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.CheckAccountBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.RentAccountBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.UserAccountBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.UserDumpBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.AccountManagementRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.AccountManagementResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ApplyAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ApplyAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.BatchCheckAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.BatchCheckAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.BatchDeleteAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.BatchDeleteAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.CheckAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.CheckAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ExtendRentRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ExtendRentResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetAccountTokenRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetAccountTokenResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetEsDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetEsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetTestAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportAccountExcelRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportAccountExcelResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportAccountTokenRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportAccountTokenResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportUserAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.KafkaRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.KrpcAccountDomainServiceGrpc.AccountDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryDumpResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryEsResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryPageRentAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryPageRentRecordsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryPageUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryPageUserAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryRentRecordsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryRentRecordsResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryUserAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.RentAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.RentAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ReturnAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ReturnAccountResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UpdateAllUserInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UpdateAllUserInfoResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UserAccountDumpRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UserDbForDumpDTO;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-30
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class AccountDomainServiceImpl extends AccountDomainServiceImplBaseV2 {

    @Autowired
    private UserAccountBizService accountBizService;

    @Autowired
    private RentAccountBizService rentAccountBizService;


    @Resource
    private UserDumpBizService userDumpBizService;

    @Autowired
    private CheckAccountBizService checkAccountBizService;

    @Autowired
    private UserAccountMapper userAccountMapper;

    @Override
    public QueryEsResponse getEsData(GetEsRequest request) {

        QueryEsResponse dumpUser = userDumpBizService.getFromEs(request);


        return dumpUser;
    }

    @Override
    public QueryEsResponse getEsDataWithData(GetEsDataRequest request) {

        QueryEsResponse dumpUser = userDumpBizService.getFromEsWithData(request);

        return dumpUser;
    }

    @Override
    public ImportAccountTokenResponse sendKafkaMsg(KafkaRequest request) {

        userDumpBizService.sendKafkaMsg(request);
        ImportAccountTokenResponse importAccountTokenResponse = ImportAccountTokenResponse.newBuilder().build();

        return importAccountTokenResponse;
    }

    @Override
    public QueryDumpResponse getDumpUser(GetTestAccountRequest request) {

        UserDbForDumpDO dumpUser = userDumpBizService.getDumpUser(request);
        UserDbForDumpDTO db = UserDbForDumpDTO.newBuilder().setUserId(dumpUser.getUserId()).setAccount(dumpUser.getAccount()).
                setAccountType(dumpUser.getAccountType())
                .setExt(dumpUser.getExt()).setCreator(dumpUser.getCreator()).setDataType(dumpUser.getDataType())
                .setTeamId(dumpUser.getTeamId())
                .build();

        return QueryDumpResponse.newBuilder()
                .setResult(BaseResultCode.SUCCESS_VALUE)
                .setData(db)
                .build();
    }

    @Override
    public UpdateAllUserInfoResponse insertDump(UserAccountDumpRequest request) {
        log.info("[AccountDomainServiceImpl] importAccountExcel request: {}", protoToJsonString(request));
        try {
            userDumpBizService.insertDump(request);
            return UpdateAllUserInfoResponse.newBuilder().build().newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] importAccountExcel bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateAllUserInfoResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] importAccountExcel error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateAllUserInfoResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public ImportAccountExcelResponse importAccountExcel(ImportAccountExcelRequest request) {
        log.info("[AccountDomainServiceImpl] importAccountExcel request: {}", protoToJsonString(request));
        try {
            accountBizService.importAccountExcel(request);
            return ImportAccountExcelResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] importAccountExcel bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ImportAccountExcelResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] importAccountExcel error, req: {}, exception: ", protoToJsonString(request), e);
            return ImportAccountExcelResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public ImportAccountTokenResponse importAccountToken(ImportAccountTokenRequest request) {
        log.info("[AccountDomainServiceImpl] importAccountToken request: {}", protoToJsonString(request));
        try {
            accountBizService.importAccountToken(request);
            return ImportAccountTokenResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .buildPartial();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] importAccountToken bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ImportAccountTokenResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] importAccountToken error, req: {}, exception: ", protoToJsonString(request), e);
            return ImportAccountTokenResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public GetTestAccountResponse getTestAccount(GetTestAccountRequest request) {
        log.info("[AccountDomainServiceImpl] getTestAccount request: {}", protoToJsonString(request));
//        throw new RuntimeException("This is a test exception");
        try {
            return GetTestAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(accountBizService.queryTestAccountList(request))
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] getTestAccount bizError, req: {}, exception: ", protoToJsonString(request), e);
            return GetTestAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] getTestAccount error, req: {}, exception: ", protoToJsonString(request), e);
            return GetTestAccountResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public ImportUserAccountResponse importUserAccount(ImportUserAccountRequest request) {
        log.info("[AccountDomainServiceImpl] importUserAccount request: {}", protoToJsonString(request));
        try {
            accountBizService.importUserAccount(request);
            return ImportUserAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] importUserAccount bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ImportUserAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] importUserAccount error, req: {}, exception: ", protoToJsonString(request), e);
            return ImportUserAccountResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    @Override
    public ApplyAccountResponse applyAccount(ApplyAccountRequest request) {
        log.info("[AccountDomainServiceImpl] importUserAccount request: {}", protoToJsonString(request));
        try {
            accountBizService.applyAccount(request);
            return ApplyAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] applyAccount bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ApplyAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] applyAccount error, req: {}, exception: ", protoToJsonString(request), e);
            return ApplyAccountResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryUserAccountResponse queryUserAccount(QueryUserAccountRequest request) {
        log.info("[AccountDomainServiceImpl] queryUserAccount request: {}", protoToJsonString(request));
        try {
            return QueryUserAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(accountBizService.queryUserAccountList(request))
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] queryUserAccount bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryUserAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] queryUserAccount error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryUserAccountResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryPageUserAccountResponse queryPageUserAccount(QueryPageUserAccountRequest request) {
        log.info("[AccountDomainServiceImpl] queryPageUserAccount request: {}", protoToJsonString(request));
        try {
            return QueryPageUserAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(accountBizService.queryPageUserAccountList(request))
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] queryPageUserAccount bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryPageUserAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] queryPageUserAccount error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryPageUserAccountResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    @Override
    public GetAccountTokenResponse getAccountToken(GetAccountTokenRequest request) {
        log.info("[AccountDomainServiceImpl] getAccountToken request: {}", protoToJsonString(request));
        try {
            return GetAccountTokenResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setToken(accountBizService.getAccountToken(request))
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] getAccountToken bizError, req: {}, exception: ", protoToJsonString(request), e);
            return GetAccountTokenResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] getAccountToken error, req: {}, exception: ", protoToJsonString(request), e);
            return GetAccountTokenResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public RentAccountResponse rentAccount(RentAccountRequest request) {
        log.info("[AccountDomainServiceImpl] RentAccount request: {}", protoToJsonString(request));
        try {
            rentAccountBizService.rentAccount(request);
            return RentAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg(RentalCode.BasicRentalCode.SUCCESSFUL_BORROW.getMessage())
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] RentAccount bizError, req: {}, exception: ", protoToJsonString(request), e);
            return RentAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] RentAccount error, req: {}, exception: ", protoToJsonString(request), e);
            return RentAccountResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public ReturnAccountResponse returnAccount(ReturnAccountRequest request) {
        log.info("[AccountDomainServiceImpl] ReturnAccount request: {}", protoToJsonString(request));
        try {
            rentAccountBizService.returnAccount(request);
            return ReturnAccountResponse.newBuilder()
                    .setResult(RentalCode.BasicRentalCode.SUCCESSFUL_BORROW.getCode())
                    .setErrorMsg(RentalCode.BasicRentalCode.SUCCESSFUL_RETURN.getMessage())
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] ReturnAccount bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ReturnAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] ReturnAccount error, req: {}, exception: ", protoToJsonString(request), e);
            return ReturnAccountResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryRentRecordsResponse queryRentRecords(QueryRentRecordsRequest request) {
        log.info("[AccountDomainServiceImpl] QueryRentRecords request: {}", protoToJsonString(request));
        try {
            return QueryRentRecordsResponse.newBuilder()
                    .setResult(RentalCode.BasicRentalCode.SUCCESSFUL_QUERY.getCode())
                    .addAllData(rentAccountBizService.queryRentRecords(request))
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] QueryRentRecords bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryRentRecordsResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] QueryRentRecords error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryRentRecordsResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryPageRentAccountResponse queryPageRentRecords(QueryPageRentRecordsRequest request) {
        log.info("[AccountDomainServiceImpl] queryPageUserAccount request: {}", protoToJsonString(request));
        try {
            return QueryPageRentAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(rentAccountBizService.queryPageRentRecords(request, true))
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] queryPageUserAccount bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryPageRentAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] queryPageUserAccount error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryPageRentAccountResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    @Override
    public QueryPageRentAccountResponse queryPageRentRecordsTest(QueryPageRentRecordsRequest request) {
        log.info("[AccountDomainServiceImpl] queryPageUserAccount request: {}", protoToJsonString(request));
        try {
            return QueryPageRentAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(rentAccountBizService.queryPageRentRecords(request, true))
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] queryPageUserAccount bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryPageRentAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] queryPageUserAccount error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryPageRentAccountResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    @Override
    public ExtendRentResponse extendRent(ExtendRentRequest request) {
        log.info("[AccountDomainServiceImpl] ExtendRent request: {}", protoToJsonString(request));
        try {
            rentAccountBizService.extendRent(request);
            return ExtendRentResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg(SUCCESSFUL_EXTEND.getMessage())
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] ExtendRent bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ExtendRentResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] ExtendRent error, req: {}, exception: ", protoToJsonString(request), e);
            return ExtendRentResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    @Override
    public UpdateAllUserInfoResponse updateAllUserInfo(UpdateAllUserInfoRequest request) {
        log.info("[AccountDomainServiceImpl] updateAllUserInfo request: {}", protoToJsonString(request));
        try {
            accountBizService.updateAllUserInfo(request);
            return UpdateAllUserInfoResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg("更新成功")
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] updateAllUserInfo bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateAllUserInfoResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AccountDomainServiceImpl] updateAllUserInfo error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateAllUserInfoResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    public AccountManagementResponse accountManagement(AccountManagementRequest request) {
        log.info("[AccountDomainServiceImpl] accountManagement request: {}", protoToJsonString(request));
        try {
            accountBizService.accountManagement(request);
            return AccountManagementResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg("更新成功")
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] accountManagement bizError, req: {}, exception: ", protoToJsonString(request), e);
            return AccountManagementResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        }
    }

    @Override
    public CheckAccountResponse checkAccount(CheckAccountRequest request) {
        log.info("[AccountDomainServiceImpl] checkAccount request: {}", protoToJsonString(request));
        try {
            checkAccountBizService.checkAccount(request);
            return CheckAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg("账号检查完成")
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] checkAccount bizError, req: {}, exception: ", protoToJsonString(request), e);
            return CheckAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        }
    }

    @Override
    public BatchCheckAccountResponse batchCheckAccount(BatchCheckAccountRequest request) {
        log.info("[AccountDomainServiceImpl] batchCheckAccount request: {}", protoToJsonString(request));
        try {
            checkAccountBizService.batchCheckAccount(request);
            return BatchCheckAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg("账号批量检查执行完成")
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] batchCheckAccount bizError, req: {}, exception: ", protoToJsonString(request), e);
            return BatchCheckAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        }
    }

    @Override
    public BatchDeleteAccountResponse deleteAccountByUid(BatchDeleteAccountRequest request) {
        log.info("[AccountDomainServiceImpl] deleteAccountByUid request: {}", protoToJsonString(request));
        try {
            QueryWrapper<UserAccountDO> userAccountDOQueryWrapper = new QueryWrapper<>();
            request.getUidList().forEach(uid -> {
                userAccountDOQueryWrapper.eq("user_id", uid);
                log.info("[AccountDomainServiceImpl] deleteAccountByUid uid: {}", uid);
                userAccountMapper.delete(userAccountDOQueryWrapper);
            });
            return BatchDeleteAccountResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg("账号删除成功")
                    .build();
        } catch (BizException e) {
            log.error("[AccountDomainServiceImpl] deleteAccountByUid bizError, req: {}, exception: ", protoToJsonString(request), e);
            return BatchDeleteAccountResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        }
    }

}
