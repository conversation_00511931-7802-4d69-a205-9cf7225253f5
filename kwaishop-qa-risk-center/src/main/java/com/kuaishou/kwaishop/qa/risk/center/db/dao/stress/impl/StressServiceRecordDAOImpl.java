package com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressServiceRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress.StressServiceRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.stress.ScenarioQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressServiceRecordBO;

@Repository
public class StressServiceRecordDAOImpl extends BaseDAOImpl<StressServiceRecordDO, ScenarioQueryCondition> implements StressServiceRecordDAO {

    @Autowired
    private StressServiceRecordMapper serviceRecordMapper;

    @Override
    protected void fillQueryCondition(ScenarioQueryCondition condition, QueryWrapper<StressServiceRecordDO> queryWrapper) {

    }

    @Override
    protected BaseMapper<StressServiceRecordDO> getMapper() {
        return serviceRecordMapper;
    }

    @Override
    public PageBO queryStressSerRecordList(QueryStressServiceRecordBO bo) {
        return null;
    }

    @Override
    public StressServiceRecordDO queryStressSerRecordById(Long id) {
        return serviceRecordMapper.queryStressServiceRecordById(id);
    }

    @Override
    public List<StressServiceRecordDO> queryStressSerRecords(QueryStressServiceRecordBO queryBO) {
        return serviceRecordMapper.queryStressServiceRecords(queryBO);
    }
}
