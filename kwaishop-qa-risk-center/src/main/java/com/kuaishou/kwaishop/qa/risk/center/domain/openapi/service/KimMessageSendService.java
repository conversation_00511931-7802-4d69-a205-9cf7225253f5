package com.kuaishou.kwaishop.qa.risk.center.domain.openapi.service;

import java.io.IOException;

import com.kuaishou.kwaishop.qa.risk.center.domain.openapi.model.bo.RequestSendTemplateMessageBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.openapi.model.bo.ResponseSendTemplateMessageBO;


public interface KimMessageSendService {

    ResponseSendTemplateMessageBO sendTemplateMessageV2(RequestSendTemplateMessageBO request) throws IOException;

}
