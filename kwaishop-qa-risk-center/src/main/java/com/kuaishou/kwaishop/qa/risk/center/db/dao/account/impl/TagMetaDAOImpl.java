package com.kuaishou.kwaishop.qa.risk.center.db.dao.account.impl;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.kuaishou.kwaishop.qa.risk.center.common.constants.SymbolConstants;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.TagMetaDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagMetaDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.account.TagMetaMapper;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/15 15:48
 * @注释
 */
@Repository
public class TagMetaDAOImpl implements TagMetaDAO {
    @Autowired
    private TagMetaMapper tagMetaMapper;

    @Override
    public TagMetaDO queryTagMetaByCode(String code) {
        return tagMetaMapper.selectByTagCode(code);
    }

    @Override
    public TagMetaDO queryTagMetaByName(String name) {
        return tagMetaMapper.selectByTagName(name);
    }

    @Override
    public List<TagMetaDO> selectAll() {
        return tagMetaMapper.selectAll();
    }

    @Override
    public TagMetaDO queryById(Long id) {
        return null;
    }

    @Override
    public Map<Long, TagMetaDO> batchQueryByIds(Collection<Long> ids) {
        return null;
    }


    @Override
    public int updateSelectiveById(TagMetaDO domainObject) {
        return 0;
    }

    @Override
    public long insert(TagMetaDO tagMetaDO) {
        if (tagMetaDO == null) {
            return 0;
        }
        tagMetaDO.setUpdateTime(System.currentTimeMillis());
        tagMetaDO.setCreateTime(System.currentTimeMillis());

        if (StringUtils.isBlank(tagMetaDO.getExt())) {
            tagMetaDO.setExt("{}");
        }
        if (StringUtils.isBlank(tagMetaDO.getUserName())) {
            tagMetaDO.setUserName(SymbolConstants.DEFAULT_USER_SYMBOL);
        }
        if (StringUtils.isBlank(tagMetaDO.getOnlineSystemName())) {
            tagMetaDO.setOnlineSystemName(SymbolConstants.DEFAULT_USER_SYMBOL);
        }

        return tagMetaMapper.insert(tagMetaDO);
    }

    @Override
    public void deleteById(TagMetaDO tagMetaDO) {
        tagMetaMapper.deleteTag(tagMetaDO);
    }
}