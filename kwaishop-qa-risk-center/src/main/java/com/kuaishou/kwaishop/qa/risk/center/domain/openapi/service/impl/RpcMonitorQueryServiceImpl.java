package com.kuaishou.kwaishop.qa.risk.center.domain.openapi.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskStringConfigKey.rpcMonitorUrl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.google.gson.JsonObject;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.domain.openapi.service.RpcMonitorQueryService;
import com.kuaishou.kwaishop.qa.risk.center.utils.JsonUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.http.HttpUtils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Response;


@Service
@Lazy
@Slf4j
public class RpcMonitorQueryServiceImpl implements RpcMonitorQueryService {

    private static final HttpUtils HTTP_UTILS = new HttpUtils();

    private static final String QUERY_API = "/rest/api/v1/clickhouse/rpcmonitor/query";

    @Override
    public List<String> querySrcMethod(String srcKsn, String st, String et) {
        List<String> res = new ArrayList<>();
        try {
            Map<String, String> value = new HashMap<>();
            String sql = String.format("select distinct src_method from l_gifshow.inf_rm_preagg_method_rtcode_data "
                    + "where data_ts >%s and data_ts <=%s and src_service='%s' "
                    + "and runtime_env='hb1_prod' and total_count>0 and src_method!='unknown'"
                    + " FORMAT JSON;", st, et, srcKsn);
            value.put("sql", sql);
            Map<String, String> headers = Maps.newHashMap();
            headers.put("Content-Type", "application/json;charset=UTF-8");
            headers.put("X-Clickhouse-Cluster", "ClickHouse-rpcMonitorDetailCluster");
            headers.put("Authorization", "Token 9fcbfbec939a7dc89618888d056a28d9");
            headers.put("X-Client-Biz", "kwaishop.kwaishop-qa");
            MediaType mediaType = MediaType.parse("application/json;charset=UTF-8");
            Response response = HTTP_UTILS.post(rpcMonitorUrl.get() + QUERY_API, headers, value, mediaType);
            String resStr = response.body().string();
            log.info("checkSrcMethodCallCount res:{}", resStr);
            JsonObject resObj = JsonUtils.stringToJson(resStr, JsonObject.class);
            String data = resObj.get("data").getAsString().replaceAll("\\\\t|\\\\n|\\\\r", "");
            log.info("checkSrcMethodCallCount data:{}", data);
            JsonObject resBO = JsonUtils.stringToJson(data, JsonObject.class);
            if (resBO.get("rows").getAsLong() == 0) {
                return res;
            }
            resBO.get("data").getAsJsonArray().forEach(
                    e -> {
                        res.add(e.getAsJsonObject().get("src_method").getAsString());
                    }
            );
        } catch (Exception ex) {
            log.error("checkSrcMethodCallCount Exception", ex);
        }
        return res;
    }

    @Override
    public boolean checkSrcMethodCallCount(String srcKsn, String srcMethod, String dataTs) {
        try {
            Map<String, String> value = new HashMap<>();
            String sql = String.format("select sum(total_count) from l_gifshow.inf_rm_preagg_method_rtcode_data "
                    + "where data_ts >=%s and src_service='%s' and src_method ='%s' "
                    + "and runtime_env ='hb1_prod' FORMAT JSON;", dataTs, srcKsn, srcMethod);
            value.put("sql", sql);
            Map<String, String> headers = Maps.newHashMap();
            headers.put("Content-Type", "application/json;charset=UTF-8");
            headers.put("X-Clickhouse-Cluster", "ClickHouse-rpcMonitorDetailCluster");
            headers.put("Authorization", "Token 9fcbfbec939a7dc89618888d056a28d9");
            headers.put("X-Client-Biz", "kwaishop.kwaishop-qa");
            MediaType mediaType = MediaType.parse("application/json;charset=UTF-8");
            Response response = HTTP_UTILS.post(rpcMonitorUrl.get() + QUERY_API, headers, value, mediaType);
            String resStr = response.body().string();
            log.info("checkSrcMethodCallCount res:{}", resStr);
            JsonObject resObj = JsonUtils.stringToJson(resStr, JsonObject.class);
            String data = resObj.get("data").getAsString().replaceAll("\\\\t|\\\\n|\\\\r", "");
            log.info("checkSrcMethodCallCount data:{}", data);
            JsonObject resBO = JsonUtils.stringToJson(data, JsonObject.class);
            if (resBO.get("rows").getAsInt() > 0 && resBO.get("data").getAsJsonArray().size() > 0
                    && resBO.get("data").getAsJsonArray().get(0).getAsJsonObject().get("sum(total_count)").getAsLong() > 0) {
                return true;
            }
            return false;
        } catch (Exception ex) {
            log.error("checkSrcMethodCallCount Exception", ex);
            return true;
        }

    }

    @Override
    public JsonObject querySrcServiceCallCount(String srcKsn, String srcMethod) {
        try {
            Map<String, String> value = new HashMap<>();
            String sql = String.format("select * from l_gifshow.inf_rm_preagg_method_rtcode_data "
                    + "where data_ts >=1732982400 and src_service='%s' and src_method ='%s' "
                    + "and runtime_env ='hb1_prod' limit 10 FORMAT JSON;", srcKsn, srcMethod);
            value.put("sql", sql);
            Map<String, String> headers = Maps.newHashMap();
            headers.put("Content-Type", "application/json;charset=UTF-8");
            headers.put("X-Clickhouse-Cluster", "ClickHouse-rpcMonitorDetailCluster");
            headers.put("Authorization", "Token 9fcbfbec939a7dc89618888d056a28d9");
            headers.put("X-Client-Biz", "kwaishop.kwaishop-qa");
            MediaType mediaType = MediaType.parse("application/json;charset=UTF-8");
            Response response = HTTP_UTILS.post(rpcMonitorUrl.get() + QUERY_API, headers, value, mediaType);
            String result = response.body().string();
            log.info("querySrcServiceCallCount res:{}", result);
            return ObjectMapperUtils.fromJSON(result, JsonObject.class);
        } catch (Exception ex) {
            log.error("querySrcServiceCallCount Exception", ex);
            return null;
        }
    }
}
