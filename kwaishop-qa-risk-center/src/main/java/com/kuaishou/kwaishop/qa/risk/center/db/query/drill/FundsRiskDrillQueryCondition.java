package com.kuaishou.kwaishop.qa.risk.center.db.query.drill;

import com.kuaishou.kwaishop.qa.risk.center.db.query.detail.DetailBaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FundsRiskDrillQueryCondition extends DetailBaseQueryCondition {


    /**
     * 风险场景id
     */
    private String subRiskId;

    /**
     * 演练方式
     */
    private String drillType;

    /**
     * 关联teamid
     */
    private String drillTeamId;

    private Long createTime;

    private Long updateTime;

    private String creator;

    private String updater;

}
