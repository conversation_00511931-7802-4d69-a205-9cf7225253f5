package com.kuaishou.kwaishop.qa.risk.center.domain.feature.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

public enum KspayRiskFeatureBranchStateEnum {
    NOMARKED(0, "未打标"),
    MARKED(1, "已打标"),

    UNKNOWN(2, "未知状态"),
    ;

    private final Integer code;

    private final String desc;

    KspayRiskFeatureBranchStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static KspayRiskFeatureBranchStateEnum of(Integer code) {
        for (KspayRiskFeatureBranchStateEnum kspayRiskFeatureBranchStateEnum: values()) {
            if (kspayRiskFeatureBranchStateEnum.getCode().equals(code) && kspayRiskFeatureBranchStateEnum.getCode() > 0) {
                return kspayRiskFeatureBranchStateEnum;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo() {
        List<EnumInfo> res = new ArrayList<>();
        for (KspayRiskFeatureBranchStateEnum typeEnum: values()) {
            if (typeEnum.getCode() > 0) {
                EnumInfo enumInfo = EnumInfo.newBuilder()
                        .setValue(typeEnum.getCode())
                        .setDesc(typeEnum.getDesc())
                        .build();
                res.add(enumInfo);
            }
        }
        return res;
    }
}
