package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import java.util.Collection;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.RiskDetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.RiskDetailDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.RiskDetailMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.RiskDetailQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.bo.RiskDetailBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-20
 */
@Repository
public class RiskDetailDAOImplImpl extends BaseDAOImpl<RiskDetailDO, RiskDetailQueryCondition> implements RiskDetailDAO {

    @Autowired
    private RiskDetailMapper riskDetailMapper;

    @Override
    protected void fillQueryCondition(RiskDetailQueryCondition condition, QueryWrapper<RiskDetailDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getName())) {
            queryWrapper.and(q -> q.like("name", condition.getName()));
        }
        if (condition.getLevel() != null && condition.getLevel() > 0) {
            queryWrapper.and(q -> q.eq("level", condition.getLevel()));
        }
        if (condition.getPlanEffective() != null && condition.getPlanEffective() > 0) {
            queryWrapper.and(q -> q.eq("plan_effective", condition.getPlanEffective()));
        }
        if (condition.getRiskType() != null && condition.getRiskType() > 0) {
            queryWrapper.and(q -> q.eq("risk_type", condition.getRiskType()));
        }
        if (condition.getTeamId() != null && condition.getTeamId() > 0) {
            queryWrapper.and(q -> q.eq("team_id", condition.getTeamId()));
        }
        if (condition.getVerifyEffective() != null && condition.getVerifyEffective() > 0) {
            queryWrapper.and(q -> q.eq("verify_effective", condition.getVerifyEffective()));
        }
        if (condition.getVerifyType() != null && condition.getVerifyType() > 0) {
            queryWrapper.and(q -> q.eq("verify_type", condition.getVerifyType()));
        }
        if (CollectionUtils.isNotEmpty(condition.getCreators())) {
            queryWrapper.and(q -> q.in("creator", condition.getCreators()));
        }
        if (condition.getDbType() != null && condition.getDbType() > 0) {
            queryWrapper.and(q -> q.eq("db_type", condition.getDbType()));
        }
        if (condition.getTableColumnCover() != null && condition.getTableColumnCover() > 0) {
            queryWrapper.and(q -> q.eq("table_column_cover", condition.getTableColumnCover()));
        }
        if (condition.getRiskCover() != null && condition.getRiskCover() > 0) {
            queryWrapper.and(q -> q.eq("risk_cover", condition.getRiskCover()));
        }
        if (condition.getPlanCover() != null && condition.getPlanCover() > 0) {
            queryWrapper.and(q -> q.eq("plan_cover", condition.getPlanCover()));
        }
        if (condition.getCenterId() != null && condition.getCenterId() > 0) {
            queryWrapper.and(q -> q.eq("center_id", condition.getCenterId()));
        }
        if (CollectionUtils.isNotEmpty(condition.getTeamIds())) {
            queryWrapper.and(q -> q.in("team_id", condition.getTeamIds()));
        }
        if (CollectionUtils.isNotEmpty(condition.getCenterIds())) {
            queryWrapper.and(q -> q.in("center_id", condition.getCenterIds()));
        }
    }

    @Override
    protected BaseMapper<RiskDetailDO> getMapper() {
        return riskDetailMapper;
    }

    @Override
    public int logicDeleted(Long id, String operator) {
        return riskDetailMapper.logicDeleted(operator, id);
    }

    @Override
    public List<RiskDetailDO> queryRiskDetailList(RiskDetailBO riskDetailBO) {
        RiskDetailQueryCondition queryCondition = RiskDetailQueryCondition.builder()
                .orderByCreateTimeDesc(true)
                .teamId(riskDetailBO.getTeamId())
                .id(riskDetailBO.getId())
                .level(riskDetailBO.getLevel())
                .riskType(riskDetailBO.getRiskType())
                .planEffective(riskDetailBO.getPlanEffective())
                .verifyEffective(riskDetailBO.getVerifyEffective())
                .verifyType(riskDetailBO.getVerifyType())
                .dbType(riskDetailBO.getDbType())
                .tableColumnCover(riskDetailBO.getTableColumnCover())
                .riskCover(riskDetailBO.getRiskCover())
                .planCover(riskDetailBO.getPlanCover())
                .centerId(riskDetailBO.getCenterId())
                .businessId(riskDetailBO.getBusinessId())
                .creator(riskDetailBO.getOperator())
                .build();
        return queryList(queryCondition);
    }

    @Override
    public PageBO<RiskDetailDO> queryPageRiskDetailList(RiskDetailBO riskDetailBO) {
        RiskDetailQueryCondition queryCondition = RiskDetailQueryCondition.builder()
                .orderByCreateTimeDesc(true)
                .teamId(riskDetailBO.getTeamId())
                .id(riskDetailBO.getId())
                .level(riskDetailBO.getLevel())
                .riskType(riskDetailBO.getRiskType())
                .planEffective(riskDetailBO.getPlanEffective())
                .verifyEffective(riskDetailBO.getVerifyEffective())
                .verifyType(riskDetailBO.getVerifyType())
                .dbType(riskDetailBO.getDbType())
                .tableColumnCover(riskDetailBO.getTableColumnCover())
                .riskCover(riskDetailBO.getRiskCover())
                .planCover(riskDetailBO.getPlanCover())
                .centerId(riskDetailBO.getCenterId())
                .businessId(riskDetailBO.getBusinessId())
                .creator(riskDetailBO.getOperator())
                .pageNo(riskDetailBO.getPageNo())
                .pageSize(riskDetailBO.getPageSize())
                .build();
        return queryPageList(queryCondition);
    }

    @Override
    public List<RiskDetailDO> queryByCenterIds(Collection<Long> centerIds, CombineQueryParam param) {
        RiskDetailQueryCondition queryCondition = RiskDetailQueryCondition.builder()
                .orderByCreateTimeDesc(true)
                .centerIds(centerIds)
                .build();
        return queryList(queryCondition);
    }

    @Override
    public List<RiskDetailDO> queryByTeamIds(Long centerId, Collection<Long> teamIds, CombineQueryParam param) {
        RiskDetailQueryCondition queryCondition = RiskDetailQueryCondition.builder()
                .orderByCreateTimeDesc(true)
                .centerId(centerId)
                .teamIds(teamIds)
                .build();
        return queryList(queryCondition);
    }

    @Override
    public List<RiskDetailDO> queryByCenterId(Long centerId, CombineQueryParam param) {
        RiskDetailQueryCondition queryCondition = RiskDetailQueryCondition.builder()
                .orderByCreateTimeDesc(true)
                .centerId(centerId)
                .build();
        return queryList(queryCondition);
    }

    @Override
    public List<RiskDetailDO> queryByTeamId(Long centerId, Long teamId, CombineQueryParam param) {
        RiskDetailQueryCondition queryCondition = RiskDetailQueryCondition.builder()
                .orderByCreateTimeDesc(true)
                .centerId(centerId)
                .teamId(teamId)
                .build();
        return queryList(queryCondition);
    }

    @Override
    public List<RiskDetailDO> queryByCenterIdsTeamIds(Collection<Long> centerIds, Collection<Long> teamIds, CombineQueryParam param) {
        RiskDetailQueryCondition queryCondition = RiskDetailQueryCondition.builder()
                .orderByCreateTimeDesc(true)
                .centerIds(centerIds)
                .teamIds(teamIds)
                .build();
        return queryList(queryCondition);
    }
}
