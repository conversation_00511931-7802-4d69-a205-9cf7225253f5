package com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.impl;

import java.util.List;
import java.util.Map;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureRequestDO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FundsRiskFeatureSqlProvider {
    @Deprecated
    public String dynamicSqlTest(FundsRiskFeatureRequestDO fundsRiskFeatureRequestDO) {
        StringBuilder sql = new StringBuilder("SELECT * FROM funds_risk_feature WHERE 1=1");

        if (fundsRiskFeatureRequestDO.getIsRisk() != null) {
            sql.append(" AND is_risk = #{isRisk}");
        }
        if (fundsRiskFeatureRequestDO.getStartTime() != null) {
            sql.append(" AND start_time >= #{startTime}");
        }
        if (fundsRiskFeatureRequestDO.getEndTime() != null) {
            sql.append(" AND end_time <= #{endTime}");
        }
        if (fundsRiskFeatureRequestDO.getStatus() != null && !fundsRiskFeatureRequestDO.getStatus().isEmpty()) {
            sql.append(" AND status IN ").append(listToInClause(fundsRiskFeatureRequestDO.getStatus()));
        }
        if (fundsRiskFeatureRequestDO.getRiskPassStatus() != null) {
            sql.append(" AND risk_pass_status = #{riskPassStatus}");
        }
        if (fundsRiskFeatureRequestDO.getDepartment() != null) {
            sql.append(" AND department = #{department}");
        }
        if (fundsRiskFeatureRequestDO.getBusinessDomain() != null) {
            sql.append(" AND business_domain = #{businessDomain}");
        }

        int offset = (fundsRiskFeatureRequestDO.getPageNo() - 1) * fundsRiskFeatureRequestDO.getPageSize();
        sql.append(" LIMIT #{offset}, #{limit}");

        return sql.toString();
    }

    private static String listToInClause(List<Integer> list) {
        if (list == null || list.isEmpty()) {
            return "";
        }
        StringBuilder inClause = new StringBuilder("(");
        for (int i = 0; i < list.size(); i++) {
            inClause.append("#{status[").append(i).append("]}");
            if (i < list.size() - 1) {
                inClause.append(", ");
            }
        }
        inClause.append(")");
        return inClause.toString();
    }

    @Deprecated
    public String dynamicSql(Map<String, Object> param) {
        FundsRiskFeatureRequestDO request = (FundsRiskFeatureRequestDO) param.get("params");
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM funds_risk_feature_view WHERE 1=1");

        // 添加过滤条件
        if (request.getRiskPassStatus() != null && !request.getRiskPassStatus().equals(-1)) {
            sql.append(" AND risk_pass_status = #{params.riskPassStatus}");
        }

        if (request.getIsRisk() != null && (request.getIsRisk() == 1 || request.getIsRisk() == 2)) {
            sql.append(" AND is_risk = #{params.isRisk}");
        }

        if (request.getDepartment() != null && !request.getDepartment().isEmpty()) {
            sql.append(" AND department = #{params.department}");
        }

        if (request.getBusinessDomain() != null && !request.getBusinessDomain().isEmpty()) {
            sql.append(" AND business_domain = #{businessDomain}");
        }
        if (request.getStatus() != null) {
            if (request.getStatus() instanceof List) {
                @SuppressWarnings("unchecked")
                List<Integer> statuses = (List<Integer>) request.getStatus();
                if (!statuses.isEmpty()) {
                    sql.append(" AND status IN (");
                    for (int i = 0; i < statuses.size(); i++) {
                        if (i > 0) {
                            sql.append(", ");
                        }
                        sql.append("#{params.status[").append(i).append("]}");
                    }
                    sql.append(")");
                }
            } else {
                sql.append(" AND status = #{params.status}");
            }
        }

        if (request.getStartTime() != null || request.getEndTime() != null) {
            sql.append(" AND update_time ");
            if (request.getStartTime() != null && request.getEndTime() != null) {
                sql.append(" BETWEEN #{params.startTime} AND #{params.endTime}");
            } else if (request.getStartTime() != null) {
                sql.append(" >= #{params.startTime}");
            } else if (request.getEndTime() != null) {
                sql.append(" <= #{params.endTime}");
            }
        }

        sql.append(" ORDER BY update_time DESC");

        if (request.getOffset() != null && request.getLimit() != null) {
            sql.append(" LIMIT #{params.offset}, #{params.limit}");
        }

        return sql.toString();
    }
    @Deprecated
    public String countSql(Map<String, Object> param) {
        FundsRiskFeatureRequestDO request = (FundsRiskFeatureRequestDO) param.get("params");
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*) FROM funds_risk_feature_view WHERE 1=1");

        // 添加过滤条件
        if (request.getRiskPassStatus() != null && !request.getRiskPassStatus().equals(-1)) {
            sql.append(" AND risk_pass_status = #{params.riskPassStatus}");
        }

        if (request.getIsRisk() != null && (request.getIsRisk() == 1 || request.getIsRisk() == 2)) {
            sql.append(" AND is_risk = #{params.isRisk}");
        }

        if (request.getDepartment() != null && !request.getDepartment().isEmpty()) {
            sql.append(" AND department = #{params.department}");
        }

        if (request.getBusinessDomain() != null && !request.getBusinessDomain().isEmpty()) {
            sql.append(" AND business_domain = #{params.businessDomain}");
        }

        if (request.getStatus() != null) {
            if (request.getStatus() instanceof List) {
                @SuppressWarnings("unchecked")
                List<Integer> statuses = (List<Integer>) request.getStatus();
                if (!statuses.isEmpty()) {
                    sql.append(" AND status IN (");
                    for (int i = 0; i < statuses.size(); i++) {
                        if (i > 0) {
                            sql.append(", ");
                        }
                        sql.append("#{params.status[").append(i).append("]}");
                    }
                    sql.append(")");
                }
            } else {
                sql.append(" AND status = #{params.status}");
            }
        }

        if (request.getStartTime() != null || request.getEndTime() != null) {
            sql.append(" AND update_time ");
            if (request.getStartTime() != null && request.getEndTime() != null) {
                sql.append(" BETWEEN #{params.startTime} AND #{params.endTime}");
            } else if (request.getStartTime() != null) {
                sql.append(" >= #{params.startTime}");
            } else if (request.getEndTime() != null) {
                sql.append(" <= #{params.endTime}");
            }
        }

        return sql.toString();
    }
    @Deprecated
    public String allSql(Map<String, Object> param) {
        FundsRiskFeatureRequestDO request = (FundsRiskFeatureRequestDO) param.get("params");
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM funds_risk_feature_view WHERE 1=1");

        // 添加过滤条件
        if (request.getRiskPassStatus() != null && !request.getRiskPassStatus().equals(-1)) {
            sql.append(" AND risk_pass_status = #{params.riskPassStatus}");
        }

        if (request.getIsRisk() != null && (request.getIsRisk() == 1 || request.getIsRisk() == 2)) {
            sql.append(" AND is_risk = #{params.isRisk}");
        }

        if (request.getDepartment() != null && !request.getDepartment().isEmpty()) {
            sql.append(" AND department = #{params.department}");
        }

        if (request.getBusinessDomain() != null && !request.getBusinessDomain().isEmpty()) {
            sql.append(" AND business_domain = #{params.businessDomain}");
        }

        if (request.getStatus() != null) {
            if (request.getStatus() instanceof List) {
                @SuppressWarnings("unchecked")
                List<Integer> statuses = (List<Integer>) request.getStatus();
                if (!statuses.isEmpty()) {
                    sql.append(" AND status IN (");
                    for (int i = 0; i < statuses.size(); i++) {
                        if (i > 0) {
                            sql.append(", ");
                        }
                        sql.append("#{params.status[").append(i).append("]}");
                    }
                    sql.append(")");
                }
            } else {
                sql.append(" AND status = #{params.status}");
            }
        }

        if (request.getStartTime() != null || request.getEndTime() != null) {
            sql.append(" AND update_time ");
            if (request.getStartTime() != null && request.getEndTime() != null) {
                sql.append(" BETWEEN #{params.startTime} AND #{params.endTime}");
            } else if (request.getStartTime() != null) {
                sql.append(" >= #{params.startTime}");
            } else if (request.getEndTime() != null) {
                sql.append(" <= #{params.endTime}");
            }
        }

        sql.append(" ORDER BY update_time DESC");

        return sql.toString();
    }
}