package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.detail.DetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultPlanDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultPlanBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-08
 */
public interface FaultPlanDAO extends DetailDAO<FaultPlanDO, CombineQueryParam> {

    int logicDelete(Long id, String operator);

    Map<Long, FaultPlanDO> batchQueryByIds(Collection<Long> ids);

    List<FaultPlanDO> queryFaultPlanList(FaultPlanBO faultPlanBO);

    List<FaultPlanDO> queryFaultPlanAll();

    PageBO<FaultPlanDO> queryPageFaultPlanList(FaultPlanBO faultPlanBO);

    List<FaultPlanDO> queryFaultPlanListByCenterId(Long centerId, Long startTime, Long endTime);
}
