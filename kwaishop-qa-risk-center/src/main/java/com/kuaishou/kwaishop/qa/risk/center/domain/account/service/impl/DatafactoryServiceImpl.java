package com.kuaishou.kwaishop.qa.risk.center.domain.account.service.impl;

import java.util.HashMap;
import java.util.Map;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.gson.JsonObject;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.kdump.caelus.center.client.IVSearchEsClient;
import com.kuaishou.kwaishop.kdump.caelus.center.client.es.request.KSearchRequest;
import com.kuaishou.kwaishop.kdump.caelus.center.client.es.response.KSearchDataResponse;
import com.kuaishou.kwaishop.kdump.caelus.center.client.es.strategy.VSearchClientConfigStrategyBuildService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.DatafactoryService;
import com.kuaishou.kwaishop.qa.risk.center.utils.JsonUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.http.HttpUtils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-27
 */
@Lazy
@Service
@Slf4j
public class DatafactoryServiceImpl implements DatafactoryService {

    private static final String URL = "https://datafactory.staging.kuaishou.com/v1/";

    public static final Map<String, String> HEADERS;

    public static final Map<String, IVSearchEsClient> ESCLIENTMAP;

    static {
        // 初始化静态变量HEADERS
        HEADERS = new HashMap<>();
        HEADERS.put("Content-Type", "application/json");
        // 添加其他的Header信息

        ESCLIENTMAP = new HashMap<>();
    }

    /**
     * @param userId
     * @param identity
     * @param permissionFlag
     */
    @Override
    public void openPermission(String userId, String identity, Integer permissionFlag) {
        HttpUtils httpUtils = new HttpUtils();
        JsonObject params = new JsonObject();
        params.addProperty("id", userId);
        params.addProperty("identity", identity);
        params.addProperty("permissionFlag", permissionFlag);
        try {
            Response response = httpUtils.post(URL + "distribute/operationPermission", HEADERS, JsonUtils.toJsonString(params));
            log.info("调数据工厂request = {}, response = {}", JsonUtils.toJsonString(params), response.body().string());
        } catch (Exception e) {
            log.error("调数据工厂出现ERROR = {}", e.getMessage());
        }
    }

    @Override
    public String getFromEs(String merchant, String name, String id) {
        IVSearchEsClient client = getEsClient(merchant, name);
        assert client != null;
        Map<String, Object> result = client.get(id, null, null);
        return ObjectMapperUtils.toJSON(result);
    }

    @Override
    public String getFromEsWithData(String merchant, String name, String keys, String values) {

        IVSearchEsClient client = getEsClient(merchant, name);

        assert client != null;
        KSearchRequest searchRequest = new KSearchRequest();

        String[] keyList = keys.split(";");
        String[] valueList = values.split(";");


        for (int i = 0; i < keyList.length; i++) {

            searchRequest.setTerm(keyList[i], new Object[]{valueList[i]});
        }


        KSearchDataResponse queryResult =
                client.query(searchRequest, Object.class);

        String res = ObjectMapperUtils.toJSON(queryResult);


        return res;
    }


    private IVSearchEsClient getEsClient(String merchant, String name) {
        String clientId = merchant + "_" + name;
        IVSearchEsClient client = null;
        if (ESCLIENTMAP.containsKey(clientId)) {
            client = ESCLIENTMAP.get(clientId);
        } else {
            client =
                    VSearchClientConfigStrategyBuildService.getClient(null, merchant + "_" + name);
            ESCLIENTMAP.put(clientId, client);
        }
        return client;
    }
}
