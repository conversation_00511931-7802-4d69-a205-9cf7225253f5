package com.kuaishou.kwaishop.qa.risk.center.db.query.fault;

import java.util.Collection;

import com.kuaishou.kwaishop.qa.risk.center.db.query.detail.DetailBaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-08
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FaultPlanQueryCondition extends DetailBaseQueryCondition {

    private String name;

    private Integer status;

    private Long startTimeGe;

    private Long startTimeLe;

    private Long endTimeGe;

    private Long endTimeLe;

    private Collection<Integer> statusList;
}
