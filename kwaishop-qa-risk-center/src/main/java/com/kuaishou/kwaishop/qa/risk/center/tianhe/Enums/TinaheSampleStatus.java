package com.kuaishou.kwaishop.qa.risk.center.tianhe.Enums;

public enum TinaheSampleStatus {
    NORMAL_SMAPLE_CREATED("1"),
    AI_SAMPLE_INIT("2"),
    AI_SAMPLE_CREATED("3");

    private final String value;

    // 构造方法
    TinaheSampleStatus(String value) {
        this.value = value;
    }

    // 获取 value（key -> value）
    public String getValue() {
        return value;
    }

    // 通过 value 查找 key（value -> key）
    public static TinaheSampleStatus fromValue(String value) {
        for (TinaheSampleStatus type : TinaheSampleStatus.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }
}
