package com.kuaishou.kwaishop.qa.risk.center.db.dao.account;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.BaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentTestAccountRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.RentAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TestAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TestAccountRentBO;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/15 15:47
 * @注释
 */
public interface RentTestAccountRecordDAO extends BaseDAO<RentTestAccountRecordDO> {
    List<RentTestAccountRecordDO> queryList(RentAccountBO rentAccountBo);

    void rentAccount(RentAccountBO rentAccountBo);

    void returnAccount(RentTestAccountRecordDO rentTestAccountRecordDO);

    PageBO<RentTestAccountRecordDO> queryPageList(TestAccountBO queryBO);

    List<RentTestAccountRecordDO> queryRentTestAccountList(TestAccountRentBO testAccountRentBO);

}
