package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.KspayFundsRiskFeatureViewService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.BizSpaceMap;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.GetKconfBizSpaceRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.GetKconfBizSpaceResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.GetLatestTeamWorkerRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.GetLatestTeamWorkerResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KrpcKspayFundsRiskFeatureViewServiceGrpc.KspayFundsRiskFeatureViewServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayBranchAccuracyFMethodsDataResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayBranchRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayFundsRiskFeatureViewBranchRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayFundsRiskFeatureViewBranchResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayFundsRiskFeatureViewRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayFundsRiskFeatureViewResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayInsertRiskFeatureViewByBizTimeRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayInsertRiskFeatureViewByBizTimeResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayUpdateFeatureViewBranchByFeatureIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayUpdateFeatureViewBranchByFeatureIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.MethodCovInfo;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-01
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class KspayRiskFeatureViewServiceImpl extends KspayFundsRiskFeatureViewServiceImplBaseV2 {

    @Autowired
    private KspayFundsRiskFeatureViewService kspayFundsRiskFeatureViewService;

    @Override
    public KspayInsertRiskFeatureViewByBizTimeResponse kspayInsertRiskFeatureViewByBizTime(
            KspayInsertRiskFeatureViewByBizTimeRequest request) {
        log.info("[KspayFundsRiskFeatureViewServiceImpl] kspayInsertRiskFeatureViewByBizTime request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayFundsRiskFeatureViewService.kspayInsertRiskFeatureViewByBizTime(request);
            return KspayInsertRiskFeatureViewByBizTimeResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[KspayFundsRiskFeatureViewServiceImpl] kspayInsertRiskFeatureViewByBizTime bizError, exception: ", e);
            return KspayInsertRiskFeatureViewByBizTimeResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayFundsRiskFeatureViewServiceImpl] kspayInsertRiskFeatureViewByBizTime error, exception: ", e);
            return KspayInsertRiskFeatureViewByBizTimeResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayUpdateFeatureViewBranchByFeatureIdResponse kspayUpdateFundsRiskFeatureViewBranchByFeatureId(
            KspayUpdateFeatureViewBranchByFeatureIdRequest request) {
        log.info("[KspayFundsRiskFeatureViewServiceImpl] KspayUpdateFundsRiskFeatureViewBranchByFeatureId request: {}",
                ProtobufUtil.protoToJsonString(request));
        try {
            kspayFundsRiskFeatureViewService.kspayUpdateFundsRiskFeatureViewBranchByFeatureId(request.getId(),
                    request.getFeatureId());
            return KspayUpdateFeatureViewBranchByFeatureIdResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[KspayFundsRiskFeatureViewServiceImpl] KspayUpdateFundsRiskFeatureViewBranchByFeatureId bizError,"
                    + " exception: ", e);
            return KspayUpdateFeatureViewBranchByFeatureIdResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayFundsRiskFeatureViewServiceImpl] KspayUpdateFundsRiskFeatureViewBranchByFeatureId error,"
                    + " exception: ", e);
            return KspayUpdateFeatureViewBranchByFeatureIdResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public KspayFundsRiskFeatureViewResponse kspayInsertFundsRiskFeatureView(
            KspayFundsRiskFeatureViewRequest request) {
        log.info("[KspayFundsRiskFeatureViewServiceImpl] kspayInsertFundsRiskFeatureView request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayFundsRiskFeatureViewService.kspayInsertFundsRiskFeatureView(request);
            return KspayFundsRiskFeatureViewResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[KspayFundsRiskFeatureViewServiceImpl] kspayInsertFundsRiskFeatureView bizError, exception: ", e);
            return KspayFundsRiskFeatureViewResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayFundsRiskFeatureViewServiceImpl] kspayInsertFundsRiskFeatureView error, exception: ", e);
            return KspayFundsRiskFeatureViewResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    @Override
    public KspayFundsRiskFeatureViewBranchResponse kspayInsertFundsRiskFeatureViewBranch(
            KspayFundsRiskFeatureViewBranchRequest request) {
        log.info("[KspayFundsRiskFeatureViewServiceImpl] kspayInsertFundsRiskFeatureViewBranch request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            kspayFundsRiskFeatureViewService.kspayInsertFundsRiskFeatureViewBranch(request);
            return KspayFundsRiskFeatureViewBranchResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[KspayFundsRiskFeatureViewServiceImpl] kspayInsertFundsRiskFeatureViewBranch bizError, exception: ", e);
            return KspayFundsRiskFeatureViewBranchResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayFundsRiskFeatureViewServiceImpl] kspayInsertFundsRiskFeatureViewBranch error, exception: ", e);
            return KspayFundsRiskFeatureViewBranchResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    @Override
    public KspayBranchAccuracyFMethodsDataResponse kspayQueryBranchAccuracyFMethodsData(
            KspayBranchRequest request) {
        log.info("[KspayFundsRiskFeatureViewServiceImpl] kspayQueryBranchAccuracyFMethodsData request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            List<MethodCovInfo> methodCovInfos =
                    kspayFundsRiskFeatureViewService.kspayQueryBranchAccuracyFMethodsData(request);

            // 检查methodCovInfos是否为空
            if (methodCovInfos == null || methodCovInfos.isEmpty()) {
                log.warn("[KspayFundsRiskFeatureViewServiceImpl] No data available for the requested repo and branch.");
                return KspayBranchAccuracyFMethodsDataResponse.newBuilder()
                        .setResult(BaseResultCode.ITEM_NOT_EXIST_VALUE)
                        .setErrorMsg("No data available.")
                        .build();
            }

            return KspayBranchAccuracyFMethodsDataResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllFinanceMethods(methodCovInfos)
                    .build();
        } catch (BizException e) {
            log.error("[KspayFundsRiskFeatureViewServiceImpl] kspayQueryBranchAccuracyFMethodsData bizError, exception: ", e);
            return KspayBranchAccuracyFMethodsDataResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayFundsRiskFeatureViewServiceImpl] kspayQueryBranchAccuracyFMethodsData error, exception: ", e);
            return KspayBranchAccuracyFMethodsDataResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public GetKconfBizSpaceResponse kspayGetKconfBizSpace(
            GetKconfBizSpaceRequest request) {
        log.info("[KspayFundsRiskFeatureViewServiceImpl] kspayInsertFundsRiskFeatureViewBranch request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            List<BizSpaceMap> bizSpaceMapList = kspayFundsRiskFeatureViewService.getKconfBizSpace(request);
            return GetKconfBizSpaceResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg("")
                    .addAllBizSpaceMap(bizSpaceMapList)
                    .build();
        } catch (BizException e) {
            log.error("[KspayFundsRiskFeatureViewServiceImpl] kspayInsertFundsRiskFeatureViewBranch bizError, exception: ", e);
            return GetKconfBizSpaceResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayFundsRiskFeatureViewServiceImpl] kspayInsertFundsRiskFeatureViewBranch error, exception: ", e);
            return GetKconfBizSpaceResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }

    }

    @Override
    public GetLatestTeamWorkerResponse kspayGetLatestTeamWorker(
            GetLatestTeamWorkerRequest request) {
        log.info("[KspayFundsRiskFeatureViewServiceImpl] kspayGetLatestTeamWorker request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            String latestTeamWorker = kspayFundsRiskFeatureViewService.kspayGetLatestTeamWorkers(request.getFeatureId());
            return GetLatestTeamWorkerResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg("")
                    .setTeamWorker(latestTeamWorker)
                    .build();
        } catch (BizException e) {
            log.error("[KspayFundsRiskFeatureViewServiceImpl] kspayGetLatestTeamWorker bizError, exception: ", e);
            return GetLatestTeamWorkerResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[KspayFundsRiskFeatureViewServiceImpl] kspayGetLatestTeamWorker error, exception: ", e);
            return GetLatestTeamWorkerResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

}
