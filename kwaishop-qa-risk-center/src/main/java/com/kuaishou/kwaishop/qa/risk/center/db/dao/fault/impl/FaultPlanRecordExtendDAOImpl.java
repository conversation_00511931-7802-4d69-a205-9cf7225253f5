package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kuaishou.infra.boot.jdbc.datasource.TransactionalDataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.FaultPlanRecordExtendDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultPlanRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.fault.FaultPlanRecordMapper;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-01-04
 */
@Service
@TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
public class FaultPlanRecordExtendDAOImpl extends ServiceImpl<FaultPlanRecordMapper, FaultPlanRecordDO> implements
        FaultPlanRecordExtendDAO {
}
