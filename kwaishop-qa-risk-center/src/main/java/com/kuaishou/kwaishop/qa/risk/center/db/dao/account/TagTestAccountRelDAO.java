package com.kuaishou.kwaishop.qa.risk.center.db.dao.account;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.BaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagTestAccountRelDO;


public interface TagTestAccountRelDAO extends BaseDAO<TagTestAccountRelDO> {
    /**
     * in链接查询账号关联的tag*
     *
     * @param accountIds
     * @return
     */
    List<TagTestAccountRelDO> getTestTagInIds(List<Long> accountIds, List<Integer> authStatusList);

    List<TagTestAccountRelDO> getTestTagInAccounts(List<Long> accountIds, List<Integer> authStatusList);

    List<TagTestAccountRelDO> getTestTagInAccount(Long accountId, List<Integer> authStatusList);

    void deleteTagFromAccount(Long accountId, String tagCode);

    List<TagTestAccountRelDO> getTagListByTestAccountIds(List<Integer> tagAuthStatusList);

    List<TagTestAccountRelDO> getTestAccountIdsByTag(String tagCode);

}
