package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.problem.biz.ProblemDetailBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.CreateProblemRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.CreateProblemResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.DeleteProblemRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.DeleteProblemResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.KrpcProblemDomainServiceGrpc.ProblemDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.PageProblemDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.ProblemDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.QueryProblemDetailListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.QueryProblemDetailListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.QueryProblemDetailPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.QueryProblemDetailPageListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.UpdateProblemRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.problem.UpdateProblemResponse;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-06
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class ProblemDomainServiceImpl extends ProblemDomainServiceImplBaseV2 {

    @Autowired
    private ProblemDetailBizService problemDetailBizService;

    @Override
    public CreateProblemResponse createProblem(CreateProblemRequest request) {
        log.info("[ProblemDomainServiceImpl] createProblem request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            problemDetailBizService.createProblem(request);
            return CreateProblemResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[ProblemDomainServiceImpl] createProblem bizError, exception: ", e);
            return CreateProblemResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[ProblemDomainServiceImpl] createProblem error, exception: ", e);
            return CreateProblemResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateProblemResponse updateProblem(UpdateProblemRequest request) {
        log.info("[ProblemDomainServiceImpl] updateProblem request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            problemDetailBizService.updateProblem(request);
            return UpdateProblemResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[ProblemDomainServiceImpl] updateProblem bizError, exception: ", e);
            return UpdateProblemResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[ProblemDomainServiceImpl] updateProblem error, exception: ", e);
            return UpdateProblemResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public DeleteProblemResponse deleteProblem(DeleteProblemRequest request) {
        log.info("[ProblemDomainServiceImpl] deleteProblem request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            problemDetailBizService.deleteProblem(request);
            return DeleteProblemResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[ProblemDomainServiceImpl] deleteProblem bizError, exception: ", e);
            return DeleteProblemResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[ProblemDomainServiceImpl] deleteProblem error, exception: ", e);
            return DeleteProblemResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryProblemDetailListResponse queryProblemDetailList(QueryProblemDetailListRequest request) {
        log.info("[ProblemDomainServiceImpl] queryProblemDetailList request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            List<ProblemDTO> dataList = problemDetailBizService.queryProblemDetailList(request);
            return QueryProblemDetailListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(dataList)
                    .build();
        } catch (BizException e) {
            log.error("[ProblemDomainServiceImpl] queryProblemDetailList bizError, exception: ", e);
            return QueryProblemDetailListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[ProblemDomainServiceImpl] queryProblemDetailList error, exception: ", e);
            return QueryProblemDetailListResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryProblemDetailPageListResponse queryProblemDetailPageList(QueryProblemDetailPageListRequest request) {
        log.info("[ProblemDomainServiceImpl] queryProblemDetailPageList request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            PageProblemDTO res = problemDetailBizService.queryProblemDetailPageList(request);
            return QueryProblemDetailPageListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(res)
                    .build();
        } catch (BizException e) {
            log.error("[ProblemDomainServiceImpl] queryProblemDetailPageList bizError, exception: ", e);
            return QueryProblemDetailPageListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[ProblemDomainServiceImpl] queryProblemDetailPageList error, exception: ", e);
            return QueryProblemDetailPageListResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }
}
