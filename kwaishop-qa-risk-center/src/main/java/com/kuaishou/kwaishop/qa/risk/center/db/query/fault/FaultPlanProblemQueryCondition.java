package com.kuaishou.kwaishop.qa.risk.center.db.query.fault;

import com.kuaishou.kwaishop.qa.risk.center.db.query.detail.DetailBaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2023-12-28
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FaultPlanProblemQueryCondition extends DetailBaseQueryCondition  {

    private String caseName;

    private Long planId;

    private Integer status;

}
