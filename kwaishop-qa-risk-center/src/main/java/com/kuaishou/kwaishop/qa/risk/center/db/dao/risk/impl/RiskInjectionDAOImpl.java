package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.RiskInjectionDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.RiskInjectionDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.InjectionRiskMapper;

@Repository
public class RiskInjectionDAOImpl implements RiskInjectionDAO {

    @Autowired
    private InjectionRiskMapper injectionRiskMapper;

    @Override
    public void insertRiskInjection(RiskInjectionDO riskInjectionDo) {
        injectionRiskMapper.insertRiskInjection(riskInjectionDo);
    }

    @Override
    public RiskInjectionDO queryRiskInjection(RiskInjectionDO riskInjectionDo) {
        return injectionRiskMapper.queryRiskInjection(riskInjectionDo.getRelationType(), riskInjectionDo.getRelationId(),
                riskInjectionDo.getDepartment(), riskInjectionDo.getDomain(),
                riskInjectionDo.getInjectionType(), riskInjectionDo.getInjectionSubType()
        );
    }

    @Override
    public void updateRiskInjectionOperate(Long id) {
        injectionRiskMapper.injectionRiskMapper(id);
    }

    @Override
    public List<RiskInjectionDO> queryRiskInjectionRelation(RiskInjectionDO riskInjectionDo) {
        return injectionRiskMapper.queryRiskInjectionRelation(riskInjectionDo.getRelationType(), riskInjectionDo.getRelationId(),
                riskInjectionDo.getDepartment(), riskInjectionDo.getDomain());
    }





}
