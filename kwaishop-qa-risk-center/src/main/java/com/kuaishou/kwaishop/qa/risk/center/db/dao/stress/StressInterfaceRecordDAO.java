package com.kuaishou.kwaishop.qa.risk.center.db.dao.stress;


import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressInterfaceRecordBO;


public interface StressInterfaceRecordDAO {

    PageBO<StressInterfaceRecordDO> queryStressInterfaceBaseList(QueryStressInterfaceRecordBO bo);

    StressInterfaceRecordDO queryStressInterfaceRecordById(Long interfaceId);

    List<StressInterfaceRecordDO> queryStressInterfaceRecords(QueryStressInterfaceRecordBO bo);

}
