package com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.impl;

import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.kuaishou.krpc.config.annotation.KrpcReference;
import com.kuaishou.kwaishop.biz.account.protobuf.rd.GetUserIdByBidRequest;
import com.kuaishou.kwaishop.biz.account.protobuf.rd.GetUserIdByBidResponse;
import com.kuaishou.kwaishop.biz.account.protobuf.rd.KrpcKwaishopBizAccountCenterRdServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagMetaDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagTestAccountRelDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.account.TagMetaMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.TestAccountConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TestAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.AddTagsToTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryMyRentTestAccountsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryMyTestAccountsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryTestAccountsRequest;

@Component
public class TestAccountConvertImpl implements TestAccountConvert {

    @KrpcReference(serviceName = "kwaishop-biz-account-center")
    private KrpcKwaishopBizAccountCenterRdServiceGrpc.IKwaishopBizAccountCenterRdService accountCenterRdService;

    @Resource
    private TagMetaMapper tagMetaMapper;

    private static final int MUL = 24 * 60 * 60 * 1000;

    @Override
    public TestAccountBO buildQueryBO(QueryMyTestAccountsRequest request) {
        return TestAccountBO.builder()
                .borrower(request.getOperator())
                .build();
    }

    @Override
    public TestAccountBO buildQueryBO(QueryTestAccountsRequest request) {
        long userId = request.getUserId();
        int loginType = request.getLoginType();
        long bid = request.getBid();
        TestAccountBO testAccountBO = TestAccountBO.builder()
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .build();
        if (userId > 0) {
            testAccountBO.setId(userId);
            testAccountBO.setUserId(userId);
        }
        if (bid > 0) {
            testAccountBO.setBid(bid);
            GetUserIdByBidRequest getUserIdByBidRequest = GetUserIdByBidRequest.newBuilder()
                    .setBid(bid)
                    .build();
            GetUserIdByBidResponse res = accountCenterRdService.getUserIdByBid(getUserIdByBidRequest);
            userId = res.getUserId();
            testAccountBO.setId(userId);
            testAccountBO.setUserId(userId);
        }
        if (loginType != 0) {
            testAccountBO.setLoginType(loginType);
        }
        if (!request.getStoreType().isEmpty()) {
            testAccountBO.setStoreType(request.getStoreType());
        }
        if (!request.getTagValue().isEmpty()) {
            testAccountBO.setTagValue(request.getTagValue());
        }
        return testAccountBO;
    }

    @Override
    public TestAccountBO buildQueryBO(QueryMyRentTestAccountsRequest request) {
        return TestAccountBO.builder()
                .borrower(request.getBorrower())
                .rentalStatus(request.getRentalStatus())
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .build();
    }

    @Override
    public TestAccountBO buildQueryBO(QueryTestAccountRequest request) {
        return TestAccountBO.builder().build();
    }

    @Override
    public TagTestAccountRelDO buildTagTestAccountRelDO(String tag, AddTagsToTestAccountRequest request) {
        TagMetaDO tagMetaDO = tagMetaMapper.selectByTagCode(tag);

        return TagTestAccountRelDO.builder()
                .testAccountId(request.getUserId())
                .bId(0L)
                .tagMetaId(0L)
                .tagOuterId(0L)
                .tagName(tagMetaDO.getTagName() == null ? "" : tagMetaDO.getTagName())
                .tagAuthStatus(2L)
                .kwaiId(request.getUserId())
                .tagValue(tag)
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .applyTime(System.currentTimeMillis())
                .creator(request.getOperator())
                .dueTime(request.getDuration() * MUL + System.currentTimeMillis())
                .duration(request.getDuration())
                .modifier(request.getOperator())
                .deleted(0)
                .bpm(Optional.of(request.getBpm()).orElse(""))
                .build();
    }
}
