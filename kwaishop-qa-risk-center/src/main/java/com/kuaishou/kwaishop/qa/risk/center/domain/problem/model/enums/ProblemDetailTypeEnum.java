package com.kuaishou.kwaishop.qa.risk.center.domain.problem.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-02
 */
public enum ProblemDetailTypeEnum {

    UNKNOWN(0, "未知类型"),
    ACCIDENT(1, "故障"),
    ONLINE_PROBLEM(2, "线上问题"),
    ;

    private final Integer code;

    private final String desc;

    ProblemDetailTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ProblemDetailTypeEnum of(Integer code) {
        for (ProblemDetailTypeEnum entityTypeEnum: values()) {
            if (entityTypeEnum.getCode().equals(code) && entityTypeEnum.getCode() > 0) {
                return entityTypeEnum;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo() {
        List<EnumInfo> res = new ArrayList<>();
        for (ProblemDetailTypeEnum typeEnum: values()) {
            if (typeEnum.getCode() > 0) {
                EnumInfo enumInfo = EnumInfo.newBuilder()
                        .setValue(typeEnum.getCode())
                        .setDesc(typeEnum.getDesc())
                        .build();
                res.add(enumInfo);
            }
        }
        return res;
    }
}
