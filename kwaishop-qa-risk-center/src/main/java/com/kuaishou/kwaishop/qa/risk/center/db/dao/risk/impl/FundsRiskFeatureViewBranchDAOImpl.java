package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;


import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.KspayBaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.FundsRiskFeatureViewBranchDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsRiskFeatureViewBranchDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.FundsRiskFeatureViewBranchMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.FundRiskFeatureViewBranchQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-01
 */
@Repository
public class FundsRiskFeatureViewBranchDAOImpl extends KspayBaseDAOImpl<FundsRiskFeatureViewBranchDO, FundRiskFeatureViewBranchQueryCondition>
        implements FundsRiskFeatureViewBranchDAO {
    @Autowired
    private FundsRiskFeatureViewBranchMapper fundsRiskFeatureViewBranchMapper;
    @Override
    public List<FundsRiskFeatureViewBranchDO> queryByCenterIds(Collection<Long> centerIds,
            CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsRiskFeatureViewBranchDO> queryByTeamIds(Long centerId, Collection<Long> teamIds,
            CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsRiskFeatureViewBranchDO> queryByCenterId(Long centerId, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsRiskFeatureViewBranchDO> queryByTeamId(Long centerId, Long teamId, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    public List<FundsRiskFeatureViewBranchDO> queryByCenterIdsTeamIds(Collection<Long> centerIds,
            Collection<Long> teamIds, CombineQueryParam queryParam) {
        return null;
    }

    @Override
    protected void fillLikeQueryCondition(FundRiskFeatureViewBranchQueryCondition condition,
            QueryWrapper<FundsRiskFeatureViewBranchDO> queryWrapper) {

    }

    @Override
    protected void fillQueryCondition(FundRiskFeatureViewBranchQueryCondition condition,
            QueryWrapper<FundsRiskFeatureViewBranchDO> queryWrapper) {

    }

    @Override
    protected BaseMapper<FundsRiskFeatureViewBranchDO> getMapper() {
        return fundsRiskFeatureViewBranchMapper;
    }
}
