package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskJsonMapConfigKey.lowCodeGrayConfig;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.service.LowCodeGrayService;

import lombok.extern.slf4j.Slf4j;
@Slf4j
@Service
public class LowCodeGrayServiceImpl implements LowCodeGrayService {
    private final String tinaheTelent = "th";

    @Override
    public boolean checkGrayStatus(String telent, String appKey) {
        Map<String, Object> grayConfig = lowCodeGrayConfig.getMap(String.class,
                Object.class);
        if (MapUtils.isNotEmpty(grayConfig)) {
            List<String> telentList = (List<String>) grayConfig.get("telent");
            List<String> tianheAppKeyList = (List<String>) grayConfig.get("tianheAppKey");
            if (!telentList.isEmpty()) {
                if (telentList.contains(tinaheTelent)) {
                    if (!tianheAppKeyList.isEmpty()) {
                        return tianheAppKeyList.contains(appKey);
                    } else {
                        // appKeyList为空，等于租户下全量放开
                        return true;
                    }
                } else {
                    return false;
                }
            }
        }
        return false;
    }
}
