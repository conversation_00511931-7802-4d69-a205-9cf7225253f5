package com.kuaishou.kwaishop.qa.risk.center.db.dao.drill.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.drill.FundsRiskDrillDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.drill.FundsRiskDrillDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.drill.FundsRiskDrillMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.drill.FundsRiskDrillQueryCondition;

@Repository
public class FundsRiskDrillDAOImpl extends BaseDAOImpl<FundsRiskDrillDO, FundsRiskDrillQueryCondition> implements FundsRiskDrillDAO {

    @Autowired
    private FundsRiskDrillMapper fundsRiskDrillMapper;


    @Override
    protected void fillQueryCondition(FundsRiskDrillQueryCondition condition, QueryWrapper<FundsRiskDrillDO> queryWrapper) {
        if (StringUtils.isNotBlank(condition.getSubRiskId())) {
            queryWrapper.and(q -> q.eq("sub_risk_id", condition.getSubRiskId()));
        }
        if (StringUtils.isNotBlank(condition.getDrillType())) {
            queryWrapper.and(q -> q.eq("drill_type", condition.getDrillType()));
        }
        if (StringUtils.isNotBlank(condition.getDrillTeamId())) {
            queryWrapper.and(q -> q.eq("drill_team_id", condition.getDrillTeamId()));
        }
        if (StringUtils.isNotBlank(condition.getCreator())) {
            queryWrapper.and(q -> q.eq("creator", condition.getCreator()));
        }
    }

    @Override
    protected BaseMapper<FundsRiskDrillDO> getMapper() {
        return fundsRiskDrillMapper;
    }


    @Override
    public PageBO<FundsRiskDrillDO> queryFundsRiskDrillList(FundsRiskDrillDO fundsRiskDrillDO) {
        FundsRiskDrillQueryCondition condition = FundsRiskDrillQueryCondition.builder()
                .subRiskId(fundsRiskDrillDO.getSubRiskId())
                .drillType(fundsRiskDrillDO.getDrillType())
                .drillTeamId(fundsRiskDrillDO.getDrillTeamId())
                .creator(fundsRiskDrillDO.getCreator())
                .build();
        return queryPageList(condition);
    }
}
