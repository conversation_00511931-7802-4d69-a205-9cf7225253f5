package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.recordHandler;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.Arrays;

import javax.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.protobuf.TextFormat;
import com.kuaishou.dp.auth.dsc.client.DscAccessTokenProvider;
import com.kuaishou.infra.framework.datasource.KsMasterVisitedManager;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.annotation.Kconfig;
import com.kuaishou.kconf.common.json.JsonMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.BashCommandConfig;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert.ReplayConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper.StrategyFlowRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.DateUtils;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.FlowRecordExtraDto;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowRecordDo;
import com.kuaishou.protobuf.databack.common.DateConstList;
import com.kuaishou.protobuf.databack.common.rerun.RerunDate;
import com.kuaishou.protobuf.databack.common.rerun.RerunDateType;
import com.kuaishou.protobuf.databack.common.rerun.RerunDownstreamFilter;
import com.kuaishou.protobuf.databack.common.rerun.RerunDownstreamType;
import com.kuaishou.protobuf.dp.scheduler.BashOperatorParamV2;
import com.kuaishou.protobuf.dp.scheduler.DagExtraInfo;
import com.kuaishou.protobuf.dp.scheduler.DagKeyV2;
import com.kuaishou.protobuf.dp.scheduler.PreDagMetaV2;
import com.kuaishou.protobuf.dp.scheduler.TaskDefV2;
import com.kuaishou.protobuf.dp.scheduler.TaskOperatorV2;
import com.kuaishou.protobuf.dp.workflow.proxy.CreatePreDagMetaAndTasksRequest;
import com.kuaishou.protobuf.dp.workflow.proxy.CreatePreDagMetaAndTasksResponse;
import com.kuaishou.protobuf.dp.workflow.proxy.ReleasePreDagForciblyRequest;
import com.kuaishou.protobuf.dp.workflow.proxy.ReleasePreDagForciblyResponse;
import com.kuaishou.protobuf.dp.workflow.proxy.rerun.AuditRerunJobRequest;
import com.kuaishou.protobuf.dp.workflow.proxy.rerun.AuditRerunJobResponse;
import com.kuaishou.protobuf.dp.workflow.proxy.rerun.CreateRerunJobByDagUuidRequest;
import com.kuaishou.protobuf.dp.workflow.proxy.rerun.CreateRerunJobByDagUuidResponse;
import com.kuaishou.workflow.proxy.sdk.client.PreDagClient;
import com.kuaishou.workflow.proxy.sdk.client.RerunJobClient;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-30
 */
@Service
@Slf4j
public class SparkLogHandler {

    private static String SQL_TEMPLATE =
                "select"
                + "    {0} as pdate,"
                + "    {1} as flowRecordId,"
                + "    scene_key as sceneKey,"
                + "    create_time as recordCreateTime,"
                + "    get_true_value_key("
                        + "get_json_object(dag_node_map,''$.STRATEGY_NODE''), action_hit_strategy_policy_codes, scene_type, ''STRATEGY''"
                        + ") as hitStrategyList,"
                + "    p_date as recordPDate,"
                + "    trace_id as id,"
                + "    feature_key_map as featureKeyMap,"
                + "    rule_exec_detail_info as ruleExecuteDetailInfo,"
                + "    end_time as recordEndTime,"
                + "    cast(current_timestamp() as bigint) as createTime,"
                + "    biz_key_info as bizKeyInfo,"
                + "    trace_id as traceId,"
                + "    request_hit_result as requestHitResult,"
                + "    get_true_value_key("
                        + "get_json_object(dag_node_map,''$.ACTION_NODE''), action_hit_strategy_policy_codes, scene_type, ''ACTION''"
                        + ") as hitActionList,"
                + "    condition_exec_detail_info as conditionExecuteDetailInfo,"
                + "    scene_type as sceneType"
                + " from"
                + "    ks_origin_ecom_log.kwaishop_apollo_strategy_policy_unitive_report_execute_record"
                + " where"
                + "    {2}"
                + " limit"
                + "    {3}";


    @Kconfig(value = "kwaishop.themisAdminCenter.sparkCommandParam", defaultValue = "")
    private Kconf<String> sparkCommandParam;

    private static final String SPARK_IN_HDFS_PATH =
            "viewfs://hadoop-lt-cluster/home/<USER>/data/udf/1563/";

    private static final String SECRET_KEY = "dca704fff08249ea93f7d4ea1d417c86";

    private static final int TIMEOUT = 1440;

    private PreDagClient preDagClient;

    private RerunJobClient rerunJobClient;

    @Autowired
    private StrategyFlowRecordMapper strategyFlowRecordMapper;

    @PostConstruct
    private void init() {
        preDagClient = new PreDagClient(PreDagMetaOperator.PRINCIPAL,
                () -> DscAccessTokenProvider.getToken(PreDagMetaOperator.PRINCIPAL, SECRET_KEY));


        rerunJobClient = new RerunJobClient(PreDagMetaOperator.PRINCIPAL,
                () -> DscAccessTokenProvider.getToken(PreDagMetaOperator.PRINCIPAL, SECRET_KEY));
    }

    public void handler(Long id) {
        KsMasterVisitedManager.setMasterVisited();
        StrategyFlowRecordDo flowRecordDo = strategyFlowRecordMapper.getFlowRecordById(id);
        FlowRecordExtraDto extraDto = JsonMapperUtils.fromJson(flowRecordDo.getExtra(), FlowRecordExtraDto.class);
        String whereClause;
        if (StringUtils.isNotBlank(extraDto.getWhereClause())) {
            whereClause = extraDto.getWhereClause();
        } else {
            whereClause = ReplayConvert.getWhereClause(
                    extraDto.getPDatePartitionList(),
                    extraDto.getPHourMinPartitionList(),
                    flowRecordDo.getSceneKeyList(),
                    extraDto.getHitStrategyList(),
                    extraDto.getHitActionList(),
                    extraDto.getRequestHitResult()
            );
        }

        String finalSql = MessageFormat.format(SQL_TEMPLATE, "'{{ds + 1}}'", id, whereClause,
                String.valueOf(flowRecordDo.getTotal()));
        log.info("SparkLogHandler.handler.sql:{}", finalSql);
        BashCommandConfig bashCommandConfig = new BashCommandConfig();
        bashCommandConfig.setTaskName("flowRecord" + id);
        bashCommandConfig.setSparkClass(
                "com.kuaishou.kwaishop.apollo.strategy.spark.application.StrategyFlowRecord");
        bashCommandConfig.setSparkJar("kwaishop-apollo-strategy-spark-1.0.403-SNAPSHOT.jar");
        bashCommandConfig.setSql(finalSql);
        createEstimateTask(bashCommandConfig);

    }

    /**
     * 创建测算任务
     */
    private void createEstimateTask(BashCommandConfig bashCommandConfig) {
        TaskDefV2 bashTask = createBashTask(bashCommandConfig);
        PreDagMetaV2 preDagMetaV2 = PreDagMetaOperator.getBasicPreDagMetaV2(bashCommandConfig.getTaskName());
        CreatePreDagMetaAndTasksResponse createPreDagMetaAndTasksResponse = createPreDag(preDagMetaV2, bashTask);
        executeNonPeriodicTask(createPreDagMetaAndTasksResponse, preDagMetaV2, bashCommandConfig);
    }

    /**
     * 创建preDag
     */
    protected CreatePreDagMetaAndTasksResponse createPreDag(PreDagMetaV2 basicPreDagMeta, TaskDefV2 bashTask) {
        // 设置不允许级联, false(默认值):表示允许级联， true:表示不允许级联
        DagExtraInfo dagExtraInfo = DagExtraInfo.newBuilder().setNotAllowCascade(true).build();
        // 组装PreDag
        CreatePreDagMetaAndTasksRequest createPreDagMetaAndTasksRequest =
                CreatePreDagMetaAndTasksRequest.newBuilder().setPreDagMetaV2(basicPreDagMeta)
                        .addAllRoutineTaskDef(Arrays.asList(bashTask)).setDagExtraInfo(dagExtraInfo).build();
        // 创建PreDag
        CreatePreDagMetaAndTasksResponse createPreDagMetaAndTasksResponse =
                preDagClient.createPreDagMetaAndTasks(createPreDagMetaAndTasksRequest);

        log.info("创建PreDag createPreDagMetaAndTasksResponse={}",
                TextFormat.shortDebugString(createPreDagMetaAndTasksResponse));
        if (createPreDagMetaAndTasksResponse.getCommonResponse().getCode() != 0) {
            log.error("创建PreDag 异常， create preDag fail, {}",
                    TextFormat.shortDebugString(createPreDagMetaAndTasksResponse.getCommonResponse()));
            throw new RuntimeException("创建PreDag 异常");
        }
        DagKeyV2 dagKeyV2 = createPreDagMetaAndTasksResponse.getDagKey();
        log.info("创建PreDag 成功，create preDag success. dagUuid:{} dagVersion:{}", dagKeyV2.getDagUuid(),
                dagKeyV2.getDagVersion());
        return createPreDagMetaAndTasksResponse;
    }

    public TaskDefV2 createBashTask(BashCommandConfig bashCommandConfig) {
        String taskName = bashCommandConfig.getTaskName();
        StringBuilder bashCommand = getBashCommand(bashCommandConfig);
        BashOperatorParamV2 build = BashOperatorParamV2.newBuilder().setCommand(bashCommand.toString()).build();
        return TaskDefV2.newBuilder().setName(taskName).setMaxTries(1) // 最大尝试次数，例如这里是一共尝试执行3次
                .setTimeout(TIMEOUT) // 执行超时时间，单位分钟，设置0会用系统默认的最大超时，即48*60分钟 (2天)
                .setOperator(TaskOperatorV2.BASH_OPERATOR) // task类型，BASH_OPERATOR
                .setCpuCoreNum(1.0) // cpu core num，本地执行bash脚本时的cpu核数（不是yarn上的计算资源），绝大多数情况设置1个即可
                .setMaxMemory(3 * 1024L) // memory MB，本地执行bash脚本时的内存MB（不是yarn上的计算资源），绝大多数情况设置3GB即可
                .setOperatorParam(build.toByteString()).build();

    }

    private StringBuilder getBashCommand(BashCommandConfig bashCommandConfig) {
        // 设置bashTask中执行的命令
        StringBuilder bashCommand = new StringBuilder();
        bashCommand.append("export SPARK_HOME=/home/<USER>/software/spark-ae").append("\n");
        bashCommand.append("spark_class=").append(bashCommandConfig.getSparkClass()).append("\n");

        bashCommand.append("spark_jar=").append(SPARK_IN_HDFS_PATH).append(bashCommandConfig.getSparkJar())
                .append("\n");

        bashCommand.append("queue_name=").append("${KUAISHOU_KWAIFLOW_JOB_YARN_QUEUE}\n");
        bashCommand.append(
                "input_path=/tmp/idp/${HADOOP_USER_NAME}/input\n"
                        + "\n"
                        + "${SPARK_HOME}/bin/spark-submit \\\n"
                        + "  --class ${spark_class} \\\n"
                        + "  --files ${SPARK_HOME}/conf/hive-site.xml \\\n"
                        + "  --queue ${queue_name} \\\n"
                        + sparkCommandParam.get() + " \\\n"
                        + "  --conf spark.addjar=\""
                        + "add jar viewfs:///home/<USER>/data/udf/365/kwaishop-apollo-strategy-udf-1.0.0-SNAPSHOT.jar\" \\\n"
                        + "  --conf spark.function=\"CREATE TEMPORARY FUNCTION get_true_value_key AS 'hudi.GetTrueValueKey'\" \\\n"
                        + "  --conf spark.sql=\"" + bashCommandConfig.getSql() + "\" \\\n"
                        + "  ${spark_jar} \\\n"
                        + "  yarn-client ${input_path} \\\n"
                        + "\n"
                        + "code=$?\n"
                        + "exit $code"
        );


        return bashCommand;
    }

    public void executeNonPeriodicTask(CreatePreDagMetaAndTasksResponse preDagMetaAndTasksResponse,
            PreDagMetaV2 basicPreDagMeta, BashCommandConfig bashCommandConfig) {
        // 当创建成功会返回唯一的dagUuid和响应的版本号 业务需要记录下来
        DagKeyV2 dagKey = preDagMetaAndTasksResponse.getDagKey();
        String dagUuid = dagKey.getDagUuid();
        int dagVersion = dagKey.getDagVersion();

        String dagId = preDagMetaAndTasksResponse.getDagKeyExtra().getDagId();
        log.info("创建PreDag 成功，create preDag success, dagId:{}, dagUuid:{} dagVersion:{}", dagId, dagUuid,
                dagVersion);

        releasePreDag(basicPreDagMeta, dagKey);

        auditRerunJob(basicPreDagMeta.getOwner(), dagUuid, basicPreDagMeta.getGroupId());
    }

    protected ReleasePreDagForciblyResponse releasePreDag(PreDagMetaV2 basicPreDagMeta, DagKeyV2 dagKey) {
        //正式发布
        long startDate = LocalDateTime.of(LocalDate.now().plusDays(-1), LocalTime.MIN).toInstant(ZoneOffset.of("+8"))
                .toEpochMilli();
        long endDate = LocalDateTime.of(LocalDate.now().plusYears(10), LocalTime.MIN).toInstant(ZoneOffset.of("+8"))
                .toEpochMilli();
        log.info("发布bash脚本，提交bash任务! dagShowName={}", basicPreDagMeta.getDagShowName());

        /**
         * start_date，是指数据时间的开始；
         * end_date，是指数据时间的结束；
         * 需要满足：start_date<=“理论调度时刻对应的数据时间”<=end_date，才会执行调度。
         */
        ReleasePreDagForciblyRequest releasePreDagRequest =
                ReleasePreDagForciblyRequest.newBuilder().setDagKey(dagKey).setStartDate(startDate) //例行开始时间
                        .setEndDate(endDate).build();

        /**
         * releasePreDagForcibly 不经过测试直接发布 PreDag。发布成功后，会生成 RoutineDag，进行周期性调度。
         */
        ReleasePreDagForciblyResponse releasePreDagResponse = preDagClient.releasePreDagForcibly(releasePreDagRequest);
        if (releasePreDagResponse.getCommonResponse().getCode() != 0) {
            log.error("发布PreDag异常 Release preDag fail, {}",
                    TextFormat.shortDebugString(releasePreDagResponse.getCommonResponse()));
            throw new RuntimeException("发布PreDag异常 Release preDag fail");
        }
        log.info("Release preDag success, routine dagKey {}",
                TextFormat.shortDebugString(releasePreDagResponse.getDagKey()));
        return releasePreDagResponse;
    }

    protected void auditRerunJob(String operator, String uuid, int groupId) {
        DateConstList constList =
                DateConstList.newBuilder().addValue(DateUtils.dayStartTime(DateUtils.getPreNDays(1))).build();
        RerunDate rerunDate = RerunDate.newBuilder().setType(RerunDateType.LIST).setList(constList).build();
        CreateRerunJobByDagUuidRequest request =
                CreateRerunJobByDagUuidRequest.newBuilder().addDagUuid(uuid).setConcurrency(4).setPriority(2)
                        .setSubmitter(operator).setRerunDate(rerunDate).setDownstreamFilter(
                                RerunDownstreamFilter.newBuilder().setDownstreamType(RerunDownstreamType.SELF).build())
                        .setGroupId(groupId).build();

        CreateRerunJobByDagUuidResponse response = rerunJobClient.createRerunJobByDagUuid(request);
        if (response.getCommonResponse().getCode() != 0) {
            log.error("createRerunJobByDagUuid CreateRerunJobByDagUuidResponse fail={},dagUuid={}",
                    TextFormat.shortDebugString(response.getCommonResponse()), uuid);
            throw new RuntimeException("createRerunJobByDagUuid CreateRerunJobByDagUuidResponse fail");
        }
        AuditRerunJobRequest auditRerunJobRequest =
                AuditRerunJobRequest.newBuilder().setRerunJobId(response.getRerunJobId()).setPass(true).build();
        AuditRerunJobResponse rerunJobResponse = rerunJobClient.auditRerunJob(auditRerunJobRequest);
        if (rerunJobResponse.getCommonResponse().getCode() != 0) {
            log.error("auditRerunJob rerunJobResponse fail={},dagUuid={}",
                    TextFormat.shortDebugString(response.getCommonResponse()), uuid);
            throw new RuntimeException("auditRerunJob rerunJobResponse fail");
        }
        log.info("auditRerunJob succeed dagUuid:{}, returnJobId:{}", uuid, response.getRerunJobId());
    }


}
