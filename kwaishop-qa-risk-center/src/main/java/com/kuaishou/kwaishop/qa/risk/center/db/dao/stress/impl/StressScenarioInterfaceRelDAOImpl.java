package com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressScenarioInterfaceRelDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioInterfaceRelDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress.StressScenarioInterfaceRelMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.stress.ScenarioQueryCondition;

@Repository
public class StressScenarioInterfaceRelDAOImpl extends BaseDAOImpl<StressScenarioInterfaceRelDO, ScenarioQueryCondition>
        implements StressScenarioInterfaceRelDAO {

    @Autowired
    private StressScenarioInterfaceRelMapper stressScenarioInterfaceRelMapper;

    @Override
    protected void fillQueryCondition(ScenarioQueryCondition condition, QueryWrapper<StressScenarioInterfaceRelDO> queryWrapper) {

    }

    @Override
    protected BaseMapper<StressScenarioInterfaceRelDO> getMapper() {
        return stressScenarioInterfaceRelMapper;
    }

    @Override
    public List<StressScenarioInterfaceRelDO> queryStressInterfaceRelByScenario(List<Long> scenarioIds) {
        if (scenarioIds == null || scenarioIds.size() == 0) {
            return new ArrayList<>();
        }
        return stressScenarioInterfaceRelMapper.getByInterfaceListByScenarios(scenarioIds);
    }

    @Override
    public StressScenarioInterfaceRelDO createRelOrUpdate(StressScenarioInterfaceRelDO stressInterfaceRelDO) {
        StressScenarioInterfaceRelDO relDO =
                stressScenarioInterfaceRelMapper.getRelById(stressInterfaceRelDO.getScenarioId(),
                stressInterfaceRelDO.getInterfaceId());
        if (relDO != null) {
                //update
            stressInterfaceRelDO.setId(relDO.getId());
            stressInterfaceRelDO.setCreateTime(relDO.getCreateTime());
            stressScenarioInterfaceRelMapper.updateById(stressInterfaceRelDO);
        } else {
                //create
            stressScenarioInterfaceRelMapper.insert(stressInterfaceRelDO);
        }

        return stressInterfaceRelDO;
    }
}
