package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.ConfigCheckResultRecordDo;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.ConfigCheckResulRecordQueryCondition;

public interface ConfigCheckResultRecordDAO {
    long insert(ConfigCheckResultRecordDo configCheckResultRecordDo);
    List<ConfigCheckResultRecordDo> queryConfigCheckResultRecord(
            ConfigCheckResulRecordQueryCondition configCheckResulRecordQueryCondition);
    List<ConfigCheckResultRecordDo> queryConfigCheckResultRecordById(Long id);
    List<ConfigCheckResultRecordDo> queryConfigCheckResultRecordByReportId(Long reportId);
}
