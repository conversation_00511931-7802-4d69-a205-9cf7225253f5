package com.kuaishou.kwaishop.qa.risk.center.domain.feature.convert;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureBranchDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundRiskFeatureDetailQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundsRiskFeatureQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayBranchAndViewIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetail;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetailSimple;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetailSimpleV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureListParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureViewParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.PageKspayRiskFeatureListDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryPageRiskFeatureListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryRiskFeatureDetailByIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryRiskFeatureDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayBranchRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayBranchRequestV2;
//import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.MethodCovInfo;

public interface KspayRiskFeatureConvert {
    FundsRiskFeatureQueryCondition buildFundsRiskFeatureListQueryCondition(QueryPageRiskFeatureListRequest request);
    FundRiskFeatureDetailQueryCondition buildFundsRiskFeatureBranchQueryCondition(QueryPageRiskFeatureListRequest request);

    FundRiskFeatureDetailQueryCondition buildFundsRiskRiskQueryCondition(KspayBranchRequest request);
    FundRiskFeatureDetailQueryCondition buildFundsRiskRiskQueryConditionV2(KspayBranchRequestV2 request);
    FundRiskFeatureDetailQueryCondition buildFundsRiskBranchQueryCondition(KspayBranchAndViewIdRequest request);
    FundRiskFeatureDetailQueryCondition buildFundsRiskFeatureDetailQueryCondition(QueryRiskFeatureDetailRequest request);
    FundRiskFeatureDetailQueryCondition buildFundsRiskFeatureDetailQueryCondition(KspayRiskFeatureDetail request);

    FundRiskFeatureDetailQueryCondition buildFundsRiskBranchQueryConditionId(QueryRiskFeatureDetailByIdRequest request);

    KspayRiskFeatureDetail buildKspayRiskFeatureDetail(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO);
    List<KspayRiskFeatureDetail> buildKspayRiskFeatureBranch(List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS);
    List<KspayRiskFeatureDetail> buildKspayRiskFeatureBranchError(List<FundsRiskFeatureBranchDO> fundsRiskFeatureBranchDOS);

    KspayRiskFeatureListParam buildKspayRiskFeatureListParam(FundsRiskFeatureDO fundsRiskFeatureDO,
                                                             List<KspayRiskFeatureDetail> kspayRiskFeatureDetails);

    FundRiskFeatureDetailQueryCondition buildFundRiskFeatureBranchQueryCondition(String featureId);
    FundRiskFeatureDetailQueryCondition buildFundRiskFeatureBranchQueryCondition(String featureId, Integer isRisk);



    PageKspayRiskFeatureListDTO convertToFeaturePageDTO(List<KspayRiskFeatureListParam> kspayRiskFeatureListParams, Integer pageNo,
                                                        Integer pageSize, Long total);

    KspayRiskFeatureDetailSimple convertToRiskDetailDTO(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDOS,
                                                        KspayBranchRequest request);
    KspayRiskFeatureDetailSimpleV2 convertToRiskDetailDTOV2(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDOS,
                                                            KspayBranchRequestV2 request, ArrayList<String> qalist,
                                                            boolean isSendMsg);

    FundsRiskFeatureDO buildHasRiskUpdateFeatureView(KspayRiskFeatureViewParam request);
    FundsRiskFeatureDO buildNoRiskUpdateFeatureView(KspayRiskFeatureViewParam request);
    FundsRiskFeatureDO buildUpdateFeatureViewTestType(KspayRiskFeatureViewParam request);

    FundsRiskFeatureBranchDO buildUpdateFeatureViewBranch(KspayRiskFeatureDetail request);

    FundsRiskFeatureBranchDO buildUpdateFeatureViewBranchByFeatureId(KspayRiskFeatureDetail request,
                                                                     Integer toRiskStatus, Integer toAuditCover);

    FundsRiskFeatureBranchDO buildUpdateFeatureViewBranchById(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO,
                                                              KspayRiskFeatureDetail request, Integer toRiskStatus, Integer toAuditCover);

    FundsRiskFeatureBranchDO buildAutoUpdateBranchById(FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO,
                                                              String updater, Integer toRiskStatus, Integer toAuditCover);

}
