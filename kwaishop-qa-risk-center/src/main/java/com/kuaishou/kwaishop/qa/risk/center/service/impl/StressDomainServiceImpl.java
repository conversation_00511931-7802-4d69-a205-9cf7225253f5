package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.biz.StressBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.CreateScenarioFromJsonRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.CreateScenarioFromJsonResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.GetScenarioRecordDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.GetScenarioRecordDetailResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.KrpcStressDomainServiceGrpc.StressDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.PageScenarioDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.PageScenarioRecordDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.PageStressInterfaceBaseDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.PageStressServiceBaseDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryInterfaceBaseListByPageRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryInterfaceBaseListByPageResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryScenarioRecordsByPageRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryScenarioRecordsByPageResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryScenariosByPageRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryScenariosByPageResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryServiceBaseListByPageRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryServiceBaseListByPageResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressInterfaceRecordDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressInterfaceWithBaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressInterfaceWithBaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressScenarioRecordDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressServiceRecordDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressServiceWithBaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressServiceWithBaseResponse;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class StressDomainServiceImpl extends StressDomainServiceImplBaseV2 {
    @Resource
    private StressBizService stressBizService;

    public QueryScenarioRecordsByPageResponse queryScenarioRecordsByPage(QueryScenarioRecordsByPageRequest queryScenarioRecordsByPageRequest) {
        log.info("[StressDomainServiceImpl] queryScenarioRecordsByPage, request: {}",
                ProtobufUtil.protoToJsonString(queryScenarioRecordsByPageRequest));
        try {
            PageScenarioRecordDTO data = stressBizService.queryPageScenarioRecordList(queryScenarioRecordsByPageRequest);
            return QueryScenarioRecordsByPageResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(data)
                    .build();
        } catch (BizException e) {
            log.error("[StressDomainServiceImpl] queryRecordsByPage bizError, exception: ", e);
            return QueryScenarioRecordsByPageResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[StressDomainServiceImpl] queryRecordsByPage error, exception: ", e);
            return QueryScenarioRecordsByPageResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    public GetScenarioRecordDetailResponse getScenarioRecordDetail(
            GetScenarioRecordDetailRequest getScenarioRecordDetailRequest) {
        log.info("[StressDomainServiceImpl] getScenarioRecordDetail, request: {}",
                ProtobufUtil.protoToJsonString(getScenarioRecordDetailRequest));
        try {
            Long id = Long.parseLong(getScenarioRecordDetailRequest.getId());
            StressScenarioRecordDTO data = stressBizService.getScenarioRecordDetail(id);
            return GetScenarioRecordDetailResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(data)
                    .build();
        } catch (BizException e) {
            log.error("[StressDomainServiceImpl] getScenarioRecordDetail bizError, exception: ", e);
            return GetScenarioRecordDetailResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();

        } catch (Exception e) {
            log.error("[StressDomainServiceImpl] getScenarioRecordDetail error, exception: ", e);
            return GetScenarioRecordDetailResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    public QueryScenariosByPageResponse queryScenariosByPage(QueryScenariosByPageRequest queryScenariosByPageRequest) {
        log.info("[StressDomainServiceImpl] queryRecordsByPage, request: {}", ProtobufUtil.protoToJsonString(queryScenariosByPageRequest));
        try {
            PageScenarioDTO data = stressBizService.queryPageScenarioList(queryScenariosByPageRequest);
            return QueryScenariosByPageResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(data)
                    .build();
        } catch (BizException e) {
            log.error("[StressDomainServiceImpl] queryRecordsByPage bizError, exception: ", e);
            return QueryScenariosByPageResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[StressDomainServiceImpl] queryRecordsByPage error, exception: ", e);
            return QueryScenariosByPageResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    public QueryScenariosByPageResponse queryScenariosByPageWithInterface(QueryScenariosByPageRequest queryScenariosByPageRequest) {
        log.info("[StressDomainServiceImpl] queryScenariosByPageWithInterface, request: {}",
                ProtobufUtil.protoToJsonString(queryScenariosByPageRequest));
        try {
            PageScenarioDTO data = stressBizService.queryPageScenarioWithInterface(queryScenariosByPageRequest);
            return QueryScenariosByPageResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(data)
                    .build();
        } catch (BizException e) {
            log.error("[StressDomainServiceImpl] queryScenariosByPageWithInterface bizError, exception: ", e);
            return QueryScenariosByPageResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[StressDomainServiceImpl] queryScenariosByPageWithInterface error, exception: ", e);
            return QueryScenariosByPageResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    public QueryInterfaceBaseListByPageResponse
    queryInterfaceBaseListByPage(QueryInterfaceBaseListByPageRequest queryInterfaceBaseListByPageRequest) {
        log.info("[StressDomainServiceImpl] queryRecordsByPage, request: {}",
                ProtobufUtil.protoToJsonString(queryInterfaceBaseListByPageRequest));
        try {
            PageStressInterfaceBaseDTO data = stressBizService.queryInterfaceBaseListByPage(queryInterfaceBaseListByPageRequest);
            return QueryInterfaceBaseListByPageResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(data)
                    .build();
        } catch (BizException e) {
            log.error("[StressDomainServiceImpl] queryRecordsByPage bizError, exception: ", e);
            return QueryInterfaceBaseListByPageResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[StressDomainServiceImpl] queryRecordsByPage error, exception: ", e);
            return QueryInterfaceBaseListByPageResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    public QueryServiceBaseListByPageResponse queryServiceBaseListByPage(QueryServiceBaseListByPageRequest queryServiceBaseListByPageRequest) {
        log.info("[StressDomainServiceImpl] queryRecordsByPage, request: {}", ProtobufUtil.protoToJsonString(queryServiceBaseListByPageRequest));
        try {
            PageStressServiceBaseDTO data = stressBizService.queryServiceBaseListByPage(queryServiceBaseListByPageRequest);
            return QueryServiceBaseListByPageResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(data)
                    .build();
        } catch (BizException e) {
            log.error("[StressDomainServiceImpl] queryRecordsByPage bizError, exception: ", e);
            return QueryServiceBaseListByPageResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[StressDomainServiceImpl] queryRecordsByPage error, exception: ", e);
            return QueryServiceBaseListByPageResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    public StressInterfaceWithBaseResponse compareInterfaceBase(StressInterfaceWithBaseRequest stressInterfaceWithBaseRequest) {
        log.info("[StressDomainServiceImpl] compareInterfaceBase, request: {}", ProtobufUtil.protoToJsonString(stressInterfaceWithBaseRequest));
        try {
            List<StressInterfaceRecordDTO> data = stressBizService.compareStressInterfaceBase(stressInterfaceWithBaseRequest);
            return StressInterfaceWithBaseResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(data)
                    .build();
        } catch (BizException e) {
            log.error("[StressDomainServiceImpl] compareInterfaceBase bizError, exception: ", e);
            return StressInterfaceWithBaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[StressDomainServiceImpl] compareInterfaceBase error, exception: ", e);
            return StressInterfaceWithBaseResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    //服务基线对比
    public StressServiceWithBaseResponse compareServiceBase(StressServiceWithBaseRequest stressServiceWithBaseRequest) {
        log.info("[StressDomainServiceImpl] compareServiceBase, request: {}", ProtobufUtil.protoToJsonString(stressServiceWithBaseRequest));
        try {
            List<StressServiceRecordDTO> data = stressBizService.compareStressSerBase(stressServiceWithBaseRequest);
            return StressServiceWithBaseResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(data)
                    .build();
        } catch (BizException e) {
            log.error("[StressDomainServiceImpl] queryRecordsByPage bizError, exception: ", e);
            return StressServiceWithBaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[StressDomainServiceImpl] queryRecordsByPage error, exception: ", e);
            return StressServiceWithBaseResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    public CreateScenarioFromJsonResponse createScenarioFromJson(CreateScenarioFromJsonRequest createScenarioFromJsonRequest) {
        log.info("[StressDomainServiceImpl] compareServiceBase, request: {}", ProtobufUtil.protoToJsonString(createScenarioFromJsonRequest));
        try {
            Boolean success = stressBizService.createScenarioFromJson(createScenarioFromJsonRequest);
            if (success) {
                return CreateScenarioFromJsonResponse.newBuilder()
                        .setResult(BaseResultCode.SUCCESS_VALUE)
                        .build();
            } else {
                return CreateScenarioFromJsonResponse.newBuilder()
                        .setResult(BaseResultCode.UNKNOWN_VALUE)
                        .setErrorMsg("更新失败")
                        .build();
            }

        } catch (BizException e) {
            log.error("[StressDomainServiceImpl] queryRecordsByPage bizError, exception: ", e);
            return CreateScenarioFromJsonResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[StressDomainServiceImpl] queryRecordsByPage error, exception: ", e);
            return CreateScenarioFromJsonResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    };
}
