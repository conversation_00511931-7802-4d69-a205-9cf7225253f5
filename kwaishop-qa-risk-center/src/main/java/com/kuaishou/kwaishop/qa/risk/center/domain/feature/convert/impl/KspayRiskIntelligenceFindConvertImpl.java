package com.kuaishou.kwaishop.qa.risk.center.domain.feature.convert.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.KspayQaRiskMapConfig.BIZ_SPACE_MAP;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.kuaishou.kspay.util.GsonUtils;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundRiskIntelligenceFindDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundRiskIntelligenceQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.feature.convert.KspayRiskIntelligenceFindConvert;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskIntelligenceFindListParam;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.PageKspayRiskIntelligenceFindListDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.QueryPageRiskFieldsRequest;

@Component
public class KspayRiskIntelligenceFindConvertImpl implements KspayRiskIntelligenceFindConvert {
    private final HashMap<String, String> bizSpaceMap = BIZ_SPACE_MAP.get();

    @Override
    public FundRiskIntelligenceQueryCondition buildRiskIntelligenceListQueryCondition(QueryPageRiskFieldsRequest request) {
        return FundRiskIntelligenceQueryCondition.builder()
                .creator(request.getCreator().trim())
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .bizDef(request.getBizDef())
                .sourceName(request.getSourceName())
                .tableName(request.getTableName())
                .columnName(request.getColumnName())
                .symbol(request.getSymbol())
                .riskLevel(request.getRiskLevel())
                .bizCode(request.getBizCode())
                .build();
    }

    @Override
    public KspayRiskIntelligenceFindListParam buildRiskIntelligenceParam(FundRiskIntelligenceFindDO fundRiskIntelligenceFindDO) {
        return KspayRiskIntelligenceFindListParam.newBuilder()
                .setId(fundRiskIntelligenceFindDO.getId().toString())
                .setBizDef(StringUtils.trimToEmpty(fundRiskIntelligenceFindDO.getBizDef()))
                .setSourceName(StringUtils.trimToEmpty(fundRiskIntelligenceFindDO.getSourceName()))
                .setDataBase(StringUtils.trimToEmpty(fundRiskIntelligenceFindDO.getDataBase()))
                .setRiskLevel(StringUtils.trimToEmpty(fundRiskIntelligenceFindDO.getRiskLevel()))
                .setTableName(StringUtils.trimToEmpty(fundRiskIntelligenceFindDO.getTableName()))
                .setColumnName(StringUtils.trimToEmpty(fundRiskIntelligenceFindDO.getColumnName()))
                .setExtraInfo(fundRiskIntelligenceFindDO.getExtraInfo())
//                .setIsMarked(fundRiskIntelligenceFindDO.getIsMarked())
                .setSymbol(fundRiskIntelligenceFindDO.getSymbol())
                .setOldTableName(StringUtils.trimToEmpty(fundRiskIntelligenceFindDO.getOldTableName()))
                .setHasAudit(fundRiskIntelligenceFindDO.getHasAudit())
                .setCreator(StringUtils.trimToEmpty(fundRiskIntelligenceFindDO.getCreator()))
                .setCreateTime(fundRiskIntelligenceFindDO.getCreateTime())
                .setUpdater(StringUtils.trimToEmpty(fundRiskIntelligenceFindDO.getUpdater()))
                .setUpdateTime(fundRiskIntelligenceFindDO.getUpdateTime())
                .setBizCode(getBizCodeByExtraInfo(fundRiskIntelligenceFindDO.getExtraInfo()))
                .build();
    }

    private String getBizCodeByExtraInfo(String extraInfo) {
       return GsonUtils.fromJSON(extraInfo, Map.class, String.class, String.class)
               .getOrDefault("biz_code", "");
    }

    @Override
    public PageKspayRiskIntelligenceFindListDTO convertToIntelligencePageDTO(
            List<KspayRiskIntelligenceFindListParam> kspayRiskIntelligenceFindListParams,
            Integer pageNo, Integer pageSize, Long total) {
        return PageKspayRiskIntelligenceFindListDTO.newBuilder()
                .setPageNo(pageNo)
                .setPageSize(pageSize)
                .setTotal(total)
                .addAllRiskIntelligenceList(kspayRiskIntelligenceFindListParams)
                .build();
    }

    @Override
    public FundRiskIntelligenceFindDO buildUpdateRiskLevelParam(
            FundRiskIntelligenceFindDO fundRiskIntelligenceFindDO, KspayRiskIntelligenceFindListParam request,
            String riskLevel) {
        return FundRiskIntelligenceFindDO.builder()
                .id(fundRiskIntelligenceFindDO.getId())
                .riskLevel(riskLevel)
                .updater(request.getUpdater())
                .updateTime(System.currentTimeMillis())
                .build();
    }
}
