package com.kuaishou.kwaishop.qa.risk.center.db.dao.klink.impl;


import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.klink.UserKlinkLoginDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.klink.UserKlinkLoginDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.klink.UserKlinkLoginMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.klink.UserKlinkLoginQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.model.UserKlinkLoginBO;


@Repository
public class UserKlinkLoginDAOImpl extends BaseDAOImpl<UserKlinkLoginDO, UserKlinkLoginQueryCondition> implements UserKlinkLoginDAO {

    @Autowired
    private UserKlinkLoginMapper userKlinkLoginMapper;

    @Override
    public PageBO<UserKlinkLoginDO> queryUserLoginInfoList(UserKlinkLoginBO userKlinkLoginBO) {
        UserKlinkLoginQueryCondition queryCondition = UserKlinkLoginQueryCondition.builder()
                .userId(userKlinkLoginBO.getUserId())
                .nickname(userKlinkLoginBO.getNickname())
                .pageNo(userKlinkLoginBO.getPageNo())
                .pageSize(userKlinkLoginBO.getPageSize())
                .build();
        return queryPageList(queryCondition);

    }

    @Override
    public long insertOrUpdateUserKlinkLogin(UserKlinkLoginDO userKlinkLoginDO) {
        try {
            return saveOrUpdate(userKlinkLoginDO);
        } catch (Exception ex) {
            return 0;
        }

    }

    @Override
    public UserKlinkLoginDO queryUserLoginInfoByUserId(long userId) {
        UserKlinkLoginQueryCondition queryCondition = UserKlinkLoginQueryCondition.builder()
                .userId(userId)
                .build();
        List<UserKlinkLoginDO> res = queryList(queryCondition);
        if (CollectionUtils.isEmpty(res)) {
            return null;
        }
        return res.get(0);
    }

    @Override
    protected void fillQueryCondition(UserKlinkLoginQueryCondition condition,
                                      QueryWrapper<UserKlinkLoginDO> queryWrapper) {
        if (condition.getUserId() != null && condition.getUserId() > 0) {
            queryWrapper.and(q -> q.eq("user_id", condition.getUserId()));
        }
        if (condition.getNickname() != null) {
            queryWrapper.and(q -> q.eq("nickname", condition.getNickname()));
        }
        if (condition.getType() != null) {
            queryWrapper.and(q -> q.eq("type", condition.getType()));
        }

    }

    @Override
    protected BaseMapper<UserKlinkLoginDO> getMapper() {
        return userKlinkLoginMapper;
    }

}
