package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.domain.datatool.biz.DataQueryExeBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.ExecuteGrpcQueryRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.ExecuteGrpcQueryResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.KrpcExecuteGrpcQueryDomainServiceGrpc.ExecuteGrpcQueryDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryExeResultByDataSourceIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryExeResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool.QueryExeResultResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2022-11-14
 */

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class DataQueryDomainServiceImpl extends ExecuteGrpcQueryDomainServiceImplBaseV2 {
    @Autowired
    private DataQueryExeBizService dataQueryExeBizService;

    @Override
    public ExecuteGrpcQueryResponse callKessMethod(ExecuteGrpcQueryRequest request) {
        return dataQueryExeBizService.callKessMethod(request);
    }

    @Override
    public QueryExeResultResponse queryExeResult(QueryExeResultRequest request) {
        return dataQueryExeBizService.queryExeResult(request);
    }

    @Override
    public QueryExeResultResponse queryExeResultByDataSourceId(QueryExeResultByDataSourceIdRequest request) {
        return dataQueryExeBizService.queryExeResultByDataSourceId(request);
    }


}
