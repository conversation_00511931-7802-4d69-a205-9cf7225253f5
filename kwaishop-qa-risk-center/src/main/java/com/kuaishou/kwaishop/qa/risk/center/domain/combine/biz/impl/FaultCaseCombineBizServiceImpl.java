package com.kuaishou.kwaishop.qa.risk.center.domain.combine.biz.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.FAULT_COMBINE_CENTER_ID_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.FAULT_COMBINE_TIME_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.OPERATOR_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityDataTypeEnum.FAULT_CASE_LEVEL_DATA;
import static com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum.TEAM_TYPE;
import static java.util.stream.Collectors.groupingBy;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDataDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultPlanRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.biz.FaultCaseCombineBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.convert.FaultCaseCombineConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityDataConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityDataBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.EntityDataService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.impl.TeamEntityServiceImpl;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.convert.FaultCaseConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCaseCombineBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCombineBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCombineProblemInfo;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCombineQueryBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultPlanRecordBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultPlanStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.service.FaultCaseService;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.service.FaultCombineService;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.service.FaultPlanService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseCombineDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseCombineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseLevelCombineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCaseProblemCombineRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultCombineProblemInfoDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultDetailsDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.FaultDetailsGroupByMemberRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.EntityDataDTO;
import com.kuaishou.kwaishop.qa.risk.center.utils.date.LocalDateUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-13
 */
@Service
@Lazy
@Slf4j
public class FaultCaseCombineBizServiceImpl implements FaultCaseCombineBizService {

    @Autowired
    private FaultCombineService faultCombineService;

    @Autowired
    private FaultCaseCombineConvert caseCombineConvert;

    @Autowired
    private EntityDataService entityDataService;

    @Autowired
    private TeamEntityServiceImpl entityService;

    @Autowired
    private EntityDataConvert entityDataConvert;

    @Autowired
    private FaultPlanService faultPlanService;

    @Autowired
    private FaultCaseService faultCaseService;

    @Autowired
    private FaultCaseConvert faultCaseConvert;


    @Override
    public FaultCaseCombineDTO getFaultCaseCombine(FaultCaseCombineRequest request) {
        FaultCombineQueryBO queryBO = caseCombineConvert.buildQueryBO(request);
        preCheck(queryBO);
        FaultCombineBO faultCombineBO = faultCombineService.getCaseCombine(queryBO);
        return caseCombineConvert.buildCaseCombineDTO(faultCombineBO);
    }

    @Override
    public List<FaultDetailsDTO> getFaultDetailsGroupByMember(FaultDetailsGroupByMemberRequest request) {

        return faultCombineService.getFaultDetailsGroupByMember(request);
    }

    @Override
    public List<FaultCombineProblemInfoDTO> getCaseProblemCombine(FaultCaseProblemCombineRequest request) {
        FaultCombineQueryBO queryBO = caseCombineConvert.buildQueryBO(request);
        preCheck(queryBO);
        List<FaultCombineProblemInfo> problemInfos = faultCombineService.getCaseProblemCombine(queryBO);
        return problemInfos.stream().map(caseCombineConvert::buildProblemInfoDTO).collect(Collectors.toList());
    }

    private void preCheck(FaultCombineQueryBO queryBO) {
        if (StringUtils.isBlank(queryBO.getOperator())) {
            throw new BizException(OPERATOR_EMPTY_ERROR);
        }
        if (queryBO.getCenterId() <= 0) {
            throw new BizException(FAULT_COMBINE_CENTER_ID_ERROR);
        }
        if (queryBO.getStartTime() <= 0 || queryBO.getEndTime() <= 0
                || queryBO.getEndTime() < queryBO.getStartTime()) {
            throw new BizException(FAULT_COMBINE_TIME_ERROR);
        }
    }

    @Override
    public List<EntityDataDTO> getCaseLevelCombine(FaultCaseLevelCombineRequest request) {
        if (StringUtils.isBlank(request.getOperator())) {
            throw new BizException(OPERATOR_EMPTY_ERROR);
        }
        if (request.getCenterId() <= 0) {
            throw new BizException(FAULT_COMBINE_CENTER_ID_ERROR);
        }
        List<EntityDO> teamDOList = entityService.queryByEntityId(request.getCenterId());
        Map<Long, EntityDO> teamMap = CollectionUtils.isEmpty(teamDOList) ? Maps.newHashMap() : teamDOList.stream()
                .collect(Maps::newHashMap, (m, v) -> m.put(v.getId(), v), Map::putAll);
        EntityDataBO entityDataBO = EntityDataBO.builder()
                .dataType(FAULT_CASE_LEVEL_DATA.getCode())
                .dt(LocalDateUtil.getYesterdayDateStringBasic())
                .entityType(TEAM_TYPE.getCode())
                .entityIds(teamMap.keySet())
                .build();
        // 先查昨日
        List<EntityDataDO> entityDataDOList = entityDataService.queryEntityDataList(entityDataBO);
        if (CollectionUtils.isEmpty(entityDataDOList)) {
            // 昨日未产出，先查前日
            entityDataBO.setDt(LocalDateUtil.getPastDateStringBasic(2));
            entityDataDOList = entityDataService.queryEntityDataList(entityDataBO);
        }
        if (request.getQueryCoverCustom()) {
            if (request.getEndTime() <= request.getStartTime()) {
                throw new BizException(FAULT_COMBINE_TIME_ERROR);
            }
            queryCustom(entityDataDOList, request.getStartTime(), request.getEndTime());
        }
        return entityDataConvert.buildEntityDataDTO(entityDataDOList, teamMap);
    }

    private void queryCustom(List<EntityDataDO> sourceData, Long startTime, Long endTime) {
        // 筛出时间段满足的演练记录
        FaultPlanRecordBO faultPlanRecordBO = FaultPlanRecordBO.builder()
                .status(FaultPlanStatusEnum.FINISH.getCode())
                .updateTimeGe(startTime)
                .updateTimeLe(endTime)
                .build();
        List<FaultPlanRecordDO> recordDOS = faultPlanService.queryFaultPlanRecordList(faultPlanRecordBO);
        if (CollectionUtils.isEmpty(recordDOS)) {
            return;
        }
        // 获取case
        Set<Long> caseIds = recordDOS.stream().map(FaultPlanRecordDO::getCaseId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(caseIds)) {
            return;
        }
        List<FaultCaseDO> caseDOS = faultCaseService.queryCaseList(faultCaseConvert.buildQueryBO(caseIds));
        if (CollectionUtils.isEmpty(caseIds)) {
            return;
        }
        Map<Long/*teamId*/, Map<Integer/*level*/, List<FaultCaseDO>>> teamLevelMap = caseDOS.stream()
                .collect(groupingBy(FaultCaseDO::getTeamId, groupingBy(FaultCaseDO::getCalleeLevel)));
        sourceData.forEach(e -> {
            FaultCaseCombineBO combineBO = fromJSON(e.getDataInfo(), FaultCaseCombineBO.class);
            combineBO.getLevelInfos().forEach(c -> {
                if (teamLevelMap.containsKey(e.getEntityId()) && teamLevelMap.get(e.getEntityId()).containsKey(c.getLevelEnum())) {
                    List<FaultCaseDO> faultCaseDOS = teamLevelMap.get(e.getEntityId()).get(c.getLevelEnum());
                    c.setCoverCount((long) faultCaseDOS.size());
                    // Tips: 计划id后续有需求在维护
                }
            });
            e.setDataInfo(toJSON(combineBO));
        });
    }
}
