package com.kuaishou.kwaishop.qa.risk.center.db.dao.feature;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.GitToDepartmentMappingParamDO;

public interface GitToDepartmentMappingDAO {
    GitToDepartmentMappingParamDO queryMappingByProjectId(Long gitProjectId);
    void insertOrUpdateMapping(GitToDepartmentMappingParamDO gitToDepartmentMappingParamDO);
    void insertMapping(GitToDepartmentMappingParamDO gitToDepartmentMappingParamDO);
    void updateMapping(GitToDepartmentMappingParamDO gitToDepartmentMappingParamDO);
}
