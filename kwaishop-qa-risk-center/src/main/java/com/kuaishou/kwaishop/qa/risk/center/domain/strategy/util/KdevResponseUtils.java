package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.util;

import com.kuaishou.intown.json.JSONArray;
import com.kuaishou.intown.json.JSONObject;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.CheckReplayStatusByPipelineResponseData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.StartReplayByPipelineResponseData;

public class KdevResponseUtils {

    public static final int SUCCESS_STATUS = 200;
    public static final int ERROR_STATUS = -1;
    public static final String SUCCESS_MESSAGE = "success";

    public static String setData(String id) {
        // 创建一个 JSONObject 实例
        JSONObject jsonObject = new JSONObject();

        // 添加固定字段及其值
        jsonObject.put("failReason", "失败原因");
        jsonObject.put("viewUrlTitle", "结果详情");
        jsonObject.put("checkUrl", "https://legoprt.corp.kuaishou.com/gateway/apollo/strategy/test/flow/CheckReplayStatusByPipeline?pipelineId=" + id);
        jsonObject.put("viewUrl", "https://legoprt.corp.kuaishou.com/gateway/apollo/strategy/test/flow/CheckReplayStatusByPipeline?pipelineId=" + id);
        jsonObject.put("cancelUrl", "");
        jsonObject.put("viewContent", "需要展示内容");
        jsonObject.put("其他字段", "其他字段值");

        // 创建一个 JSONArray 实例用于 xxx_reportList
        JSONArray reportList = new JSONArray();

        // 创建一个 JSONObject 实例用于 reportList 的元素
        JSONObject report = new JSONObject();
        report.put("title", "自定义报告标题");
        report.put("url", "https://urls");

        // 将 report 添加到 reportList 中
        reportList.add(report);

        // 将 reportList 添加到 jsonObject 中
        jsonObject.put("xxx_reportList", reportList);

        // 返回 JSON 字符串
        return jsonObject.toJSONString();
    }
    public static StartReplayByPipelineResponseData setDataFixed(String id) {
       return StartReplayByPipelineResponseData.newBuilder()
                .setFailReason("失败原因")
                .setViewUrlTitle("结果详情")
                .setCheckUrl("https://legoprt.corp.kuaishou.com/gateway/apollo/strategy/test/flow/CheckReplayStatusByPipeline?pipelineId=" + id)
                .setViewUrl("https://legoprt.corp.kuaishou.com/gateway/apollo/strategy/test/flow/CheckReplayStatusByPipeline?pipelineId=" + id)
                .setCancelUrl("")
                .setViewContent("需要展示的内容")
                .build();
    }


    public static String setCheckReplayStatusData(String pass, String result) {
        JSONObject jsonObject = new JSONObject();
        // 添加基础字段
        jsonObject.put("failReason", "失败原因");
        if (!pass.equals("null")) {
            jsonObject.put("pass", pass);
        }
        jsonObject.put("viewUrlTitle", "结果详情");
        jsonObject.put("viewUrl", "");
        jsonObject.put("viewContent", result);
        jsonObject.put("其他字段", "其他字段值");

        // 创建 xxxReportList 数组
        JSONArray xxxReportList = new JSONArray();
        JSONObject reportItem = new JSONObject();
        reportItem.put("title", "自定义报告标题");
        reportItem.put("url", "https://urls");
        xxxReportList.add(reportItem);

        // 添加 xxxReportList 到主 JSON 对象
        jsonObject.put("xxxReportList", xxxReportList);

        // 创建 xxxQrCodeReportList 数组
        JSONArray xxxQrCodeReportList = new JSONArray();
        JSONObject qrCodeItem = new JSONObject();
        qrCodeItem.put("title", "二维码标题");
        qrCodeItem.put("url", "https://urls");
        xxxQrCodeReportList.add(qrCodeItem);

        // 添加 xxxQrCodeReportList 到主 JSON 对象
        jsonObject.put("xxxQrCodeReportList", xxxQrCodeReportList);
        // 返回 JSON 对象的字符串表示
        return jsonObject.toJSONString();
    }
    public static String setCheckReplayStatusData(String pass, String result, String pipelineId) {
        JSONObject jsonObject = new JSONObject();
        // 添加基础字段
        jsonObject.put("failReason", "失败原因");
        if (!pass.equals("null")) {
            jsonObject.put("pass", pass);
        }
        jsonObject.put("viewUrlTitle", "结果详情");
        jsonObject.put("viewUrl", "");
        jsonObject.put("checkUrl", "https://legoprt.corp.kuaishou.com/gateway/apollo/strategy/test/flow/CheckReplayStatusByPipeline?pipelineId=" + pipelineId);
        jsonObject.put("cancelUrl", "https://");
        jsonObject.put("viewContent", result);
        jsonObject.put("其他字段", "其他字段值");

        // 创建 xxxReportList 数组
        JSONArray xxxReportList = new JSONArray();
        JSONObject reportItem = new JSONObject();
        reportItem.put("title", "自定义报告标题");
        reportItem.put("url", "https://urls");
        xxxReportList.add(reportItem);

        // 添加 xxxReportList 到主 JSON 对象
        jsonObject.put("xxx_reportList", xxxReportList);

        // 创建 xxxQrCodeReportList 数组
        JSONArray xxxQrCodeReportList = new JSONArray();
        JSONObject qrCodeItem = new JSONObject();
        qrCodeItem.put("title", "二维码标题");
        qrCodeItem.put("url", "https://urls");
        xxxQrCodeReportList.add(qrCodeItem);

        // 添加 xxxQrCodeReportList 到主 JSON 对象
        jsonObject.put("xxxQrCodeReportList", xxxQrCodeReportList);
        // 返回 JSON 对象的字符串表示
        return jsonObject.toJSONString();
    }

    public static CheckReplayStatusByPipelineResponseData setCheckReplayStatusDataFixed(String pass, String result, String pipelineId) {
        return CheckReplayStatusByPipelineResponseData.newBuilder()
                .setPass(pass)
                .setFailReason("fail reason")
                .setViewContent(result)
                .setViewUrlTitle("检查结果")
                .setViewUrl("https://legoprt.corp.kuaishou.com/gateway/apollo/strategy/test/flow/CheckReplayStatusByPipeline?pipelineId=" + pipelineId)
                .build();

    }
}