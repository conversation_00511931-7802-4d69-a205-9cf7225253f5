package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.service.recordHandler;

import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskLongConfigKey.realTimeLogExpireTime;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskLongConfigKey.realTimeLogRefreshTime;

import java.time.Duration;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.api.client.util.Sets;
import com.google.common.base.Splitter;
import com.google.common.cache.LoadingCache;
import com.kuaishou.framework.concurrent.AsyncReloadCacheLoader;
import com.kuaishou.infra.framework.common.util.KsCacheBuilder;
import com.kuaishou.kwaishop.qa.risk.center.common.Enum.FlowType;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper.StrategyFlowRecordMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyFlowRecordDo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-21
 */
@Service
@Slf4j
public class RealTimeLogHandler {

    @Autowired
    private StrategyFlowRecordMapper strategyFlowRecordMapper;

    private static final long MAX_SIZE = 3000;

    private final LoadingCache<String, Set<Long>> realTimeLogCache = KsCacheBuilder.newBuilder().maximumSize(MAX_SIZE)
            .refreshAfterWrite(() -> Duration.ofSeconds(realTimeLogRefreshTime.get()))
            .expireAfterAccess(() -> Duration.ofMinutes(realTimeLogExpireTime.get())).concurrencyLevel(20)
            .enablePerf("qa.risk.local.cache.authCache").build(new AsyncReloadCacheLoader<String, Set<Long>>() {
                @Override
                public Set<Long> load(String sceneKey) {
                    return getRecordData(sceneKey);
                }
            });

    private Set<Long> getRecordData(String sceneKey) {
        Set<Long> set = Sets.newHashSet();
        List<StrategyFlowRecordDo> strategyFlowRecordDoList =
                strategyFlowRecordMapper.getFlowRecordByFlowType(FlowType.REAL_TIME.getCode());
        for (StrategyFlowRecordDo strategyFlowRecordDo : strategyFlowRecordDoList) {
            if (StringUtils.isNotBlank(strategyFlowRecordDo.getSceneKeyList())) {
                List<String> sceneKeyList = Splitter.on(",").splitToList(strategyFlowRecordDo.getSceneKeyList());
                if (sceneKeyList.contains(sceneKey)) {
                    set.add(strategyFlowRecordDo.getId());
                }
            }
        }
        return set;
    }
}
