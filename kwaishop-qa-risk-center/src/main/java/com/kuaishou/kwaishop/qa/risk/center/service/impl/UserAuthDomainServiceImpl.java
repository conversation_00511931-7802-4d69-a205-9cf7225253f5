package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.impl.UserAuthBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.KrpcUserAuthDomainServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UpdateUserAuthRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UpdateUserAuthResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-24
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class UserAuthDomainServiceImpl extends KrpcUserAuthDomainServiceGrpc.UserAuthDomainServiceImplBaseV2 {

    @Autowired
    private UserAuthBizService userAuthBizService;

    @Override
    public UpdateUserAuthResponse updateUserAuth(UpdateUserAuthRequest request) {
        log.info("[UserAuthDomainServiceImpl] updateUserAuthRequest request = {}", protoToJsonString(request));
        try {
            userAuthBizService.updateAuthInfo(request);
            return UpdateUserAuthResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setErrorMsg("更新成功")
                    .build();
        } catch (BizException e) {
            log.error("[UserAuthDomainServiceImpl] updateUserAuthRequest bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateUserAuthResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[UserAuthDomainServiceImpl] updateUserAuthRequest error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateUserAuthResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }
}
