package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-30
 */
@Target({METHOD, FIELD})
@Retention(RUNTIME)
public @interface Column {
    String name() default "";
}
