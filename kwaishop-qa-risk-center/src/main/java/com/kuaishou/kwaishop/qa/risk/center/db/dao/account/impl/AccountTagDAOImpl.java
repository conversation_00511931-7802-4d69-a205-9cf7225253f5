package com.kuaishou.kwaishop.qa.risk.center.db.dao.account.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.AccountTagDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagTestAccountRelDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.account.TagTestAccountRelMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.account.TestAccountMapper;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.TagBaseInfoDTO;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/15 15:48
 * @注释
 */
@Repository
public class AccountTagDAOImpl implements AccountTagDAO {

    @Autowired
    private TestAccountMapper testAccountMapper;

    @Autowired
    private TagTestAccountRelMapper tagTestAccountRelMapper;

    @Override
    public TagBaseInfoDTO getTagByUid(Long uid) {
        return null;
    }

    protected BaseMapper<TagTestAccountRelDO> getMapper() {
        return tagTestAccountRelMapper;
    }
}
