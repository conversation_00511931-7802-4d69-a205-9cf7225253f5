package com.kuaishou.kwaishop.qa.risk.center.domain.openapi.service;

import com.kuaishou.kwaishop.qa.risk.center.domain.openapi.model.bo.RequestSendTemplateMessageBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.openapi.model.bo.ResponseSendTemplateMessageBO;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

public interface OpenApi {


    /**
     * 发送变量模板交互式消息
     */
    @Headers({"Content-Type:application/json"})
    @POST("/openapi/v2/message/template/send")
    Call<ResponseSendTemplateMessageBO> sendTemplateMessageV2(@Body RequestSendTemplateMessageBO body);


}
