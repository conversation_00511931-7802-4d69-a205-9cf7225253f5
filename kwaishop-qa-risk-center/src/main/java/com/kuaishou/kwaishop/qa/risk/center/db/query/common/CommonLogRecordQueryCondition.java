package com.kuaishou.kwaishop.qa.risk.center.db.query.common;

import com.kuaishou.kwaishop.qa.risk.center.db.query.BaseQueryCondition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-24
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CommonLogRecordQueryCondition extends BaseQueryCondition {

    /**
     * log类型
     */
    private Integer logType;

    /**
     * 日期
     */
    private String dt;

    /**
     * log名称
     */
    private String name;

    private String dtStart;

    private String dtEnd;
}
