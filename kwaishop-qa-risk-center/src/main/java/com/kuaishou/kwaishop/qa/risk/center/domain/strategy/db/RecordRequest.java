package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db;


import java.util.List;

import com.kuaishou.intown.json.JSONObject;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class RecordRequest {

    private String taskId;

    private JSONObject sqlTemplate;

    private String user;

    private String pDate; //录制分区

    private List<String> scene; // 场景

    private String taskName;

    private String taskDescription;

    private String expectLaneId;
}