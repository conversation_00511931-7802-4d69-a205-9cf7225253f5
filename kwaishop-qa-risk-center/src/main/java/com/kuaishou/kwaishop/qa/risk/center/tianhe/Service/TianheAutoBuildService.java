package com.kuaishou.kwaishop.qa.risk.center.tianhe.Service;

import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskJsonMapConfigKey.tianheAiConfig;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.commons.text.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.krpc.config.annotation.KrpcMethod;
import com.kuaishou.krpc.config.annotation.KrpcReference;
import com.kuaishou.kwaishop.lang.bridge.protobuf.ChatCompletionRequest;
import com.kuaishou.kwaishop.lang.bridge.protobuf.ChatCompletionResponse;
import com.kuaishou.kwaishop.lang.bridge.protobuf.KrpcKwaishopLangBridgeModelServiceGrpc;
import com.kuaishou.kwaishop.lang.bridge.protobuf.Message;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.Action;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ActionSetExtendData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AddSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AddSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AutoExcuteSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AutoExcuteSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AutoExtractSampleFromImageRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AutoExtractSampleFromImageResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AutoExtractSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AutoExtractSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.BatchExecuteSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.BatchExecuteSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.JudgeImgRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.JudgeImgResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateValidActionSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateValidActionSetResponse;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Sample.ExtractAction;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Sample.TianheExecuteSampleDO;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Sample.TianheSampleCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Enums.TianheActionType;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Enums.TianheSampleExecuteStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.Sample.TianheExecuteSampleMapper;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.Sample.TianheSampleCaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Utils.TianheSampleCaseUtils;
import com.kuaishou.kwaishop.tianhe.galax.center.client.protobuf.GetPageRequest;
import com.kuaishou.kwaishop.tianhe.galax.center.client.protobuf.GetPageResponse;
import com.kuaishou.kwaishop.tianhe.galax.center.client.protobuf.KrpcKwaishopTianheGalaxPageServiceGrpc;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class TianheAutoBuildService {

    private final Gson gson;

    private final TianheSampleCaseMapper tianheSampleCaseMapper;

    private final TianheSampleCaseUtils tianheSampleCaseUtils;

    private final TianheExecuteSampleMapper tianheExecuteSampleMapper;

    @Autowired
    private TianheSampleCaseService tianheSampleCaseService;

//    @KrpcReference(serviceName = "grpc_langBridgeService")
//    private KrpcKwaishopLangBridgeModelServiceGrpc.IKwaishopLangBridgeModelService kwaishopLangBridgeModelService;

    @KrpcReference(serviceName = "kwaishop-lang-bridge", methodConfigs = {@KrpcMethod(name = "chatCompletion", timeout = 100000)})
    private KrpcKwaishopLangBridgeModelServiceGrpc.IKwaishopLangBridgeModelService kwaishopLangBridgeModelService;
    @KrpcReference(serviceName = "kwaishop-tianhe-galax-center")
    private KrpcKwaishopTianheGalaxPageServiceGrpc.IKwaishopTianheGalaxPageService tianheGalaxPageService;


    private static final List<String> TARGET_TYPES = Collections.unmodifiableList(Arrays.asList(
            "@es/tianhe-basic-materials::Button",
            "@es/tianhe-basic-materials::RangePicker"
    ));

    @Autowired
    public TianheAutoBuildService(TianheSampleCaseMapper tianheSampleCaseMapper,
            TianheSampleCaseUtils tianheSampleCaseUtils,
            TianheExecuteSampleMapper tianheExecuteSampleMapper) {
        gson = new Gson();
        this.tianheExecuteSampleMapper = tianheExecuteSampleMapper;
        this.tianheSampleCaseMapper = tianheSampleCaseMapper;
        this.tianheSampleCaseUtils = tianheSampleCaseUtils;
    }

    public AutoExtractSampleResponse updateAISample(AutoExcuteSampleRequest request) {
        return handleUpdateAISample(request);
    }

    private AutoExtractSampleResponse handleUpdateAISample(AutoExcuteSampleRequest request) {
        log.info("handleUpdateAISample request {}", ObjectMapperUtils.toJSON(request));
        AutoExtractSampleResponse.Builder autoExtractSampleResponse =
                AutoExtractSampleResponse.newBuilder();
        try {
            TianheSampleCaseDO sample = tianheSampleCaseMapper.selectById(request.getSampleId());
            String ext = tianheSampleCaseUtils.getExt(sample.getExt());
            JsonElement jsonElement = JsonParser.parseString(ext);
            JsonObject jsonObject = jsonElement.getAsJsonObject();
            String changeOrderId = jsonObject.has("changeOrderId") ? jsonObject.get("changeOrderId").getAsString() : null;
            String username = sample.getCreator();
            String pageCode = sample.getPageCode();
            String appKey = sample.getAppKey();
            String prtUrl = sample.getPrtUrl();
            String testLaneId = jsonObject.has("testLaneId") ? jsonObject.get("testLaneId").getAsString() : null;

            // 解析当前用例页面协议，提取actionSet
            List<ActionSetExtendData> totalActionSetExtendDataList =
                    new ArrayList<ActionSetExtendData>();
            List<Action> actionList = new ArrayList<>();
            // 查询页面协议
            GetPageRequest getPageRequest = GetPageRequest.newBuilder()
                    .setUserName(username)
                    .setChangeOrderId(Long.valueOf(changeOrderId))
                    .setPageCode(pageCode)
                    .setAppKey(appKey)
                    .build();
            GetPageResponse page = tianheGalaxPageService.getPage(getPageRequest);
            // 对于一份页面协议，需要对所有场景的组件进行自动生成, actionSetExtendDataList 存储全部的用例数据
            for (TianheActionType tianheActionType : TianheActionType.values()) {
                if (tianheActionType.getValue() == TianheActionType.QUERY.getValue()) {
                    continue;
                }
                String protocol = getSimpleProtocol(page.getPages().getProtocol(),
                        tianheActionType.getValue());
                // 构造单动作AI生成入参
                AutoExtractSampleRequest singleAutoExtractSampleRequest =
                        AutoExtractSampleRequest.newBuilder()
                                .setUserName(username)
                                .setAppKey(appKey)
                                .setComponentType(tianheActionType.getValue())
                                .setProtocol(protocol)
                                .build();
                AutoExtractSampleResponse singleAutoExtractSampleRes =
                        autoExtractSample(singleAutoExtractSampleRequest);

                List<ActionSetExtendData> singleActionSetExtendDataList =
                        singleAutoExtractSampleRes.getActionList().stream().map(action -> ActionSetExtendData.newBuilder()
                                .setActionType(action.getActionType())
                                .setXpath(action.getXpath())
                                .setPytestCode(action.getPytestCode())
                                .build()).collect(Collectors.toList());

                totalActionSetExtendDataList.addAll(singleActionSetExtendDataList);
            }
            if (totalActionSetExtendDataList.size() <= 0) {
                // 如果没有生成任何用例，插入一条默认的页面查询用例
                ActionSetExtendData actionSetExtendData = ActionSetExtendData.newBuilder()
                        .setActionType(TianheActionType.QUERY.getValue())
                        .setXpath("")
                        .setPytestCode("")
                        .build();
                totalActionSetExtendDataList.add(actionSetExtendData);
            }
            // 首次 updateSample， 将actionSet存储
            log.info("handleUpdateAISample start addSample");
            UpdateValidActionSetRequest updateValidActionSetRequest =
                    UpdateValidActionSetRequest.newBuilder()
                            .addAllActionSet(totalActionSetExtendDataList)
                            .setId(Integer.valueOf(request.getSampleId())).build();
            log.info("handleUpdateAISample addSample done");
            UpdateValidActionSetResponse updateValidActionSetResponse =
                    tianheSampleCaseService.updateValidActionSet(updateValidActionSetRequest);

            // actionSet check， 通过ui自动化回调接口 二次update
            tianheSampleCaseUtils.selfCheck(Long.valueOf(request.getSampleId()), prtUrl, testLaneId);
            autoExtractSampleResponse.setResult(1)
                    .setSuccess(true)
                    .build();
        } catch (Exception e) {
            log.error("handleUpdateAISample error", e);
            autoExtractSampleResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("用例更新失败，请稍后重试")
                    .build();
        }
        return autoExtractSampleResponse.build();
    }

    public AutoExtractSampleResponse autoExtractSample(AutoExtractSampleRequest request) {
        return handleAutoExtractSampleSetResponse(request);
    }
    private AutoExtractSampleResponse handleAutoExtractSampleSetResponse(AutoExtractSampleRequest request) {
        log.info("handleAutoBuildSampleSetResponse request {}",
                ObjectMapperUtils.toJSON(request));
        AutoExtractSampleResponse.Builder autoBuildSampleSetResponse =
                AutoExtractSampleResponse.newBuilder();
        try {
            // 查询AI大模型获取xpath
            Map<String, Object> aiComponentConfig = tianheAiConfig.getMap(String.class,
                    Object.class);
            String bizKey = (String) aiComponentConfig.get("bizKey");
            String model = (String) aiComponentConfig.get("model");
            String operator = (String) aiComponentConfig.get("operator");
            String patternWords = (String) aiComponentConfig.get("patternWords");
//            String content = simpleProtocal + "\n" + patternWords;
            String content = request.getProtocol() + "\n" + patternWords;
            ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.newBuilder()
                    .setBizKey(bizKey)
                    .setModel(model)
                    .setOperator(operator)
                    .addMessages(Message.newBuilder().setContent(content)
                            .setRole("user")
                            .build())
                    .build();
            log.info("handleAutoExtractSampleSetResponse modifiedRequest {}",
                    ObjectMapperUtils.toJSON(chatCompletionRequest));
            ChatCompletionResponse chatCompletionResponse =
                    kwaishopLangBridgeModelService.chatCompletion(chatCompletionRequest);
            log.info("handleManualPassResponse chatCompletionResponse {}",
                    ObjectMapperUtils.toJSON(chatCompletionResponse));
            // 从chatCompletionResponse 的result中 解析动作json
            String jsonStr = chatCompletionResponse.getResult();
            log.info("handleManualPassResponse jsonStr {}",
                    ObjectMapperUtils.toJSON(jsonStr));

            if (!StringUtils.isEmpty(jsonStr)) {
                ObjectMapper mapper = new ObjectMapper();
                JsonNode resultJsonNode = mapper.readTree(jsonStr);
                String retContent = String.valueOf(resultJsonNode.get("choices").get(0).get(
                        "message").get("content"));
                log.info("handleManualPassResponse retContent {}",
                        ObjectMapperUtils.toJSON(retContent));
                // 从 content 中提取 ```json ... ``` 块的内容
                String extractedJson = extractJsonFromContent(retContent);
                log.info("handleAutoExtractSampleSetResponse extractedJson {}",
                        ObjectMapperUtils.toJSON(extractedJson));
                String tJson = StringEscapeUtils.unescapeJava(extractedJson).trim();

                log.info("handleAutoExtractSampleSetResponse after transform extractedJson {}",
                        ObjectMapperUtils.toJSON(tJson));

                ObjectMapper extractedJsonMapper = new ObjectMapper();
                // 用proto的反序列化会报错，重新定义了一个
                ExtractAction[] extractActions = extractedJsonMapper.readValue(tJson,
                        ExtractAction[].class);
                List<ExtractAction> extractActionsList = Arrays.asList(extractActions);
                List<Action> actionList = new ArrayList<>();

                for (ExtractAction extractAction : extractActionsList) {
                    // click场景 不是Button 就是 RangePicker
                    if (extractAction.getActionType().equals("Button") || extractAction.getActionType().equals("RangePicker")) {
                        actionList.add(Action.newBuilder()
                                .setActionType(TianheActionType.CLICK.getValue())
                                .setXpath(extractAction.getXpath())
                                .setPytestCode(extractAction.getPytestCode())
                                .build());
                    }
                    // 其他场景
                }
                autoBuildSampleSetResponse.setSuccess(true)
                        .setResult(1)
                        .addAllAction(actionList);
            }
        } catch (Exception e) {
            log.error("handleAutoExtractSampleSetResponse error ", e);
            autoBuildSampleSetResponse
                    .setResult(2)
                    .setSuccess(false);
        }
        return autoBuildSampleSetResponse.build();
    }

    public AutoExtractSampleFromImageResponse autoExtractSampleFromImage(AutoExtractSampleFromImageRequest request) {
        return handleAutoExtractSampleFromImageResponse(request);
    }

    private AutoExtractSampleFromImageResponse handleAutoExtractSampleFromImageResponse(AutoExtractSampleFromImageRequest request) {
        log.info("handleAutoExtractSampleFromImageResponse request {}",
                ObjectMapperUtils.toJSON(request));
        AutoExtractSampleFromImageResponse.Builder autoExtractSampleFromImageResponse =
                AutoExtractSampleFromImageResponse.newBuilder();
        try {
            // 查询AI大模型获取xpath
            Map<String, Object> aiComponentConfig = tianheAiConfig.getMap(String.class,
                    Object.class);
            String bizKey = (String) aiComponentConfig.get("bizKey");
            String model = (String) aiComponentConfig.get("model");
            String operator = (String) aiComponentConfig.get("operator");
            String patternWords = (String) aiComponentConfig.get("patternWordsImg");
            String content = patternWords;
            List<String> imageUrls = new ArrayList<>();
            imageUrls.add(request.getImgOri());
            imageUrls.add(request.getImgTar());
            ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.newBuilder()
                    .setBizKey(bizKey)
                    .setModel(model)
                    .setOperator(operator)
                    .addMessages(Message.newBuilder().setContent(content)
                            .setRole("user")
                            .addAllImageUrls(imageUrls)
                            .build())
                    .build();

            log.info("handleAutoExtractSampleSetResponse modifiedRequest {}",
                    ObjectMapperUtils.toJSON(chatCompletionRequest));
            ChatCompletionResponse chatCompletionResponse =
                    kwaishopLangBridgeModelService.chatCompletion(chatCompletionRequest);
            log.info("handleManualPassResponse chatCompletionResponse {}",
                    ObjectMapperUtils.toJSON(chatCompletionResponse));

        } catch (Exception e) {
            log.error("handleAutoExtractSampleSetResponse error ", e);
            autoExtractSampleFromImageResponse
                    .setResult(2)
                    .setSuccess(false);
        }
        return autoExtractSampleFromImageResponse.build();
    }

    public AutoExcuteSampleResponse autoExcuteSampleById(AutoExcuteSampleRequest request) {
        return handleAutoExcuteSampleByIdResponse(request);
    }

    private AutoExcuteSampleResponse handleAutoExcuteSampleByIdResponse(AutoExcuteSampleRequest request) {
        return AutoExcuteSampleResponse.newBuilder().build();
    }

    // todo 这里的Protocol获取需要兼容多场景
    private String getSimpleProtocol(String protocol, String componentType) throws IOException {
        // todo 场景类型枚举兼容
        // Click场景
        String simpleProtocol = "";
        if (componentType.equals(TianheActionType.CLICK.getValue())) {
            simpleProtocol = getClickSimpleProtocol(protocol);
        }
        return simpleProtocol;
        // 表单提交场景
    }

    private String getClickSimpleProtocol(String protocol) throws IOException {
        // 创建ObjectMapper实例
        ObjectMapper objectMapper = new ObjectMapper();
        // 解析JSON字符串为JsonNode
        JsonNode rootNode = objectMapper.readTree(protocol);
        // 获取"view"节点
        JsonNode viewNode = rootNode.get("view");
        // 创建结果JSON结构
        ObjectNode resultNode = objectMapper.createObjectNode();
        ArrayNode valueArray = resultNode.putArray("value");
        // 遍历"view"节点的所有字段
        Iterator<Map.Entry<String, JsonNode>> fields = viewNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            JsonNode fieldValue = field.getValue();
            checkForEs(fieldValue, field.getKey(), objectMapper, valueArray);
        }
        return String.valueOf(resultNode);
    }
    private static void checkForEs(JsonNode node, String fieldName, ObjectMapper objectMapper, ArrayNode valueArray) {
        if (node.isObject()) {
            Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> field = fields.next();
                String fieldKey = field.getKey();
                JsonNode fieldValue = field.getValue();

                // 检查是否是目标类型
                if (fieldKey.equals("type") && TARGET_TYPES.contains(fieldValue.asText())) {
                    String type = fieldValue.asText();
                    JsonNode propsNode = node.get("props");

                    // 创建结果对象
                    ObjectNode fieldResult = objectMapper.createObjectNode();
                    fieldResult.put("type", type);

                    // 将props转换为ObjectNode
                    if (propsNode != null && propsNode.isObject()) {
                        fieldResult.set("props", propsNode);
                    } else {
                        fieldResult.set("props", objectMapper.createObjectNode());
                    }

                    // 将结果添加到数组中
                    valueArray.add(fieldResult);
                } else {
                    checkForEs(fieldValue, fieldName, objectMapper, valueArray);
                }
            }
        } else if (node.isArray()) {
            for (JsonNode arrayElement : node) {
                checkForEs(arrayElement, fieldName, objectMapper, valueArray);
            }
        }
    }

    private static String extractJsonFromContent(String content) {
        String startTag = "```json";
        String endTag = "```";

        int startIndex = content.indexOf(startTag);
        if (startIndex == -1)  {
            return null;
        }

        startIndex += startTag.length();
        int endIndex = content.indexOf(endTag, startIndex);
        if (endIndex == -1) {
            return null;
        }

        return content.substring(startIndex, endIndex).trim();
    }

    public AutoExtractSampleResponse testSample(AutoExtractSampleRequest request) {
        return handleTestSample(request);
    }

    private AutoExtractSampleResponse handleTestSample(AutoExtractSampleRequest request) {
        AutoExtractSampleResponse.Builder  autoExtractSampleResponse =
                AutoExtractSampleResponse.newBuilder();
        try {
            List<ActionSetExtendData> totalActionSetExtendDataList =
                    new ArrayList<ActionSetExtendData>();
            List<Action> actionList = new ArrayList<>();
            // 默认动作页面查询
            ActionSetExtendData actionSetExtendData = ActionSetExtendData.newBuilder()
                    .setActionType(TianheActionType.QUERY.getValue())
                    .setXpath("")
                    .setPytestCode("")
                    .build();
            totalActionSetExtendDataList.add(actionSetExtendData);
            // 查询页面协议
            GetPageRequest getPageRequest = GetPageRequest.newBuilder()
                    .setUserName(request.getUserName())
                    .setChangeOrderId(Long.valueOf(request.getChangeOrderId()))
                    .setPageCode(request.getPageCode())
                    .setAppKey(request.getAppKey())
                    .build();
            GetPageResponse page = tianheGalaxPageService.getPage(getPageRequest);
            // 对于一份页面协议，需要对所有场景的组件进行自动生成, actionSetExtendDataList 存储全部的用例数据
            for (TianheActionType tianheActionType : TianheActionType.values()) {
//                tianheActionType.name()
                // protocol预处理下， 不然传递过去太大了，入参太大不太好
                // todo 在外层查询一次protocol即可
                String protocol = getSimpleProtocol(page.getPages().getProtocol(),
                        tianheActionType.getValue());
                // 构造单动作AI生成入参
                AutoExtractSampleRequest singleAutoExtractSampleRequest =
                        AutoExtractSampleRequest.newBuilder()
                        .setUserName(request.getUserName())
                        .setChangeOrderId(request.getChangeOrderId())
                        .setAppKey(request.getAppKey())
                        .setOnlineUrl(request.getOnlineUrl())
                        .setPageCode(request.getPageCode())
                        .setComponentType(tianheActionType.getValue())
                        .setProtocol(protocol)
                        .build();
                AutoExtractSampleResponse singleAutoExtractSampleRes =
                        autoExtractSample(singleAutoExtractSampleRequest);

                List<ActionSetExtendData> singleActionSetExtendDataList =
                        singleAutoExtractSampleRes.getActionList().stream().map(action -> ActionSetExtendData.newBuilder()
                        .setActionType(action.getActionType())
                        .setXpath(action.getXpath())
                        .setPytestCode(action.getPytestCode())
                        .build()).collect(Collectors.toList());

                totalActionSetExtendDataList.addAll(singleActionSetExtendDataList);
            }
            // addSample
            String uuid = UUID.randomUUID().toString();
            String onlineUrl = "https://cps.kwaixiaodian.com/pc/leader/tianhe/leader-apply/experience"
                    + "-Indicators";
            String prtUrl = "https://cps-kwaixiaodian.test.gifshow"
                    + ".com/pc/leader/tianhe/leader-apply/experience-Indicators";

            AddSampleRequest addSampleRequest = AddSampleRequest.newBuilder()
                    .setName("test" + uuid).setPlatform("天河")
                    .setOnlineUrl(onlineUrl)
                    .setPrtUrl(prtUrl)
                    .setCreator("zhanghongbin")
                    .setAppKey(request.getAppKey())
                    .addAllActionSet(totalActionSetExtendDataList)
                    //历史逻辑，pageCode 塞的是onlineUrl
                    .setPageCode(onlineUrl)
                    .setDomainCode("testDomainCode").build();
            AddSampleResponse sampleResponse =  tianheSampleCaseService.addSampleResponse(addSampleRequest);
            log.info("AutoExtractSampleResponse sampleResponse {}",
                    ObjectMapperUtils.toJSON(sampleResponse));
            // querySample && execute
            String telent = "th";
            long mainDeployVersion = 1;
            long mainDeployId = 1;
            List<String> prodUrlList = new ArrayList<>();
            prodUrlList.add(onlineUrl);
            // 复用下mq消息链路的执行触发
            BatchExecuteSampleRequest mqExecuteSampleRequest =
                    BatchExecuteSampleRequest.newBuilder()
                            .setChangeOrderId(Long.parseLong(request.getChangeOrderId()))
                            .setTelnet(telent)
                            .setLastDeployVersion(mainDeployVersion)
                            .setLastChangeDeployId(mainDeployId)
                            .addAllProdUrl(prodUrlList)
                            .build();
            // 执行这里替换成新模版
            BatchExecuteSampleResponse mqExecuteSampleResponse =
                    tianheSampleCaseService.mqExecuteSampleResponse(mqExecuteSampleRequest);

            // 执行结果查询，获取两张图片url，然后将url和content文本传递给agent进行处理
        } catch (Exception e) {
            log.error("autoExtractSampleResponse {}", e);
        }
        return autoExtractSampleResponse.build();
    }

    /**
     * 返回Agent结果str，由上层解析，这样可以将解析结果也返回,方便在前端进行展示
     * @param testUrl
     * @param targetUrl
     * @param xpath
     * @return
     */
    private String judgeImgResultByAgent(String testUrl, String targetUrl, String xpath) {
        String checkContent = "";
        try {
            // 查询AI大模型获取xpath
            Map<String, Object> aiComponentConfig = tianheAiConfig.getMap(String.class,
                    Object.class);
            String bizKey = (String) aiComponentConfig.get("bizKey");
            String model = (String) aiComponentConfig.get("model");
            String operator = (String) aiComponentConfig.get("operator");
            String patternWords = (String) aiComponentConfig.get("patternWordsImg");
            String content = patternWords.replace("xpath", xpath);
            List<String> imageUrls = new ArrayList<>();
            imageUrls.add(testUrl);
            imageUrls.add(targetUrl);
            ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.newBuilder()
                    .setBizKey(bizKey)
                    .setModel(model)
                    .setOperator(operator)
                    .addMessages(Message.newBuilder().setContent(content)
                            .setRole("user")
                            .addAllImageUrls(imageUrls)
                            .build())
                    .build();

            log.info("handleAutoExtractSampleSetResponse modifiedRequest {}",
                    ObjectMapperUtils.toJSON(chatCompletionRequest));
            ChatCompletionResponse chatCompletionResponse =
                    kwaishopLangBridgeModelService.chatCompletion(chatCompletionRequest);
            String jsonStr = chatCompletionResponse.getResult();
            if (!StringUtils.isEmpty(jsonStr)) {
                ObjectMapper mapper = new ObjectMapper();
                JsonNode resultJsonNode = mapper.readTree(jsonStr);
                String retContent = String.valueOf(resultJsonNode.get("choices").get(0).get(
                        "message").get("content"));

                checkContent =  StringEscapeUtils.unescapeJava(retContent);
                log.info("handleAutoExtractSampleSetResponse retContent {}", retContent);
            }
        } catch (Exception e) {
            log.error("handleAutoExtractSampleSetResponse error ", e);
        }
        return checkContent;
    }
    public JudgeImgResponse judgeImgResult(JudgeImgRequest request) {
        return handleJudgeImgResult(request);
    };

    /**
     * agent根据两张图片和文本promot判断正确性
     * @param request
     * @return
     */
    private JudgeImgResponse handleJudgeImgResult(JudgeImgRequest request) {
        log.info("handleJudgeImgResult JudgeImgRequest  {}",
                ProtobufUtil.protoToJsonString(request));
        JudgeImgResponse.Builder judgeImgResponse =
                JudgeImgResponse.newBuilder();
        try {
            // 根据 executeId  actionType path smapleId 查找对应记录，然后获取存储的cdn地址
            QueryWrapper<TianheExecuteSampleDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("action_type", request.getActionType());
            queryWrapper.in("execute_id", request.getExecuteId());
            queryWrapper.in("xpath", request.getXpath());
            queryWrapper.in("sample_id", request.getSampleId());
            queryWrapper.in("deleted", 0);

            List<TianheExecuteSampleDO> executeList =
                    tianheExecuteSampleMapper.selectList(queryWrapper);

            // 预期只有一条数据
            if (executeList.size() > 0) {
                JsonElement jsonElement = JsonParser.parseString(executeList.get(0).getExt());
                JsonObject jsonObject = jsonElement.getAsJsonObject();
                Integer executeStatus = executeList.get(0).getExecuteStatus();
                if (executeStatus == TianheSampleExecuteStatusEnum.AUTO_PASSED.ordinal()
                        || executeStatus == TianheSampleExecuteStatusEnum.MANUAL_PASSED.ordinal()) {
                    // 获取图像cdn地址
                    String prtPhotonameUrl = jsonObject.has("prt_photoname") ? jsonObject.get("prt_photoname").getAsString() : null;
                    String onlinePhotonameUrl = jsonObject.has("online_photoname") ? jsonObject.get("online_photoname").getAsString() : null;
                    String platformSource = jsonObject.has("platform_source") ? jsonObject.get(
                            "platform_source").getAsString() : null;
                    String appKey = jsonObject.has("app_key")
                            ? jsonObject.get("app_key").getAsString() : null;
                    String pageCode = jsonObject.has("page_code")
                            ? jsonObject.get("page_code").getAsString() : null;
                    String testLaneId = jsonObject.has("testLaneId") ? jsonObject.get("testLaneId").getAsString() : null;
                    String diffRate = jsonObject.has("diff_rate")
                            ? jsonObject.get("diff_rate").getAsString() : null;


                    TianheExecuteSampleDO tianheExecuteSampleDO = new TianheExecuteSampleDO();
                    UpdateWrapper<TianheExecuteSampleDO> updateWrapper = new UpdateWrapper<>();

                    Optional.of(request.getExecuteId()).filter(value -> !value.isEmpty())
                            .ifPresent(value -> updateWrapper.eq("execute_id", value));
                    Optional.of(request.getSampleId()).filter(value -> !(value.equals(0)))
                            .ifPresent(value -> updateWrapper.eq("sample_id", value));
                    Optional.of(request.getActionType()).filter(value -> !value.isEmpty())
                            .ifPresent(value -> updateWrapper.eq("action_type", value));
                    Optional.of(request.getXpath()).filter(value -> !value.isEmpty())
                            .ifPresent(value -> updateWrapper.eq("xpath", value));

                    String checkContent = judgeImgResultByAgent(prtPhotonameUrl, onlinePhotonameUrl,
                            request.getXpath());
                    String jsonContent = extractJsonFromContent(checkContent);
                    log.info("handleJudgeImgResult jsonContent {}", jsonContent);
                    ObjectMapper retMapper = new ObjectMapper();
                    JsonNode root = retMapper.readTree(jsonContent);
                    boolean checkResult = root.path("checkResult").asBoolean(false);

                    Map<String, Object> map = new HashMap<>();
                    map.put("prt_photoname", prtPhotonameUrl);
                    map.put("online_photoname", onlinePhotonameUrl);
                    map.put("platform_source", platformSource);
                    map.put("app_key", appKey);
                    map.put("page_code", pageCode);
                    map.put("testLaneId", testLaneId);
                    map.put("diff_rate", diffRate);
                    map.put("aiExecuteContent", checkContent);
                    tianheExecuteSampleDO.setExt(ObjectMapperUtils.toJSON(map));

                    if (checkResult) {
                        tianheExecuteSampleDO.setExecuteStatus(TianheSampleExecuteStatusEnum.AI_PASSED.ordinal());
                    } else {
                        tianheExecuteSampleDO.setExecuteStatus(TianheSampleExecuteStatusEnum.AI_NOT_PASSED.ordinal());
                    }
                    tianheExecuteSampleMapper.update(tianheExecuteSampleDO, updateWrapper);
                }
            }
        } catch (Exception e) {
            judgeImgResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("用例更新失败，请稍后重试")
                    .build();
        }
        return judgeImgResponse.build();
    }
}
