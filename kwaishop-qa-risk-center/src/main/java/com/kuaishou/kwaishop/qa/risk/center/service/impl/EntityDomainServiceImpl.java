package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.biz.EntityBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.CreateEntityRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.CreateEntityResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.DeleteEntityRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.DeleteEntityResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.EntityPBDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.EntityPBPageDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.KrpcEntityDomainServiceGrpc.EntityDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryEntityListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryEntityListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryEntityPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryEntityPageListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.UpdateEntityRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.UpdateEntityResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-25
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class EntityDomainServiceImpl extends EntityDomainServiceImplBaseV2 {

    @Autowired
    private EntityBizService entityBizService;

    @Override
    public CreateEntityResponse createEntity(CreateEntityRequest request) {
        log.info("[EntityDomainServiceImpl] createEntity request: {}", protoToJsonString(request));
        try {
            entityBizService.createEntity(request);
            return CreateEntityResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[EntityDomainServiceImpl] createEntity bizError, req: {}, exception: ", protoToJsonString(request), e);
            return CreateEntityResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[EntityDomainServiceImpl] createEntity error, req: {}, exception: ", protoToJsonString(request), e);
            return CreateEntityResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateEntityResponse updateEntity(UpdateEntityRequest request) {
        log.info("[EntityDomainServiceImpl] updateEntity request: {}", protoToJsonString(request));
        try {
            entityBizService.updateEntity(request);
            return UpdateEntityResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[EntityDomainServiceImpl] updateEntity bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateEntityResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[EntityDomainServiceImpl] updateEntity error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateEntityResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public DeleteEntityResponse deleteEntity(DeleteEntityRequest request) {
        log.info("[EntityDomainServiceImpl] deleteEntity request: {}", protoToJsonString(request));
        try {
            entityBizService.deleteEntity(request);
            return DeleteEntityResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[EntityDomainServiceImpl] deleteEntity bizError, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteEntityResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[EntityDomainServiceImpl] deleteEntity error, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteEntityResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryEntityListResponse queryEntityList(QueryEntityListRequest request) {
        log.info("[EntityDomainServiceImpl] queryEntityList request: {}", protoToJsonString(request));
        try {
            List<EntityPBDTO> dataList = entityBizService.queryEntityList(request);
            return QueryEntityListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .addAllData(dataList)
                    .build();
        } catch (BizException e) {
            log.error("[EntityDomainServiceImpl] queryEntityList bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryEntityListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[EntityDomainServiceImpl] queryEntityList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryEntityListResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryEntityPageListResponse queryEntityPageList(QueryEntityPageListRequest request) {
        log.info("[EntityDomainServiceImpl] queryEntityPageList request: {}", protoToJsonString(request));
        try {
            EntityPBPageDTO data = entityBizService.queryEntityPageList(request);
            return QueryEntityPageListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(data)
                    .build();
        } catch (BizException e) {
            log.error("[EntityDomainServiceImpl] queryEntityPageList bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryEntityPageListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[EntityDomainServiceImpl] queryEntityPageList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryEntityPageListResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }
}
