package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.phantomthief.tuple.TwoTuple;
import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.KspayRiskDetailBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.bo.BatchImportRiskBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.BatchImportRiskRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.BatchImportRiskResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KrpcBatchImportRiskServiceGrpc.BatchImportRiskServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayCreateRiskDetailRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayRiskDetailParam;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.TransferExcelUtil;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class BatchImportRiskServiceImpl extends BatchImportRiskServiceImplBaseV2 {

    @Autowired
    private KspayRiskDetailBizService kspayRiskDetailBizService;

    @Override
    public BatchImportRiskResponse batchImportRiskApply(BatchImportRiskRequest request) {

        log.info("[BatchImportRiskResponse] batchImportRiskApply request: {}", ProtobufUtil.protoToJsonString(request));

        //进行base64解密
        byte[] excelBytes = Base64.getDecoder().decode(request.getFileBase64());
        TwoTuple<String, List<BatchImportRiskBO>> transferData = TransferExcelUtil.excelToBean(excelBytes);

        List<BatchImportRiskBO> data = new ArrayList<BatchImportRiskBO>();
        //处理过程中无错误码
        if (transferData.getFirst() == null) {
            data = transferData.getSecond();
        }

        log.info("开始处理数据: {}", data);

        String businessDomain = request.getBusinessDomain();

        Map<String, List<BatchImportRiskBO>> dataMap = data
                .stream().collect(Collectors.groupingBy(BatchImportRiskBO -> businessDomain + "&"
                + BatchImportRiskBO.getYwllDesc() + "&" + BatchImportRiskBO.getZsfxx()));

        log.info("合并后的数据: {}", dataMap);

        dataMap.forEach((key, list) -> {
            log.info("组装请求数据key: {},value:{}", key, list);
            List<KspayRiskDetailParam> subRiskList = new ArrayList<KspayRiskDetailParam>();
            list.forEach(BatchImportRiskBO -> {
                KspayRiskDetailParam param = KspayRiskDetailParam.newBuilder()
                        .setGitId(BatchImportRiskBO.getGitId())
                        .setDbColumns(BatchImportRiskBO.getDbColumns())
                        .setSubRiskName("韩艳萍的风险")
                        .setTotalRiskAmount("10")
                        .setRiskType("子风险类型")
                        .setLevel(1)
                        .setSubRiskStatus(1)
                        .build();
                subRiskList.add(param);
            });

            String[] splitStrings = key.split("&");
            KspayCreateRiskDetailRequest createRequest = KspayCreateRiskDetailRequest.newBuilder()
                    .setBusinessDomain(businessDomain)
                    .setRiskProduct(splitStrings[1])
                    .setRiskEvent(splitStrings[2])
                    .setBelongGroup(request.getBelongGroup())
                    .setDepartment(request.getDepartment())
                    .setOwner("韩艳萍")
                    .addAllSubRiskList(subRiskList)
                    .build();
            log.info("组装好的插入风险项数据: {}", createRequest);
            kspayRiskDetailBizService.kspayCreateRiskDetail(createRequest);
        });

        BatchImportRiskResponse response = BatchImportRiskResponse.newBuilder().setErrorMsg("SUCCESS")
                .build();

        return response;

    }


}
