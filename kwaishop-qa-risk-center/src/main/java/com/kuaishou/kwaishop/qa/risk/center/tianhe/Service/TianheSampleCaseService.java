package com.kuaishou.kwaishop.qa.risk.center.tianhe.Service;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ActionSetExtendData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AddSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AddSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.BatchExecuteSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.BatchExecuteSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ExecuteFinishRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ExecuteFinishResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ExecuteResult;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ExecuteSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ExecuteSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListExecuteExtendData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListExecuteRecord;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListHistoryExecuteRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListHistoryExecuteResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListSampleExtendData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListSampleRecord;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListSingleHistoryExecuteRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListSingleHistoryExecuteResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ManualPassRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ManualPassResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.PageResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.QueryActionSetByIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.QueryActionSetByIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.TianheSampleMessageEvent;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateValidActionSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateValidActionSetResponse;
import com.kuaishou.kwaishop.qa.risk.center.task.rocketmq.producer.TianheSampleMqProducer;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Sample.TianheExecuteSampleDO;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.Sample.TianheSampleCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Enums.TianheErrCodeEnum;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Enums.TianheSampleActionCode;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Enums.TianheSampleExecuteStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Enums.TianheSampleMessageType;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Enums.TinaheSampleStatus;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.Sample.TianheExecuteSampleMapper;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.Sample.TianheSampleCaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Utils.TianheSampleCaseUtils;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-18
 */
@Service
@Slf4j
public class TianheSampleCaseService {
    private final TianheSampleCaseMapper tianheSampleCaseMapper;
    private final TianheExecuteSampleMapper tianheExecuteSampleMapper;
    private final TianheSampleCaseUtils tianheSampleCaseUtils;

    @Resource
    private TianheSampleMqProducer tianheSampleMqProducer;
    @Autowired
    public TianheSampleCaseService(TianheSampleCaseMapper tianheSampleCaseMapper, TianheExecuteSampleMapper tianheExecuteSampleMapper,
            TianheSampleCaseUtils tianheSampleCaseUtils) {
        this.tianheSampleCaseMapper = tianheSampleCaseMapper;
        this.tianheExecuteSampleMapper = tianheExecuteSampleMapper;
        this.tianheSampleCaseUtils = tianheSampleCaseUtils;
    }

    public ListSampleResponse listSampleResponse(ListSampleRequest request) {
        return handleListSampleResponse(request);
    }

    private ListSampleResponse handleListSampleResponse(ListSampleRequest request) {
        log.info("handleListSampleResponse request {}", ObjectMapperUtils.toJSON(request));
        ListSampleResponse.Builder listSampleResponse = ListSampleResponse.newBuilder();
        try {
            QueryWrapper<TianheSampleCaseDO> queryWrapper = new QueryWrapper<>();
            int pageSize = request.getPage().getPageSize();
            int pageNum = request.getPage().getPageNum();
            pageSize = pageSize > 0 ? pageSize : 10;
            pageNum = Math.max(pageNum, 1);
            queryWrapper.eq("deleted", 0);

            Optional.of(request.getId()).filter(value -> !value.isEmpty())
                    .ifPresent(value -> queryWrapper.eq("id", value));
            Optional.of(request.getName()).filter(value -> !value.isEmpty())
                    .ifPresent(value -> queryWrapper.eq("name", value));
            Optional.of(request.getAppKey()).filter(value -> !value.isEmpty())
                    .ifPresent(value -> queryWrapper.eq("app_key", value));
            Optional.of(request.getCreator()).filter(value -> !value.isEmpty())
                    .ifPresent(value -> queryWrapper.eq("creator", value));
            // 创建时间倒序排序
            queryWrapper.orderByDesc("create_time");

            Page<TianheSampleCaseDO> p = new Page<>(pageNum, pageSize);
            IPage<TianheSampleCaseDO> result = tianheSampleCaseMapper.selectPage(p, queryWrapper);
            int total = (int) result.getTotal();
            List<TianheSampleCaseDO> list = result.getRecords();
            int pageCount = (int) result.getPages();
            PageResponse page = PageResponse.newBuilder()
                    .setPageSize(pageSize).setPageNum(pageNum).setTotalCount(total).setPageCount(pageCount)
                    .build();

            List<ListSampleRecord> items = new ArrayList<>();
            for (TianheSampleCaseDO record: list) {
                String operatorSet;
                if (record.getCreator().equals(request.getOperator())) {
                    // 15 = 1111, 分别表示拥有 编辑 、执行、删除和执行记录查看权限
                    operatorSet = "15";
                } else {
                    // 5 = 0101, 表示拥有执行和执行记录查看权限
                    operatorSet = "5";
                }

                String ext = tianheSampleCaseUtils.getExt(record.getExt());
                items.add(ListSampleRecord.newBuilder()
                        .setId((int) record.getId())
                        .setName(record.getName())
                        .setAppKey(record.getAppKey())
                        .setPageCode(record.getPageCode())
                        .setExt(ext)
                        .setOperatorSet(operatorSet)
                        .setCreator(record.getCreator())
                        .setPlatformSource(record.getPlatformSource())
                        .setPageCode(record.getPageCode())
                        .setStatus(String.valueOf(record.getStatus()))
                        .addAllActionSet(ObjectMapperUtils.fromJSON(record.getActionSet(), List.class, ActionSetExtendData.class))
                        .build());
            }
            ListSampleExtendData extendData = ListSampleExtendData.newBuilder()
                    .setTotal(total)
                    .addAllList(items)
                    .build();
            listSampleResponse.setResult(ErrorCode.BasicErrorCode.SUCCESS.getCode())
                    .setErrorMsg("success")
                    .setSuccess(true)
                    .setData(extendData)
                    .setPage(page)
                    .build();
        } catch (Exception e) {
            log.error("handleListSampleResponse error ", e);
            listSampleResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg("列表查询异常，请稍后重试")
                    .setSuccess(false)
                    .build();
        }
        log.info("handleListSampleResponse response {}", ObjectMapperUtils.toJSON(listSampleResponse.build()));
        return listSampleResponse.build();
    }
    public AddSampleResponse addSampleResponse(AddSampleRequest request) {
        return handleAddSampleResponse(request);
    }

    /**
     * 将请求转换为DO类，insert
     * @param request sample相关信息
     * @return BaseResponse
     */
    private AddSampleResponse handleAddSampleResponse(AddSampleRequest request) {
        log.info("handleAddSampleResponse request {}", ObjectMapperUtils.toJSON(request));
        AddSampleResponse.Builder addSampleResponse = AddSampleResponse.newBuilder();
        try {
            if (!tianheSampleCaseUtils.isValidUrl(request.getPrtUrl()) || !tianheSampleCaseUtils.isValidUrl(request.getOnlineUrl())) {
                return AddSampleResponse.newBuilder()
                        .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                        .setSuccess(false)
                        .setErrorMsg("输入的url格式有误")
                        .build();
            }
            // 校验下actionSet是否为空
            if (request.getActionSetList().isEmpty()) {
                return AddSampleResponse.newBuilder()
                        .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                        .setSuccess(false)
                        .setErrorMsg("至少需要一个校验动作")
                        .build();
            }
            long timestamp = System.currentTimeMillis();

            TianheSampleCaseDO data = new TianheSampleCaseDO();
            data.setName(request.getName());
            data.setStatus(Integer.parseInt(TinaheSampleStatus.NORMAL_SMAPLE_CREATED.getValue()));
            data.setPlatformSource(request.getPlatform());
            data.setDomainCode(request.getDomainCode());
            data.setAppKey(request.getAppKey());
            data.setPageCode(request.getPageCode());
            data.setCreateTime(timestamp);
            data.setUpdateTime(timestamp);
            data.setDeleted(0);
            data.setCreator(request.getCreator()); //创建人是拥有者
            data.setOperators(request.getCreator());
            data.setModifier(request.getCreator());
            data.setPrtUrl(request.getPrtUrl());
            data.setOnlineUrl(request.getOnlineUrl());
            data.setActionSet(ObjectMapperUtils.toJSON(request.getActionSetList()));

            Map<String, Object> map = new HashMap<>();
            // prtUrl 和 onlineUrl
            map.put("prtUrl", request.getPrtUrl());
            map.put("onlineUrl", request.getOnlineUrl());
            map.put("accountUID", request.getAccountUid());
            map.put("password", request.getPwd());
            map.put("targetLaneId", request.getTargetLaneId());
            map.put("testLaneId", request.getTestLaneId());
            data.setExt(ObjectMapperUtils.toJSON(map));
            int insertCount = tianheSampleCaseMapper.insertSample(data);
            boolean flag = insertCount > 0;
            int result = flag ? 1 : -1;
            String errMsg = flag ? "success" : "fail";

            addSampleResponse.setResult(result)
                    .setSuccess(flag)
                    .setErrorMsg(errMsg)
                    .build();
        } catch (Exception e) {
            log.error("AddSampleResponse error ", e);
            addSampleResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("新增用例异常，请稍后重试")
                    .build();
        }
        return addSampleResponse.build();
    }

    /**
     * 智能用例新建
     * @param request
     * @return
     */
    public AddSampleResponse addAiSampleResponse(AddSampleRequest request) {
        return handleAddAiSampleResponse(request);
    }

    /**
     * 将请求转换为DO类，insert
     * @param request sample相关信息
     * @return BaseResponse
     */
    private AddSampleResponse handleAddAiSampleResponse(AddSampleRequest request) {
        log.info("handleAddAiSampleResponse request {}", ObjectMapperUtils.toJSON(request));
        AddSampleResponse.Builder addSampleResponse = AddSampleResponse.newBuilder();
        try {
            if (!tianheSampleCaseUtils.isValidUrl(request.getPrtUrl())) {
                return AddSampleResponse.newBuilder()
                        .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                        .setSuccess(false)
                        .setErrorMsg("输入的url格式有误")
                        .build();
            }
            long timestamp = System.currentTimeMillis();

            TianheSampleCaseDO data = new TianheSampleCaseDO();
            data.setName(request.getName());
            data.setStatus(Integer.parseInt(TinaheSampleStatus.AI_SAMPLE_INIT.getValue()));
            data.setPlatformSource(request.getPlatform());
            data.setDomainCode(request.getDomainCode());
            data.setAppKey(request.getAppKey());
            data.setPageCode(request.getPageCode());
            data.setCreateTime(timestamp);
            data.setUpdateTime(timestamp);
            data.setDeleted(0);
            data.setCreator(request.getCreator()); //创建人是拥有者
            data.setOperators(request.getCreator());
            data.setModifier(request.getCreator());
            data.setPrtUrl(request.getPrtUrl());
            data.setActionSet(ObjectMapperUtils.toJSON(request.getActionSetList()));

            Map<String, Object> map = new HashMap<>();
            // prtUrl 测试环境页面Url
            map.put("prtUrl", request.getPrtUrl());
            map.put("accountUID", request.getAccountUid());
            map.put("password", request.getPwd());
            map.put("testLaneId", request.getTestLaneId());
            map.put("changeOrderId", request.getChangeOrderId());
            data.setExt(ObjectMapperUtils.toJSON(map));
            int insertCount = tianheSampleCaseMapper.insertSample(data);
            boolean flag = insertCount > 0;
            if (flag) {
                long sampleId = data.getId();
                TianheSampleMessageEvent tianheSampleMessageEvent =
                        TianheSampleMessageEvent.newBuilder()
                                .setSampleId(String.valueOf(sampleId))
                                .setType(TianheSampleMessageType.CREATE.getValue()).build();
                tianheSampleMqProducer.sendAsyncMessage(tianheSampleMessageEvent);
            }
            int result = flag ? 1 : -1;
            String errMsg = flag ? "success" : "fail";

            addSampleResponse.setResult(result)
                    .setSuccess(flag)
                    .setErrorMsg(errMsg)
                    .build();
        } catch (Exception e) {
            log.error("handleAddAiSampleResponse error ", e);
            addSampleResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("新增智能用例异常，请稍后重试")
                    .build();
        }
        return addSampleResponse.build();
    }

    public DeleteSampleResponse deleteSampleResponse(DeleteSampleRequest request) {
        return handleDeleteSampleResponse(request);
    }

    /**
     *
     * @param request sampleId
     * @return BaseResponse
     */
    private DeleteSampleResponse handleDeleteSampleResponse(DeleteSampleRequest request) {
        log.info("handleDeleteSampleResponse request {}", ObjectMapperUtils.toJSON(request));
        DeleteSampleResponse.Builder deleteSampleResponse = DeleteSampleResponse.newBuilder();
        try {
            TianheSampleCaseDO sample = tianheSampleCaseMapper.selectById(request.getId());
            // 校验操作人和用例状态
            if (!sample.getCreator().equals(request.getOperator())) {
                return DeleteSampleResponse.newBuilder()
                        .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                        .setErrorMsg("不能操作非本人创建的用例")
                        .setSuccess(false)
                        .build();
            } else if (sample.getDeleted().equals(1)) {
                return DeleteSampleResponse.newBuilder()
                        .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                        .setErrorMsg("该用例已被删除")
                        .setSuccess(false)
                        .build();
            } else {
                long timestamp = System.currentTimeMillis();
                TianheSampleCaseDO tianheSampleCaseDO = new TianheSampleCaseDO();
                UpdateWrapper<TianheSampleCaseDO> updateWrapper = new UpdateWrapper<>();

                updateWrapper.eq("id", request.getId());
                tianheSampleCaseDO.setUpdateTime(timestamp);
                tianheSampleCaseDO.setDeleted(1);
                int updateCount = tianheSampleCaseMapper.update(tianheSampleCaseDO, updateWrapper);

                boolean flag = updateCount > 0;
                int result = flag ? 1 : -1;
                String errMsg = flag ? "success" : "fail";

                deleteSampleResponse.setResult(result)
                        .setSuccess(flag)
                        .setErrorMsg(errMsg)
                        .build();
            }
        } catch (Exception e) {
            log.error("DeleteSampleResponse error ", e);
            deleteSampleResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("用例删除异常，请稍后重试")
                    .build();
        }
        return deleteSampleResponse.build();
    }

    public UpdateSampleResponse updateSampleResponse(UpdateSampleRequest request) {
        return handleUpdateSampleResponse(request);
    }

    private UpdateSampleResponse handleUpdateSampleResponse(UpdateSampleRequest request) {
        log.info("handleUpdateSampleResponse request {}", ObjectMapperUtils.toJSON(request));
        UpdateSampleResponse.Builder updateSampleResponse = UpdateSampleResponse.newBuilder();
        try {
            // 校验操作人
            TianheSampleCaseDO sample = tianheSampleCaseMapper.selectById(request.getId());
            if (!sample.getCreator().equals(request.getModifier())) {
                updateSampleResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                        .setErrorMsg("不能操作非本人创建的用例")
                        .setSuccess(false)
                        .build();
            }
            // 校验actionSet是否为空
            if (request.getActionSetList().isEmpty()) {
                updateSampleResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                        .setErrorMsg("至少需要一个校验动作")
                        .setSuccess(false)
                        .build();
            }
            if (sample.getDeleted().equals(1)) {
                // 用例已被删除
                updateSampleResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                        .setErrorMsg("用例已被删除")
                        .setSuccess(false)
                        .build();
            }
            long timestamp = System.currentTimeMillis();
            TianheSampleCaseDO tianheSampleCaseDO = new TianheSampleCaseDO();
            UpdateWrapper<TianheSampleCaseDO> updateWrapper = new UpdateWrapper<>();

            updateWrapper.eq("id", request.getId());
            tianheSampleCaseDO.setUpdateTime(timestamp);
            tianheSampleCaseDO.setName(request.getName());
            tianheSampleCaseDO.setPageCode(request.getPageCode());
            tianheSampleCaseDO.setModifier(request.getModifier());
            tianheSampleCaseDO.setActionSet(ObjectMapperUtils.toJSON(request.getActionSetList()));
            Map<String, Object> map = new HashMap<>();
            // prtUrl 和 onlineUrl
            map.put("prtUrl", request.getPrtUrl());
            map.put("onlineUrl", request.getOnlineUrl());
            map.put("accountUID", request.getAccountUid());
            map.put("password", request.getPwd());
            map.put("targetLaneId", request.getTargetLaneId());
            map.put("testLaneId", request.getTestLaneId());
            tianheSampleCaseDO.setExt(ObjectMapperUtils.toJSON(map));
            int updateCount = tianheSampleCaseMapper.update(tianheSampleCaseDO, updateWrapper);

            boolean flag = updateCount > 0;
            int result = flag ? 1 : -1;
            String errMsg = flag ? "success" : "fail";

            updateSampleResponse.setResult(result)
                    .setSuccess(flag)
                    .setErrorMsg(errMsg)
                    .build();
        } catch (Exception e) {
            log.error("UpdateSampleResponse error", e);
            updateSampleResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("用例更新失败，请稍后重试")
                    .build();
        }
        return updateSampleResponse.build();
    }

    public UpdateSampleResponse updateAiSampleResponse(UpdateSampleRequest request) {
        return handleUpdateAiSampleResponse(request);
    }

    private UpdateSampleResponse handleUpdateAiSampleResponse(UpdateSampleRequest request) {
        log.info("handleUpdateSampleResponse request {}", ObjectMapperUtils.toJSON(request));
        UpdateSampleResponse.Builder updateSampleResponse = UpdateSampleResponse.newBuilder();
        try {

            TianheSampleCaseDO sample = tianheSampleCaseMapper.selectById(request.getId());
            // 校验actionSet是否为空
            if (request.getActionSetList().isEmpty()) {
                updateSampleResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                        .setErrorMsg("智能用例至少需要一个校验动作")
                        .setSuccess(false)
                        .build();
            }
            if (sample.getDeleted().equals(1)) {
                // 用例已被删除
                updateSampleResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                        .setErrorMsg("用例已被删除")
                        .setSuccess(false)
                        .build();
            }
            long timestamp = System.currentTimeMillis();
            TianheSampleCaseDO tianheSampleCaseDO = new TianheSampleCaseDO();
            UpdateWrapper<TianheSampleCaseDO> updateWrapper = new UpdateWrapper<>();

            updateWrapper.eq("id", request.getId());
            tianheSampleCaseDO.setUpdateTime(timestamp);
            tianheSampleCaseDO.setName(request.getName());
            tianheSampleCaseDO.setPageCode(request.getPageCode());
            tianheSampleCaseDO.setModifier(request.getModifier());
            tianheSampleCaseDO.setActionSet(ObjectMapperUtils.toJSON(request.getActionSetList()));
            Map<String, Object> map = new HashMap<>();
            // prtUrl
            map.put("prtUrl", request.getPrtUrl());
            map.put("accountUID", request.getAccountUid());
            map.put("password", request.getPwd());
            map.put("testLaneId", request.getTestLaneId());
            map.put("changeOrderId", request.getChangeOrderId());
            tianheSampleCaseDO.setExt(ObjectMapperUtils.toJSON(map));
            int updateCount = tianheSampleCaseMapper.update(tianheSampleCaseDO, updateWrapper);

            boolean flag = updateCount > 0;
            int result = flag ? 1 : -1;
            String errMsg = flag ? "success" : "fail";

            updateSampleResponse.setResult(result)
                    .setSuccess(flag)
                    .setErrorMsg(errMsg)
                    .build();
        } catch (Exception e) {
            log.error("UpdateSampleResponse error", e);
            updateSampleResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("用例更新失败，请稍后重试")
                    .build();
        }
        return updateSampleResponse.build();
    }

    public ExecuteSampleResponse executeSampleResponse(ExecuteSampleRequest request) {
        return handleExecuteSampleResponse(request);
    }

    /**
     * @param request sampleId、changeOrderId、lastChangeDeployId、lastDeployVersion
     * @return BaseResponse
     */
    private ExecuteSampleResponse handleExecuteSampleResponse(ExecuteSampleRequest request) {
        log.info("handleExecuteSampleResponse request {}", ObjectMapperUtils.toJSON(request));
        ExecuteSampleResponse.Builder executeSampleResponse = ExecuteSampleResponse.newBuilder();
        try {
            // 20250304放开 执行权限不校验
            // 执行记录对应用例
            String executeId = UUID.randomUUID().toString();
            String changeOrderId = "";

            // 2024.12.24新增逻辑，灵筑执行会传变更单id
            boolean isChangeOrderIdEmpty = request.getChangeOrderId() == 0;
            boolean isLastChangeDeployIdEmpty = request.getLastChangeDeployId() == 0;
            boolean isLastDeployVersionEmpty = request.getLastDeployVersion() == 0;
            if (!isChangeOrderIdEmpty && !isLastChangeDeployIdEmpty && !isLastDeployVersionEmpty) {
                changeOrderId = request.getChangeOrderId() + "_" + request.getLastChangeDeployId() + "_"
                        + request.getLastDeployVersion();
            }

            int insertCount = tianheSampleCaseUtils.executeSingleSample(request.getId(), executeId, changeOrderId);
            boolean flag = insertCount > 0;
            int result = flag ? 1 : -1;
            String errMsg = flag ? "success" : "fail";

            executeSampleResponse.setResult(result)
                    .setSuccess(flag)
                    .setErrorMsg(errMsg)
                    .build();
        } catch (Exception e) {
            log.error("ExecuteSampleResponse error ",  e);
            executeSampleResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("用例执行异常，请稍后重试")
                    .build();
        }
        return executeSampleResponse.build();
    }

    public ListSingleHistoryExecuteResponse listSingleHistoryExecuteResponse(ListSingleHistoryExecuteRequest request) {
        return handleListSingleHistoryExecuteResponse(request);
    }
    private ListSingleHistoryExecuteResponse handleListSingleHistoryExecuteResponse(ListSingleHistoryExecuteRequest request) {
        log.error("handleListSingleHistoryExecuteResponse request {}", ObjectMapperUtils.toJSON(request));
        ListSingleHistoryExecuteResponse.Builder listSingleHistoryExecuteResponse =
                ListSingleHistoryExecuteResponse.newBuilder();
        try {
            TianheSampleCaseDO sample = tianheSampleCaseMapper.selectById(request.getId());

            QueryWrapper<TianheExecuteSampleDO> queryWrapper = new QueryWrapper<>();
            int pageSize = request.getPage().getPageSize();
            int pageNum = request.getPage().getPageNum();
            pageSize = pageSize > 0 ? pageSize : 10;
            pageNum = Math.max(pageNum, 1);

            queryWrapper.eq("sample_id", request.getId());
            queryWrapper.orderByDesc("create_time");

            Page<TianheExecuteSampleDO> p = new Page<>(pageNum, pageSize);
            IPage<TianheExecuteSampleDO> result = tianheExecuteSampleMapper.selectPage(p, queryWrapper);

            int total = (int) result.getTotal();
            List<TianheExecuteSampleDO> list = result.getRecords();
            int pageCount = (int) result.getPages();
            PageResponse page = PageResponse.newBuilder()
                    .setPageSize(pageSize).setPageNum(pageNum).setTotalCount(total).setPageCount(pageCount)
                    .build();

            List<ListExecuteRecord> records = new ArrayList<>();
            for (TianheExecuteSampleDO record: list) {
                records.add(ListExecuteRecord.newBuilder()
                                .setExecuteId(record.getExecuteId())
                                .setSampleId(record.getSampleId())
                                .setName(sample.getName())
                                .setAppKey(sample.getAppKey())
                                .setPageCode(sample.getPageCode())
                                .setExt(tianheSampleCaseUtils.getExecuteExt(sample.getExt(), record.getExt()))
                                .setDiffRate(tianheSampleCaseUtils.getDiffRate(record.getExt()))
                                .setActionType(TianheSampleActionCode.getMessageByCode(record.getActionType()))
                                .setCreateTime(record.getCreateTime())
                                .setStatus(record.getStatus())
                                .setPlatformSource(sample.getPlatformSource())
                                .setExecuteStatus(record.getExecuteStatus())
                                .setXpath(Optional.ofNullable(record.getXpath()).orElse(""))
                                .build()
                );
            }

            ListExecuteExtendData executeRecord = ListExecuteExtendData.newBuilder()
                    .setTotal(total)
                    .addAllList(records)
                    .build();
            listSingleHistoryExecuteResponse.setResult(ErrorCode.BasicErrorCode.SUCCESS.getCode())
                    .setSuccess(true)
                    .setErrorMsg("success")
                    .setData(executeRecord)
                    .setPage(page)
                    .build();

        } catch (Exception e) {
            log.error("handleListSingleHistoryExecuteResponse error ", e);
            listSingleHistoryExecuteResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setSuccess(false)
                    .setErrorMsg("历史记录查询异常，请稍后重试")
                    .build();
        }
        return listSingleHistoryExecuteResponse.build();
    }
    public ListHistoryExecuteResponse listHistoryExecuteResponse(ListHistoryExecuteRequest request) {
        return handleListHistoryExecuteResponse(request);
    }
    private ListHistoryExecuteResponse handleListHistoryExecuteResponse(ListHistoryExecuteRequest request) {
        log.error("handleListHistoryExecuteResponse request {}", ObjectMapperUtils.toJSON(request));
        ListHistoryExecuteResponse.Builder listHistoryExecuteResponse =
                ListHistoryExecuteResponse.newBuilder();
        try {
            QueryWrapper<TianheExecuteSampleDO> queryWrapper = new QueryWrapper<>();
            int pageSize = request.getPage().getPageSize();
            int pageNum = request.getPage().getPageNum();
            String changeOrderId = "";
            pageSize = pageSize > 0 ? pageSize : 10;
            pageNum = Math.max(pageNum, 1);

            Optional.of(request.getPlatformSource()).filter(value -> !value.isEmpty())
                    .ifPresent(value -> queryWrapper.eq("JSON_EXTRACT(ext, '$.platform_source')", value));
            Optional.of(request.getAppKey()).filter(value -> !value.isEmpty())
                    .ifPresent(value -> queryWrapper.eq("JSON_EXTRACT(ext, '$.app_key')", value));
            Optional.of(request.getPageCode()).filter(value -> !value.isEmpty())
                    .ifPresent(value -> queryWrapper.eq("JSON_EXTRACT(ext, '$.page_code')", value));
            Optional.of(request.getExecuteId()).filter(value -> !value.isEmpty())
                    .ifPresent(value -> queryWrapper.eq("execute_id", value));
            Optional.of(request.getSampleId()).filter(value -> !value.isEmpty())
                    .ifPresent(value -> queryWrapper.eq("sample_id", value));
            Optional.of(request.getName()).filter(value -> !value.isEmpty())
                    .ifPresent(value -> queryWrapper.eq("name", value));
            if (!request.getChangeOrderId().equals("") && !request.getLastChangeDeployId().equals("")
                    && !request.getLastDeployVersion().equals("")) {
                changeOrderId = request.getChangeOrderId() + "_" + request.getLastChangeDeployId() + "_"
                        + request.getLastDeployVersion();
                queryWrapper.eq("change_order_id", changeOrderId);
            }
            queryWrapper.orderByDesc("create_time");

            Page<TianheExecuteSampleDO> p = new Page<>(pageNum, pageSize);
            IPage<TianheExecuteSampleDO> result = tianheExecuteSampleMapper.selectPage(p, queryWrapper);
            int total = (int) result.getTotal();
            if (!changeOrderId.equals("") && total == 0) {
                // 特殊处理下，天河部署后，会生成查看执行记录的链接，但是这个链接里只有在部署成功后才能看到记录
                // 避免用户产生疑惑，给一个toast提示
                listHistoryExecuteResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                        .setErrorMsg("最近一次PRT主干未部署成功，执行记录未生成，请稍后查看")
                        .setSuccess(false)
                        .build();
            } else {
                List<TianheExecuteSampleDO> list = result.getRecords();
                int pageCount = (int) result.getPages();
                PageResponse page = PageResponse.newBuilder()
                        .setPageSize(pageSize).setPageNum(pageNum).setTotalCount(total).setPageCount(pageCount)
                        .build();

                List<ListExecuteRecord> records = new ArrayList<>();
                for (TianheExecuteSampleDO record: list) {
                    TianheSampleCaseDO sample = tianheSampleCaseMapper.selectById(record.getSampleId());
                    records.add(ListExecuteRecord.newBuilder()
                            .setExecuteId(record.getExecuteId())
                            .setSampleId(record.getSampleId())
                            .setName(sample.getName())
                            .setAppKey(sample.getAppKey())
                            .setPageCode(sample.getPageCode())
                            .setExt(tianheSampleCaseUtils.getExecuteExt(sample.getExt(), record.getExt()))
                            .setDiffRate(tianheSampleCaseUtils.getDiffRate(record.getExt()))
                            .setActionType(TianheSampleActionCode.getMessageByCode(record.getActionType()))
                            .setCreateTime(record.getCreateTime())
                            .setStatus(record.getStatus())
                            .setPlatformSource(sample.getPlatformSource())
                            .setExecuteStatus(record.getExecuteStatus())
                            .setXpath(Optional.ofNullable(record.getXpath()).orElse(""))
                            .build()
                    );
                }

                ListExecuteExtendData executeRecord = ListExecuteExtendData.newBuilder()
                        .setTotal(total)
                        .addAllList(records)
                        .build();
                listHistoryExecuteResponse.setResult(ErrorCode.BasicErrorCode.SUCCESS.getCode())
                        .setErrorMsg("success")
                        .setSuccess(true)
                        .setData(executeRecord)
                        .setPage(page)
                        .build();
            }
        } catch (Exception e) {
            log.error("handleListHistoryExecuteResponse error ", e);
            listHistoryExecuteResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg("执行记录查询失败，请稍后重试")
                    .setSuccess(false)
                    .build();
        }
        return listHistoryExecuteResponse.build();
    }

    public ExecuteFinishResponse executeFinishResponse(ExecuteFinishRequest request) {
        return handleExecuteFinishResponse(request);
    }

    /**
     * 开个后门，kafka暂时整不了，UI自动化那边直接先回调这个接口改记录状态
     * @param request 执行记录主键、需修改的状态值
     * @return BaseResponse
     */
    private ExecuteFinishResponse handleExecuteFinishResponse(ExecuteFinishRequest request) {
        log.error("handleExecuteFinishResponse request {}", ObjectMapperUtils.toJSON(request));
        ExecuteFinishResponse.Builder executeFinishResponse = ExecuteFinishResponse.newBuilder();
        try {
            for (ExecuteResult record : request.getListList()) {
                boolean flag = tianheSampleCaseUtils.diffRateEqualsZero(record.getDiffRate());
                int executeStatus = flag ? TianheSampleExecuteStatusEnum.AUTO_PASSED.ordinal()
                        : TianheSampleExecuteStatusEnum.NOT_PASSED.ordinal();
                tianheExecuteSampleMapper.updateStatus(request.getExecuteId(), record.getSampleId(), record.getActionType(), record.getXpath(),
                        1, record.getDiffRate() + "%", executeStatus);
                // 异步消费判断图像结果是否符合AI预期，然后更新执行结果状态
                TianheSampleMessageEvent tianheSampleMessageEvent =
                        TianheSampleMessageEvent.newBuilder()
                                .setSampleId(String.valueOf(record.getSampleId()))
                                .setType(TianheSampleMessageType.EXECUTE.getValue())
                                .setActionType(String.valueOf(record.getActionType()))
                                .setXpath(record.getXpath())
                                .setExecuteId(request.getExecuteId())
                                .build();
                tianheSampleMqProducer.sendAsyncMessage(tianheSampleMessageEvent);

            }
            executeFinishResponse.setResult(ErrorCode.BasicErrorCode.SUCCESS.getCode())
                    .setErrorMsg("success")
                    .setSuccess(true)
                    .build();
        } catch (Exception e) {
            log.error("handleExecuteFinishResponse error ", e);
            executeFinishResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg("回调更新数据异常，请稍后重试")
                    .setSuccess(false)
                    .build();
        }
        return executeFinishResponse.build();
    }
    public ManualPassResponse manualPassResponse(ManualPassRequest request) {
        return handleManualPassResponse(request);
    }

    private ManualPassResponse handleManualPassResponse(ManualPassRequest request) {
        log.error("handleManualPassResponse request {}", ObjectMapperUtils.toJSON(request));
        ManualPassResponse.Builder manualPassResponse = ManualPassResponse.newBuilder();
        try {
            TianheExecuteSampleDO tianheExecuteSampleDO = new TianheExecuteSampleDO();
            UpdateWrapper<TianheExecuteSampleDO> updateWrapper = new UpdateWrapper<>();

            // 20250304修改，不判断权限
            Optional.of(request.getExecuteId()).filter(value -> !value.isEmpty())
                    .ifPresent(value -> updateWrapper.eq("execute_id", value));
            Optional.of(request.getSampleId()).filter(value -> !(value == 0))
                    .ifPresent(value -> updateWrapper.eq("sample_id", value));
            Optional.of(request.getActionType()).filter(value -> !(value == 0))
                    .ifPresent(value -> updateWrapper.eq("action_type", value));
            Optional.of(request.getXpath()).filter(value -> !value.isEmpty())
                    .ifPresent(value -> updateWrapper.eq("xpath", value));

            tianheExecuteSampleDO.setExecuteStatus(TianheSampleExecuteStatusEnum.MANUAL_PASSED.ordinal());

            tianheExecuteSampleMapper.update(tianheExecuteSampleDO, updateWrapper);

            manualPassResponse.setResult(ErrorCode.BasicErrorCode.SUCCESS.getCode())
                    .setErrorMsg("success")
                    .setSuccess(true)
                    .build();
        } catch (Exception e) {
            log.error("handleManualPassResponse error ", e);
            manualPassResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg("确认通过异常，请稍后重试")
                    .setSuccess(false)
                    .build();
        }
        return manualPassResponse.build();
    }

    /**
     * 接mq传的onlineUrl list，批量查用例，调单用例执行
     * @param request onlineUrl list、changeOrderId、lastDeployVersion、lastChangeDeployId
     * @return BaseResponse
     */
    public BatchExecuteSampleResponse mqExecuteSampleResponse(BatchExecuteSampleRequest request) {
        log.error("mqExecuteSampleResponse request {}", ObjectMapperUtils.toJSON(request));
        BatchExecuteSampleResponse.Builder batchExecuteSampleResponse =
                BatchExecuteSampleResponse.newBuilder();
        try {
            String changeOrderId = request.getChangeOrderId() + "_" + request.getLastChangeDeployId() + "_"
                    + request.getLastDeployVersion();
            String executeId = UUID.randomUUID().toString();
            log.error("mqExecuteSampleResponse changeOrderId {} executeId {}",
                    changeOrderId, executeId);
            QueryWrapper<TianheSampleCaseDO> queryWrapper = new QueryWrapper<>();
            // pageCode 存储了天河dilu链接，mq传过来的也是dilu链接，所以根据这个进行匹配
            queryWrapper.in("page_code", request.getProdUrlList());
            queryWrapper.in("deleted", 0);
            List<TianheSampleCaseDO> sampleCaseList = tianheSampleCaseMapper.selectList(queryWrapper);

            for (TianheSampleCaseDO sample: sampleCaseList) {
                tianheSampleCaseUtils.executeSingleSample((int) sample.getId(), executeId, changeOrderId);
            }
            batchExecuteSampleResponse.setResult(ErrorCode.BasicErrorCode.SUCCESS.getCode())
                    .setErrorMsg("")
                    .setSuccess(true)
                    .build();
        } catch (Exception e) {
            log.error("mqExecuteSampleResponse error ", e);
            batchExecuteSampleResponse.setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg("执行失败")
                    .setSuccess(false)
                    .build();

        }
        return batchExecuteSampleResponse.build();
    }

    /**
     * 包个通用的批量执行接口出来
     * @param samples 用例Id list
     * @param executeId 执行id
     * @param changeOrderId 变更单id 大多数场景为空，这里是为了兼容天河的场景
     * @return true 执行成功，false 执行失败
     */
    public boolean batchExecuteSampleResponse(List<Integer> samples, String executeId, String changeOrderId) {
        try {
            for (long sampleId : samples) {
                tianheSampleCaseUtils.executeSingleSample((int) sampleId, executeId, changeOrderId);
            }
            return true;
        } catch (Exception e) {
            log.error("批量执行错误 ", e);
            return false;
        }
    }


    public UpdateValidActionSetResponse updateValidActionSet(UpdateValidActionSetRequest request) {
        return handleUpdateValidActionSet(request);
    }

    private UpdateValidActionSetResponse handleUpdateValidActionSet(UpdateValidActionSetRequest request) {
        log.info("handleUpdateValidActionSet {}", ObjectMapperUtils.toJSON(request));
        try {
            TianheSampleCaseDO tianheSampleCaseDO = tianheSampleCaseMapper.selectById(request.getId());
            tianheSampleCaseDO.setActionSet(ObjectMapperUtils.toJSON(request.getActionSetList()));
            // AI_SAMPLE_CREATED 表示有actionSet数据了，目前首次和selfCheck后更新actionSet没有区分状态，都是用的AI_SAMPLE_CREATED，后续可以区分下
            tianheSampleCaseDO.setStatus(Integer.parseInt(TinaheSampleStatus.AI_SAMPLE_CREATED.getValue()));
            tianheSampleCaseMapper.updateById(tianheSampleCaseDO);
            return UpdateValidActionSetResponse.newBuilder()
                    .setResult(TianheErrCodeEnum.SUCCESS.getCode()).setErrorMsg("").setSuccess(true)
                    .build();
        } catch (Exception e) {
            log.error("handleUpdateValidActionSet error", e);
            return UpdateValidActionSetResponse.newBuilder()
                    .setResult(TianheErrCodeEnum.SERVER_ERROR.getCode()).setErrorMsg("服务异常").setSuccess(false)
                    .build();
        }
    }

    public QueryActionSetByIdResponse queryActionSetById(QueryActionSetByIdRequest request) {
        return handleQueryActionSetById(request);
    }

    private QueryActionSetByIdResponse handleQueryActionSetById(QueryActionSetByIdRequest request) {
        log.info("handleQueryActionSetById request: {}", ObjectMapperUtils.toJSON(request));
        try {
            TianheSampleCaseDO tianheSampleCaseDO = tianheSampleCaseMapper.selectById(request.getId());
            log.info("lqyDEBUG tianheSampleCaseDO: {}", tianheSampleCaseDO.getActionSet());
            List<ActionSetExtendData> actionSet = ObjectMapperUtils.fromJSON(
                    tianheSampleCaseDO.getActionSet(), List.class, ActionSetExtendData.class);
            QueryActionSetByIdResponse.Builder builder = QueryActionSetByIdResponse.newBuilder();
            for (ActionSetExtendData action : actionSet) {
                builder.addActionSet(action);
            }
            return builder.setResult(1).setErrorMsg("").setSuccess(true).build();
//            return QueryActionSetByIdResponse.newBuilder()
//                    .addAllActionSet(actionSet)
//                    .setResult(1)
//                    .setErrorMsg("")
//                    .setSuccess(true).build();

        } catch (Exception e) {
            log.error("handleQueryActionSetById error, request: {}", ObjectMapperUtils.toJSON(request), e);
            return QueryActionSetByIdResponse.newBuilder()
                    .setResult(11).setErrorMsg("查询动作集异常").setSuccess(false).build();
        }
    }
}