package com.kuaishou.kwaishop.qa.risk.center.tianhe.Domain.SampleSet;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-04-07
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tianhe_risk_sample_set_execute_record")
public class TianheExecuteSampleSetDO implements Serializable {
    private static final long serialVersionUID = -6985725478859223026L;
    @TableId(value = "id", type = IdType.AUTO)
    private long id;
    /**
     * 用例集ID
     */
    private Long setId;
    /**
     * 用例集名称
     */
    private String setName;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 更新时间
     */
    private Long updateTime;
    /**
     * 执行ID
     */
    private String executeId;
}
