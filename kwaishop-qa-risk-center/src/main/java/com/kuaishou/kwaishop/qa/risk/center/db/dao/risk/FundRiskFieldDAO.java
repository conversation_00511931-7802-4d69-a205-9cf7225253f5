package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundRiskFieldDO;

public interface FundRiskFieldDAO {

    // 插入一条字段记录
    void insertRiskField(FundRiskFieldDO fundRiskFieldDO);

    // 查询单个字段是否存在
    List<FundRiskFieldDO> getSimpleFieldByColumnName(String tableName, String columnName);

    // 更新某个字段
    int updateField(FundRiskFieldDO fundRiskFieldDO);

    // 查询所有的字段
    List<FundRiskFieldDO> getAllFields();

    List<FundRiskFieldDO> getFieldsByCondition(String tableName);

    List<FundRiskFieldDO> getDistinctTableNameList();

    int deleteRecordByTableName(String tableName, String column);

}
