package com.kuaishou.kwaishop.qa.risk.center.domain.datatool.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataSourceBo {
    private String name;

    private Integer type;

    private String creatorName;

    private String description;

    private String content;

    /**
     * 页码
     */
    private Integer pageNo;
    /**
     * 页容量
     */
    private Integer pageSize;
}
