package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.dao;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.strategy.model.StrategyTrafficRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy.ReplayDetailRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-08-08
 */
public interface StrategyTrafficRecordDAO {
    PageBO<StrategyTrafficRecordDO> queryPageList(ReplayDetailRequest request);

    List<StrategyTrafficRecordDO> queryAllByTaskId(String taskId);

    List<StrategyTrafficRecordDO> queryAllByIds(List<Long> ids);
}
