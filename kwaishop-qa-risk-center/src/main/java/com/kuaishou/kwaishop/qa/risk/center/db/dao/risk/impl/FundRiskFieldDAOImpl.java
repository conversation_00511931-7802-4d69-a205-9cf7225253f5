package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.risk.FundRiskFieldDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundRiskFieldDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.risk.FundRiskFieldMapper;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023/11/7 16:50
 */
@Repository
public class FundRiskFieldDAOImpl implements FundRiskFieldDAO {

    @Autowired
    private FundRiskFieldMapper fundRiskFieldMapper;

    @Override
    public void insertRiskField(FundRiskFieldDO fundRiskFieldDO) {
        fundRiskFieldMapper.insertField(fundRiskFieldDO);
    }

    @Override
    public List<FundRiskFieldDO> getSimpleFieldByColumnName(String tableName, String columnName) {
        return fundRiskFieldMapper.getSimpleFieldByColumnName(tableName, columnName);
    }

    @Override
    public int updateField(FundRiskFieldDO fundRiskFieldDO) {
        return fundRiskFieldMapper.updateField(fundRiskFieldDO);
    }

    @Override
    public List<FundRiskFieldDO> getAllFields() {
        return null;
    }

    @Override
    public List<FundRiskFieldDO> getFieldsByCondition(String tableName) {
        return fundRiskFieldMapper.getFieldsByCondition(tableName);
    }

    @Override
    public List<FundRiskFieldDO> getDistinctTableNameList() {
        return fundRiskFieldMapper.getDistinctTableNameList();
    }

    @Override
    public int deleteRecordByTableName(String tableName, String columnName) {
        return fundRiskFieldMapper.deleteRecordByTableName(tableName, columnName);
    }


}
