package com.kuaishou.kwaishop.qa.risk.center.domain.openapi.service;

import java.util.List;

import com.google.gson.JsonObject;


public interface RpcMonitorQueryService {

    JsonObject querySrcServiceCallCount(String srcKsn, String srcMethod);

    boolean checkSrcMethodCallCount(String srcKsn, String srcMethod, String dataTs);

    List<String> querySrcMethod(String srcKsn, String st, String et);
}
