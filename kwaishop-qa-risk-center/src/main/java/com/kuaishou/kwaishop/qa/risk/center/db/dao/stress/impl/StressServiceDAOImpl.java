package com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.impl.BaseDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressServiceDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress.StressServiceMapper;
import com.kuaishou.kwaishop.qa.risk.center.db.query.stress.ScenarioQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.StressServiceBO;

@Repository
public class StressServiceDAOImpl extends BaseDAOImpl<StressServiceDO, ScenarioQueryCondition> implements StressServiceDAO {

    @Resource
    private StressServiceMapper stressServiceMapper;

    @Override
    protected void fillQueryCondition(ScenarioQueryCondition condition, QueryWrapper<StressServiceDO> queryWrapper) {

    }

    @Override
    protected BaseMapper<StressServiceDO> getMapper() {
        return stressServiceMapper;
    }

    @Override
    public StressServiceDO createStressServiceOrUpdate(StressServiceBO stressServiceBO) {

        StressServiceDO stressServiceDO = stressServiceMapper.selectByServiceName(stressServiceBO.getServiceName());

        if (stressServiceDO != null && stressServiceDO.getId() > 0) {
            //update
            stressServiceDO.setUpdateTime(System.currentTimeMillis());
            stressServiceDO.setServiceName(stressServiceBO.getServiceName().trim());
            stressServiceDO.setStatus(stressServiceBO.getStatus());
            stressServiceDO.setDeleted(stressServiceBO.getDeleted());
            stressServiceDO.setServiceDesc(stressServiceBO.getServiceDesc());
            stressServiceDO.setUpdateTime(stressServiceBO.getUpdateTime());
            stressServiceDO.setCreateTime(stressServiceBO.getCreateTime());
            stressServiceMapper.updateById(stressServiceDO);
        } else {
            //create
            stressServiceDO = convertStressServiceBOToDO(stressServiceBO);
            stressServiceMapper.insertService(stressServiceDO);
        }
        return stressServiceDO;
    }
    private StressServiceDO convertStressServiceBOToDO(StressServiceBO stressServiceBO) {
        StressServiceDO stressServiceDO = new StressServiceDO();
        stressServiceDO.setServiceName(stressServiceBO.getServiceName().trim());
        stressServiceDO.setStatus(stressServiceBO.getStatus());
        stressServiceDO.setDeleted(stressServiceBO.getDeleted());
        stressServiceDO.setServiceDesc(stressServiceBO.getServiceDesc());
        stressServiceDO.setUpdateTime(stressServiceBO.getUpdateTime());
        stressServiceDO.setCreateTime(stressServiceBO.getCreateTime());
        return stressServiceDO;
    }
}
