package com.kuaishou.kwaishop.qa.risk.center.domain.feature.biz;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayAuditRiskEventResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRelateRiskEventRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayRiskDetailParam;

public interface KspayCheckInfoResourceService {
    List<KspayRiskDetailParam> queryRelateHistoryRiskEvent(KspayRelateRiskEventRequest request);
    String querySimpleRelateHistoryRiskEvent(KspayRelateRiskEventRequest request);
    KspayAuditRiskEventResponse getKspayAuditRiskEvent(KspayRelateRiskEventRequest request);


}
