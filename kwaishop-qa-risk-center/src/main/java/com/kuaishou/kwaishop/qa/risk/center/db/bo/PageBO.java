package com.kuaishou.kwaishop.qa.risk.center.db.bo;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageBO<T extends BaseDO> {

    private Integer pageNo;

    private Integer pageSize;

    private Long total;

    private List<T> data;

}
