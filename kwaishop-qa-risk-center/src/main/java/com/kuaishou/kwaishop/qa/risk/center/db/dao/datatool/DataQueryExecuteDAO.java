package com.kuaishou.kwaishop.qa.risk.center.db.dao.datatool;

import java.util.List;
import java.util.Map;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataQueryDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2022-11-14
 */
public interface DataQueryExecuteDAO {

    long insertDataQuery(DataQueryDO dataQuery);

    Long queryIdByDataSourceId(Long dataSourceId);

    int updateSelectiveById(DataQueryDO dataQuery);

    int logicDeleted(Long id, String operator);

    DataQueryDO findById(long id);

    DataQueryDO findByDataSourceId(long id);

    List<DataQueryDO> findAllList(Map<String, Object> param);

    void updateLog(Long id, String log);

    void clearLog(Long id);

    void updateResult(Long id, String result);

    void updateStatus(Long id, Integer status);


}
