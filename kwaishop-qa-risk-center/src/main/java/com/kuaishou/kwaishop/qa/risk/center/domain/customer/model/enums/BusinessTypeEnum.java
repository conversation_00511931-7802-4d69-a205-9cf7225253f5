package com.kuaishou.kwaishop.qa.risk.center.domain.customer.model.enums;


public enum BusinessTypeEnum {

    XIAODIAN(0, "商家客服"),
    SERVICEMARKET(2, "服务市场"),
    DISTRIBUTE(3, "商达团"),
    ;

    private final Integer code;

    private final String desc;

    BusinessTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static BusinessTypeEnum of(Integer code) {
        for (BusinessTypeEnum entityTypeEnum: values()) {
            if (entityTypeEnum.getCode().equals(code) && entityTypeEnum.getCode() > 0) {
                return entityTypeEnum;
            }
        }
        return null;
    }
}
