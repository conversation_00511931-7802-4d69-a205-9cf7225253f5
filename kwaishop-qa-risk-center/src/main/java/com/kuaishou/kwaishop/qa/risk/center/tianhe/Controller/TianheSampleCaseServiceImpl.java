package com.kuaishou.kwaishop.qa.risk.center.tianhe.Controller;



import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AddSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.AddSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.DeleteSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ExecuteFinishRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ExecuteFinishResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ExecuteSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ExecuteSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.KrpcTianheSampleCaseServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListHistoryExecuteRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListHistoryExecuteResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListSingleHistoryExecuteRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ListSingleHistoryExecuteResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ManualPassRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.ManualPassResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.QueryActionSetByIdRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.QueryActionSetByIdResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateSampleRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateSampleResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateValidActionSetRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe.UpdateValidActionSetResponse;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper.Sample.TianheSampleCaseMapper;
import com.kuaishou.kwaishop.qa.risk.center.tianhe.Service.TianheSampleCaseService;

import lombok.extern.slf4j.Slf4j;
/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-18
 */

@Slf4j
@Service
@KrpcService(server = "custom-servers-config", serviceName = "kwaishop-qa-risk-center")
public class TianheSampleCaseServiceImpl extends KrpcTianheSampleCaseServiceGrpc.TianheSampleCaseServiceImplBaseV2 {

    @Autowired
    private TianheSampleCaseService tianheSampleCaseService;

    @Autowired
    private TianheSampleCaseMapper tianheSampleCaseMapper;

    @Override
    public ListSampleResponse listSample(ListSampleRequest request) {
        return tianheSampleCaseService.listSampleResponse(request);
    }

    @Override
    public AddSampleResponse addSample(AddSampleRequest request) {
        return tianheSampleCaseService.addSampleResponse(request);
    }

    @Override
    public AddSampleResponse addAiSample(AddSampleRequest request) {
        return tianheSampleCaseService.addAiSampleResponse(request);
    }

    @Override
    public DeleteSampleResponse deleteSample(DeleteSampleRequest request) {
        return tianheSampleCaseService.deleteSampleResponse(request);
    }

    @Override
    public UpdateSampleResponse updateSample(UpdateSampleRequest request) {
        return tianheSampleCaseService.updateSampleResponse(request);
    }

    @Override
    public UpdateSampleResponse updateAiSample(UpdateSampleRequest request) {
        return tianheSampleCaseService.updateAiSampleResponse(request);
    }

    @Override
    public ExecuteSampleResponse executeSample(ExecuteSampleRequest request) {
        return tianheSampleCaseService.executeSampleResponse(request);
    }

    @Override
    public ListSingleHistoryExecuteResponse listSingleHistoryExecute(ListSingleHistoryExecuteRequest request) {
        return tianheSampleCaseService.listSingleHistoryExecuteResponse(request);
    }

    @Override
    public ListHistoryExecuteResponse listHistoryExecute(ListHistoryExecuteRequest request) {
        return tianheSampleCaseService.listHistoryExecuteResponse(request);
    }

    @Override
    public ExecuteFinishResponse executeFinish(ExecuteFinishRequest request) {
        return tianheSampleCaseService.executeFinishResponse(request);
    }

    @Override
    public ManualPassResponse manualPass(ManualPassRequest request) {
        return tianheSampleCaseService.manualPassResponse(request);
    }

    @Override
    public QueryActionSetByIdResponse queryActionSetById(QueryActionSetByIdRequest request) {
        return  tianheSampleCaseService.queryActionSetById(request);
    }

    /**
     * AI生成后的用例action通过UI自动化校验是否有效，回调这个接口更新用例
     * @param request id, actionSet
     * @return responseCode
     */
    @Override
    public UpdateValidActionSetResponse updateValidActionSet(UpdateValidActionSetRequest request) {
        return tianheSampleCaseService.updateValidActionSet(request);
    }
}
