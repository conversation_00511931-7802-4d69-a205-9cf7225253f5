package com.kuaishou.kwaishop.qa.risk.center.domain.strategy.convert;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.util.StringUtils;

import lombok.extern.slf4j.Slf4j;
import scala.collection.mutable.StringBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-30
 */
@Slf4j
public class StringOptUtils {

    public static final String REGEX1 = "\\{(.+?)\\}";

    /**
     * Convert a name in camelCase to an underscored name in lower case.
     * Any upper case letters are converted to lower case with a preceding underscore.
     *
     * @param name the original name
     * @return the converted name
     * @see #lowerCaseName
     * @since 4.2
     */
    public static String underscoreName(String name) {
        if (!StringUtils.hasLength(name)) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        result.append(lowerCaseName(name.substring(0, 1)));
        for (int i = 1; i < name.length(); i++) {
            String s = name.substring(i, i + 1);
            String slc = lowerCaseName(s);
            if (!s.equals(slc)) {
                result.append("_").append(slc);
            } else {
                result.append(s);
            }
        }
        return result.toString();
    }

    private static String lowerCaseName(String name) {
        return name.toLowerCase(Locale.US);
    }

    public static String valueOf(Object obj) {
        if (null == obj) {
            return null;
        }
        return obj.toString();
    }

    public static String emptyIfBlank(String str) {
        return org.apache.commons.lang3.StringUtils.defaultIfBlank(
                str, org.apache.commons.lang3.StringUtils.EMPTY);
    }

    public static String render(String template, Map<String, String> data, String regex) {
        if (StringUtils.isEmpty(template)) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }
        if (Objects.isNull(data)) {
            return template;
        }
        try {
            StringBuffer appendReplaceSb = new StringBuffer();
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(template);

            while (matcher.find()) {
                String key = matcher.group(1);
                String value = data.get(key);

                matcher.appendReplacement(appendReplaceSb, value);
            }
            matcher.appendTail(appendReplaceSb);
            return appendReplaceSb.toString();
        } catch (Exception e) {
            log.warn("render exception:", e);
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }
    }

    public static Map renderUrl2Map(String url) {
        Map<String, String> map = new HashMap<>();
        try {
            if (!StringUtils.isEmpty(url)) {
                String[] arr = url.split("&");
                for (String s : arr) {
                    if (s.split("=").length > 1) {
                        String key = s.split("=")[0];
                        String value = s.split("=")[1];
                        map.put(key, value);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("renderUrl2Map exception:", e);
        }
        return map;
    }

    public static String divisionCharacter(String name) {
        if (!StringUtils.hasLength(name)) {
            return "";
        }
        String result = name.substring(name.lastIndexOf("-") + 1);
        return result;
    }
}
