package com.kuaishou.kwaishop.qa.risk.center.db.dao.stress;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.BaseDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressScenarioBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.StressScenarioBO;

public interface StressScenarioDAO<StressScenarioDO extends BaseDO, CombineQueryParam>  {

    PageBO<StressScenarioDO> queryPageList(QueryStressScenarioBO bo);
    PageBO<com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioDO> queryPageListWithInterface(QueryStressScenarioBO bo);
    StressScenarioDO createStressScenarioOrUpdate(StressScenarioBO stressScenarioBO);
}
