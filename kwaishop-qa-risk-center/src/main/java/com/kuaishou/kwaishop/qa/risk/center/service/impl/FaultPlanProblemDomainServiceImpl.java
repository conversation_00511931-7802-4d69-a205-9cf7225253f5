package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.biz.FaultPlanProblemBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.CreateFaultPlanProblemRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.CreateFaultPlanProblemResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.DeleteFaultPlanProblemRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.DeleteFaultPlanProblemResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.FaultPlanProblemDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.KrpcFaultPlanProblemDomainServiceGrpc.FaultPlanProblemDomainServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.PageFaultPlanProblemDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultPlanProblemListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultPlanProblemListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultPlanProblemPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.QueryFaultPlanProblemPageListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateFaultPlanProblemRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.fault.UpdateFaultPlanProblemResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2024-01-18
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class FaultPlanProblemDomainServiceImpl extends FaultPlanProblemDomainServiceImplBaseV2 {

    @Autowired
    private FaultPlanProblemBizService faultPlanProblemBizService;

    @Override
    public CreateFaultPlanProblemResponse createFaultPlanProblem(CreateFaultPlanProblemRequest request) {
        log.info("[FaultPlanProblemDomainServiceImpl] createFaultPlanProblem request: {}", protoToJsonString(request));
        try {
            faultPlanProblemBizService.createFaultPlanProblem(request);
            return CreateFaultPlanProblemResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanProblemDomainServiceImpl] createFaultPlanProblem bizError, req: {}, exception: ", protoToJsonString(request), e);
            return CreateFaultPlanProblemResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanProblemDomainServiceImpl] createFaultPlanProblem error, req: {}, exception: ", protoToJsonString(request), e);
            return CreateFaultPlanProblemResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateFaultPlanProblemResponse updateFaultPlanProblem(UpdateFaultPlanProblemRequest request)  {
        log.info("[FaultPlanProblemDomainServiceImpl] updateFaultPlanProblem request: {}", protoToJsonString(request));
        try {
            faultPlanProblemBizService.updateFaultPlanProblem(request);
            return UpdateFaultPlanProblemResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanProblemDomainServiceImpl] updateFaultPlanProblem bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateFaultPlanProblemResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanProblemDomainServiceImpl] updateFaultPlanProblem error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateFaultPlanProblemResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public DeleteFaultPlanProblemResponse deleteFaultPlanProblem(DeleteFaultPlanProblemRequest request) {
        log.info("[FaultPlanProblemDomainServiceImpl] deleteFaultPlanProblem request: {}", protoToJsonString(request));
        try {
            faultPlanProblemBizService.deleteFaultPlanProblem(request);
            return DeleteFaultPlanProblemResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanProblemDomainServiceImpl] deleteFaultPlanProblem bizError, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteFaultPlanProblemResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanProblemDomainServiceImpl] deleteFaultPlanProblem error, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteFaultPlanProblemResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryFaultPlanProblemListResponse queryFaultPlanProblemList(QueryFaultPlanProblemListRequest request) {
        log.info("[FaultPlanProblemDomainServiceImpl] queryFaultPlanProblemList request: {}", protoToJsonString(request));
        try {
            List<FaultPlanProblemDTO> res = faultPlanProblemBizService.queryFaultPlanProblemList(request);
            return QueryFaultPlanProblemListResponse.newBuilder()
                    .addAllDetails(res)
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        }  catch (BizException e) {
            log.error("[FaultPlanProblemDomainServiceImpl] queryFaultPlanProblemList bizError, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultPlanProblemListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanProblemDomainServiceImpl] queryFaultPlanProblemList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultPlanProblemListResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public QueryFaultPlanProblemPageListResponse queryFaultPlanProblemPageList(QueryFaultPlanProblemPageListRequest request) {
        log.info("[FaultPlanProblemDomainServiceImpl] queryFaultPlanProblemPageList request: {}", protoToJsonString(request));
        try {
            PageFaultPlanProblemDTO res = faultPlanProblemBizService.queryFaultPlanProblemPageList(request);
            return QueryFaultPlanProblemPageListResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .setData(res)
                    .build();
        } catch (BizException e) {
            log.error("[FaultPlanProblemDomainServiceImpl] queryFaultPlanProblemPageList bizError, req: {}, exception: ",
                    protoToJsonString(request), e);
            return QueryFaultPlanProblemPageListResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[FaultPlanProblemDomainServiceImpl] queryFaultPlanProblemPageList error, req: {}, exception: ", protoToJsonString(request), e);
            return QueryFaultPlanProblemPageListResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }
}
