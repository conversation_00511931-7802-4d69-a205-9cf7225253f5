package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskJsonMapConfigKey.shopUiConfig;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.kess.KrpcKessShopUiServiceGrpc;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.kess.RerunRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.kess.ReturnResponse;
import com.kuaishou.kwaishop.qa.risk.center.utils.http.KessHttpUtils;

import lombok.extern.slf4j.Slf4j;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/10/14 21:23
 * @注释
 */
@Service
@Slf4j
@KrpcService(registry = "custom-registry-config-kess")
public class ShopUiRerunService extends KrpcKessShopUiServiceGrpc.KessShopUiServiceImplBaseV2 {

    @Override
    public ReturnResponse shopUiRerun(RerunRequest request) {
        rerun();
        return ReturnResponse.newBuilder()
                .setResult(BaseResultCode.SUCCESS_VALUE)
                .build();
    }

    public void rerun() {
        Map<String, Object> map = shopUiConfig.getMap(String.class, Object.class);

        String url = (String) map.get("listUrl");
        String json = ObjectMapperUtils.toJSON(map.get("listParam"));
        String cookie = "EGG_SESS=PiJyipX9WWXC28_9OIq52b4HP5tKj2R5m6H-8qWxtfpu0mVRgZZWiB4uFMJQc_Bm";

        Map<String, String> header = new HashMap<>();
        header.put("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_0) "
                + "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36");
        header.put("Accept", "application/json");
        header.put("Cookie", cookie);

        String res = KessHttpUtils.get(url, json, header);
        JsonObject jsonObject = new Gson().fromJson(res, JsonObject.class);
        JsonArray array = jsonObject.getAsJsonObject("data").get("list").getAsJsonArray();
        for (JsonElement element : array) {
            Long taskId = element.getAsJsonObject().get("id").getAsLong();
            log.info("rerun taskId:{}", taskId);
            int status = element.getAsJsonObject().get("failures").getAsInt();
            //如果包含失败任务
            if (status > 0) {
                String taskUrl = (String) map.get("taskUrl");
                String t = ObjectMapperUtils.toJSON(map.get("taskParam"));
                Map<String, Object> taskParam = ObjectMapperUtils.fromJson(t);
                taskParam.put("taskId", taskId);
                String taskJson = ObjectMapperUtils.toJSON(taskParam);
                String taskRes = KessHttpUtils.get(taskUrl, taskJson, header);

                JsonObject taskObject = new Gson().fromJson(taskRes, JsonObject.class);
                JsonArray datas = taskObject.getAsJsonArray("data");
                //仅重试错误case
                for (JsonElement data : datas) {
                    if (data.getAsJsonObject().get("failures").getAsInt() == 1) {
                        long execId = data.getAsJsonObject().get("id").getAsLong();
                        log.info("rerun failure execId:{}", execId);
                        String execUrl = (String) map.get("execUrl");
                        Map<String, Long> m = new HashMap<>();
                        m.put("execId", execId);

                        KessHttpUtils.postJson(execUrl, ObjectMapperUtils.toJSON(m), header);
                    }
                }

            }
        }

    }

}
