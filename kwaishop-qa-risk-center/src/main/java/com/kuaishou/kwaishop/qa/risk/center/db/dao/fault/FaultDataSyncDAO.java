package com.kuaishou.kwaishop.qa.risk.center.db.dao.fault;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultDataSyncDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultDataSyncBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-15
 */
public interface FaultDataSyncDAO {

    FaultDataSyncDO queryByUnique(Long centerId, Long teamId, String ksn);

    long insert(FaultDataSyncDO domainObject);

    List<FaultDataSyncDO> queryFaultDataSyncList(FaultDataSyncBO faultCaseBO);

    PageBO<FaultDataSyncDO> queryPageFaultDataSyncList(FaultDataSyncBO faultCaseBO);

    int updateSelectiveById(FaultDataSyncDO domainObject);

    FaultDataSyncDO queryById(Long id);
}
