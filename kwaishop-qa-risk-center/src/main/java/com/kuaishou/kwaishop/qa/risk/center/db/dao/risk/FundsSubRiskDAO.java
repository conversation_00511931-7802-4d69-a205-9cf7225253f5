package com.kuaishou.kwaishop.qa.risk.center.db.dao.risk;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.KspayPageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.detail.KspayDetailDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsSubRiskDO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.risk.FundsSubRiskQueryCondition;
import com.kuaishou.kwaishop.qa.risk.center.domain.combine.model.bo.CombineQueryParam;


/**
 * <AUTHOR>
 * Created on 2022-12-26
 */
public interface FundsSubRiskDAO extends KspayDetailDAO<FundsSubRiskDO, CombineQueryParam> {
    long insert(FundsSubRiskDO fundsSubRiskDO);

    KspayPageBO<FundsSubRiskDO> queryPageRiskDetailList(FundsSubRiskQueryCondition fundsSubRiskQueryCondition);

    List<FundsSubRiskDO> queryFundsSubRisk(FundsSubRiskQueryCondition fundsSubRiskQueryCondition);

    FundsSubRiskDO queryBySubRiskId(String subRiskId);

    int updateSelectiveById(FundsSubRiskDO fundsSubRiskDO);
    List<FundsSubRiskDO> getAllList();
}
