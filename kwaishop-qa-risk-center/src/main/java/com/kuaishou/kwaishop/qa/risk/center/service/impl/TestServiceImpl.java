package com.kuaishou.kwaishop.qa.risk.center.service.impl;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.service.impl.AccountRentApprovalServiceImpl;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.handler.service.impl.AccountTagAddApprovalServiceImpl;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserAccountService;
import com.kuaishou.kwaishop.qa.risk.center.domain.auth.service.AuthService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.EntityDataCalFactory;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.service.FaultDataSyncService;
import com.kuaishou.kwaishop.qa.risk.center.fetch.fault.KstableFetchService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.BPMTestRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.BPMTestResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.FixRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.FixResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.KrpcTestServiceGrpc.TestServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.TestRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.TestResponse;
import com.kuaishou.merchant.utils.ProtobufUtil;

import lombok.extern.slf4j.Slf4j;



/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-15
 */
@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class TestServiceImpl extends TestServiceImplBaseV2 {

    @Autowired
    private KstableFetchService kstableFetchService;

    @Autowired
    private FaultDataSyncService faultDataSyncService;

    @Autowired
    private AuthService authService;

    @Autowired
    private EntityDataCalFactory factory;

    @Autowired
    private UserAccountService userAccountService;

    @Autowired
    private AccountTagAddApprovalServiceImpl accountTagAddApprovalService;

    @Autowired
    private AccountRentApprovalServiceImpl accountRentApprovalService;

    @Override
    public TestResponse test(TestRequest request) {
        log.info("[TestServiceImpl] test request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            factory.getService(1).exec(request.getOperator());
//            KstableResponseBO res = kstableFetchService.getKstableRes("kwaishop-merchant-growth-center");
//            FaultDataSyncBO data = FaultDataSyncBO.builder()
//                    .centerId(1L)
//                    .teamId(20L)
//                    .operator("chenshengrui")
//                    .ksn(request.getOperator())
//                    .build();
//            faultDataSyncService.createSync(data);
            return TestResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            log.error("[TestServiceImpl] test bizError, exception: ", e);
            return TestResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[TestServiceImpl] test error, exception: ", e);
            return TestResponse.newBuilder()
                    .setResult(BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public FixResponse fixAccountContent(FixRequest request) {
        log.info("[TestServiceImpl] test request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            userAccountService.fixAccount(request.getFrom(), request.getTo());
            return FixResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            return FixResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        }
    }

    @Override
    public BPMTestResponse testBPM(BPMTestRequest request) {
        log.info("[TestServiceImpl] testBPM request: {}", ProtobufUtil.protoToJsonString(request));
        try {
            Map<String, Object> content = ObjectMapperUtils.fromJson(request.getBpm());
            accountRentApprovalService.execute(content);
            return BPMTestResponse.newBuilder()
                    .setResult(BaseResultCode.SUCCESS_VALUE)
                    .build();
        } catch (BizException e) {
            return BPMTestResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        }
    }
}
