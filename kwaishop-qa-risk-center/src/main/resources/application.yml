# ======    KsBoot 推荐配置        =====
# =====================================
# 开启 access log，日志位置：${LOG_HOME}/access.log
logging:
  access:
    enabled: true

spring: # 设置当前应用的名称和 BizDef，必填
  application:
    name: kwaishop-qa-risk-center
    use-standardized-name: false
    biz-def: KWAISHOP_MERCHANT_GROWTH

ksboot:
  krpc:
    enabled: true
    client-enabled: true
    servers:
      - id: custom-servers-config
        protocol: grpc
        shutdownSilenceWaitMs: 60000
    registries:
      - id: custom-registry-config-kess
        protocol: kess
        warmupSeconds: 210
  data-sources:
    - name: kwaishopQaRiskCenter
      for-candidate: kwaishopQaRiskCenterTest
    - name: kwaishopQaAccuracy

kwaishop:
  service:
    type: service

mybatis:
  configuration:
    map-underscore-to-camel-case: true