ksboot:
  krpc:
    enabled: true
    client-enabled: true
    servers:
      - id: custom-servers-config
        protocol: grpc
        shutdownSilenceWaitMs: 60000
    registries:
      - id: custom-registry-config-kess
        protocol: kess
        warmupSeconds: 210
  data-sources:
    - name: kwaishopQaRiskCenter
      for-candidate: kwaishopQaRiskCenterTest
    - name: kwaishopQaAccuracy

mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  db-config:
    logic-delete-field: deleted
    logic-delete-value: 1
    logic-not-delete-value: 0

mybatis:
  configuration:
    map-underscore-to-camel-case: true

# center服务需要用到的个性化配置
---
spring:
  profiles: center
  application:
    name: kwaishop-qa-risk-center
    use-standardized-name: false
    biz-def: KWAISHOP_QA_RISK

kwaishop:
  service:
    type: service

# task服务需要用到的个性化配置
---
spring:
  profiles: task
  application:
    name: kwaishop-qa-risk-center-task
    use-standardized-name: false
    biz-def: KWAISHOP_QA_RISK

kwaishop:
  service:
    type: task
