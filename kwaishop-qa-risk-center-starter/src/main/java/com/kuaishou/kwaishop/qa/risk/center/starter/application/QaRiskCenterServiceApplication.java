package com.kuaishou.kwaishop.qa.risk.center.starter.application;


import org.mybatis.spring.annotation.MapperScan;
import org.springframework.scheduling.annotation.EnableAsync;

import com.kuaishou.infra.boot.KsSpringApplicationBuilder;
import com.kuaishou.infra.boot.autoconfigure.KsBootApplication;
import com.kuaishou.krpc.config.spring.context.annotation.EnableKrpc;
import com.kuaishou.kwaishop.qa.risk.center.task.rocketmq.KwaishopApolloStrategyFlowRecallConsumer;


/**
 * <AUTHOR> <<EMAIL>>
 */

@EnableAsync
@KsBootApplication(scanBasePackages =
        {"com.kuaishou.kwaishop.qa.risk", "com.kuaishou.merchant.client.user.*",
                "com.kuaishou.kwaishop.sellerdata.management.service", "com.kuaishou.kwaishop.apollo.strategy.center",
                "com.kuaishou.kwaishop.shop.material.center", "com.kuaishou.kwaishop.cs.account.facade.client.service",
                "com.kuaishou.infra.passport.internal.sdk.service", "com.kuaishou.mmu.*", "com.kuaishou.kwaishop.biz.account.*"})
@MapperScan({"com.kuaishou.kwaishop.qa.risk.center.db.mapper", "com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper",
        "com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper", "com.kuaishou.kwaishop.qa.risk.center.aitest.mapper"})
@EnableKrpc(scanBasePackages = {"com.kuaishou.kwaishop.qa.risk.center.service.impl",
        "com.kuaishou.kwaishop.qa.risk.center.tianhe.Controller", "com.kuaishou.kwaishop.qa.risk.center.aitest.service"})
public class QaRiskCenterServiceApplication {

    public static void main(String[] args) {
        System.setProperty("spring.profiles.active", "center");
        KsSpringApplicationBuilder.just()
                .service(KwaishopApolloStrategyFlowRecallConsumer.class)
                .run(args);
    }

}