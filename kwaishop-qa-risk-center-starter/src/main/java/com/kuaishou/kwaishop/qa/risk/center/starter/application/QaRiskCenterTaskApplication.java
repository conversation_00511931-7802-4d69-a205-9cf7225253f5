package com.kuaishou.kwaishop.qa.risk.center.starter.application;

import org.mybatis.spring.annotation.MapperScan;

import com.kuaishou.infra.boot.KsSpringApplicationBuilder;
import com.kuaishou.infra.boot.autoconfigure.KsBootApplication;
import com.kuaishou.kwaishop.qa.risk.center.task.kafka.KspayQaKconfChangedEventConsumer;
import com.kuaishou.kwaishop.qa.risk.center.task.kafka.KspayQaRiskFeatureStateConsumer;
import com.kuaishou.kwaishop.qa.risk.center.task.kafka.KwaishopQaRiskDpmConsumer;
import com.kuaishou.kwaishop.qa.risk.center.task.kafka.KwaishopStrategyFlowRecallRealtimeKafkaConsumer;
import com.kuaishou.kwaishop.qa.risk.center.task.kafka.KwaishopStrategyRecallSparkConsumer;
import com.kuaishou.kwaishop.qa.risk.center.task.kbus.common.KwaishopQaRiskCommonLogResolver;
import com.kuaishou.kwaishop.qa.risk.center.task.kbus.entity.KwaishopQaRiskEntityResolver;
import com.kuaishou.kwaishop.qa.risk.center.task.kbus.fault.KwaishopQaRiskFaultDataSyncResolver;
import com.kuaishou.kwaishop.qa.risk.center.task.kbus.fault.KwaishopQaRiskFaultPlanRecordResolver;
import com.kuaishou.kwaishop.qa.risk.center.task.kbus.fault.KwaishopQaRiskFaultPlanResolver;
import com.kuaishou.kwaishop.qa.risk.center.task.rocketmq.KspayRiskKatFinishMqConsumer;
import com.kuaishou.kwaishop.qa.risk.center.task.rocketmq.KwaishopApolloStrategyFlowRecallConsumer;
import com.kuaishou.kwaishop.qa.risk.center.task.rocketmq.KwaishopLingzhuCicdMqConsumer;
import com.kuaishou.kwaishop.qa.risk.center.task.rocketmq.KwaishopQaRiskLogMqConsumer;
import com.kuaishou.kwaishop.qa.risk.center.task.rocketmq.KwaishopQaRiskTianheSampleMqConsumer;
import com.kuaishou.kwaishop.qa.risk.center.task.rocketmq.RiskDbFundFieldInsertMqConsumer;
import com.kuaishou.kwaishop.qa.risk.center.task.rocketmq.producer.TianheSampleMqProducer;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KspayQaConfigScanTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KspayQaRiskFeatureMarkedStatisticTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KspayQaRiskFeatureSendMessageTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KspayQaRiskFeatureViewBranchDataUpdateTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KspayQaRiskFeatureViewTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaiTestAccountReturnTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopQaAutoReturnAccount;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopQaDailyLogTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopQaDataCaseTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopQaDataSQLTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopQaEntityDataTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopQaFaultDataTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopQaFaultPlanStartTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopQaFaultReportCheckTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopQaFaultReportTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopQaFaultSceneUpdateTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopQaFaultSrcMethodRecommendTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopQaHourTimeDataTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopQaRealtimeDataTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopQaRiskDayLogTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopQaUpdateAuthTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopShopUiRerunTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopTagAutoDeleteTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.KwaishopUpdataAccountTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.LowProblemNotifySendTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.RiskDbAllFieldGenTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.RiskDbAuditRiskGenTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.RiskDbFieldGenTask;
import com.kuaishou.kwaishop.qa.risk.center.task.schedule.RiskMeasureScoreGenTask;


@KsBootApplication(scanBasePackages =
        {"com.kuaishou.kwaishop.qa.risk", "com.kuaishou.merchant.client.user.*",
                "com.kuaishou.kwaishop.apollo.strategy.center", "com.kuaishou.kwaishop.cs.account.facade.client.service",
                "com.kuaishou.infra.passport.internal.sdk.service", "com.kuaishou.mmu.*"})
@MapperScan({"com.kuaishou.kwaishop.qa.risk.center.db.mapper", "com.kuaishou.kwaishop.qa.risk.center.domain.strategy.db.mapper",
        "com.kuaishou.kwaishop.qa.risk.center.tianhe.Mapper", "com.kuaishou.kwaishop.qa.risk.center.aitest.mapper"})
public class QaRiskCenterTaskApplication {

    public static void main(String[] args) {
        System.setProperty("spring.profiles.active", "task");
        KsSpringApplicationBuilder.just()
                // binlog

                .service(KwaishopQaRiskEntityResolver.class)
                .service(KwaishopQaRiskFaultPlanResolver.class)
                .service(KwaishopQaRiskFaultDataSyncResolver.class)
                .service(KwaishopQaRiskFaultPlanRecordResolver.class)
                .service(KwaishopQaRiskCommonLogResolver.class)

                // mq_consumer
                .service(KwaishopQaRiskLogMqConsumer.class)
                .service(RiskDbFundFieldInsertMqConsumer.class)
                .service(KspayQaKconfChangedEventConsumer.class)
                .service(KwaishopApolloStrategyFlowRecallConsumer.class)
                .service(KwaishopStrategyFlowRecallRealtimeKafkaConsumer.class)
                .service(KwaishopStrategyRecallSparkConsumer.class)
                .service(KwaishopQaRiskDpmConsumer.class)
                .service(KspayQaRiskFeatureStateConsumer.class)
                .service(KspayRiskKatFinishMqConsumer.class)
                .service(KwaishopLingzhuCicdMqConsumer.class)
                .service(KwaishopQaRiskTianheSampleMqConsumer.class)
                .service(TianheSampleMqProducer.class)

                // task
                .service(KwaishopQaRiskDayLogTask.class)
                .service(KwaishopQaDailyLogTask.class)
                .service(KwaishopQaEntityDataTask.class)
                .service(KwaishopQaFaultPlanStartTask.class)
                .service(KspayQaRiskFeatureViewTask.class)
                .service(KwaishopQaFaultReportTask.class)
                .service(KwaishopQaFaultDataTask.class)
                .service(KspayQaRiskFeatureViewBranchDataUpdateTask.class)
                .service(KspayQaRiskFeatureSendMessageTask.class)
                .service(KspayQaRiskFeatureMarkedStatisticTask.class)
                .service(RiskDbFieldGenTask.class)
                .service(RiskDbAllFieldGenTask.class)
                .service(RiskDbAuditRiskGenTask.class)
                .service(KwaishopQaAutoReturnAccount.class)
                .service(KwaishopQaUpdateAuthTask.class)
                .service(KwaishopQaDataCaseTask.class)
                .service(KwaishopQaDataSQLTask.class)
                .service(KwaishopQaRealtimeDataTask.class)
                .service(KwaishopQaHourTimeDataTask.class)
                .service(RiskMeasureScoreGenTask.class)
                .service(KspayQaConfigScanTask.class)
                .service(KwaishopQaFaultSceneUpdateTask.class)
                .service(LowProblemNotifySendTask.class)
                .service(KwaishopShopUiRerunTask.class)
                .service(KwaishopQaFaultSrcMethodRecommendTask.class)
                .service(KwaishopQaFaultReportCheckTask.class)
                .service(KwaiTestAccountReturnTask.class)
                .service(KwaishopUpdataAccountTask.class)
                .service(KwaishopTagAutoDeleteTask.class)
                .run(args);
    }

}
