package com.kuaishou.test;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.errorcode.impl.ErrorCodeDAOImpl;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.errorcode.ErrorCodeDO;
import com.kuaishou.kwaishop.qa.risk.center.starter.application.QaRiskCenterServiceApplication;

@SpringBootTest(classes = {QaRiskCenterServiceApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@RunWith(SpringRunner.class)
public class ErrorCodeDAOImplTest {


    @Autowired
    private static ErrorCodeDAOImpl errorCodeDAO;


    @Test
    void insert(){

        ErrorCodeDO errorCodeDO = new ErrorCodeDO();

        errorCodeDO.setErrorCode("S-0003-0005");
        errorCodeDO.setErrorCodeDesc("S-0003-0005");
        errorCodeDO.setCreator("mojin");
        errorCodeDO.setCreateTime(System.currentTimeMillis());
        errorCodeDO.setUpdateTime(System.currentTimeMillis());
        errorCodeDAO.insert(errorCodeDO);


    };
    @Test
    void updateById(){
        ErrorCodeDO errorCodeDO = new ErrorCodeDO();
        errorCodeDO.setId(1l);
        errorCodeDO.setErrorCode("S-0003-0005");
        errorCodeDO.setErrorCodeDesc("S-0003-0005");
        errorCodeDO.setCreator("mojin");
        errorCodeDO.setCreateTime(System.currentTimeMillis());
        errorCodeDO.setUpdateTime(System.currentTimeMillis());
        errorCodeDAO.updateById(errorCodeDO);
    };

    void deleteById(Long id){
        errorCodeDAO.deleteById(id);
    };

    @Test
    void getErrorCodeList(){
        String creator = "mojin";
        Long current = 1L;
        Long size = 10L;
        errorCodeDAO.getErrorCodeList(creator, current, size);
    };

    @Test
    void getById(Long id){
        errorCodeDAO.deleteById(1L);
    };
    @Test
    void queryByErrorCode(){
        String errorCode = "";
        errorCodeDAO.queryByErrorCode(errorCode);

    };
}
