package com.kuaishou.test;

import java.util.Map;

import org.junit.Test;

import com.kuaishou.kconf.common.json.JsonMapperUtils;

import io.jsonwebtoken.lang.Assert;

public class ErrorCodeTest {

    @Test
    public void JsonParasTest() {
        String json = "{\n"
                + "   \"min_position\": 4,\n"
                + "   \"has_more_items\": true,\n"
                + "   \"items_html\": \"Bike\",\n"
                + "   \"new_latent_count\": 2,\n"
                + "   \"data\": {\n"
                + "      \"length\": 25,\n"
                + "      \"text\": \"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor "
                + "incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation "
                + "ullamco laboris nisi ut aliquip ex ea commodo consequat.\"\n"
                + "   },\n"
                + "   \"numericalArray\": [\n"
                + "      25,\n"
                + "      24,\n"
                + "      24,\n"
                + "      26,\n"
                + "      30\n"
                + "   ],\n"
                + "   \"StringArray\": [\n"
                + "      \"Nitrogen\",\n"
                + "      \"Oxygen\",\n"
                + "      \"Nitrogen\",\n"
                + "      \"Oxygen\"\n"
                + "   ],\n"
                + "   \"multipleTypesArray\": true,\n"
                + "   \"objArray\": [\n"
                + "      {\n"
                + "         \"class\": \"upper\",\n"
                + "         \"age\": 4\n"
                + "      },\n"
                + "      {\n"
                + "         \"class\": \"upper\",\n"
                + "         \"age\": 5\n"
                + "      },\n"
                + "      {\n"
                + "         \"class\": \"middle\",\n"
                + "         \"age\": 3\n"
                + "      },\n"
                + "      {\n"
                + "         \"class\": \"middle\",\n"
                + "         \"age\": 3\n"
                + "      },\n"
                + "      {\n"
                + "         \"class\": \"lower\",\n"
                + "         \"age\": 7\n"
                + "      }\n"
                + "   ]\n"
                + "}";

        Map<String, Object> stringObjectMap = JsonMapperUtils.fromJson(json);
        Object minPosition = stringObjectMap.get("min_position");
        Assert.isInstanceOf(Integer.class, minPosition);
        Object has_more_items = stringObjectMap.get("has_more_items");
        Assert.isInstanceOf(Boolean.class, has_more_items);
        Object items_html = stringObjectMap.get("items_html");
        Assert.isInstanceOf(String.class, items_html);
    }

}
