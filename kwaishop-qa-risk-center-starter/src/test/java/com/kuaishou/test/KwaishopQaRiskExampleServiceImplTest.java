package com.kuaishou.test;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.FundsRiskFeatureDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.query.feature.FundsRiskFeatureStatesUpdateCondition;
import com.kuaishou.kwaishop.qa.risk.center.starter.application.QaRiskCenterServiceApplication;

/**
 * <AUTHOR> <<EMAIL>>
 */
//@Slf4j
@SpringBootTest(classes = {QaRiskCenterServiceApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@RunWith(SpringRunner.class)
public class KwaishopQaRiskExampleServiceImplTest {

    /**
     * 调用示例
     */

    @Autowired
    private static FundsRiskFeatureDAO fundsRiskFeatureDAO;

    @Test
    public void test() {
        FundsRiskFeatureStatesUpdateCondition fundsRiskFeatureStatesUpdateCondition = FundsRiskFeatureStatesUpdateCondition.builder()
                .featureId("36694")
                .status(9)
                .updateTime(System.currentTimeMillis())
                .build();
        fundsRiskFeatureDAO.updateFundsRiskFeatureViewState(fundsRiskFeatureStatesUpdateCondition);
    }


}
