package com.kuaishou.test;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.errorcode.BizAreaDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.errorcode.BizAreaDO;
import com.kuaishou.kwaishop.qa.risk.center.starter.application.QaRiskCenterServiceApplication;

import io.jsonwebtoken.lang.Assert;

@SpringBootTest(classes = {QaRiskCenterServiceApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@RunWith(SpringRunner.class)
public class BizAreaDAOImplTest {

    @Autowired
    private static BizAreaDAO bizAreaDAO;

    @Test
    void testInsert() {
        BizAreaDO bizAreaDO = new BizAreaDO();
        bizAreaDO.setId(System.currentTimeMillis());
        bizAreaDO.setAreaCode("S-3003-" + System.currentTimeMillis());
        bizAreaDO.setAreaCodeDesc("test");
        bizAreaDO.setOwner("mojin");
        bizAreaDO.setCreator("mojin");
        bizAreaDO.setCreateTime(System.currentTimeMillis());
        bizAreaDO.setUpdateTime(System.currentTimeMillis());
        bizAreaDAO.insert(bizAreaDO);
    }

    @Test

    void testGetById() {
        bizAreaDAO.getById(1L);
    }


    @Test
    void updateById(){
        BizAreaDO bizAreaDO = new BizAreaDO();
        bizAreaDO.setId(1L);
        bizAreaDO.setAreaCode("S-3003-" + System.currentTimeMillis());
        bizAreaDO.setAreaCodeDesc("test");
        bizAreaDO.setOwner("mojin");
        bizAreaDO.setCreator("mojin");
        bizAreaDO.setCreateTime(System.currentTimeMillis());
        bizAreaDO.setUpdateTime(System.currentTimeMillis());
        bizAreaDAO.updateById(bizAreaDO);
    };

    @Test
    void deleteById(){
        bizAreaDAO.deleteById(1L);
    }
    @Test
    void queryByBizArea(){
        String areaCode = "test";
        BizAreaDO bizAreaDO = bizAreaDAO.queryByBizArea(areaCode);
        Assert.notNull(bizAreaDO);
    }

    @Test
    void getBizAreaList(){
        String creator = "mojin";
        Long current = 1L;
        Long size = 10L;
        bizAreaDAO.getBizAreaList(creator, current, size);

    };
}
