syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.jenkins";
option java_outer_classname = "JenkinsTriggerServiceProto";

message JenkinsRequest {

  string source_name = 1;

}

message JenkinsResponse {

  int32 result = 1;

  string error_msg = 2;
}

service JenkinsTriggerService {
  rpc Execute (JenkinsRequest) returns (JenkinsResponse);
}