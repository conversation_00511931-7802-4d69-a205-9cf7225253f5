syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.entity";
option java_outer_classname = "EntityExtServiceProto";


message QueryTeamListRequest {

  int64 id = 1;

  int64 center_id = 2;

  string name = 3;

  string leader = 4;

  string operator = 99;
}

message QueryTeamNamesRequest {

  string operator = 1;
}

message TeamInfo {

  int64 id = 1;

  int64 center_id = 2;

  string center_name = 3;

  string name = 4;

  string leader = 5;

  string creator = 6;

  string modifier = 7;

  int64 create_time = 8;

  int64 update_time = 9;

  string member_names = 10;

  string uic = 11;

}

message QueryTeamListResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated TeamInfo data = 3;

}

message QueryTeamPageListRequest {

  int64 id = 1;

  int64 center_id = 2;

  int32 entity_type = 3;

  string name = 4;

  string leader = 5;

  int32 page_no = 6;

  int32 page_size = 7;

  string operator = 99;

}

message PageTeamInfo {

  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated TeamInfo team_info = 4;

}

message QueryTeamPageListResponse {

  int32 result = 1;

  string error_msg = 2;

  PageTeamInfo data = 3;

}

message UpdateMembersRequest {

  string operator = 1;

  string names = 2;

  int64 team_id = 3;
}

message UpdateMembersResponse {

  int32 result = 1;

  string error_msg = 2;
}

message QueryMemberPageListRequest {

  int64 id = 1;

  int64 center_id = 2;

  string name = 3;

  int64 team_id = 4;

  string operator = 5;

  int32 page_no = 6;

  int32 page_size = 7;

}

message QueryMemberPageListResponse {

  int32 result = 1;

  string error_msg = 2;

  PageMemberInfo data = 3;
}

message QueryTeamNamesResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated TeamInfoMap team_info = 3;
}

message TeamInfoMap {

  string id = 1;

  string name = 2;
}

message MemberInfo {

  int64 id = 1;

  string name = 2;

  int64 center_id = 3;

  string center_name = 4;

  int64 team_id = 5;

  string team_name = 6;

  int64 create_time = 7;

  int32 status = 8;
}

message PageMemberInfo {

  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated MemberInfo member_info = 4;

}

service EntityExtService {

  rpc QueryTeamList (QueryTeamListRequest) returns (QueryTeamListResponse);

  rpc QueryTeamPageList (QueryTeamPageListRequest) returns (QueryTeamPageListResponse);

  rpc UpdateMembers (UpdateMembersRequest) returns (UpdateMembersResponse);

  rpc QueryMemberPageList (QueryMemberPageListRequest) returns (QueryMemberPageListResponse);

  rpc GetTeamNames (QueryTeamNamesRequest) returns (QueryTeamNamesResponse);
}
