syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.entity";
option java_outer_classname = "EntityServiceProto";


message CreateEntityRequest {

  string operator = 1;

  int64 entity_id = 2;

  int32 entity_type = 3;

  string name = 4;

  int32 status = 5;

  string ext = 6;

  string extra1 = 7;

  string extra2 = 8;

  string extra3 = 9;
}

message CreateEntityResponse {

  int32 result = 1;

  string error_msg = 2;
}

message EntityPBDTO {

  int64 id = 1;

  int64 entity_id = 2;

  int32 entity_type = 3;

  string name = 4;

  int32 status = 5;

  string ext = 6;

  string extra1 = 7;

  string extra2 = 8;

  string extra3 = 9;

  string creator = 10;

  string modifier = 11;

  int64 create_time = 12;

  int64 update_time = 13;
}

message UpdateEntityRequest {

  int64 id = 1;

  int64 entity_id = 2;

  int32 entity_type = 3;

  string name = 4;

  int32 status = 5;

  string ext = 6;

  string extra1 = 7;

  string extra2 = 8;

  string extra3 = 9;

  string operator = 12;

}

message UpdateEntityResponse {

  int32 result = 1;

  string error_msg = 2;
}

message DeleteEntityRequest {

  string operator = 1;

  int64 id = 2;

  int32 entity_type = 3;

}

message DeleteEntityResponse {

  int32 result = 1;

  string error_msg = 2;

}

message QueryEntityListRequest {

  int64 id = 1;

  int64 entity_id = 2;

  int32 entity_type = 3;

  string name = 4;

  int32 status = 5;

  string ext = 6;

  string extra1 = 7;

  string extra2 = 8;

  string extra3 = 9;

  string operator = 10;

}


message QueryEntityListResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated EntityPBDTO data = 3;
}


message QueryEntityPageListRequest {

  int64 id = 1;

  int64 entity_id = 2;

  int32 entity_type = 3;

  string name = 4;

  int32 status = 5;

  string ext = 6;

  string extra1 = 7;

  string extra2 = 8;

  string extra3 = 9;

  string operator = 10;

  int32 page_no = 11;

  int32 page_size = 12;

}

message EntityPBPageDTO {

  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated EntityPBDTO entity_infos = 4;

}

message QueryEntityPageListResponse {

  int32 result = 1;

  string error_msg = 2;

  EntityPBPageDTO data = 3;

}

message EntityDataDTO {

  int64 id = 1;

  /**
   * 实体类型
   */
  int32 entity_type = 2;

  /**
   * 实体id
   */
  int64 entity_id = 3;

  /**
   * 实体名称
   */
  string name = 4;

  /**
   * 日期
   */
  string dt = 5;

  /**
   * 数据详情
   */
  string data_info = 6;
}


service EntityDomainService {

  rpc CreateEntity (CreateEntityRequest) returns (CreateEntityResponse);

  rpc UpdateEntity (UpdateEntityRequest) returns (UpdateEntityResponse);

  rpc DeleteEntity (DeleteEntityRequest) returns (DeleteEntityResponse);

  rpc QueryEntityList (QueryEntityListRequest) returns (QueryEntityListResponse);

  rpc QueryEntityPageList (QueryEntityPageListRequest) returns (QueryEntityPageListResponse);
}
