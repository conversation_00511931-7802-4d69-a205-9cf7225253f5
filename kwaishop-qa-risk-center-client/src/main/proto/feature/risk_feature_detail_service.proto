syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

import "risk/kspay_funds_risk_feature_view_service.proto";
import "risk/kspay_risk_detail_service.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.feature";
option java_outer_classname = "KspayRiskFeatureDetailProto";

// 查询feature列表

message QueryPageRiskFeatureListRequest {
  string department = 1;

  string status = 2;

  string feature_name = 3;

  string team_worker = 4;

  int32 page_no = 5;

  int32 page_size = 6;

  string creator = 7;  //cookies 获取

  int32 is_risk = 8;  // 是否有资损风险

  string feature_id = 9;  // 新增, 前端会传入
  int64 start_time = 10;
  int64 end_time = 11;
  bool is_from_platform_task = 12;  // 请求是否来自platform的task
  int32 test_type = 13;  // 提测任务类型
  string business_domain = 14;  // 业务域
  bool is_query_by_create_time = 15;  // 是否根据创建时间查询

}

message QueryPageRiskFeatureListResponse {

  int32 result = 1;

  string error_msg = 2;

  PageKspayRiskFeatureListDTO data = 3;
}

message PageKspayRiskFeatureListDTO {

  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated KspayRiskFeatureListParam risk_feature_list = 4;

  int32 view_type = 5;
}

message KspayRiskFeatureListParam {
  // funds_risk_feature_view: feature视图基本信息

  string id = 1;  // 主键id

  string department = 2;  // 所属部门中心

  string business_domain = 3;  // 所属业务域

  string feature_id = 4;  // feature_id

  string feature_name = 5;  // feature视图名字

  string team_id = 6;  // team_id

  string risk_coe = 7;  // 风险系数

  int32 risk_pass_status = 8;  // 6.25-add 资金风险准出状态

  string team_name = 9;  // team_name

  string team_worker = 10;  // 参与人

  int32 status = 11;  // 当前状态

  int64 create_time = 12;  // 创建时间

  int64 update_time = 13;  // 更新时间

  string creator = 14;  // 创建人

  string updater = 15;  // 修改人

  repeated KspayRiskFeatureDetail kspay_risk_feature_detail = 16;  // feature详情

  string score = 17; // 风险分数

  int32 is_risk = 18;  // 是否有资损风险
  string extra_data = 19;  // 打标扩展字段
  int32 test_type = 20;  // 提测任务类型
}

// 查询feature详情

message QueryRiskFeatureDetailRequest {
  //  string id = 1;  // 主键

  string feature_id = 2;  // feature_id
}

// 根据id查询feature详情
message QueryRiskFeatureDetailByIdRequest {
  string id = 1;  // 主键
}

// 自定义查询条件
message  KspayBranchAndViewIdRequest{
  string  repo_name    = 1; // 应用名称
  string branch_name   = 2; //分支名
  string feature_view_id   = 3; //view_id
}

message QueryRiskFeatureDetailResponse {
  int32 result = 1;

  string error_msg = 2;

  repeated KspayRiskFeatureDetail data = 3;
}

message KspayRiskFeatureDetail {
  // funds_risk_feature_view_branch: 分支风险信息
  string id = 1;  // 子表主键id

  int32 feature_view_id = 2;  // 视图基本信息id

  string feature_id = 3;  // 视图id

  string repo_name = 4;  // 应用名称

  string branch_name = 5;  // 分支名

  string method_entrance = 6;  // 方法入口, 查询精准平台

  string accuracy_url = 7;  // 精准平台调整地址, 查询精准平台

  int32 change_method_count = 8;  // 改动方法数

  int32 fund_risk_method_count = 9;  // 资金风险涉及方法数

  string test_result = 10;  // 测试结果, 查询精准平台

  int32 risk_status = 11;  // 6.25-add 资金风险测试结果

  int32 audit_cover = 12;  // 6.25-add 核对是否覆盖

  int64 create_time = 13;  // 创建时间

  int64 update_time = 14;  // 更新时间

  string creator = 15;  // 创建人

  string updater = 16;  // 修改人

  string risk_status_updater = 17;  // 6.25-add 资金风险测试结果标记人

  string audit_cover_updater = 18;  // 6.25-add 核对是否覆盖标记人

  string risk_table_fields = 19;  // 资损表&字段

  string score = 20;

  string check_chains = 21;

  string kat_record = 22;  // kat自动化数据
}

// 更新feature_view
message UpdateFeatureViewResponse {
  int32 result = 1;

  string error_msg = 2;
}

// 更新feature_view_branch
message UpdateFeatureViewBranchResponse {
  int32 result = 1;

  string feature_id = 2;

  string error_msg = 3;
}

// feature_view参数
message KspayRiskFeatureViewParam {
  // funds_risk_feature_view: feature视图基本信息

  string id = 1;  // 主键id

  string department = 2;  // 所属部门中心

  string business_domain = 3;  // 所属业务域

  string feature_id = 4;  // feature_id

  string feature_name = 5;  // feature视图名字

  string team_id = 6;  // team_id

  string risk_coe = 7;  // 风险系数

  int32 risk_pass_status = 8;  // 6.25-add 资金风险准出状态

  string team_name = 9;  // team_name

  string team_worker = 10;  // 参与人

  int32 status = 11;  // 当前状态

  int64 create_time = 12;  // 创建时间

  int64 update_time = 13;  // 更新时间

  string creator = 14;  // 创建人

  string updater = 15;  // 修改人

  KspayRiskUserMarkInfo extra_data = 16;  // 扩展字段

  int32 test_type = 17;  // 提测任务类型

  int32 online_confirm = 18;  // 上线后要求确认项
}

// 主表用户打标数据字段
message KspayRiskUserMarkInfo {
  int32 is_valid_risk_find = 1;  // 风险识别是否有效-全部，部分，无
  bool is_check_info_valid = 2;  // 是否有修改新增对账信息
  bool is_add_kat_case = 3;  // 是都新增自动化
  bool is_find_risk = 4;  // 是否有资损未识别
  bool is_has_drill = 5;  // 是否有进行演练
  bool is_update_drill = 6;  // 对账是否更新
  int32 add_kat_num = 7;  // 新增自动化个数
  //  bool is_valid_risk = 8;  // 有资损风险是否有效【已有】
  bool is_has_reinforced = 8;  // 有效风险是否有加固
}

// 根据仓库和分支名查询
message KspayRiskFeatureDetailSimple{
  string id = 1;  // 子表主键id

  int32 feature_view_id = 2;  // 视图基本信息id

  int32 feature_id = 3;  // 视图id

  string repo_name = 4;  // 应用名称

  string branch_name = 5;  // 分支名

  string method_entrance = 6;  // 方法入口, 查询精准平台

  string accuracy_url = 7;  // 精准平台调整地址, 查询精准平台

  int32 change_method_count = 8;  // 改动方法数

  int32 fund_risk_method_count = 9;  // 资金风险涉及方法数

  string test_result = 10;  // 测试结果, 查询精准平台

  int32 risk_status = 11;  // 6.25-add 资金风险测试结果

  int32 audit_cover = 12;  // 6.25-add 核对是否覆盖

  repeated MethodCovInfo method_cov_infos = 13;

  string risk_table_fields = 14;  // 资损表和字段
}

message KspayQueryRiskByRepoAndBranchResponse{
  int32 result = 1;
  string error_msg = 2;
  repeated KspayRiskFeatureDetailSimple data = 3;
}

// 关联存量风险接口request
message KspayRelateRiskEventRequest{
  string  repo_name    = 1; // 应用名称 (对应对账接口的application)
  string branch_name   = 2; //分支名
}

message KspayRelateRiskEventResponse{
  int32 result = 1;
  string error_msg = 2;
  repeated KspayRiskDetailParam sub_risk_list = 3;
}

message KspaySimpleRelateRiskEventResponse{
  int32 result = 1;
  string error_msg = 2;
  string sub_risk_name = 3;
}

// 对账接口请求参数【未用】

message CheckInfoTables{
  string table_name = 1;
  repeated string fields_list = 2;
}

message RiskFieldCheckInfoParam{
  repeated CheckInfoTables check_info_tables = 1;
  string application = 2;
}

message CheckInfoResponse{
  string code = 1;
  repeated TableData data = 2;
  string host = 3;
  int64 port = 4;
  string time_stamp = 5;
}

message TableData{
  string table_name = 1;
  repeated FieldCheckInfoList field_check_info_list = 2;
}

message FieldCheckInfoList{
  string field = 1;
  repeated CheckInfo check_list = 2;
}

message CheckInfo{
  string check_id = 1;
  string check_name = 2;
}







// 根据仓库和分支名查询
message KspayRiskFeatureDetailSimpleV2{
  string fail_reason = 1;

  string view_url_title = 2;

  string view_url = 3;

  string view_content = 4;
  //
  //  string repo_name = 1;  // 应用名称
  //
  //  string branch_name = 2;  // 分支名
  //
  //  string accuracy_url = 3;  // 精准平台调整地址, 查询精准平台
  //
  //  int32 change_method_count = 8;  // 改动方法数
  //
  //  int32 fund_risk_method_count = 9;  // 资金风险涉及方法数
  //
  //  string test_result = 10;  // 测试结果, 查询精准平台
  //
  //  int32 risk_status = 11;  // 6.25-add 资金风险测试结果
  //
  //  int32 audit_cover = 12;  // 6.25-add 核对是否覆盖
  repeated AssertReportList assert_report_list = 5;

  string kdev_notice_users = 6;  //资损测试结果通知对应的测试同学

  string kdev_notice_content = 7;
}

message AssertReportList{
  string title = 1;
  string url = 2;
}



message KspayQueryRiskByRepoAndBranchResponseV2{
  int32 status = 1;
  string message = 2;
  KspayRiskFeatureDetailSimpleV2 data = 3;
}

message KspayAuditRiskEventResponse{
  int32 result = 1;
  repeated CheckInfo check_list = 2;
  repeated RiskEventList risk_event_list = 3;
}

message RiskEventList{
  string risk_name = 1;
  string sub_risk_id_link = 2;
}


message GetRiskEntryAndFieldsStatisticRequest {
  int64 start_time = 1;
  int64 end_time = 2;
  bool is_repo_duplicate = 3;  // 仓库是否去重统计
  bool is_method_duplicate = 4;  // 方法是否去重统计
  bool is_field_duplicate = 5;  // 风险字段是否去重统计
  int32 is_risk = 6;  // 是否有资损
  string department = 7;
}

message GetRiskEntryAndFieldsStatisticResponse {
  int32 result = 1;

  int32 kspay_all_entry = 2;

  int32 kspay_all_fields = 3;
  int32 kspay_all_repo = 4;
}

message KdevAccuracyMsgSendRequest {
  string repo_name = 1;
  string branch_name = 2;
}

message KdevAccuracyMsgSendResponse {
  int32 result = 1;
  string error_msg = 2;
}

// 统计接口
message PageQueryRiskFeatureStatisticRequest {
  int32 page_no = 1;
  int32 page_size = 2;
  string feature_name = 3;
  int64 start_time = 4;
  int64 end_time = 5;
  int32 is_risk = 6;
  repeated int32 status = 7;  // 需求状态
  int32 risk_pass_status = 8;  // 是否打标
  string department = 9;
  string business_domain = 10;
}

message RiskFeatureStatisticResponse {
  int32 result = 1;
  string err_msg = 2;
  repeated FeatureStatisticInfo feature_statistic_info = 3;
  int64 total_count = 4;
  int32 page_no = 5;
  int32 page_size = 6;
}

message FeatureStatisticInfo {
  string feature_name = 1;
  string qa_list = 2;
  int32 risk_pass_status = 3;
  int32 is_risk = 4;
  int64 update_time = 5;
  KspayRiskUserMarkInfo risk_user_mark_info = 6;
  int32 status = 7;  // 需求状态
  string department = 8;
  string business_domain = 9;
}

message QueryCheckChainsById {
  int64 chain_id = 1;
}

message QueryCheckChainsByIdResponse {
  int32 status = 1;
  string message = 2;
  string data = 3;
}

message MarkIfChainsValid {
  int64 rule_id = 1;
  string rule_content = 2;
  int32 is_valid = 3;
  string repo_name = 4;
  string branch_name = 5;
  int64 feature_view_id = 6;
  int64 chain_id = 7;
}

message MarkIfChainsValidResponse {
  int32 status = 1;
  string message = 2;
  string data = 3;
}

message KspayQueryCheckChainsInfo {
  string repo_name = 1;
  string branch_name = 2;
  int64 feature_view_id = 3; //123用于查库
  string risk_table_fields = 4;
}

message KspayQueryCheckInfoListRequest {
  repeated KspayQueryCheckChainsInfo check_chains_info = 1;
}

message CheckChainsList {
  string repo_name = 1;
  repeated ChainDetails check_chains = 2;
}
message ChainDetails {
  int64 chain_id = 1;
  string chain_key = 2;
  string chain_name = 3;
  int32 chain_state = 4;
  int64 update_time = 5;
  string rule_is_valid = 6;
}
message KspayQueryCheckInfoResponse {
  string result = 1;
  string error_msg = 2;
  repeated CheckChainsList check_chains_map = 3;
}

// 获取智能规则推荐数据统计
message QueryCheckChainsStatisticRequest {
  string department = 1;
  string status = 2;
  string feature_name = 3;
  string team_worker = 4;
  int32 is_risk = 5;  // 是否有资损风险
  string feature_id = 6;  // 新增, 前端会传入
  int64 start_time = 7;
  int64 end_time = 8;
  string business_domain = 9;  // 业务域
}

message QueryCheckChainsStatisticResponse {
  int32 code = 1;
  string message = 2;
  repeated CheckChainsList data = 3;
  int64 total_count = 4;
}
service KspayRiskFeatureDetailService {
  rpc KspayQueryPageRiskFeatureList (QueryPageRiskFeatureListRequest) returns (QueryPageRiskFeatureListResponse);

  rpc KspayQueryRiskFeatureDetail (QueryRiskFeatureDetailRequest) returns (QueryRiskFeatureDetailResponse);

  rpc KspayQueryRiskFeatureDetailById (QueryRiskFeatureDetailByIdRequest) returns (QueryRiskFeatureDetailResponse);

  rpc KspayUpdateFeatureView (KspayRiskFeatureViewParam) returns (UpdateFeatureViewResponse);

  rpc KspayUpdateFeatureViewBranch (KspayRiskFeatureDetail) returns (UpdateFeatureViewBranchResponse);

  rpc KspayUpdateFeatureViewBranchAllStatus (KspayRiskFeatureDetail) returns (UpdateFeatureViewBranchResponse);

  rpc KspayUpdateBranchAllStatusByFeatureId (KspayRiskFeatureDetail) returns (UpdateFeatureViewBranchResponse);

  rpc KspayQueryRiskByRepoAndBranchName (KspayBranchRequest) returns (KspayQueryRiskByRepoAndBranchResponse);

  rpc KspayQueryRiskByRepoAndBranchNameV2 (KspayBranchRequestV2) returns (KspayQueryRiskByRepoAndBranchResponseV2);

  rpc KspayQueryRiskByRepoAndBranchAndViewId (KspayBranchAndViewIdRequest) returns (QueryRiskFeatureDetailResponse);

  rpc KspayRelateHistoryRiskEvent (KspayRelateRiskEventRequest) returns (KspayRelateRiskEventResponse);

  rpc KspaySimpleRelateHistoryRiskEvent (KspayRelateRiskEventRequest) returns (KspaySimpleRelateRiskEventResponse);

  rpc KspayAuditRiskEvent(KspayRelateRiskEventRequest) returns (KspayAuditRiskEventResponse);

  rpc GetKspayRiskStatistic(GetRiskEntryAndFieldsStatisticRequest) returns (GetRiskEntryAndFieldsStatisticResponse);

  rpc KspayKdevAccuracyMsgSend(KdevAccuracyMsgSendRequest) returns (KdevAccuracyMsgSendResponse);

  rpc KspayPageQueryRiskFeatureStatistic (PageQueryRiskFeatureStatisticRequest) returns (RiskFeatureStatisticResponse);

  rpc KspayQueryCheckChainsById (QueryCheckChainsById) returns (QueryCheckChainsByIdResponse);

  rpc KspayMarkIfChainsValid (MarkIfChainsValid) returns (MarkIfChainsValidResponse);

  rpc KspayQueryCheckInfos(KspayQueryCheckInfoListRequest) returns (KspayQueryCheckInfoResponse);
  rpc KspayUpdateFeatureViewTestType (KspayRiskFeatureViewParam) returns (UpdateFeatureViewResponse);
  rpc KspayQueryCheckChainsStatistic (QueryCheckChainsStatisticRequest) returns (QueryCheckChainsStatisticResponse);
}