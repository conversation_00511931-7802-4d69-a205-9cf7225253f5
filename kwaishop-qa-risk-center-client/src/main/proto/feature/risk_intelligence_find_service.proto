syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.feature";
option java_outer_classname = "KspayRiskIntelligenceFindProto";

// 查询风险表、字段
message QueryPageRiskFieldsRequest {
  string biz_def = 1;  // 业务域

  string source_name = 2;  // 数据源

  string table_name = 3;  // 表名

  string column_name = 4;  // 列名

  string symbol = 5;  // 特征值

  int32 page_no = 6;  // 页码

  int32 page_size = 7;  // 页数

  string creator = 8;  //cookies 获取
  
  string biz_code = 9;

  string risk_level = 10;  // 风险等级
}

message QueryPageRiskFieldsResponse {
  int32 result = 1;

  string error_msg = 2;

  PageKspayRiskIntelligenceFindListDTO data = 3;
}

message PageKspayRiskIntelligenceFindListDTO {

  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated KspayRiskIntelligenceFindListParam risk_intelligence_list = 4;
}

message KspayRiskIntelligenceFindListParam {
  string id = 1;  // 主键id

  string biz_def = 2;  // 业务域

  string source_name = 3;  // 数据源

  string data_base = 4;  // 数据库

  string table_name = 5;  // 表名

  string column_name = 6;  // 列名

  string risk_level = 7;  // 风险等级

  string extra_info = 8;  // 扩展信息

  string symbol = 9;  // 特征值

//  int64 is_marked = 10;  // 是否已标记-todo 是否需要
  string old_table_name = 10;  // 分表前原表名

  int64 has_audit = 11;  // 对账是否覆盖

  int64 create_time = 12;  // 创建时间

  int64 update_time = 13;  // 更新时间

  string creator = 14;  // 创建人

  string updater = 15;  // 修改人
  string biz_code = 16;  // 业务code
}

message KspayUpdateMarkedForAllFieldsRequest {
  // funds_all_fields 字段全表
  string id = 1;  // 主键id

  string biz_def = 2;

  string source_name = 3;

  string data_base = 4;

  string table_name = 5;

  string column_name = 6;

  int32 is_marked = 7;

  string extra_info = 8;

  int64 create_time = 10;  // 创建时间

  int64 update_time = 11;  // 更新时间

  string creator = 12;  // 创建人

  string updater = 13;  // 修改人
}

message KspayUpdateMarkedForRiskFieldsResponse {
  int32 result = 1;

  string error_msg = 2;

  string id = 3;  // 返回下主键id
}


message KspayQuerySymbolListRequest {
  string null_request = 1;
}

message KspayQuerySymbolListResponse {
    int32 result = 1;

    string error_msg = 2;

    repeated string symbol = 3;
}

service KspayRiskIntelligenceFindService {
  rpc KspayQueryPageRiskIntelligenceFindList (QueryPageRiskFieldsRequest) returns (QueryPageRiskFieldsResponse);

  rpc KspayUpdateRiskLevelForRiskFields (KspayRiskIntelligenceFindListParam) returns (KspayUpdateMarkedForRiskFieldsResponse);

  rpc KspayQuerySymbolList (KspayQuerySymbolListRequest) returns (KspayQuerySymbolListResponse);
}

