syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;
option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.feature";
option java_outer_classname = "KspayGitToDepartmentMappingProto";


message GitToDepartmentMappingParam {
  int64 id = 1;
  int64 first_department_id = 2;
  int64 second_department_id = 3;
  int64 third_department_id = 4;
  int64 git_project_id = 5;
  string first_department_name = 6;
  string second_department_name = 7;
  string third_department_name = 8;
  string git_name = 9;
  string git_description = 10;
  string creator = 11;
  string updater = 12;
  int64 create_time = 13;
  int64 update_time = 14;
}

message GitToDepartmentMappingResult {
  int32 result = 1;
  string error_msg = 2;
}

message QueryGitToMappingRequest {
  int32 page_no = 1;
  int32 page_size = 2;
  int64 first_department_id = 3;
  int64 second_department_id = 4;
  int64 third_department_id = 5;
  int64 git_project_id = 6;
  string first_department_name = 7;
  string second_department_name = 8;
  string third_department_name = 9;
  string git_name = 10;
}

message QueryGitToMappingResponse {
  int32 result = 1;
  string err_msg = 2;
  repeated GitToMappingInfo git_to_mapping_info = 3;
  repeated FirstDepartmentInfo first_department_info = 4;
  repeated SecondDepartmentInfo second_department_info = 5;
  int64 total_count = 6;
}

message GitToMappingInfo {
  int64 first_department_id = 1;
  int64 second_department_id = 2;
  int64 third_department_id = 3;
  int64 git_project_id = 4;
  string first_department_name = 5;
  string second_department_name = 6;
  string third_department_name = 7;
  string git_name = 8;
  string git_description = 9;
}

message FirstDepartmentInfo {
  int64 first_department_id = 1;
  string first_department_name = 2;
}

message SecondDepartmentInfo {
  int64 second_department_id = 1;
  string second_department_name = 2;
}

service KspayGitToDepartmentMappingService {
  rpc KspayInsertOrUpdateGitToDepartmentMapping (GitToDepartmentMappingParam) returns (GitToDepartmentMappingResult);

  rpc QueryGitMappingToDepartment (QueryGitToMappingRequest) returns (QueryGitToMappingResponse);

}
