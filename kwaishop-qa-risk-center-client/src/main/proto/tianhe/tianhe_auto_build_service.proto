syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

import "kess/proto/scheduler/common.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe";
//option java_outer_classname = "TianheSampleSetServiceProto";

message AutoExtractSampleRequest{
  string user_name = 1;
  string change_order_id = 2;
  string page_code = 3;
  string app_key = 4;
  string online_url = 5;
  string component_type = 6;
  string protocol = 7;
}

message AutoExtractSampleResponse{
  int32 result = 1;
  string error_msg = 2;
  repeated Action action = 3;
  bool success = 4;
}

message AutoExtractSampleFromImageRequest{
  string user_name = 1;
  string change_order_id = 2;
  string page_code = 3;
  string app_key = 4;
  string online_url = 5;
  string img_ori = 6;
  string img_tar = 7;
}
message AutoExtractSampleFromImageResponse{
  int32 result = 1;
  string error_msg = 2;
  repeated Action action = 3;
  bool success = 4;
}

message Action{
  string action_type = 1;
  string xpath = 2;
  string pytest_code = 3;
}

message AutoExcuteSampleRequest{
  string sample_id = 1;
}
message AutoExcuteSampleResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}

message JudgeImgRequest {
  string sample_id = 1;
  string execute_id = 2;
  string action_type = 3;
  string xpath = 4;
}

message JudgeImgResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}

service TianheAutoBuildService{
  rpc UpdateAISample(AutoExcuteSampleRequest) returns (AutoExtractSampleResponse);
  rpc JudgeImgResult(JudgeImgRequest) returns (JudgeImgResponse);
  rpc AutoExtractSample(AutoExtractSampleRequest) returns (AutoExtractSampleResponse);
  rpc AutoExtractSampleFromImage(AutoExtractSampleFromImageRequest) returns (AutoExtractSampleFromImageResponse);
  rpc AutoExcuteSampleById(AutoExcuteSampleRequest) returns (AutoExcuteSampleResponse);
  rpc Test(AutoExtractSampleRequest) returns (AutoExtractSampleResponse);
}
