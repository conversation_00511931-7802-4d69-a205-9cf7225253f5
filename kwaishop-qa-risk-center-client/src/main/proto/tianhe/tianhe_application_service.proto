syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

import "kess/proto/scheduler/common.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe";
//option java_outer_classname = "TianheApplicationServiceProto";

message AddApplicationRequest{
  string name = 1;
  string creator = 2;
  repeated string operators = 3;
  string app_key = 4;
}
message AddApplicationResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
message ListApplicationRequest{
  string operator = 1;
}
message ListApplicationResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
  ListApplicationData data = 4;
}
message ListApplicationData{
  int32 total = 1;
  repeated ListApplicationRecord app_list = 2;
}
message ListApplicationRecord{
  int32 app_id = 1;
  string name = 2;
  bool has_permission = 3;
  string app_key = 4;
  string operators = 5;
  repeated ListSampleSetRecord set_list = 6;
}
message ListSampleSetRecord{
  int32 set_id = 1;
  string name = 2;
}
message UpdateApplicationRequest{
  string name = 1;
  repeated string operators = 2;
  string app_key = 3;
  string operator = 4;
  int32 id = 5;
}
message UpdateApplicationResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
message DeleteApplicationRequest{
  int32 id = 1;
  string operator = 2;
}
message DeleteApplicationResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
service TianheApplicationService{
  rpc AddApplication(AddApplicationRequest) returns (AddApplicationResponse);
  rpc ListApplication(ListApplicationRequest) returns (ListApplicationResponse);
  rpc UpdateApplication(UpdateApplicationRequest) returns (UpdateApplicationResponse);
  rpc DeleteApplication(DeleteApplicationRequest) returns (DeleteApplicationResponse);
}
