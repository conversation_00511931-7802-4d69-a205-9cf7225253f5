syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

import "kess/proto/scheduler/common.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe";
//option java_outer_classname = "TianheSampleCaseServiceProto";

message PageRequest{
  int32 page_num = 1;
  int32 page_size = 2;
}
message PageResponse{
  int32 page_num = 1;
  int32 page_size = 2;
  int32 total_count = 3;
  int32 page_count = 4;
}
message AddSampleRequest{
  string name = 1;
  string domain_code = 2;
  string app_key = 3;
  string page_code = 4;
  string account_uid = 5; //存ext
  string pwd = 6; //存ext
  string creator = 7;
  string prt_url = 8;
  string online_url = 9;
  repeated ActionSetExtendData action_set = 10;
  string platform = 11; //后续对接别的平台，暂时只有天河
  string test_lane_id = 12;
  string target_lane_id = 13;
  string change_order_id = 14;
}
message ActionSetExtendData{
  string action_type = 1;
  string xpath = 2;
  string pytest_code = 3;
}
message AddSampleResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
message UpdateSampleRequest{
  string name = 1;
  string app_key = 3; //不可编辑
  string page_code = 4; //不可编辑
  string account_uid = 5;
  string pwd = 6;
  string modifier = 7;
  string prt_url = 8;
  string online_url = 9;
  repeated ActionSetExtendData action_set = 10;
  int32 id = 12;
  string test_lane_id = 13;
  string target_lane_id = 14;
  string change_order_id = 15;
}
message UpdateSampleResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
message DeleteSampleRequest{
  int32 id = 1;
  string operator = 2;
}
message DeleteSampleResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
message ListSampleRequest{
  string domain_code = 1;
  string app_key = 2;
  string page_code = 3;
  string operator = 4;
  string platform = 5;
  string id = 6;
  string name = 7;
  string sample_type = 8; // 一期先不整
  string creator = 9;
  PageRequest page = 10;
}
message ListSampleResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
  ListSampleExtendData data = 4;
  PageResponse page = 5;
}
message ListSampleExtendData{
  int32 total = 1;
  repeated ListSampleRecord list = 2;
}
message ListSampleRecord{
  int32 id = 1;
  string name = 2;
  string app_key = 3;
  string page_code = 4;
  string ext = 5;
  string operator_set = 6;
  string creator = 7;
  string platform_source = 8;
  string status = 9;
  repeated ActionSetExtendData action_set = 10;
}
message ExecuteSampleRequest{
  int32 id = 1;
  string operator = 2;
  uint64 change_order_id = 3; //变更单id
  uint64 last_change_deploy_id = 4; //变更单id
  uint64 last_deploy_version = 5; //变更单版本
}
message ExecuteSampleResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
message BatchExecuteSampleRequest{
  uint64 change_order_id = 1; //变更单id
  uint64 last_change_deploy_id = 2; //最近一次的部署主单,灵筑设置了拦截，最近一次必须是主干部署(当有天河页面时)
  uint64 last_deploy_version = 3; //最近一次的部署版本
  string telnet = 4;
  repeated string prod_url = 5;
}
message BatchExecuteSampleResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
message ListSingleHistoryExecuteRequest{
  int32 id = 1;
  string operator = 2;
  PageRequest page = 3;
}
message ListSingleHistoryExecuteResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
  ListExecuteExtendData data = 4;
  PageResponse page = 5;
}
message ListExecuteExtendData{
  int32 total = 1;
  repeated ListExecuteRecord list = 2;
}
message ListExecuteRecord{
  string execute_id = 1;
  int64 sample_id = 2;
  string name = 3;
  string app_key = 4;
  string action_type = 5; // 返回文字
  string page_code = 6;
  string ext = 7;
  string diff_rate = 8;
  string operator_set = 9;
  int64 create_time = 10;
  int32 status = 11;
  string platform_source = 12;
  int32 execute_status = 13;
  string xpath = 14; // 25.05.19 新增，需要xpath*actionType维度作为主键
}
message ListHistoryExecuteRequest{
  string platform_source = 1;
  string app_key = 2;
  string page_code = 3;
  string execute_id = 4;
  string sample_id = 5;
  string change_order_id = 6;
  string last_change_deploy_id = 7;
  string last_deploy_version = 8;
  string operator = 9;
  string name = 10;
  PageRequest page = 11;
}
message ListHistoryExecuteResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
  ListExecuteExtendData data = 4;
  PageResponse page = 5;
}
message ExecuteFinishRequest{
  string execute_id = 1;
  repeated ExecuteResult list = 2;
}
message ExecuteFinishResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
message ExecuteResult{
  int64 sample_id = 1;
  int32 action_type = 2;
  string xpath = 3;
  string origin_image = 4;
  string detect_image = 5;
  double diff_rate = 6;
}
message ManualPassRequest{
  string execute_id = 1;
  int32 sample_id = 2;
  int32 action_type = 3;
  string xpath = 4;
  string operator = 5;
}
message ManualPassResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
message QueryActionSetByIdRequest{
  int32 id = 1;
}
message QueryActionSetByIdResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
  repeated ActionSetExtendData action_set = 4;
}
message UpdateValidActionSetRequest{
  int32 id = 1;
  repeated ActionSetExtendData action_set = 2;
}
message UpdateValidActionSetResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
service TianheSampleCaseService{
  rpc ListSample(ListSampleRequest) returns (ListSampleResponse);
  rpc AddSample(AddSampleRequest) returns (AddSampleResponse);
  rpc AddAiSample(AddSampleRequest) returns (AddSampleResponse);
  rpc DeleteSample(DeleteSampleRequest) returns (DeleteSampleResponse);
  rpc UpdateSample(UpdateSampleRequest) returns (UpdateSampleResponse);
  rpc UpdateAiSample(UpdateSampleRequest) returns (UpdateSampleResponse);
  rpc ExecuteSample(ExecuteSampleRequest) returns (ExecuteSampleResponse);
  rpc BatchExecuteSample(BatchExecuteSampleRequest) returns (BatchExecuteSampleResponse);
  rpc ListSingleHistoryExecute(ListSingleHistoryExecuteRequest) returns (ListSingleHistoryExecuteResponse);
  rpc ListHistoryExecute(ListHistoryExecuteRequest) returns (ListHistoryExecuteResponse);
  rpc ExecuteFinish(ExecuteFinishRequest) returns (ExecuteFinishResponse);
  rpc ManualPass(ManualPassRequest) returns (ManualPassResponse);
  rpc QueryActionSetById(QueryActionSetByIdRequest) returns (QueryActionSetByIdResponse);
  rpc UpdateValidActionSet(UpdateValidActionSetRequest) returns (UpdateValidActionSetResponse);
}
