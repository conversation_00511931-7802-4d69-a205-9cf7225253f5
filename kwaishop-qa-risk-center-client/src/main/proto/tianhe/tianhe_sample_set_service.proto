syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

import "kess/proto/scheduler/common.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.tianhe";
//option java_outer_classname = "TianheSampleSetServiceProto";

message AddSampleSetRequest{
  string name = 1;
  string creator = 2;
  int32 is_inspection_required = 3;
  string inspection_frequency = 4;
  int32 app_id = 5;
}
message AddSampleSetResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
message DeleteSampleSetRequest{
  int32 id = 1;
  string operator = 2;
}
message DeleteSampleSetResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
message UpdateSampleSetRequest{
  int32 id = 1;
  string name = 2;
  int32 is_inspection_required = 3;
  string inspection_frequency = 4;
}
message UpdateSampleSetResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
message QuerySampleBySetIdRequest{
  int32 sample_set_id = 1;
}
message QuerySampleBySetIdResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
  QuerySampleBySetIdData data = 4;
}
message QuerySampleBySetIdData{
  int32 total = 1;
  repeated QuerySampleBySetIdRecord list = 2;
}
message QuerySampleBySetIdRecord{
  int32 id = 1;
  string name = 2;
  string app_key = 3;
  string page_code = 4;
  string platform_source = 5;
}
message InsertCaseIntoSetRequest{
  int32 case_id = 1;
  int32 set_id = 2;
}
message InsertCaseIntoSetResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
message DeleteCaseFromSetRequest{
  int32 case_id = 1;
  int32 set_id = 2;
}
message DeleteCaseFromSetResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}

message SampleSetDebugRequest{
  int32 set_id = 1;
  int32 app_id = 2;
}
message SampleSetDebugResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
message ExecuteSampleSetRequest{
  int32 set_id = 1;
  string operator = 2;
}
message ExecuteSampleSetResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
  string execute_id = 4;
}
message QuerySampleSetExecuteListRequest{
  int32 set_id = 1;
}
message QuerySampleSetExecuteListResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
  int32 total = 4;
  repeated SampleSetExecuteListData set_execute_list = 5;
}
message SampleSetExecuteListData{
  string execute_id = 1;
  int32 execute_count = 2;
  repeated SampleExecuteListData sample_execute_list = 3;
}
message SampleExecuteListData{
  int32 sample_id = 1;
  string sample_name = 2;
  int64 create_time = 3;
  int32 execute_status = 4;
  int32 action_type = 5;
  string ext = 6;
  string xpath = 7;
}
message TianheDebugRequest{
  int32 i = 1;
}
message TianheDebugResponse{
  int32 result = 1;
  string error_msg = 2;
  bool success = 3;
}
service TianheSampleSetService{
  rpc AddSampleSet(AddSampleSetRequest) returns (AddSampleSetResponse);
  rpc DeleteSampleSet(DeleteSampleSetRequest) returns (DeleteSampleSetResponse);
  rpc UpdateSampleSet(UpdateSampleSetRequest) returns (UpdateSampleSetResponse);
  rpc QuerySampleBySetId(QuerySampleBySetIdRequest) returns (QuerySampleBySetIdResponse);
  rpc InsertCaseIntoSet(InsertCaseIntoSetRequest) returns (InsertCaseIntoSetResponse);
  rpc DeleteCaseFromSet(DeleteCaseFromSetRequest) returns (DeleteCaseFromSetResponse);
  rpc SampleSetDebug(SampleSetDebugRequest) returns (SampleSetDebugResponse);
  rpc ExecuteSampleSet(ExecuteSampleSetRequest) returns (ExecuteSampleSetResponse);
  rpc QuerySampleSetExecuteList(QuerySampleSetExecuteListRequest) returns (QuerySampleSetExecuteListResponse);
  rpc Debug(TianheDebugRequest) returns (TianheDebugResponse);
}
