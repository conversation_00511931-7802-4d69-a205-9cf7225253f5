syntax="proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.risk";
option java_outer_classname = "RiskMeasureServiceProto";

message GetMeasureScoreRequest {
  string model = 1;
  string business  = 2;
  string start_time = 3;
  string end_time = 4;
  string biz_code = 5;
}

message GetMeasureScoreResponse {
  string code = 1;
  string msg = 2;
  repeated RiskScore risk_score_list = 3;
}

message GenerateMeasureScoreRequest{
  string model = 1;
}

message GenerateMeasureScoreResponse {
  string code = 1;
  string msg = 2;
}

message RiskScore {
  string id = 1;
  string risk_desc = 2;
  string business = 3;
  string score = 4;
}

service RiskMeasureService{
  rpc GetMeasureScore (GetMeasureScoreRequest) returns (GetMeasureScoreResponse);
  rpc GenerateMeasureScore (GenerateMeasureScoreRequest) returns (GenerateMeasureScoreResponse);
}