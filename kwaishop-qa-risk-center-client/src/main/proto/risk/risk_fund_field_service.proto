syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.risk";
option java_outer_classname = "RiskFundFieldServiceProto";

message RiskFundField{
  string biz_def = 1;
  string source_name = 2;
  string data_base = 3;
  string table_name = 4;
  string column_name = 5;
  string extra_info = 6;
  string risk_level = 7;
  string symbol = 8;
  string creator = 9;
  string updater = 10;
  string do_type = 11;
  int32 has_audit = 12;
  string biz_code = 13;
}