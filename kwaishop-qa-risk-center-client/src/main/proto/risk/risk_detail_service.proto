syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.risk";
option java_outer_classname = "RiskDetailServiceProto";


message CreateRiskDetailRequest {

  string name = 1;

  int32 risk_type = 2;

  string risk_desc = 3;

  int32 verify_type = 4;

  string risk_link = 5;

  string practice_report = 6;

  int32 verify_effective = 7;

  string plan_link = 8;

  int32 plan_effective = 9;

  int32 level = 10;

  string operator = 11;

  int64 team_id = 12;

  int64 business_id = 13;

  string business_line = 14;

  string business_line_desc = 15;

  int32 db_type = 16;

  string db_change_desc = 17;

  string service_ksn = 18;

  string service_name = 19;

  int64 git_id = 20;

  string git_name = 21;

  string db_table_name = 22;

  string table_column_name = 23;

  int32 table_column_cover = 24;

  int32 risk_cover = 25;

  string breakdown_effect_desc = 26;

  string plan_desc = 27;

  string person_liable = 28;

  int64 fund_exp = 29;

  int32 plan_cover = 30;

  int64 center_id = 31;

  string business_name = 32;
}


message CreateRiskDetailResponse {

  int32 result = 1;

  string error_msg = 2;
}

message RiskDetailParam {

  string name = 1;

  int32 risk_type = 2;

  string risk_desc = 3;

  int32 verify_type = 4;

  string risk_link = 5;

  string practice_report = 6;

  int32 verify_effective = 7;

  string plan_link = 8;

  int32 plan_effective = 9;

  int32 level = 10;
}

message BatchCreateRiskDetailRequest {

  repeated RiskDetailParam risk_detail_param = 1;

  string operator = 2;
}

message BatchCreateRiskDetailResponse {

  int32 result = 1;

  string error_msg = 2;
}

message UpdateRiskDetailRequest {

  string name = 1;

  int32 risk_type = 2;

  string risk_desc = 3;

  int32 verify_type = 4;

  string risk_link = 5;

  string practice_report = 6;

  int32 verify_effective = 7;

  string plan_link = 8;

  int32 plan_effective = 9;

  int32 level = 10;

  string operator = 11;

  int64 id = 12;

  int64 team_id = 13;

  string business_line = 14;

  string business_line_desc = 15;

  int32 db_type = 16;

  string db_change_desc = 17;

  string service_ksn = 18;

  string service_name = 19;

  int64 git_id = 20;

  string git_name = 21;

  string db_table_name = 22;

  string table_column_name = 23;

  int32 table_column_cover = 24;

  int32 risk_cover = 25;

  string breakdown_effect_desc = 26;

  string plan_desc = 27;

  string person_liable = 28;

  int64 fund_exp = 29;

  int32 plan_cover = 30;

  int64 business_id = 31;

  int64 center_id = 32;

  string business_name = 33;

}

message UpdateRiskDetailResponse {

  int32 result = 1;

  string error_msg = 2;
}

message DeleteRiskDetailRequest {

  string operator = 1;

  int64 id = 2;
}

message DeleteRiskDetailResponse {

  int32 result = 1;

  string error_msg = 2;
}

message QueryRiskDetailRequest {

  string name = 1;

  int32 risk_type = 2;

  int32 verify_type = 3;

  int32 verify_effective = 4;

  int32 plan_effective = 5;

  int32 level = 6;

  int32 view_type = 7;

  string operator = 8;

  int64 id = 9;

  int64 team_id = 10;

  int64 center_id = 11;

}

message QueryRiskDetailResponse {

  int32 result = 1;

  string error_msg = 2;

  RiskDetailViewDTO data = 3;
}

message RiskDetailViewDTO {

  int32 view_type = 1;

  repeated RiskDetailDTO risk_detail_list = 3;

}

message RiskDetailDTO {

  string name = 1;

  int32 risk_type = 2;

  string risk_desc = 3;

  int32 verify_type = 4;

  string risk_link = 5;

  string practice_report = 6;

  int32 verify_effective = 7;

  string plan_link = 8;

  int32 plan_effective = 9;

  int32 level = 10;

  string creator = 11;

  int64 id = 12;

  string modifier = 13;

  int64 create_time = 14;

  int64 update_time = 15;

  int64 team_id = 16;

  string team_name = 17;

  string risk_type_desc = 18;

  string verify_type_desc = 19;

  string verify_effective_desc = 20;

  string plan_effective_desc = 21;

  string level_desc = 22;

  int64 business_id = 23;

  string business_line = 24;

  string business_line_desc = 25;

  int32 db_type = 26;

  string db_change_desc = 27;

  string service_ksn = 28;

  string service_name = 29;

  int64 git_id = 30;

  string git_name = 31;

  string db_table_name = 32;

  string table_column_name = 33;

  int32 table_column_cover = 34;

  int32 risk_cover = 35;

  string breakdown_effect_desc = 36;

  string plan_desc = 37;

  string person_liable = 38;

  int64 fund_exp = 39;

  int32 plan_cover = 40;

  string business_name = 41;

  string db_type_desc = 42;

  string table_column_cover_desc = 43;

  string risk_cover_desc = 44;

  string plan_cover_desc = 45;

  int64 center_id = 46;

  string center_name = 47;

}

message QueryPageRiskDetailRequest {

  string name = 1;

  int32 risk_type = 2;

  int32 verify_type = 3;

  int32 verify_effective = 4;

  int32 plan_effective = 5;

  int32 level = 6;

  int32 view_type = 7;

  string operator = 8;

  int64 id = 9;

  int32 page_no = 10;

  int32 page_size = 11;

  int64 team_id = 12;

  int64 center_id = 13;

}

message QueryPageRiskDetailResponse {

  int32 result = 1;

  string error_msg = 2;

  PageRiskDetailDTO data = 3;
}

message PageRiskDetailDTO {

  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated RiskDetailDTO risk_detail_list = 4;

  int32 view_type = 5;
}


service RiskDetailDomainService {

  rpc CreateRiskDetail (CreateRiskDetailRequest) returns (CreateRiskDetailResponse);

  rpc UpdateRiskDetail (UpdateRiskDetailRequest) returns (UpdateRiskDetailResponse);

  rpc DeleteRiskDetail (DeleteRiskDetailRequest) returns (DeleteRiskDetailResponse);

  rpc QueryRiskDetailList (QueryRiskDetailRequest) returns (QueryRiskDetailResponse);

  rpc QueryPageRiskDetailList (QueryPageRiskDetailRequest) returns (QueryPageRiskDetailResponse);

}
