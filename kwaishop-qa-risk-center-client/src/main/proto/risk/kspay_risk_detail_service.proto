syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.risk";
option java_outer_classname = "KspayRiskDetailServiceProto";


  message QueryPageKspayRiskDetailRequest {

    string sub_risk_name = 1;

    int32 level = 2;

    string risk_type = 3;

    string owner = 4; // 对应数据库 creator

    string creator = 5; //cookies 获取

    int32 page_no = 6;

    int32 page_size = 7;

    string    department         = 8; // 所属部门中心
    string    business_domain         = 9; // 所属业务域


  }

  message QueryPageKspayRiskDetailResponse {

    int32 result = 1;

    string error_msg = 2;

    PageKspayRiskDetailDTO data = 3;
  }

  message PageKspayRiskDetailDTO {

    int32 page_no = 1;

    int32 page_size = 2;

    int64 total = 3;

    repeated KspayRiskDetailParam sub_risk_detail_list = 4;

    int32 view_type = 5;
  }


  message KspayCreateRiskDetailRequest {
    int32 risk_id = 1; //funds_risk_product_event id
    //funds_risk_product_event 表
    string    risk_product    = 2; //风险产品功能

    string    risk_event      = 3; //风险项

    string    risk_event_desc = 4; //风险描述

    string    department      = 5; //所属部门中心

    string    business_domain = 6;//所属业务域

    string    belong_group    = 7; //所属小组

    string    owner           = 8; //负责人

    string    creator         = 9; // 创建者

    // 子风险详情和防控类型
    repeated KspayRiskDetailParam sub_risk_list   = 10;

    string  feature_id = 11;

  }


  message KspayCreateRiskDetailResponse {

    int32  result    = 1;

    string error_msg = 2;
  }

message KspayCreateRiskDefenseResponse {

  int32  result    = 1;

  string error_msg = 2;
}

  message KspayRiskDetailParam {

    // funds_sub_risk 表信息

    string   sub_risk_id                    = 1; // funds_sub_risk id

    string    risk_event_id         = 2; // funds_risk_product_event id

    string   sub_risk_name         = 3; //子风险点名字

    string   sub_risk_detail_desc  = 4; //子风险点详情描述

    string   ksn_name              = 5; //服务名

    string   git_id                = 6; //git工程id

    string   git_project           = 7;//git工程名

    int32    level                 = 8; //风险等级

    string   risk_type             = 9; //风险类型',数据一致性，三方风险，安全风险

    string   total_risk_amount     = 10; //资金风险敞口

    int32    db_type               = 11; //db更新类型

    string   db_up_condition       = 12; //db更新条件

    string   ksn_method            = 13; //服务方法名

    string   db_table_name         = 14; //库表名

    string   db_columns            = 15; //字段名

    int32    sub_risk_status       = 16; // 子风险点当前状态

    //funds_risk_defense_style 信息

    repeated KspayRiskDefenseDetail sub_risk_defense_list = 17;

    string    creator         = 18; // 创建者

    string    department         = 19; // 所属部门中心
    string    business_domain         = 20; // 所属业务域
    string    belong_group         = 21; // 所属小组

    int64    create_time         = 22; // 创建时间

    string    risk_pre_plan         = 23; //风险预案
    string    is_valid_plan         = 24; //预案有效性
    string    is_covered_plan         = 25; //预案是否覆盖


  }


  message  KspayRiskDefenseDetail{
    string  sub_risk_defense_id             = 1; // funds_risk_defense_style id

    string  sub_risk_id    = 2; // funds_sub_risk id

    string defense_type   = 3; //防御手段',//自动化、实时对账

    string defense_info   = 4; //防御内容',//自动化case，对账配置地址

    int32  is_valid       = 5; //是否完成有效性验证

    string check_url      = 6; //对应链接

    int32  alert_level    = 7; //报警优先级

    int32  defense_status = 8; //状态',//是否覆盖，1已覆盖，0未覆盖

    string    creator         = 9; // 创建者

  }

  message KspayRiskDefenseRequest{
    string  department = 1; // 所属部门中心
    string  business_domain = 2; // 所属业务域
  }

  message KspayRiskListResponse {
    int32  result    = 1;
    string error_msg = 2;
    repeated KspayRiskResponse kspay_risk_list = 3;

 }

  message KspayRiskResponse {
    int64     risk_id = 1; //funds_risk_product_event id

    string    risk_product    = 2; //风险产品功能

    string    risk_event      = 3; //风险项

    string    risk_event_desc = 4; //风险描述

    string    department      = 5; //所属部门中心

    string    business_domain = 6;//所属业务域

    string    belong_group    = 7; //所属小组

    string    owner           = 8; //负责人

    string    creator         = 9; // 创建者
  }

  message KspayUpdateSubRiskResponse {
    int32  result    = 1;
    string error_msg = 2;

  }

message KspayUpdateDefenseResponse {
  int32  result    = 1;
  string error_msg = 2;

}

  message KspaySubRiskDetailParam {

    // funds_sub_risk 表信息

    string   sub_risk_id           = 1; // funds_sub_risk id

    string   sub_risk_name         = 2; //子风险点名字

    string   sub_risk_detail_desc  = 3; //子风险点详情描述

    string   ksn_name              = 4; //服务名

    string   git_id                = 5; //git工程id

    string   git_project           = 6;//git工程名

    int32    level                 = 7; //风险等级

    string   risk_type             = 8; //风险类型',数据一致性，三方风险，安全风险

    string   total_risk_amount     = 9; //资金风险敞口

    int32    db_type               = 10; //db更新类型

    string   db_up_condition       = 11; //db更新条件

    string   ksn_method            = 12; //服务方法名

    string   db_table_name         = 13; //库表名

    string   db_columns            = 14; //字段名

    int32    sub_risk_status       = 15; // 子风险点当前状态

    string    updater         = 16; //

    int64    update_time         = 17; //

    string    risk_pre_plan         = 18; //风险预案
    string    is_valid_plan         = 19; //预案有效性
    string    is_covered_plan         = 20; //预案是否覆盖

  }


service KspayRiskDetailDomainService {

      rpc KspayCreateRiskDefense (KspayRiskDefenseDetail) returns (KspayCreateRiskDefenseResponse);

      rpc KspayQueryPageRiskDetailList (QueryPageKspayRiskDetailRequest) returns (QueryPageKspayRiskDetailResponse);

      rpc KspayCreateRiskDetail (KspayCreateRiskDetailRequest) returns (KspayCreateRiskDetailResponse);

      rpc KspayQueryRiskList (KspayRiskDefenseRequest) returns (KspayRiskListResponse);

      rpc KspayUpdateSubRisk (KspaySubRiskDetailParam) returns (KspayUpdateSubRiskResponse);

      rpc KspayUpdateDefense (KspayRiskDefenseDetail) returns (KspayUpdateDefenseResponse);
  }

