syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;
import "google/protobuf/any.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.risk";
option java_outer_classname = "KspayConfigRiskServiceProto";

// 查询规则配置
message KspayConfigRiskQueryRequest {
  string rule_name = 1;  // 规则名
}

message KspayConfigRiskQueryResponse {
  int32 result = 1;

  string error_msg = 2;

  repeated ConfigRiskDetail data = 3;
}

message ConfigRiskDetail {
  int64 id = 1;

  string rule_name = 2;  // 规则名

  string compare_rule = 3;  // 比较规则

  string expression_rule = 4;  // 表达式规则

  string config_type = 5;  // 规则类型

  string effective_region = 6;  // 生效范围

  string data = 7;  // 扩展字段

  string create_time = 8;

  string update_time = 9;

  string creator = 10;

  string updater = 11;
}


// 读取kconf配置值
message KspayKconfKeyRequest {
  string key = 1;

  string env = 2;  // 标识请求环境
}

message KspayKconfKeyResponse {
  int32 result = 1;

  string message = 2;

  KconfData data = 3;
}

message KconfData {
  string key = 1;

  string description = 2;  // 配置描述

  string type = 3;  // 配置类型

  int32  version = 4;  // 配置版本

  string status = 5;  // 配置状态

  string created_date = 6;  // 创建时间

  string creator = 7;// 创建人

  string modifier = 8;  // 最近修改人

  string modify_date = 9;  // 最近修改时间

  repeated SubConfig sub_configs = 10;  // 子配置

  bool favorite= 11;  // 是否收藏

  bool need_review = 12;  // 是否强制review

  bool resetable = 13;  // 是否可修改

  repeated Resetable resetables = 14;  // 可修改的属性

  Setting settings = 15;  // 配置设置

  google.protobuf.Any sub_config_relation_map = 16;  // 分级发布映射配置

  bool show_gray_services = 17;  // 是否展示自定义环境第二tab
}

message SubConfig {
  int64 snapshot_id = 1;  // 配置环境id

  string stage = 2;  // 代表环境

  string data = 3;  // 配置内容、

  string modifier = 4;  // 测试程序

  string modified_date = 5;

  GrayRelease gray_release = 6;  // 自定义环境参数

}

message Resetable {
  string key = 1;

  string type = 2;  // 配置描述
}

message Setting {
  google.protobuf.Any inherited = 1;
  google.protobuf.Any computed = 2;
}

message GrayRelease {
  string name = 1;  // 自定义环境名称

  repeated string hosts = 2;  // 主机列表

  repeated string service = 3;  // 服务列表

  repeated KwsService kws_service = 4;  // kws环境参数

  repeated string pod_name = 5;  // 实例名

}

message KwsService {
  string ksn = 1;  // ksn服务名

  string stage = 2;  // 环境

  string az = 3;  // az

  string physical_az = 4;  // 物理az

  string lane = 5;  // 泳道

  string group = 6;  // 服务分组
}

// 获取路径下kconf列表
message KspayKconfListRequest {
  string parent_dir = 1;

  string sub_dir = 2;
}

message KspayKconfListResponse {
  int32 result = 1;

  string message = 2;

  repeated string data = 3;
}

message KspayQueryConfigRulesCheckResultRequest{
  string report_name = 1;
  int32 result = 2;
  int32 type = 3;
  int64 start_time = 4;
  int64 end_time = 5;
}

message KspayQueryConfigRulesCheckResultResponse{
  int32 result = 1;
  string message = 2;
  repeated ConfigRulesCheckResultData data = 3;
}

message ConfigRulesCheckResultData{
  int64 id = 1;
  string report_name = 2;
  int32 result = 3;
  string scan_rule = 4;
  string failure_content = 5;
  int64 create_time = 6;
  int32 type = 7;
  string rule_id = 8;
  string config_type = 9;
  string kconf_key = 10;
}

message KspayQueryConfigCheckResultRecordRequest{
  int64 id = 1;
  int64 report_id = 2;
}

message KspayQueryConfigCheckResultRecordResponse{
  int32 result = 1;
  string message = 2;
  repeated ConfigCheckResultRecordData data = 3;
}

message ConfigCheckResultRecordData{
  int64 id = 1;
  string config_data_type = 2;
  string config_info = 3;
  string scan_rule_name = 4;
  string remarks = 5;
  int64 report_id = 6;
  int32 update_interval = 7;
  string error_data_info = 8;
  int32 gray_status = 9;
  int32 is_issue = 10;
}


message KspayKconfKey {
  string key = 1;
}

message QueryKconfConfigRulesCheckResponse{
  int32 result = 1;
  string message = 2;
  repeated ConfigRulesCheckResultData data = 3;
}



service KspayConfigRiskService {

  rpc KspayConfigRiskQuery (KspayConfigRiskQueryRequest) returns (KspayConfigRiskQueryResponse);

  rpc KspayKconfValueQuery (KspayKconfKeyRequest) returns (KspayKconfKeyResponse);

  rpc KspayKconfListQuery (KspayKconfListRequest) returns (KspayKconfListResponse);

  rpc KspayQueryConfigRulesCheckResult (KspayQueryConfigRulesCheckResultRequest) returns (KspayQueryConfigRulesCheckResultResponse);
  rpc KspayQueryConfigCheckResultRecord (KspayQueryConfigCheckResultRecordRequest) returns (KspayQueryConfigCheckResultRecordResponse);

  rpc QueryKconfConfigRulesCheckResult (KspayKconfKey) returns (QueryKconfConfigRulesCheckResponse);

}