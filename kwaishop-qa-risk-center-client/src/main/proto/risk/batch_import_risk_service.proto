syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.risk";
option java_outer_classname = "BatchImportRiskServiceProto";


message BatchImportRiskRequest{

  string department = 1;

  string business_domain = 2;

  string belong_group = 3;

  string file_base64 = 4;

}


message BatchImportRiskResponse{

  int32 result = 1;

  string error_msg = 2;
}


service BatchImportRiskService {
  rpc BatchImportRiskApply (BatchImportRiskRequest) returns (BatchImportRiskResponse);
}
