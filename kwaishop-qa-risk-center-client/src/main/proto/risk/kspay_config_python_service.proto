syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.risk";
option java_outer_classname = "KspayConfigPythonServiceProto";

// 执行python脚本
message KspayConfigPythonQueryRequest {

  int64 id = 1;
  string kconf_data = 2;

}

message KspayConfigPythonQueryResponse {

  bool result = 1;
  string message = 2;

}




service KspayConfigPythonService {

  rpc KspayConfigRiskQuery (KspayConfigPythonQueryRequest) returns (KspayConfigPythonQueryResponse);

}