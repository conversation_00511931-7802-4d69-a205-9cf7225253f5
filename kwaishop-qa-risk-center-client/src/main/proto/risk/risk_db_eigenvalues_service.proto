syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.risk";
option java_outer_classname = "KspayRiskDbEigenvaluesServiceProto";


  message KspayRiskGenerateEigenvaluesRequest {
    string policy = 1; // 策略
  }

  message KspayRiskGenerateEigenvaluesResponse {
    string code = 1;
    string msg = 2;
  }

service KspayRiskDbEigenvaluesService {
    rpc KspayRiskGenerateEigenvalues (KspayRiskGenerateEigenvaluesRequest) returns (KspayRiskGenerateEigenvaluesResponse);
}