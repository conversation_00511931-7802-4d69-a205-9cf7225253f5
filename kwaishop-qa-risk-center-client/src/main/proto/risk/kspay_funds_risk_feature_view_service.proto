syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.risk";
option java_outer_classname = "KspayFundsRiskFeatureViewServiceProto";

  message  KspayFundsRiskFeatureViewRequest{
    string department  = 1; // department  所属部门中心
    string business_domain    = 2; //  业务域
    string feature_id   = 3; //featureId
    string feature_name   = 4; //featureName
    string team_id       = 5; //teamId
    string team_name      = 6; //teamName
    string risk_coe    = 7; //风险系数
    string team_worker = 8; //参与人
    string status         = 9; // 当前状态
    int32 is_risk = 10;  // 是否有资损
  }

  message KspayFundsRiskFeatureViewResponse {
  int32  result    = 1;
  string error_msg = 2;
  }

  message  KspayFundsRiskFeatureViewBranchRequest{
    string  feature_view_id  = 1; // feature_view_id
    string  repo_name    = 2; // 应用名称
    string branch_name   = 3; //分支名
    string method_entrance   = 4; //方法入口
    string  accuracy_url       = 5; //精准平台跳转地址
    string change_method_amount      = 6; //改动方法数
    string  fund_risk_method_amount    = 7; //资金风险涉及方法数
    string  test_result = 8; //测试结果
    string risk_table_fields = 9;  // 资损风险表&字段
  }

  message KspayFundsRiskFeatureViewBranchResponse {
    int32  result    = 1;
    string error_msg = 2;
  }

  message KspayInsertRiskFeatureViewByBizTimeRequest{
    string biz_space_name = 1;
    int64 start_time = 2;
    int64 end_time = 3;
    string page_no = 4;
    string page_size = 5;
  }

  message KspayInsertRiskFeatureViewByBizTimeResponse{
    int32  result    = 1;
    string error_msg = 2;
  }

  message  KspayBranchRequest{
    string  repo_name    = 1; // 应用名称
    string branch_name   = 2; //分支名
    bool is_return_entry = 3; // 是否需要返回entry字段
  }

  message  KspayBranchRequestV2{
    string repo_name     = 1; // 应用名称
    string branch_name   = 2; //分支名
    string team_id       = 3; //任务id
  }



  message KspayBranchAccuracyFMethodsDataResponse{
    int32  result    = 1;
    string error_msg = 2;
    repeated MethodCovInfo finance_methods = 3;
  }

  message PayFinanceLossPoint{
    string table = 1;
    string field = 2;
  }

  message MethodCovInfo{
    string full_name = 1;
    string coverage = 2;
    string report_url = 3;
    repeated PayFinanceLossPoint pay_finance_loss_point = 4;
  }

  message  KspayUpdateFeatureViewBranchByFeatureIdRequest{
    int64 id = 1;
    string  feature_id   = 2;
  }

  message KspayUpdateFeatureViewBranchByFeatureIdResponse{
    int32  result    = 1;
    string error_msg = 2;
  }

  message GetKconfBizSpaceRequest{
    int32 biz_space_key = 1;
  }

  message GetKconfBizSpaceResponse{
    int32  result    = 1;
    string error_msg = 2;
    repeated BizSpaceMap biz_space_map = 3;
  }

  message BizSpaceMap{
    string biz_space_key = 1;
    string biz_space_name = 2;
  }

  message GetLatestTeamWorkerRequest{
    string feature_id = 1;
  }
  message GetLatestTeamWorkerResponse{
    int32  result    = 1;
    string error_msg = 2;
    string team_worker = 3;
  }

service KspayFundsRiskFeatureViewService {
    rpc KspayInsertFundsRiskFeatureView (KspayFundsRiskFeatureViewRequest) returns (KspayFundsRiskFeatureViewResponse);
    rpc KspayInsertFundsRiskFeatureViewBranch (KspayFundsRiskFeatureViewBranchRequest) returns (KspayFundsRiskFeatureViewBranchResponse);
    rpc KspayInsertRiskFeatureViewByBizTime (KspayInsertRiskFeatureViewByBizTimeRequest) returns (KspayInsertRiskFeatureViewByBizTimeResponse);
    rpc KspayUpdateFundsRiskFeatureView (KspayFundsRiskFeatureViewRequest) returns (KspayFundsRiskFeatureViewResponse);
    rpc KspayUpdateFundsRiskFeatureViewBranch (KspayFundsRiskFeatureViewBranchRequest) returns (KspayFundsRiskFeatureViewBranchResponse);
    rpc KspayQueryBranchAccuracyFMethodsData (KspayBranchRequest) returns (KspayBranchAccuracyFMethodsDataResponse);
    rpc KspayUpdateFundsRiskFeatureViewBranchByFeatureId (KspayUpdateFeatureViewBranchByFeatureIdRequest) returns (KspayUpdateFeatureViewBranchByFeatureIdResponse);
    rpc KspayGetKconfBizSpace (GetKconfBizSpaceRequest) returns (GetKconfBizSpaceResponse);
    rpc KspayGetLatestTeamWorker (GetLatestTeamWorkerRequest) returns (GetLatestTeamWorkerResponse);
}

