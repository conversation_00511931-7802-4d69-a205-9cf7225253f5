syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.risk";
option java_outer_classname = "KspayRiskInjectionServiceProto";


message KspayRiskInjectionRequest {

    int32 id = 1;

    string relation_type = 2;

    string relation_id = 3;

    string department = 4;

    string domain = 5;

    int32 injection_type = 6;

    int32 injection_sub_type = 7;

    bool is_operate = 8;

    int32 risk_injection_id = 9;

    string creator = 10;

    string updater = 11;

    bool is_matched = 12;

    string injection_detail = 13;

    string injection_result = 14;

    int64 create_time = 15;

    int64 update_time = 16;

    string source = 17;//来源

    string repo_name = 18;//应用名称

    string branch_name = 19;//分支名称

    //KspayRiskInjectionInfo data = 17; //现在记录演练新增的来源

}
//message  KspayRiskInjectionInfo{
//    string source = 1;//来源
//
//    string repo_name = 2;//应用名称
//
//    string branch_name = 3;//分支名称
//}



message KspayRiskInjectionResponse {

    int32 result = 1;

    string error_msg = 2;
}


message KspayRiskInjectionRecordResponse {

    int32 result = 1;

    string error_msg = 2;

    repeated ListRiskInjectionInfo  list_risk_injection_info = 3;
}


message ListRiskInjectionInfo{
    repeated RiskInjectionInfo risk_injection_info= 1;
}

message RiskInjectionInfo {

    int32 injection_type = 1;

    int32 injection_sub_type = 2;

    bool is_operate = 3;

    repeated ListInjectionDetailInfo list_injection_detail_info = 4;

}


message  ListInjectionDetailInfo {

    repeated  InjectionDetailInfo injection_detail_info = 1;

}


message InjectionDetailInfo {
    bool is_matched = 1;

    string injection_result = 2;

    string injection_detail = 3;

    int64 injection_time = 4;

    int64 update_time = 5;

    string creator = 6;

    string updater = 7;
}








service KspayRiskInjectionService {

    rpc InsertRiskInjection (KspayRiskInjectionRequest) returns (KspayRiskInjectionResponse);

    rpc UpdateRiskInjection (KspayRiskInjectionRequest) returns (KspayRiskInjectionResponse);

    rpc QueryRiskInjectionRecord (KspayRiskInjectionRequest) returns (KspayRiskInjectionRecordResponse);

}

