syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;
import "google/protobuf/any.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.risk";
option java_outer_classname = "KspayConfigScanServiceProto";

message KspayConfigScanByRuleRequest {
  string rule_name = 1;  // 规则名
  string config_dir = 2;  // 配置地址
}

message KspayConfigScanByScriptRuleRequest {
  int64 id = 1;  // 规则id
  string config_dir = 2;  // 配置地址
}

message KspayConfigScanByRuleResponse {
  int32 result = 1;
  string error_msg = 2;
  string  config_dir = 3;
  repeated ErrorDetail data = 4;
}

message ErrorDetail{
    string  rule_name = 1;
    repeated RuleDetail rule_detail = 2;
}

message RuleDetail{
    string rule_type = 1;
    string rule_info = 2;
    int32 update_interval = 3;
    string error_data_info = 4;
    int32 gray_status = 5;
    int32 is_issue = 6;
}

service KspayConfigScanService {

  rpc KspayConfigScanByRule (KspayConfigScanByRuleRequest) returns (KspayConfigScanByRuleResponse);
  rpc KspayConfigScanByScriptRule (KspayConfigScanByScriptRuleRequest) returns (KspayConfigScanByRuleResponse);

}