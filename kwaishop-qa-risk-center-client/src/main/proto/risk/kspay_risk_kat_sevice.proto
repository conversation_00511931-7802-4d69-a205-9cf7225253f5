syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.risk";
option java_outer_classname = "KspayRiskKatServiceProto";


message KspayRiskKatQueryRequest {
  string branch = 1;
  string type = 2;
  string repository = 3;
  repeated RiskInfo risk_info = 4;
  string feature_id = 5;
  string staging_lane_id = 6;
}
message RiskInfo {
  string table = 1;
  repeated string risk_fields = 2;
}

message KspayRiskKatQueryResponse {
  string status = 1;
  string message = 2;
  repeated AutoTestCaseInfo data = 3;
}
message AutoTestCaseInfo {
  string package_name = 1;
  string class_name = 2;
  string method_name = 3;
  string desc = 4;
  repeated DbAssert db_assert = 5;
}

message DbAssert {
  string table_name = 1;
  repeated string column_name = 2;
}

service KspayRiskKatService {
  rpc KspayGetKatAutoTest (KspayRiskKatQueryRequest) returns (KspayRiskKatQueryResponse);
}