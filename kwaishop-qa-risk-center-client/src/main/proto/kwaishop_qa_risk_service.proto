syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

import "kwaishop_qa_risk_common.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf";
option java_outer_classname = "KwaishopQaRiskCenterExampleServiceProto";


message ExampleMethodRequest {
  RiskExampleObject example_object = 1; // ...
}

message ExampleMethodResponse {
  // 考虑引入MerchantResponseCode
  bool success = 1; // ...
}

// grpc service example, 命名可能不包含example段，则直接为KwaishopQaRiskService
service KwaishopQaRiskExampleService {
  rpc ExampleMethod (ExampleMethodRequest ) returns (ExampleMethodResponse);
}