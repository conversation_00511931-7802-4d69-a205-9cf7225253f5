syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.dpm";
option java_outer_classname = "DpmServiceProto";

// 审批-工单信息
message ApprovalEntityDTO {
  string entity_id = 1;
  string entity_title = 2;
  string flow_name = 3; // 流程名称，审批流唯一标识
  string draft_name = 4;
  string state = 5; // 状态
}

// 审批-表单信息
message ApprovalEntityDataDTO {
  string title = 1; // 标题
  string applicant = 2; // 申请人
  string apply_time = 3; // 申请时间
  string biz_code = 4; // bizCode，每个业务不同
  string name = 5; // 业务名称，每个业务不同
  string desc = 6; // 业务描述，每个业务不同
  string config_detail_link = 7; // 配置详情链接，每个业务不同
  map<string, string> biz_data = 8;
}

// 审批-下一环节信息
message ApprovalNextActDTO {
  string act_name = 1; // 当前环节名(End表示流程结束)
  string act_title = 2; // 当前环节中文名
  string act_desc = 3; // 当前环节描述
}

message StartApprovalRequest {
  string user_name = 1; // 用户名(跟kim邮箱前缀对应)
  string flow_name = 2; // 流程名称,用于发起不同的审批流
  string biz_code = 3; // 流程code，每个流程不同
  repeated string code_value = 4; //打标code
  int32 duration = 5;
  int64 user_id = 6;
  string account = 7;
  string password = 8;
  int32 login_type = 9;
}

// 发起审批-响应
message StartApprovalResponse {
  int32 result = 1;
  string error_msg = 2;
  string code = 3; // 报错code S-0224-6651
  string msg = 4; // 实际错误文案
}

// 审批-驳回结单回调-请求
message ApprovalDenyEndCallbackRequest {
  ApprovalEntityDTO entity = 1; // 审批-工单信息
  ApprovalEntityDataDTO entity_data = 2; // 审批-表单信息
}

// 审批-驳回结单回调-响应
message ApprovalDenyEndCallbackResponse {
  int32 result = 1;
  string error_msg = 2;
  string code = 3; // 报错code S-0224-6651
  string msg = 4; // 实际错误文案
  string state = 5; // DONE表示提单/审批/驳回通过，FAIL表示提交/驳回失败
}

// 审批-撤回结单回调-请求
message ApprovalWithdrawEndCallbackRequest {
  ApprovalEntityDTO entity = 1; // 审批-工单信息
  ApprovalEntityDataDTO entity_data = 2; // 审批-表单信息
}

// 审批-撤回结单回调-响应
message ApprovalWithdrawEndCallbackResponse {
  int32 result = 1;
  string error_msg = 2;
  string code = 3; // 报错code S-0224-6651
  string msg = 4; // 实际错误文案
  string state = 5; // DONE表示提单/审批/驳回通过，FAIL表示提交/驳回失败
}

// 审批-提交回调-请求
message ApprovalSubmitCallbackRequest {
  ApprovalEntityDTO entity = 1; // 审批-工单信息
  ApprovalEntityDataDTO entity_data = 2; // 审批-表单信息
  ApprovalNextActDTO next_act = 3; // 审批-下一环节信息
}

// 审批-提交回调-响应
message ApprovalSubmitCallbackResponse {
  int32 result = 1;
  string error_msg = 2;
  string code = 3; // 报错code S-0224-6651
  string msg = 4; // 实际错误文案
  string state = 5; // DONE表示提单/审批/驳回通过，FAIL表示提交/驳回失败
}


message QueryBPMAccountCreatorRequest{
  string secret_key = 1;
  CustomizeVar customize_var = 2;
  string business_id = 3;
  string task_key = 4;
  string process_key = 5;
  Variables variables = 6;
}

message CustomizeVar{
  string user_id = 1;
}

message Variables {// Assignees
  string leader = 1;                               // 领导
  bool time_out_auto_pass = 2;                        // 超时自动通过
  string business_id = 3;                           // 业务ID
  bool rule = 4;                                   // 规则
  string process_name = 5;                         // 流程名称
  string param = 6;                               // 参数
  string process_key = 7;                          // 流程键
  string applicant_id = 8;                         // 申请人ID
  string applicant_first_dept = 9;                  // 申请人第一部门
  string form_type = 10;                            // 表单类型
  string comments = 11;                            // 评论
  bool pass = 12;                                  // 是否通过
  string hrbp = 13;                                // HRBP
  string initiator_name = 14;                       // 发起人姓名
  string initiator_time = 15;                       // 发起时间
  string initiator_id = 16;                         // 发起人ID
  string initiator_org = 17;                        // 发起组织
  string last_assignee = 18;                        // 最后指派人
  string business_summary = 19;   // 业务摘要
  string cross_leader = 20;                         // 跨部门领导
  string operation = 29;                           // 操作
  string dept_code = 30;                            // 部门代码
  string business_explain = 31;                     // 业务说明
}

message QueryBPMAccountCreatorResponse{
  int32 result = 1;
  string error_msg = 2;
  repeated NormalAuditorList normal_auditor_list = 3;
  repeated MultiInstanceAuditorList multi_instance_auditor_list = 4;
}

message NormalAuditorList{
  string username = 1;
  string task_name = 2;
}

message MultiInstanceAuditorList{
  string username = 1;
  string task_name = 2;
}


service TestDpmService {
  // 审批-发起审批流，内部通过入参中flowName路由
  rpc StartApproval(StartApprovalRequest) returns (StartApprovalResponse);
  // 审批-驳回结单回调
  rpc ApprovalDenyEndCallback(ApprovalDenyEndCallbackRequest) returns (ApprovalDenyEndCallbackResponse);
  // 审批-撤回结单回调
  rpc ApprovalWithdrawEndCallback(ApprovalWithdrawEndCallbackRequest) returns (ApprovalWithdrawEndCallbackResponse);
  // 审批-点击通过后提交回调
  rpc ApprovalSubmitCallback(ApprovalSubmitCallbackRequest) returns (ApprovalSubmitCallbackResponse);
  // 老账号池 租借账号审批，获取账号创建者信息
  rpc QueryBPMAccountCreator(QueryBPMAccountCreatorRequest)returns(QueryBPMAccountCreatorResponse);
}