syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;


option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.errorcode";
option java_outer_classname = "ErrorCodeServiceProto";


message AddBizAreaRequest {

  string area_code = 1;

  string area_code_desc = 2;

  string owner = 3;

  string cur_user = 4;

}

message UpdateBizAreaRequest {

  int64 id = 1;

  string area_code = 2;

  string area_code_desc = 3;

  string owner = 4;

  string cur_user = 5;
}

message DeleteBizAreaRequest {

  int64 id = 1;

  string cur_user = 2;
}

message BizAreaListRequest {

  int64 page_num = 1;
  int64 page_size = 2;
  bool created_by_me = 3;
  string cur_user = 4;

}

message AddErrorCodeRequest {

  string error_code = 1;

  string error_code_desc = 2;

  string cur_user = 3;

}

message UpdateErrorCodeRequest {

  int64 id = 1;

  string error_code = 2;

  string error_code_desc = 3;

  string cur_user = 4;

}

message DeleteErrorCodeRequest {

  int64 id = 1;

  string cur_user = 2;

}

message ErrorCodeListRequest {
  int64 page_num = 1;
  int64 page_size = 2;
  bool created_by_me = 3;
  string cur_user = 4;
}

message ErrorInfoRequest {
  string error_code = 1;
}


message ErrorCodeQueryRequest {
  string error_code = 1;
}

message Result {
  int64 result = 1;
  string code = 2;
  string error_msg = 3;
}

message AddBizAreaResponse {
  Result result = 1;
}

message DeleteBizAreaResponse {
  Result result = 1;
}

message UpdateBizAreaResponse {
  Result result = 1;
}

message BizAreaEntity{
  int64 id = 1;

  string area_code = 2;

  string area_code_desc = 3;

  string owner = 4;

  string creator = 5;
}

message BizAreaListResponse {

  Result result = 1;

  repeated BizAreaEntity biz_area_entity = 2;

  int64 page_num = 3;

  int64 total_page = 4;


}


message AddErrorCodeResponse {
  Result result = 1;
}

message DeleteErrorCodeResponse {
  Result result = 1;
}

message UpdateErrorCodeResponse {
  Result result = 1;
}

message ErrorCodeEntity{
  int64 id = 1;

  string error_code = 2;

  string error_code_desc = 3;

  string creator = 4;
}

message ErrorCodeListResponse {

  Result result = 1;

  repeated ErrorCodeEntity error_code_entity = 2;

  int64 page_num = 3;

  int64 total_page = 4;

}

message ErrorCodeInfoResponse {

  Result result = 1;
  string owner = 2;
  string error_code = 3;
  string desc = 4;

}

message ErrorCodeQueryResponse {
  Result result = 1;
  string owner = 2; //错误码负责人
  string end = 3; //错误码所属分层
  string desc = 4; //错误码含义
}




service ErrorCodeRobotService {

  rpc AddBizArea (AddBizAreaRequest) returns (AddBizAreaResponse);

  rpc UpdateBizArea (UpdateBizAreaRequest) returns (UpdateBizAreaResponse);

  rpc DeleteBizArea (DeleteBizAreaRequest) returns (DeleteBizAreaResponse);

  rpc QueryBizAreaList (BizAreaListRequest) returns (BizAreaListResponse);

  rpc AddErrorCode (AddErrorCodeRequest) returns (AddErrorCodeResponse);

  rpc UpdateErrorCode (UpdateErrorCodeRequest) returns (UpdateErrorCodeResponse);

  rpc DeleteErrorCode (DeleteErrorCodeRequest) returns (DeleteErrorCodeResponse);

  rpc GetErrorCodeList (ErrorCodeListRequest) returns (ErrorCodeListResponse);

  rpc GetErrorCodeInfo (ErrorInfoRequest) returns (ErrorCodeInfoResponse);

  rpc QueryErrorCodeInfo (ErrorCodeQueryRequest) returns (ErrorCodeQueryResponse);
}