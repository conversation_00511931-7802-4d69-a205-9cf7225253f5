syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.drill";
option java_outer_classname = "FundsDrillServiceProto";


message CreateFundsRiskDrillRequest{

  int64 id = 1;

  string sub_risk_id = 2;

  string drill_type = 3;

  string drill_result = 4;

  string drill_team_id = 5;

  int64 create_time = 6;

  int64 update_time = 7;

  string creator = 8;

  string updater = 9;

  string operator = 10;

}

message CreateFundsRiskDrillResponse{

  int32 result = 1;

  string error_msg = 2;
}

message UpdateFundsRiskDrillRequest{

  int64 id = 1;

  string sub_risk_id = 2;

  string drill_type = 3;

  string drill_result = 4;

  string drill_team_id = 5;

  int64 create_time = 6;

  int64 update_time = 7;

  string creator = 8;

  string updater = 9;

  string operator = 10;

}

message UpdateFundsRiskDrillResponse{

  int32 result = 1;

  string error_msg = 2;

}

message QueryRiskDrillRequest{


  int64 id = 1;

  string sub_risk_id = 2;

  string drill_type = 3;

  string drill_result = 4;

  string drill_team_id = 5;

  int64 create_time = 6;

  int64 update_time = 7;

  string creator = 8;

  string updater = 9;

  string operator = 10;

}

message QueryRiskDrillResponse{

  int32 result = 1;

  string error_msg = 2;

  FundsRiskDrillDTO data = 3;

}

message QueryRiskDrillListRequest{

  int64 id = 1;

  string sub_risk_id = 2;

  string drill_type = 3;

  string drill_result = 4;

  string drill_team_id = 5;

  int64 create_time = 6;

  int64 update_time = 7;

  string creator = 8;

  string updater = 9;

}

message QueryRiskDrillListResponse{

  int32 result = 1;

  string error_msg = 2;

  RiskDrillViewDTO data = 3;
}

message RiskDrillViewDTO{

  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated FundsRiskDrillDTO funds_risk_drill_list = 4;

  int32 view_type = 5;

}

message  FundsRiskDrillDTO{

  int64 id = 1;

  string sub_risk_id = 2;

  string drill_type = 3;

  string drill_result = 4;

  string drill_team_id = 5;

  int64 create_time = 6;

  int64 update_time = 7;

  string creator = 8;

  string updater = 9;
}


service FundsDrillService {
  rpc CreateFundsRiskDrill (CreateFundsRiskDrillRequest) returns (CreateFundsRiskDrillResponse);
  rpc UpdateFundsRiskDrill (UpdateFundsRiskDrillRequest) returns (UpdateFundsRiskDrillResponse);
  rpc QueryFundsRiskDrillDetail (QueryRiskDrillRequest) returns (QueryRiskDrillResponse);
  rpc QueryFundsRiskDrillList (QueryRiskDrillListRequest) returns (QueryRiskDrillListResponse);
}