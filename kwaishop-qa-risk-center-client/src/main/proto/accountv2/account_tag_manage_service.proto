syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2";
option java_outer_classname = "TestAccountAccountTagManageServiceProto";

message TagBaseInfoDTO {
  int64 tag_id = 1;
  string tag_name = 2;
  string tag_value = 3;
  int32 tag_subject_type = 4;
  int64 user_id = 5;
  string user_name = 6;
  int64 account_type = 7;
  int32 status = 8;
  int64 risk_level = 9;
  string online_system_name = 10;
  string ext = 11;
  int64 b_id = 12;

}



message GetTagMetaInfosRequest{
  repeated string codes = 1;
  string tag_name = 2;
}

message GetTagMetaInfosResponse{
  int32 result = 1;
  repeated TagBaseInfoDTO tags = 2;
}

message AddTagMetaInfoRequest{
  string operator = 1;
  repeated TagBaseInfoDTO tags = 2;
}

message AddTagMetaInfoResponse{
  int32 result = 1;
  string desc = 2;
}

message AddTagsToTestAccountRequest{
  string operator = 1;
  int64 user_id = 2;
  repeated string tag_codes = 3;
  int64 duration = 4;
  string bpm = 5;
}

message AddTagsToTestAccountResponse{
  int32 result = 1;
  string desc = 2;
}

message RemoveTagsToSubjectRequest{
  string operator = 1;
  int64 user_id = 2;
  repeated string tag_codes = 3;
}

message RemoveTagsToTestAccountResponse{
  int32 result = 1;
  string desc = 2;
}


message CreateTestAccountRequest {

  string operator = 1;

  int64 center_id = 2;

  int64 team_id = 3;

  string account = 4;

  string password = 5;

  int32 login_type = 6;

  int64 user_id = 7;

  int64 bid = 8;
}

message CreateTestAccountResponse {

  int32 result = 1;

  string error_msg = 2;

}

message RentTestAccountRequest {

  int64 user_id = 1;

  string borrower = 2;

  int32 duration = 3;

  int64 bid = 4;
}

message RentTestAccountResponse {

  int32 result = 1;

  string error_msg = 2;
}


message QueryTestAccountsRequest {

  int64 user_id = 1;

  int32 login_type = 2;

  int32 data_type = 3;

  int32 account_type = 4;

  int32 status = 5;

  string operator = 6;

  int32 merchant_permission = 7;

  int32 distributor_permission = 8;

  int32 promoter_permission = 9;

  int32 leader_permission = 10;

  int64 bid = 11;

  int32 page_no = 12;

  int32 page_size = 13;

  string store_type = 14;

  bool deposit = 15;

  string tag_value = 16;
}

message QueryTestAccountsResponse {

  int32 result = 1;

  string error_msg = 2;

  PageTestAccountDTO data = 3;

}

message QueryTestAccountRequest {

  int64 user_id = 1;

  int64 bid = 11;

  int64 id = 12;

}

message QueryTestAccountResponse {

  int32 result = 1;

  string error_msg = 2;

  TestAccountDTO data = 3;

}

message QueryMyTestAccountsRequest {

  int64 user_id = 1;

  int32 login_type = 2;

  int32 data_type = 3;

  int32 account_type = 4;

  int32 status = 5;

  string operator = 6;

  int32 merchant_permission = 7;

  int32 distributor_permission = 8;

  int32 promoter_permission = 9;

  int32 leader_permission = 10;

  int64 bid = 11;

  int32 page_no = 12;

  int32 page_size = 13;
}



message QueryMyTestAccountsResponse {

  int32 result = 1;

  string error_msg = 2;

  PageTestAccountDTO data = 3;

}


message QueryMyRentTestAccountsRequest {

  string borrower = 1;

  int32 page_no = 2;

  int32 page_size = 3;
  //'租借/归还/审批状态 0表示租借被拒绝, 1表示已租借成功,2表示已归还,3表示租借发起待审批,4表示已过期'
  int32 rental_status = 4;

  int64 user_id = 5;
}



message QueryMyRentTestAccountsResponse {

  int32 result = 1;

  string error_msg = 2;

  PageTestUserRentAccountDTO data = 3;

}

message DisableAccountRequest{
  int64 user_id = 1;
  string operator = 2;
  int64 bid = 3;
}

message DisableAccountResponse{
  int32 result = 1;

  string error_msg = 2;
}

message DisableTagRequest{
  string tag_value = 1;
  string operator = 2;
}

message DisableTagResponse{
  int32 result = 1;
  string error_msg = 2;
}


message GetAccountTagsRequest{
  int64 user_id = 1;
  string operator = 2;
  int64 bid = 3;
}

message GetAccountTagsResponse{
  int32 result = 1;
  repeated TagBaseInfoDTO data = 2;
  string error_msg = 3;

}

message PageTestAccountDTO {

  int64 total = 1;

  int32 page_no = 2;

  int32 page_size = 3;

  repeated TestAccountDTO details = 4;
}

message PageTestUserRentAccountDTO {

  int64 total = 1;

  int32 page_no = 2;

  int32 page_size = 3;

  repeated RentTestAccountDTO details = 4;
}
message TestAccountDTO {

  int64 bid = 1;

  int64 user_id = 2;

  int32 login_type = 3;

  string login_type_desc = 4;

  int32 status = 5;

  string creator = 6;

  string modifier = 7;

  string status_desc = 8;

  repeated string borrower = 9;

  int32 modifiable = 10;

  int32 modifiable_manage = 11;

  string store_name = 12;

  string store_type = 13;

  bool deposit = 14;

  bool is_seller = 15;

  bool merchant_permission = 16;

  bool distributor_permission = 17;

  bool celebrity_permission = 18;

  bool recruiting_leader = 19;

  string comment = 20;

  string shop_score = 21;

  string promoter_score = 22;

  string shop_rating = 23;

  string seller_name = 24;

  repeated TagBaseInfoDTO tag_base_info = 25;
}


message RentTestAccountDTO {
  int64 id = 1;
  string custom_account = 2;
  string proxy_pd = 3;
  string account = 4;
  int32 rental_status = 5;
  int32 rental_time = 6;
  int64 due_time = 7;
  int32 borrower = 8;
  TestAccountDTO test_user_account = 9;
  string due_time_desc = 10;

}

message ReturnTestAccountRequest {

  int64 user_id = 1;

  string borrower = 2;
}

message ReturnTestAccountResponse {

  int32 result = 1;

  string error_msg = 2;
}


message TestBPMRequest {

  string data = 1;
}

message TestBPMResponse {

  int32 result = 1;

  string error_msg = 2;
}


service TestAccountAccountTagManageService {
  // 查询标签元信息
  rpc GetTagMetaInfos(GetTagMetaInfosRequest) returns (GetTagMetaInfosResponse);
  // 添加标签原信息
  rpc AddTagMetaInfo(AddTagMetaInfoRequest) returns (AddTagMetaInfoResponse);
  // 给主体打标(支持打多个)
  rpc AddTagsToTestAccount(AddTagsToTestAccountRequest) returns (AddTagsToTestAccountResponse);
  // 给主体下标
  rpc RemoveTagsToTestAccount(RemoveTagsToSubjectRequest) returns (RemoveTagsToTestAccountResponse);
  //创建测试账号
  rpc CreateTestAccount(CreateTestAccountRequest) returns (CreateTestAccountResponse);
  //租借测试账号
  rpc RentTestAccount(RentTestAccountRequest) returns (RentTestAccountResponse);
  //查询所有测试账号列表
  rpc QueryTestAccounts(QueryTestAccountsRequest) returns (QueryTestAccountsResponse);
  //单查有测试账号detail
  rpc QueryTestAccount(QueryTestAccountRequest) returns (QueryTestAccountResponse);
  //查询所我的有测试账号列表
  rpc QueryMyTestAccounts(QueryMyTestAccountsRequest) returns (QueryMyTestAccountsResponse);
  //查询我租借的所有测试账号
  rpc QueryMyRentTestAccounts(QueryMyRentTestAccountsRequest) returns (QueryMyRentTestAccountsResponse);
  //注销账号
  rpc DisableAccount(DisableAccountRequest) returns (DisableAccountResponse);
  //注销标签
  rpc DisableTag(DisableTagRequest) returns (DisableTagResponse);
  //获取账号tag
  rpc GetAccountTags(GetAccountTagsRequest) returns (GetAccountTagsResponse);
  // 归还账户
  rpc ReturnTestAccount(ReturnTestAccountRequest) returns (ReturnTestAccountResponse);

  rpc TestBPM(TestBPMRequest)returns (TestBPMResponse);
}