syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;
import "entity/entity_service.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.combine";
option java_outer_classname = "CombineServiceProto";


message QueryRiskSummaryDataRequest {

  string operator  = 1;

  int32  date_type = 2;

}

message QueryRiskSummaryDataResponse {

  int32              result    = 1;

  string             error_msg = 2;

  RiskSummaryDataDTO data      = 3;

}

message QueryGroupByRiskSummaryDataRequest {

  string operator  = 1;

  int32  date_type = 2;

  int64  team_id   = 3;

  int64  center_id = 4;

}

message QueryGroupByRiskSummaryDataResponse {

  int32                       result    = 1;

  string                      error_msg = 2;

  repeated RiskSummaryDataDTO data      = 3;

}

message RiskSummaryDataDTO {

  int64                       risk_detail_total      = 1;

  int64                       risk_cover_count       = 2;

  int64                       verify_effective_count = 3;

  int64                       plan_effective_count   = 4;

  int64                       high_level_count       = 5;

  int64                       medium_level_count     = 6;

  int64                       low_level_count        = 7;

  int32                       view_type              = 8;

  // 监控覆盖率
  int64                       risk_cover_rate        = 9;

  // 监控有效率
  int64                       verify_effective_rate  = 10;

  // 预案有效率
  int64                       plan_effective_rate    = 11;

  // 高风险占比
  int64                       high_level_rate        = 12;

  // 中风险占比
  int64                       medium_level_rate      = 13;

  // 低风险占比
  int64                       low_level_rate         = 14;

  // 新风险增数
  int64                       new_risk_count         = 15;

  // 预案覆盖数
  int64                       plan_cover_count       = 16;

  // 预案覆盖率
  int64                       plan_cover_rate        = 17;

  // 资金敞口
  int64                       fund_exp_total         = 18;

  // id
  int64                       id                     = 19;

  // 名称
  string                      name                   = 20;

  // 实体类型
  int32                       entity_type            = 21;

  // 内部实体数据
  repeated RiskSummaryDataDTO inner_data             = 22;

}

message EnumInfo {

  int32  value = 1;

  string desc  = 2;
}

message StringEnumInfo {

  string value = 1;

  string desc  = 2;
}

message EnumInfoDTO {

  repeated EnumInfo       effective_type          = 1;

  repeated EnumInfo       level_type              = 2;

  repeated EnumInfo       risk_type               = 3;

  repeated EnumInfo       verify_type             = 4;

  repeated EnumInfo       cover_type              = 5;

  repeated EnumInfo       db_type                 = 6;

  repeated EnumInfo       entity_type             = 7;

  repeated EnumInfo       date_type               = 8;

  repeated EnumInfo       problem_type            = 9;

  repeated EnumInfo       problem_missing_type    = 10;

  repeated EnumInfo       accident_level          = 11;

  repeated EnumInfo       online_problem_level    = 12;

  repeated StringEnumInfo fault_call_type         = 13;

  repeated StringEnumInfo fault_relationship_type = 14;

  repeated StringEnumInfo fault_case_source_type  = 15;

  repeated EnumInfo       fault_sync_status       = 16;

  repeated EnumInfo       fault_conform_type      = 17;

  repeated EnumInfo       fault_problem_type      = 18;

  repeated EnumInfo       fault_plan_status       = 19;

  repeated StringEnumInfo fault_plan_env          = 20;
}

message EnumInfoRequest {

  string operator = 1;
}

message EnumInfoResponse {

  int32       result    = 1;

  string      error_msg = 2;

  EnumInfoDTO data      = 3;

}

message FaultCaseCombineRequest {

  string operator   = 1;

  int64  start_time = 2;

  int64  end_time   = 3;

  int64  center_id  = 4;
}

message FaultCaseCombineResponse {

  int32               result    = 1;

  string              error_msg = 2;

  FaultCaseCombineDTO data      = 3;

}

message FaultCaseCombineDTO {

  int64                        case_sum        = 1;

  int64                        case_record_sum = 2;

  repeated FaultCombineInfoDTO combine_info    = 3;
}

message FaultDetailsDTO{
  string member_name      = 1;
  int64  fault_record_num = 2;
}

message FaultCombineInfoDTO {

  int64  entity_id                   = 1;

  string entity_name                 = 2;

  int32  entity_type                 = 3;

  int64  case_count                  = 4;

  int64  case_record_count           = 5;

  int64  case_problem_count          = 6;

  int64  case_problem_unsolved_count = 7;

  int64  case_problem_solved_count   = 8;
}


message FaultCaseProblemCombineRequest {

  string operator   = 1;

  int64  start_time = 2;

  int64  end_time   = 3;

  int64  center_id  = 4;
}

message FaultCaseProblemCombineResponse {

  int32                               result    = 1;

  string                              error_msg = 2;

  repeated FaultCombineProblemInfoDTO data      = 3;

}

message FaultCombineProblemInfoDTO {

  string problem_type  = 1;

  int64  problem_count = 2;
}

message FaultCaseLevelCombineRequest {

  string operator           = 1;

  int64  center_id          = 2;

  bool   query_cover_custom = 3;

  int64  start_time         = 4;

  int64  end_time           = 5;
}

message FaultCaseLevelCombineResponse {

  int32                  result    = 1;

  string                 error_msg = 2;

  repeated EntityDataDTO data      = 3;
}

message FaultDetailsGroupByMemberResponse{
  int32                    result    = 1;

  string                   error_msg = 2;

  repeated FaultDetailsDTO data      = 3;
}

message FaultDetailsGroupByMemberRequest{
  string operator   = 1;

  int64  start_time = 2;

  int64  end_time   = 3;

}

service CombineDomainService {

  rpc QueryRiskSummaryData(QueryRiskSummaryDataRequest) returns (QueryRiskSummaryDataResponse);

  rpc QueryGroupByRiskSummaryData(QueryGroupByRiskSummaryDataRequest) returns (QueryGroupByRiskSummaryDataResponse);

  rpc GetEnumInfo(EnumInfoRequest) returns (EnumInfoResponse);

  rpc FaultCaseCombine (FaultCaseCombineRequest) returns (FaultCaseCombineResponse);

  rpc FaultCaseProblemCombine (FaultCaseProblemCombineRequest) returns (FaultCaseProblemCombineResponse);

  rpc FaultCaseLevelCombine (FaultCaseLevelCombineRequest) returns (FaultCaseLevelCombineResponse);

  rpc FaultDetailsGroupByMember(FaultDetailsGroupByMemberRequest) returns (FaultDetailsGroupByMemberResponse);
}