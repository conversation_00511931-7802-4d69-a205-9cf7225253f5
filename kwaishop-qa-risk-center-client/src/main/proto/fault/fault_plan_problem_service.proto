syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.fault";
option java_outer_classname = "FaultPlanProblemServiceProto";

message CreateFaultPlanProblemRequest {
  string operator = 1;

  string case_name = 2;

  int64 plan_id = 3;

  string problem_type = 4;

  string problem_team = 5;

  repeated int64 relation_case = 6;

  int64 resolve_time = 7;

  int64 real_resolve_time = 8;

  int32 status = 9;

  string problem_detail = 10;

  repeated string problem_pic = 11;

  string auto_case_detail = 12;

  repeated string auto_case_pic = 13;

  string monitor_detail = 14;

  repeated string monitor_pic = 15;

  string risk_plan_detail = 16;

  repeated string risk_plan_pic = 17;

  int64 team_id = 18;

  int64 center_id = 19;
}

message CreateFaultPlanProblemResponse {
  int32 result = 1;

  string error_msg = 2;
}

message UpdateFaultPlanProblemRequest {
  string operator = 1;

  string case_name = 2;

  int64 plan_id = 3;

  string problem_type = 4;

  string problem_team = 5;

  repeated int64 relation_case = 6;

  int64 resolve_time = 7;

  int64 real_resolve_time = 8;

  int32 status = 9;

  string problem_detail = 10;

  repeated string problem_pic = 11;

  string auto_case_detail = 12;

  repeated string auto_case_pic = 13;

  string monitor_detail = 14;

  repeated string monitor_pic = 15;

  string risk_plan_detail = 16;

  repeated string risk_plan_pic = 17;

  int64 id = 18;

  int64 team_id = 19;

  int64 center_id = 20;
}

message UpdateFaultPlanProblemResponse {

  int32 result = 1;

  string error_msg = 2;
}

message DeleteFaultPlanProblemRequest {

  string operator = 1;

  int64 id = 2;
}

message DeleteFaultPlanProblemResponse {

  int32 result = 1;

  string error_msg = 2;
}

message QueryFaultPlanProblemListRequest {
  string operator = 1;

  string name = 2;

  int64 team_id = 3;

  int64 center_id = 4;

  int64 id = 5;

  int64 plan_id = 8;

  int32 status = 9;

}

message FaultPlanProblemDTO {
  string creator = 1;

  string case_name = 2;

  int64 plan_id = 3;

  string problem_type = 4;

  string problem_team = 5;

  repeated int64 relation_case = 6;

  int64 resolve_time = 7;

  int64 real_resolve_time = 8;

  int32 status = 9;

  string problem_detail = 10;

  repeated string problem_pic = 11;

  string auto_case_detail = 12;

  repeated string auto_case_pic = 13;

  string monitor_detail = 14;

  repeated string monitor_pic = 15;

  string risk_plan_detail = 16;

  repeated string risk_plan_pic = 17;

  string center_name = 18;

  string team_name = 19;

  string id = 20;

  string plan_name = 21;

}

message PageFaultPlanProblemDTO {
  int64 total = 1;

  int32 page_no = 2;

  int32 page_size = 3;
  repeated  FaultPlanProblemDTO details = 4;
}

message QueryFaultPlanProblemListResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated FaultPlanProblemDTO details = 3;

}

message QueryFaultPlanProblemPageListRequest {
  string operator = 1;

  string name = 2;

  int64 team_id = 3;

  int64 center_id = 4;

  int64 id = 5;

  int64 plan_id = 8;

  int32 status = 9;

  int32 page_no = 11;

  int32 page_size = 12;

}

message QueryFaultPlanProblemPageListResponse {

  int32 result = 1;

  string error_msg = 2;

  PageFaultPlanProblemDTO data = 3;

}

service FaultPlanProblemDomainService{

  rpc CreateFaultPlanProblem (CreateFaultPlanProblemRequest) returns(CreateFaultPlanProblemResponse);
  rpc UpdateFaultPlanProblem (UpdateFaultPlanProblemRequest) returns(UpdateFaultPlanProblemResponse);
  rpc DeleteFaultPlanProblem (DeleteFaultPlanProblemRequest) returns(DeleteFaultPlanProblemResponse);
  rpc QueryFaultPlanProblemList (QueryFaultPlanProblemListRequest) returns(QueryFaultPlanProblemListResponse);
  rpc QueryFaultPlanProblemPageList (QueryFaultPlanProblemPageListRequest) returns(QueryFaultPlanProblemPageListResponse);

}

