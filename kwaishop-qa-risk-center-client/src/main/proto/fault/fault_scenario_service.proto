syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.fault";
option java_outer_classname = "FaultScenarioServiceProto";


message ImportFaultScenarioRequest{

  string cdn_url = 1;

  int64  team_id = 2;

}

message ImportFaultScenarioResponse{
  int32  result    = 1;

  string error_msg = 2;
}

message QueryScenarioListRequest{
  string operator     = 1;
  int64  team_id      = 2;

  int64  center_id    = 3;
  string caller_name  = 4;
  string business_tag = 5;
  int32  index_level  = 6;

  int64  start_time   = 7;

  int64  end_time     = 8;

}

message FaultScenarioDTO {

  int64          team_id                  = 1;

  int64          center_id                = 2;

  string         team_name                = 3;

  string         center_name              = 4;

  string         business_tag             = 5;

  int32          index_level              = 6;

  repeated int64 plan_ids                 = 7;

  int64          need_count               = 8;

  int64          do_count                 = 9;

  int64          monitor_intercept_count  = 10;

  int64          preplan_intercept_count  = 11;

  int64          autotest_intercept_count = 12;

  repeated int64 record_ids               = 13;

  bool           pass                     = 14;

}

message FaultRecordScenarioDTO{
  string business_tag       = 1;
  int32  index_level        = 2;
  int64  case_id            = 3;
  int32  monitor_intercept  = 4;
  int32  preplan_intercept  = 5;
  int32  autotest_intercept = 6;
  int64  plan_id            = 7;
  int64  record_id          = 8;
  int64  team_id            = 9;
}


message QueryScenarioListResponse{
  int32                     result    = 1;

  string                    error_msg = 2;

  repeated FaultScenarioDTO data      = 3;

}

message QueryPageScenarioRequest{
  int64  team_id   = 1;
  string scenario  = 2;
  int32  page_no   = 3;
  int32  page_size = 4;
  string level     = 5;
  repeated string status    = 6;
  int64  id        = 7;
  string entry     = 8;
}


message QueryPageScenarioResponse{

  int32                result    = 1;

  string               error_msg = 2;

  PageFaultScenarioDTO data      = 3;
}


message FaultScenarioV2DTO{

  int64          team_id                  = 1;

  int64          center_id                = 2;

  string         team_name                = 3;

  string         center_name              = 4;

  string         scenario                 = 5;

  string         level                    = 6;

  int64          do_count                 = 7;

  int64          monitor_intercept_count  = 8;

  int64          preplan_intercept_count  = 9;

  int64          autotest_intercept_count = 10;

  repeated int64 record_ids               = 11;

  int64          pass_count               = 12;

  string         business_entry           = 13;

  string         entry_type               = 14;

  string         entry_ksn                = 15;

  int64          id                       = 16;

  string         status                   = 17;

}

message PageFaultScenarioDTO{

  int64                       total     = 1;

  int32                       page_no   = 2;

  int32                       page_size = 3;

  repeated FaultScenarioV2DTO details   = 4;

}

message GetScenarioWithOperatorRequest{

  string operator = 1;

}

message GetScenarioWithOperatorResponse{

  int32                 result    = 1;

  string                error_msg = 2;

  repeated ScenarioInfo data      = 3;

}

message ScenarioInfo{
  int64  id   = 1;
  string name = 2;
}

message QueryAccuracyAnalyzeTaskRequest{
  int64 id = 1;
}

message QueryAccuracyAnalyzeTaskResponse{

  int32                  result    = 1;

  string                 error_msg = 2;
  AccuracyAnalyzeTaskDTO data      = 3;

}

message AccuracyAnalyzeTaskDTO{
  string application       = 1;
  string result            = 2;
  int64  gmt_created       = 3;
  int64  gmt_modified      = 4;
  string kdev_require_link = 5;
  int64  id                = 6;
}


message ExportAccuracyDataResponse{
  int32  result    = 1;

  string error_msg = 2;
}

message ExportAccuracyDataRequest{

}

message QueryFaultScenarioMethodResponse{

  int32                       result    = 1;

  string                      error_msg = 2;

  repeated FaultScenarioV2DTO data      = 3;
}

message QueryFaultScenarioMethodRequest{

  int64  team_id   = 1;
  string entry     = 2;
  string entry_ksn = 3;

}

message UpdateFaultScenarioStatusResponse{

  int32  result    = 1;

  string error_msg = 2;

}

message UpdateFaultScenarioStatusRequest{

  string type            = 1;

  string create_time     = 2;

  string branch          = 3;

  string require_link    = 4;

  int64  fault_record_id = 5;

  string operators       = 6;

  string scenario_id     = 7;

  string task_id         = 8;

  bool   need_fault      = 9;

  string reason          = 10;

}

message QueryFaultChangeRecordListResponse{
  int32                         result    = 1;

  string                        error_msg = 2;

  repeated FaultChangeRecordDTO data      = 3;
}

message QueryFaultChangeRecordListRequest{
  int64 scenario_id = 1;
}

message FaultChangeRecordDTO{

  string type            = 1;

  int64  create_time     = 2;

  string branch          = 3;

  string require_link    = 4;

  int64  fault_record_id = 5;

  string operators       = 6;

  int64  scenario_id     = 7;

  int64  task_id         = 8;

  int64  plan_id         = 9;

  string conclusion      = 10;

}

message UpdateFaultScenarioInfoRequest{
  int64  id            = 1;
  string scenario_name = 2;
  string entry         = 3;
  string level         = 4;
  string ksn           = 5;
}

message UpdateFaultScenarioInfoResponse{

  int32  result    = 1;

  string error_msg = 2;

}

message CreateFaultScenarioRequest{
  string scenario_name = 1;
  string entry         = 2;
  string level         = 3;
  int64  center_id     = 4;
  int64  team_id       = 5;
  string type          = 6;
  string ksn           = 7;
  string operator      = 8;
}

message CreateFaultScenarioResponse{
  int32  result    = 1;

  string error_msg = 2;
}

message DeleteFaultScenarioRequest{
  int64  id       = 1;
  string operator = 2;
}

message DeleteFaultScenarioResponse{
  int32  result    = 1;

  string error_msg = 2;
}

message FaultScenarioStatisticsDTO{
  string scenario_name       = 1;
  int64  change_num          = 2;
  int64  fault_interval      = 3;
  int64  scenario_id         = 4;
  int64  team_id             = 5;
  int64  evaluate_interval   = 6;
  bool   need_fault          = 7;
  bool   need_evaluate       = 8;
  bool   need_evaluate_delay = 9;
  bool   is_evaluated        = 10;
  bool   is_fault            = 11;
  bool   need_fault_delay    = 12;
  bool   not_need_fault      = 13;
  bool   fault_delay         = 14;
  int64 fault_count = 15;
  int64 evaluated_count = 16;
}

message FaultTeamStatisticsDTO{
  string                              team_name               = 1;
  repeated FaultScenarioStatisticsDTO detail                  = 2;
  int64                               change_num              = 3;
  int64                               max_fault_interval      = 4;
  int64                               team_id                 = 5;
  int64                               max_evaluate_interval   = 6;
  int64                               avg_fault_interval      = 7;
  int64                               avg_evaluate_interval   = 8;
  int64                               need_fault_num          = 9;
  int64                               need_evaluate_num       = 10;
  int64                               need_evaluate_delay_num = 11;
  int64                               evaluated_num           = 12;
  int64                               fault_num               = 13;
  int64                               need_fault_delay_num    = 14;
  int64                               not_need_fault_num      = 15;
  int64                               fault_delay_num         = 16;
  int64 fault_count = 17;
  int64 evaluated_count = 18;
}

message CalculateFaultTeamStatisticsRequest{
  int64 start_time = 1;
  int64 end_time   = 2;

}

message CalculateFaultTeamStatisticsResponse{
  int32                           result    = 1;

  string                          error_msg = 2;

  repeated FaultTeamStatisticsDTO data      = 3;
}


message QuerySrcServiceCallCountRequest{
  string src_ksn    = 1;
  string src_method = 2;
  string start_time = 3;
  string end_time   = 4;
}

message QuerySrcServiceCallCountResponse{
  int32           result    = 1;

  string          error_msg = 2;

  repeated string data      = 3;

}

message QueryRecommendSrcMethodRequest{

  string src_service = 1;
  string src_method  = 2;
  int64  team_id     = 3;
  int32  page_size   = 4;
  int32  page_no     = 5;

}

message QueryRecommendSrcMethodResponse{

  int32                     result    = 1;

  string                    error_msg = 2;

  PageRecommendSrcMethodDTO data      = 3;
}

message PageRecommendSrcMethodDTO{
  int64                          total     = 1;

  int32                          page_no   = 2;

  int32                          page_size = 3;

  repeated RecommendSrcMethodDTO details   = 4;


}

message RecommendSrcMethodDTO{
  string src_service = 1;
  string src_method  = 2;
  int64  create_time = 3;
  int64  id          = 4;

}

message UpdateRecommendMethodTypeRequest{

  int64 id   = 1;
  int32 type = 2;
}

message UpdateRecommendMethodTypeResponse{
  int32  result    = 1;

  string error_msg = 2;

}

service FaultScenarioDomainService {
  rpc QueryFaultScenarioList (QueryScenarioListRequest) returns (QueryScenarioListResponse);
  rpc ImportFaultScenario(ImportFaultScenarioRequest) returns(ImportFaultScenarioResponse);
  rpc QueryPageFaultScenario (QueryPageScenarioRequest) returns(QueryPageScenarioResponse);
  rpc GetScenarioWithOperator(GetScenarioWithOperatorRequest) returns (GetScenarioWithOperatorResponse);
  rpc QueryAccuracyAnalyzeTask(QueryAccuracyAnalyzeTaskRequest) returns (QueryAccuracyAnalyzeTaskResponse);
  rpc ExportAccuracyData(ExportAccuracyDataRequest) returns (ExportAccuracyDataResponse);
  rpc QueryFaultScenarioMethod(QueryFaultScenarioMethodRequest) returns (QueryFaultScenarioMethodResponse);
  rpc UpdateFaultScenarioStatus(UpdateFaultScenarioStatusRequest) returns(UpdateFaultScenarioStatusResponse);
  rpc QueryFaultChangeRecordList(QueryFaultChangeRecordListRequest) returns(QueryFaultChangeRecordListResponse);
  rpc UpdateFaultScenarioInfo(UpdateFaultScenarioInfoRequest) returns(UpdateFaultScenarioInfoResponse);
  rpc CreateFaultScenario(CreateFaultScenarioRequest) returns(CreateFaultScenarioResponse);
  rpc DeleteFaultScenario(DeleteFaultScenarioRequest) returns(DeleteFaultScenarioResponse);
  rpc CalculateFaultTeamStatistics(CalculateFaultTeamStatisticsRequest) returns(CalculateFaultTeamStatisticsResponse);
  rpc QuerySrcServiceCallCount(QuerySrcServiceCallCountRequest) returns(QuerySrcServiceCallCountResponse);
  rpc QueryRecommendSrcMethod(QueryRecommendSrcMethodRequest) returns(QueryRecommendSrcMethodResponse);
  rpc UpdateRecommendMethodType(UpdateRecommendMethodTypeRequest) returns(UpdateRecommendMethodTypeResponse);
}

