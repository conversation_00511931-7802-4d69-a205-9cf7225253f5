syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;
import "fault/fault_case_service.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.fault";
option java_outer_classname = "FaultPlanServiceProto";


message CreateFaultPlanRequest {

  string         operator       = 1;

  string         name           = 2;

  int64          team_id        = 3;

  int64          center_id      = 4;

  int64          start_time     = 5;

  int64          end_time       = 6;

  repeated int64 relation_case  = 7;

  int32          type           = 8;

  string         risk_plan      = 9;

  string         monitor        = 10;

  string         auto_case_path = 11;

  string         role           = 12;

  int32          priority       = 13;

}

message CreateFaultPlanResponse {

  int32  result    = 1;

  string error_msg = 2;
}

message UpdateFaultPlanRequest {

  string         operator       = 1;

  string         name           = 2;

  int64          team_id        = 3;

  int64          center_id      = 4;

  int64          start_time     = 5;

  int64          end_time       = 6;

  repeated int64 relation_case  = 7;

  int64          id             = 8;

  string         risk_plan      = 9;

  string         monitor        = 10;

  string         auto_case_path = 11;

  string         role           = 12;

  int32          priority       = 13;

  int32          type           = 14;
}

message UpdateFaultPlanResponse {

  int32  result    = 1;

  string error_msg = 2;
}

message DeleteRelationCaseRequest {

  string operator = 1;

  int64  plan_id  = 2;

  int64  case_id  = 3;
}

message DeleteRelationCaseResponse {

  int32  result    = 1;

  string error_msg = 2;
}

message DeleteFaultPlanRequest {

  string operator = 1;

  int64  id       = 2;
}

message DeleteFaultPlanResponse {

  int32  result    = 1;

  string error_msg = 2;
}

message QueryFaultPlanListRequest {

  string         operator   = 1;

  string         name       = 2;

  int64          team_id    = 3;

  int64          center_id  = 4;

  int64          id         = 5;

  int64          start_time = 8;

  int64          end_time   = 9;

  repeated int64 ids        = 10;

  repeated int32 status     = 11;
}

message QueryFaultPlanListByCaseIdRequest {

  string operator  = 1;

  int64  case_id   = 2;

  int64  team_id   = 3;

  int64  center_id = 4;

  int32  page_no   = 5;

  int32  page_size = 6;

}

message FaultPlanDTO {

  int64          id              = 1;

  string         name            = 2;

  int64          team_id         = 3;

  int64          center_id       = 4;

  string         team_name       = 5;

  string         center_name     = 6;

  int64          start_time      = 7;

  int64          end_time        = 8;

  int64          update_time     = 9;

  repeated int64 relation_case   = 10;

  int32          status          = 11;

  string         status_desc     = 12;

  int64          real_start_time = 13;

  int64          real_end_time   = 14;

  int32          case_size       = 15;

  int32          priority        = 16;

  string         monitor         = 17;

  string         risk_plan       = 18;

  string         auto_case_path  = 19;

  int32          type            = 20;

  string         role            = 21;
}

message QueryFaultPlanListResponse {

  int32                 result    = 1;

  string                error_msg = 2;

  repeated FaultPlanDTO data      = 3;
}

message QueryFaultPlanPageListRequest {
  string         operator   = 1;

  string         name       = 2;

  int64          team_id    = 3;

  int64          center_id  = 4;

  int32          page_no    = 5;

  int32          page_size  = 6;

  int64          id         = 7;

  int64          start_time = 8;

  int64          end_time   = 9;

  repeated int64 ids        = 10;

  repeated int32 status     = 11;
}

message PageFaultPlanDTO {

  int64                 total     = 1;

  int32                 page_no   = 2;

  int32                 page_size = 3;

  repeated FaultPlanDTO details   = 4;
}

message QueryFaultPlanPageListResponse {

  int32            result    = 1;

  string           error_msg = 2;

  PageFaultPlanDTO data      = 3;
}

message StartFaultPlanRequest {
  string operator    = 1;

  int64  id          = 2;

  string plan_affect = 3;

  string head        = 4;

  string plan_env    = 5;
}

message StartFaultPlanResponse {
  int32  result    = 1;

  string error_msg = 2;
}

message UpdateFaultPlanRecordRequest {

  string operator     = 1;

  int64  id           = 2;

  int32  conform_type = 3;

  string report       = 4;

  int32  update_type  = 5;
}

message UpdateFaultPlanRecordResponse {
  int32  result    = 1;

  string error_msg = 2;
}

message BatchUpdateFaultPlanRecordRequest {
  string         operator     = 1;

  int64          plan_id      = 2;

  int32          conform_type = 3;

  repeated int64 ids          = 4;
}

message BatchUpdateFaultPlanRecordResponse {
  int32  result    = 1;

  string error_msg = 2;
}

message QueryFaultPlanRecordListRequest {

  string         operator     = 1;

  int64          center_id    = 2;

  int64          team_id      = 3;

  bool           query_report = 4;

  repeated int32 status       = 5;

}

message FaultPlanRecordDTO {
  int64           id                = 1;

  string          case_name         = 2;

  int64           team_id           = 3;

  int64           center_id         = 4;

  string          team_name         = 5;

  string          center_name       = 6;

  int32           conform_type      = 7;

  string          conform_type_desc = 8;

  string          report            = 9;

  string          status_desc       = 13;

  string          plan_name         = 14;

  string          kstable_link      = 15;

  repeated string problem_list      = 16;

  FaultCaseDTO    case_info         = 17;

  string          pass              = 18;

  string          monitor_info      = 19;

  string          preplan_info      = 20;

  string          autotest_info     = 21;

  string          modifier          = 22;

  int64           update_time       = 23;

  int64           plan_id           = 24;

}

message FaultPlanCaseRequest {

  string operator  = 1;

  int64  plan_id   = 2;

  int32  page_no   = 3;

  int32  page_size = 4;
}

message FaultPlanCaseResponse {

  int32                     result    = 1;

  string                    error_msg = 2;

  repeated FaultPlanCaseDTO data      = 3;
}

message FaultPlanCaseDTO {

  string       plan_name = 1;

  FaultCaseDTO case_info = 3;
}

message QueryFaultPlanRecordListResponse {

  int32                       result    = 1;

  string                      error_msg = 2;

  repeated FaultPlanRecordDTO data      = 3;
}

message PageFaultPlanRecordDTO {

  int64                       total     = 1;

  int32                       page_no   = 2;

  int32                       page_size = 3;

  repeated FaultPlanRecordDTO details   = 4;

}

message QueryFaultPlanRecordPageListRequest {

  string         operator  = 1;

  int64          id        = 2;

  int32          page_no   = 3;

  int32          page_size = 4;

  int64          plan_id   = 5;

  int64          case_id   = 6;

  repeated int32 status    = 7;

}

message QueryFaultPlanRecordPageListResponse {

  int32                  result    = 1;

  string                 error_msg = 2;

  PageFaultPlanRecordDTO data      = 3;
}

message FaultPlanDelayMessage {

  int64 id         = 1;

  int64 start_time = 2;

  int64 end_time   = 3;

  int64 version    = 4;

}

message QueryPlanRecordReportRequest {
  string operator = 1;
  int64  id       = 2;
}

message KcdnDTO {
  string name     = 1;
  string url      = 2;
  string response = 3;
  string status   = 4;
}

message PlanRecordReportDTO {
  string           caller_info        = 1;
  string           callee_info        = 2;
  string           autotest_intercept = 3;
  string           autotest_info      = 4;
  repeated KcdnDTO autotest_pic       = 5;
  string           monitor_intercept  = 6;
  string           monitor_info       = 7;
  repeated KcdnDTO monitor_pic        = 8;
  string           pre_plan_intercept = 9;
  string           pre_plan_info      = 10;
  repeated KcdnDTO pre_plan_pic       = 11;
  int64            inject_time        = 12;
  int64            alerts_time        = 13;
  int64            stop_time          = 14;
  string           pass               = 15;
  string           scenario_name      = 16;
  int64            scenario_id        = 17;
}



message QueryPlanRecordReportResponse {

  int32               result    = 1;

  string              error_msg = 2;

  PlanRecordReportDTO data      = 3;
}

message UpdatePlanRecordReportRequest {
  string           caller_info        = 1;
  string           callee_info        = 2;
  string           autotest_intercept = 3;
  string           autotest_info      = 4;
  repeated KcdnDTO autotest_pic       = 5;
  string           monitor_intercept  = 6;
  string           monitor_info       = 7;
  repeated KcdnDTO monitor_pic        = 8;
  string           pre_plan_intercept = 9;
  string           pre_plan_info      = 10;
  repeated KcdnDTO pre_plan_pic       = 11;
  int64            inject_time        = 12;
  int64            alerts_time        = 13;
  int64            stop_time          = 14;
  int64            id                 = 15;
  string           operator           = 16;
  string           pass               = 17;
  int64            scenario_id        = 18;
}

message UpdatePlanRecordReportResponse {
  int32  result    = 1;

  string error_msg = 2;
}

message QueryFaultPlanRecordByIdsRequest {
  repeated int64 id = 1;
}

message QueryFaultPlanRecordByIdsResponse {

  int32                       result    = 1;

  string                      error_msg = 2;

  repeated FaultPlanRecordDTO data      = 3;
}

message CheckfaultReportPicRequest {

  string url = 1;
}

message CheckfaultReportPicResponse{
  int32  result    = 1;

  string error_msg = 2;

  repeated string words = 3;
}

service FaultPlanDomainService {

  rpc CreateFaultPlan (CreateFaultPlanRequest) returns(CreateFaultPlanResponse);

  rpc UpdateFaultPlan (UpdateFaultPlanRequest) returns(UpdateFaultPlanResponse);

  rpc DeleteFaultPlan (DeleteFaultPlanRequest) returns(DeleteFaultPlanResponse);

  rpc QueryFaultPlanList (QueryFaultPlanListRequest) returns(QueryFaultPlanListResponse);

  rpc StartFaultPlan(StartFaultPlanRequest) returns (StartFaultPlanResponse);

  rpc QueryFaultPlanPageList (QueryFaultPlanPageListRequest) returns (QueryFaultPlanPageListResponse);

  rpc QueryFaultPlanRecordList(QueryFaultPlanRecordListRequest) returns(QueryFaultPlanRecordListResponse);

  rpc QueryFaultPlanRecordPageList (QueryFaultPlanRecordPageListRequest) returns(QueryFaultPlanRecordPageListResponse);

  rpc UpdateFaultPlanRecord (UpdateFaultPlanRecordRequest) returns(UpdateFaultPlanRecordResponse);

  rpc FaultPlanCase (FaultPlanCaseRequest) returns (FaultPlanCaseResponse);

  rpc DeleteRelationCase (DeleteRelationCaseRequest) returns (DeleteRelationCaseResponse);

  rpc BatchUpdateFaultPlanRecord (BatchUpdateFaultPlanRecordRequest) returns (BatchUpdateFaultPlanRecordResponse);

  rpc QueryPlanRecordReport(QueryPlanRecordReportRequest) returns (QueryPlanRecordReportResponse);

  rpc UpdatePlanRecordReport(UpdatePlanRecordReportRequest) returns (UpdatePlanRecordReportResponse);

  rpc QueryFaultPlanPageListByCaseId(QueryFaultPlanListByCaseIdRequest) returns (QueryFaultPlanPageListResponse);

  rpc QueryFaultPlanRecordListByIds(QueryFaultPlanRecordByIdsRequest) returns (QueryFaultPlanRecordByIdsResponse);

  rpc CheckfaultReportPic(CheckfaultReportPicRequest) returns (CheckfaultReportPicResponse);

}