syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.fault";
option java_outer_classname = "FaultAutoServiceProto";


message ApiAutoTestCallRequest{
  string case_path    = 1;
  string lane_id      = 2;
  string execute_user = 3;

}

message ApiAutoTestCallResponse{
  int32  result    = 1;

  string error_msg = 2;

  string data      = 3;
}

message RpcKessCallRequest {

  string ksn          = 1;
  string full_method  = 2;
  string json_param   = 3;
  string lane_id      = 4;
  string execute_user = 5;

}

message RpcKessCallResponse{

  int32  result    = 1;

  string error_msg = 2;

  string data      = 3;

}


service FaultAutoDomainService {

  rpc ApiAutoTestCall (ApiAutoTestCallRequest) returns (ApiAutoTestCallResponse);

  rpc RpcKessCall(RpcKessCallRequest) returns (RpcKessCallResponse);

}