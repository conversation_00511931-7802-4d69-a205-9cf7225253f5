syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.fault";
option java_outer_classname = "FaultCaseServiceProto";

message CreateCaseRequest {
  string operator                = 1;

  int64  team_id                 = 2;

  int64  center_id               = 3;

  string name                    = 4;

  string catalog                 = 5;

  string caller_ksn              = 6;

  string caller_name             = 7;

  string caller_full_method_name = 8;

  string caller_method_name      = 9;

  string caller_type             = 10;

  string callee_ksn              = 11;

  string callee_name             = 12;

  string callee_full_method_name = 13;

  string callee_method_name      = 14;

  string callee_type             = 15;

  string relationship            = 16;

  int32  caller_level            = 17;

  int32  callee_level            = 18;

  string business_tag = 19;

  int32 index_level = 20;

  string caller_full_method_name_2 = 21;

  string callee_full_method_name_2 = 22;

}

message CreateCaseResponse {

  int32  result    = 1;

  string error_msg = 2;
}

message UpdateCaseRequest {
  string         operator                = 1;

  int64          team_id                 = 2;

  int64          center_id               = 3;

  string         name                    = 4;

  string         catalog                 = 5;

  string         caller_ksn              = 6;

  string         caller_name             = 7;

  string         caller_full_method_name = 8;

  string         caller_method_name      = 9;

  string         caller_type             = 10;

  string         callee_ksn              = 11;

  string         callee_name             = 12;

  string         callee_full_method_name = 13;

  string         callee_method_name      = 14;

  string         callee_type             = 15;

  string         relationship            = 16;

  int64          id                      = 17;

  int32          caller_level            = 18;

  int32          callee_level            = 19;

  repeated int64 ids                     = 20;

  int32          index_level             = 21;

  bool           not_need_cover          = 22;

  string         not_need_cover_reason   = 23;

  string business_tag = 24;
}

message UpdateCaseResponse {

  int32  result    = 1;

  string error_msg = 2;
}

message DeleteCaseRequest {

  string operator = 1;

  int64  id       = 2;
}

message DeleteCaseResponse {
  int32  result    = 1;

  string error_msg = 2;
}

message QueryCaseListRequest {
  string         operator                = 1;

  int64          team_id                 = 2;

  int64          center_id               = 3;

  string         name                    = 4;

  string         catalog                 = 5;

  string         caller_ksn              = 6;

  string         caller_name             = 7;

  string         caller_full_method_name = 8;

  string         caller_method_name      = 9;

  string         caller_type             = 10;

  string         callee_ksn              = 11;

  string         callee_name             = 12;

  string         callee_full_method_name = 13;

  string         callee_method_name      = 14;

  string         callee_type             = 15;

  string         relationship            = 16;

  int64          id                      = 17;

  int32          caller_level            = 18;

  int32          callee_level            = 19;

  int32          tag                     = 20;

  repeated int32 caller_level_list       = 21;

  repeated int32 callee_level_list       = 22;

  string         business_tag            = 23;

}

message FaultCaseDTO {

  int64  id                      = 1;

  int64  team_id                 = 2;

  int64  center_id               = 3;

  string name                    = 4;

  string catalog                 = 5;

  string caller_ksn              = 6;

  string caller_name             = 7;

  string caller_full_method_name = 8;

  string caller_method_name      = 9;

  string caller_type             = 10;

  string callee_ksn              = 11;

  string callee_name             = 12;

  string callee_full_method_name = 13;

  string callee_method_name      = 14;

  string callee_type             = 15;

  string relationship            = 16;

  string team_name               = 17;

  string center_name             = 18;

  int64  update_time             = 19;

  string source                  = 20;

  string relationship_desc       = 21;

  string last_result             = 22;

  int64  last_record_id          = 23;

  int32  caller_level            = 24;

  int32  callee_level            = 25;

  string caller_level_desc       = 26;

  string callee_level_desc       = 27;

  string business_tag            = 28;

  int32 index_level = 29;

  bool not_need_cover = 30;

  string not_need_cover_reason = 31;

}

message QueryCaseListResponse {

  int32                 result    = 1;

  string                error_msg = 2;

  repeated FaultCaseDTO data      = 3;
}

message QueryPageCaseListRequest {
  string         operator                = 1;

  int64          team_id                 = 2;

  int64          center_id               = 3;

  string         name                    = 4;

  string         catalog                 = 5;

  string         caller_ksn              = 6;

  string         caller_name             = 7;

  string         caller_full_method_name = 8;

  string         caller_method_name      = 9;

  string         caller_type             = 10;

  string         callee_ksn              = 11;

  string         callee_name             = 12;

  string         callee_full_method_name = 13;

  string         callee_method_name      = 14;

  string         callee_type             = 15;

  string         relationship            = 16;

  int64          id                      = 17;

  int32          page_no                 = 18;

  int32          page_size               = 19;

  int32          caller_level            = 20;

  int32          callee_level            = 21;

  int32          tag                     = 22;

  repeated int32 caller_level_list       = 23;

  repeated int32 callee_level_list       = 24;

  string         business_tag            = 25;
}

message PageFaultCaseDTO {

  int64                 total     = 1;

  int32                 page_no   = 2;

  int32                 page_size = 3;

  repeated FaultCaseDTO details   = 4;
}

message QueryPageCaseListResponse {

  int32            result    = 1;

  string           error_msg = 2;

  PageFaultCaseDTO data      = 3;
}

message BatchUpdateCaseTagRequest {

  string         operator = 1;

  int32          tag_type = 2;

  repeated int64 ids      = 3;

}

message BatchUpdateCaseTagResponse {

  int32  result    = 1;

  string error_msg = 2;
}

message BatchUpdateCaseBusinessTagRequest {
  string         operator     = 1;

  string         business_tag = 2;

  repeated int64 ids          = 3;
}

message BatchUpdateCaseBusinessTagResponse {

  int32  result    = 1;

  string error_msg = 2;
}


service FaultCaseDomainService {
  rpc CreateFaultCase (CreateCaseRequest) returns (CreateCaseResponse);

  rpc UpdateFaultCase (UpdateCaseRequest) returns (UpdateCaseResponse);

  rpc DeleteFaultCase (DeleteCaseRequest) returns (DeleteCaseResponse);

  rpc QueryFaultCaseList (QueryCaseListRequest) returns (QueryCaseListResponse);

  rpc QueryFaultCasePageList (QueryPageCaseListRequest) returns (QueryPageCaseListResponse);

  rpc BatchUpdateCaseTag (BatchUpdateCaseTagRequest) returns (BatchUpdateCaseTagResponse);

  rpc BatchUpdateCaseBusinessTag (BatchUpdateCaseBusinessTagRequest) returns (BatchUpdateCaseBusinessTagResponse);

  rpc BatchUpdateById (UpdateCaseRequest) returns (UpdateCaseResponse);
}

