syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.fault";
option java_outer_classname = "FaultDataSyncServiceProto";


message CreateFaultDataSyncRequest {

  string operator = 1;

  int64 team_id = 2;

  int64 center_id = 3;

  string ksn = 4;

}

message CreateFaultDataSyncResponse {

  int32 result = 1;

  string error_msg = 2;
}

message UpdateFaultDataSyncRequest {

  string operator = 1;

  int64 id = 2;
}

message UpdateFaultDataSyncResponse {

  int32 result = 1;

  string error_msg = 2;
}

message QueryFaultDataSyncPageListRequest {

  string operator = 1;

  int64 id = 2;

  int64 center_id = 3;

  int64 team_id = 4;

  string ksn = 5;

  int32 page_no = 6;

  int32 page_size = 7;

}

message FaultDataSyncDTO {

  int64 id = 1;

  int64 center_id = 2;

  int64 team_id = 3;

  string ksn = 4;

  int32 status = 5;

  string status_desc = 6;

  string center_name = 7;

  string team_name = 8;

  int64 update_time = 9;
}

message PageFaultDataSyncDTO {

  int64 total = 1;

  int32 page_no = 2;

  int32 page_size = 3;

  repeated FaultDataSyncDTO details = 4;

}

message QueryFaultDataSyncPageListResponse {

  int32 result = 1;

  string error_msg = 2;

  PageFaultDataSyncDTO data = 3;
}


service FaultDataSyncDomainService {

  rpc CreateFaultDataSync (CreateFaultDataSyncRequest) returns (CreateFaultDataSyncResponse);

  rpc UpdateFaultDataSync (UpdateFaultDataSyncRequest) returns (UpdateFaultDataSyncResponse);

  rpc QueryFaultDataSyncPageList (QueryFaultDataSyncPageListRequest) returns (QueryFaultDataSyncPageListResponse);

}

