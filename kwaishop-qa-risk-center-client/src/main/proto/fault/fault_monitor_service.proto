syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.fault";
option java_outer_classname = "FaultMonitorServiceProto";


message GetAlarmGroupCallBackRequest{
    string title = 1;
    int64 rule_id = 2;
    string rule_name = 3;
    string rule_link = 4;
    string status = 5;
    string source = 6;
    string priority = 7;
    string event_id = 8;
    string message = 9;
    string uic = 10;
    string webhook = 11;
    int64 timestamp = 12;
    string callback_link = 13;
}

message GetAlarmGroupCallBackResponse{
  int32  result    = 1;

  string error_msg = 2;
}
service FaultMonitorDomainService {

  rpc GetAlarmGroupCallBack (GetAlarmGroupCallBackRequest) returns (GetAlarmGroupCallBackResponse);

}