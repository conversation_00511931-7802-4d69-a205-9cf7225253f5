syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy";
option java_outer_classname = "StrategyFlowRecallServiceProto";

message CommonResponse {
  int32 result = 1;
  string err_msg = 2;
}

message FlowRecordListRequest {
  int64 id = 1;
  string name = 2;
  string scene_key = 3;
  string create_user = 4;
  string start_time = 5;
  string end_time = 6;
  int32 flow_type = 7;
  int32 flow_status = 8;
  int32 page_num = 9;
  int32 page_size = 10;
}

message FlowRecordListResponse {
    CommonResponse resp = 1;
    string data = 2;
}

message FlowRecordDetailRequest {
  int64 id = 1;
}


message FlowRecordDetailResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message FlowRecordAddRequest {
  string name = 1;
  string description = 2;
  int32 flow_type = 4;
  string operator = 5;
  StrategyLog strategy_log = 6;
  RealTime real_time = 7;
  CustomHive custom_hive = 8;
}

//策略离线执行日志流量
message StrategyLog {
  repeated string scene_key_list = 1;
  int32 total = 2;
  repeated string p_date_partition = 3;
  repeated string p_hour_min_partition = 4;
  int32 request_hit_result = 5;    //默认0，未命中1，命中2
  repeated string hit_strategy = 6;
  repeated string hit_action = 7;
  string where_clause = 8;
}

//实时流量
message RealTime {
  repeated string scene_key_list = 1;
}

//自定义hive表流量
message CustomHive {
  int64 id = 1;
}

message FlowRecordVoResponse {
  string name = 1;
  string description = 2;
  int32 flow_type = 4;
  string operator = 5;
  StrategyLogVo strategy_log = 6;
  RealTime real_time = 7;
  CustomHive custom_hive = 8;
}

//策略离线执行日志流量
message StrategyLogVo {
  repeated string scene_key_list = 1;
  int32 total = 2;
  string p_date_partition = 3;
  string p_hour_min_partition = 4;
  int32 request_hit_result = 5;    //默认0，未命中1，命中2
  string hit_strategy = 6;
  string hit_action = 7;
  string where_clause = 8;
}


message FlowRecordAddResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message StrategyFlowTestRequest {
  string sql = 1;
}

message StrategyFlowTestResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message GetAllInstantsRequest {
  string service_name = 1;

  string stage = 2;
}

message GetAllInstantsResponse {
  CommonResponse resp = 1;
  repeated InstantInfo instant_info = 2;
}

message InstantInfo {
  string host_name = 1;

  string ip = 2;

  string port = 3;
}

message FlowReplayAddRequest {
  string name = 1;
  string description = 2;
  int64 flow_record_id = 3;
  int32 replay_qps = 4;
  ReplayConfig replay_config_one = 5;
  ReplayConfig replay_config_two = 6;
  string scene_key = 7;
  string white_feature_key = 8;
  string black_feature_key = 9;
  string operator = 10;
}

message FlowReplayAddResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message ReplayConfig {
  int32 replay_type = 1;    //选择为 1历史流量结果，2泳道，3服务分组，4自定义机器
  string land_id = 2;           //按照泳道选择
  string group = 3;             //按照服务分组选择
  repeated string pod_info_list = 4;
}

message PodInfo {
  string full_name = 1;
  string ip = 2;
  string port = 3;
}

message TestLandIdRpcRequest {
  string scene_key = 1;
  string param = 2;
  string land_id = 3;
  string scene_type = 4;
}

message TestLandIdRpcResponse {
  CommonResponse resp = 1;
  string data = 2;
}


message TestGroupRpcRequest {
  string scene_key = 1;
  string param = 2;
  string group = 3;
  string scene_type = 4;
}

message TestGroupRpcResponse {
  CommonResponse resp = 1;
  string data = 2;
}


message TestIpHostRpcRequest {
  string scene_key = 1;
  string param = 2;
  string ip = 3;
  int32 port = 4;
  string scene_type = 5;
}

message TestIpHostListRpcRequest {
  repeated PodInfo pod_info_list = 1;
  string scene_key = 2;
  string feature_map = 3;
}

message TestIpHostRpcResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message FlowReplayListRequest {
  int64 id = 1;
  string name = 2;
  int64 flow_record_id = 3;
  string scene_key = 4;
  int32 replay_status = 5;
  string land_id = 6;
  string group = 7;
  string ip_port = 8;
  string create_user = 9;
  int64 replay_start_time_start = 10;
  int64 replay_start_time_end = 11;
  int64 replay_end_time_start = 12;
  int64 replay_end_time_end = 13;
  int32 page_num = 14;
  int32 page_size = 15;
  int32 diff_result = 16;
}

message FlowReplayListResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message FlowReplayDetailRequest {
  int64 id = 1;
}

message FlowReplayDetailResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message TestQpsFlowRequest {
  int32 qps = 1;
  int32 time = 2;
}

message TestQpsFlowResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message ClickHouseQueryTestRequest {
  string sql = 1;
}

message ClickHouseQueryTestResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message FlowRecordDetailListRequest {
  int64 flow_record_id = 1;
  string scene_key = 2;
  string id = 3;
  string p_date = 4;
  int32 request_hit_result = 5;
  string biz_key_info = 6;
  int32 page_num = 7;
  int32 page_size = 8;
}

message FlowRecordDetailListResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message GetFlowRecordDetailInfoRequest {
  string id = 1;
  int64 flow_record_id = 2;
}

message GetFlowRecordDetailInfoResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message OneServiceQueryTestRequest {
  string p_date = 1;
  repeated string scene_keys = 2;
  int32 offset = 3;
  int32 limit = 4;
}

message OneServiceQueryTestResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message TestOneServiceRequest {
  string date = 1;
}

message TestOneServiceResponse {
   CommonResponse resp = 1;
   string data = 2;
}

message SetRedisRequest {
  string key = 1;
  string value = 2;
}

message SetRedisResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message GetDateFromCacheRequest {
  int64 id = 1;
}

message GetDateFromCacheResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message GetFlowRecordAnalysisResultRequest {
  int64 id = 1;
}

message GetFlowRecordAnalysisResultResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message GetFlowRecordAnalysisResultWithMdRequest {
  int64 id = 1;
}

message GetFlowRecordAnalysisResultWithMdResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message GetFlowReplayDetailRequest {
  string id = 1;
  int64 flow_replay_id = 2;
}

message GetFlowReplayDetailResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message GetFlowReplayDetailListRequest {
  string id = 1;
  int64 flow_replay_id = 2;
  string scene_key = 3;
  string p_date = 4;
  int32 diff_result = 5;
  string biz_key_info = 6;
  int32 page_num = 7;
  int32 page_size = 8;
}

message GetFlowReplayDetailListResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message GetFlowReplayAnalysisResultRequest {
  int64 id = 1;
}

message GetFlowReplayAnalysisResultResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message StopReplayRequest {
  int64 id = 1;
}

message StopReplayResponse {
  CommonResponse resp = 1;
  string data = 2;
}

message StartReplayByPipelineRequest {
  string project_name = 1;

  string lane_id = 2;

  string branch_id = 3;

  string team_id = 4;

  string execute_user = 5;

  string pipeline_url = 6;

  int64 kdev_job_log_id = 7;

  string kdev_job_log_token = 8;

  string kdev_pipeline_log_creator = 9;
}

message StartReplayByPipelineResponseData {
  string fail_reason = 1;
  string view_url_title = 2;
  string view_url = 3;
  string check_url = 4;
  string cancel_url = 5;
  string view_content = 6;
}

message StartReplayByPipelineResponseFixed {
  int32 status = 1;

  string message = 2;

  StartReplayByPipelineResponseData data = 3;
}

message StartReplayByPipelineResponse {
  int32 status = 1;

  string message = 2;

  string data = 3;
}

message CheckReplayStatusByPipelineRequest {
  string pipeline_id = 1;
}

message CheckReplayStatusByPipelineResponseReport {
  string title = 1;
  string url = 2;
}

message ReplayQrCodeReportList {
  string title = 1;
  string url = 2;
}

message CheckReplayStatusByPipelineResponseData {
  string fail_reason = 1;
  string pass = 2;
  string view_url_title = 3;
  string view_url = 4;
  string view_content = 5;
}

message CheckReplayStatusByPipelineResponseFixed {
  int32 status = 1;
  string message = 2;
  CheckReplayStatusByPipelineResponseData data = 3;
}

message CheckReplayStatusByPipelineResponse {
  int32 status = 1;

  string message = 2;

  string data = 3;
}

//回放查询主
service StrategyFlowReplayService {
  //回放主表新增
  rpc FlowReplayAdd(FlowReplayAddRequest) returns (FlowReplayAddResponse);
  //回放主表列表
  rpc FlowReplayList(FlowReplayListRequest) returns (FlowReplayListResponse);
  //回放主表详情
  rpc FlowReplayDetail(FlowReplayDetailRequest) returns (FlowReplayDetailResponse);

  //回放明细表详情
  rpc GetFlowReplayDetailList(GetFlowReplayDetailListRequest) returns (GetFlowReplayDetailListResponse);
  //回放明细表详情
  rpc GetFlowReplayDetail(GetFlowReplayDetailRequest) returns (GetFlowReplayDetailResponse);

  rpc TestLandIdRpc(TestLandIdRpcRequest) returns (TestLandIdRpcResponse);

  rpc TestGroupRpc(TestGroupRpcRequest) returns (TestGroupRpcResponse);

  rpc TestIpHostRpc(TestIpHostRpcRequest) returns (TestIpHostRpcResponse);

  rpc TestIpHostListRpc(TestIpHostListRpcRequest) returns (TestIpHostRpcResponse);

  rpc TestQpsFlow(TestQpsFlowRequest) returns (TestQpsFlowResponse);

  rpc ClickHouseQueryTest (ClickHouseQueryTestRequest) returns (ClickHouseQueryTestResponse);

  rpc GetReplayCache(GetDateFromCacheRequest) returns (GetDateFromCacheResponse);

  rpc GetFlowReplayAnalysisResult(GetFlowReplayAnalysisResultRequest) returns (GetFlowReplayAnalysisResultResponse);

  rpc StopReplay(StopReplayRequest) returns (StopReplayResponse);

  // 流水线触发接口
  rpc StartReplayByPipeline(StartReplayByPipelineRequest) returns (StartReplayByPipelineResponseFixed);

  // 流水线检查接口
  rpc CheckReplayStatusByPipeline(CheckReplayStatusByPipelineRequest) returns (CheckReplayStatusByPipelineResponseFixed);
}

//流量表查询类
service StrategyFlowRecordService {
  //流量主表查询接口
  rpc FlowRecordList (FlowRecordListRequest) returns (FlowRecordListResponse);
  //流量主表详情
  rpc FlowRecordDetail (FlowRecordDetailRequest) returns (FlowRecordDetailResponse);
  //流量主表新增
  rpc FlowRecordAdd (FlowRecordAddRequest) returns (FlowRecordAddResponse);

  rpc FlowRecordDetailList (FlowRecordDetailListRequest) returns (FlowRecordDetailListResponse);

  rpc GetFlowRecordDetailInfo (GetFlowRecordDetailInfoRequest) returns (GetFlowRecordDetailInfoResponse);

  rpc StrategyFlowTest (StrategyFlowTestRequest) returns (StrategyFlowTestResponse);

  rpc OneServiceQueryTest (OneServiceQueryTestRequest) returns (OneServiceQueryTestResponse);

  // 获取容器云实例列表
  rpc GetAllInstants (GetAllInstantsRequest) returns (GetAllInstantsResponse);

  rpc TestOneService(TestOneServiceRequest) returns (TestOneServiceResponse);

  rpc SetRedis(SetRedisRequest) returns (SetRedisResponse);

  rpc GetRedis(SetRedisRequest) returns (SetRedisResponse);

  rpc GetDateFromCache(GetDateFromCacheRequest) returns (GetDateFromCacheResponse);

  rpc GetFlowRecordAnalysisResult(GetFlowRecordAnalysisResultRequest) returns (GetFlowRecordAnalysisResultResponse);

  rpc GetFlowRecordAnalysisResultWithMd(GetFlowRecordAnalysisResultWithMdRequest) returns (GetFlowRecordAnalysisResultWithMdResponse);
}


