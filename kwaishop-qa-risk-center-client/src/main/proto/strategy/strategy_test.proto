syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.strategy";
option java_outer_classname = "StrategyTestDomainServiceProto";

message StrategyTestRequest {

  string operator = 1;

  int64 user_id = 2;
}

message StrategyTestResponse {

  int32 result = 1;

  string error_msg = 2;


}

message StartRecordingRequest {

  repeated string scene_name = 1;

  int32 record_count = 2;

  string record_time = 3;  // ISO 8601格式

  string user = 4;

  string p_date = 5;

  string sql = 6;

  string expect_lane_id = 7;

  string task_description = 8;

  string task_name = 9;
}

message StartRecordingResponse {

  int32 result = 1;

  string error_msg = 2;
}

message QueryTrafficRequest {

  string scene_name = 1;

  string start_time = 2;  // ISO 8601格式

  string end_time = 3;    // ISO 8601格式
}

message QueryTrafficResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated TrafficRecord records = 3;
}

message TrafficRecord {

  int32 id = 1;

  string scene_name = 2;

  string record_time = 3;

  string traffic_data = 4;

  string expected_result = 5;
}


// 录制任务查询请求
message QueryPageRecordTaskRequest {

  string scene_key = 1;      // 场景key

  string creator = 2;        // 录制人

  string record_partition = 3; // 录制分区

  int64 start_time = 4;     // 录制时间

  int32 page_no = 5;

  int32 page_size = 6;

  int32 status = 7;

  int64 end_time = 8;
}

// 录制任务查询响应
message QueryPageRecordTaskResponse {

  int32 result = 1;

  string error_msg = 2;

  PageRecordTaskDetailDTO data = 3;

  map<string, PageRecordTaskDetailDTO> map = 4;
}

message PageRecordTaskDetailDTO {
  int64 total = 1;

  int32 page_no = 2;

  int32 page_size = 3;

  repeated StrategyRecordListDTO details = 4;
}

// 查看接口请求
message ViewTrafficRecordRequest {
  int64 task_id = 1;         // 录制任务ID
}

// 查看接口响应
message ViewTrafficRecordResponse {
  repeated StrategyTrafficRecordDTO strategy_traffic_records = 1;
}

// 回放详情查询请求
message ReplayDetailRequest {
  string task_id = 1;         // 录制任务ID

  string scene_key = 2;      // 场景key

  int32 diff_conclusion = 3; // diff结论

  int32 page_no = 4;

  int32 page_size = 5;
}

// 回放详情查询响应
message ReplayDetailResponse {
  repeated StrategyTrafficRecordDTO strategy_traffic_records = 1;
}

message QueryPageReplayDetailResponse {

  int32 result = 1;

  string error_msg = 2;

  PageReplayDetailDTO data = 3;

  map<string, PageReplayDetailDTO> map = 4;
}

message PageReplayDetailDTO {

  int64 total = 1;

  int32 page_no = 2;

  int32 page_size = 3;

  repeated StrategyTrafficRecordDTO details = 4;
}

// 数据库表的消息定义

// strategy_record_list 表的定义
message StrategyRecordListDTO {
  int64 id = 1;
  string scene_name = 2;
  string record_partition = 3;
  string status = 4;
  int64 create_time = 5;
  int64 update_time = 6;
  int64 task_id = 7;
  string creator = 8;
  string record_sql = 9;

  string expect_lane_id = 10;

  string diff_value = 11;

  string task_name = 12;

  string task_description = 13;
}

// strategy_traffic_record 表的定义
message StrategyTrafficRecordDTO {
  int64 id = 1;
  string scene_name = 2;
  int64 record_time = 3;
  string traffic_data = 4;
  string expected_result = 5;
  string ext = 6;
  int64 create_time = 7;
  int64 update_time = 8;
  string creator = 9;
  string modifier = 10;
  int32 deleted = 11;
  int64 version = 12;
  int64 record_task_id = 13;
  string actual_result = 14;
  string diff_conclusion = 15;
  int64 replay_time = 16;
  string replay_lane_id = 17;
}

message ReplayRequest {

  repeated int64 id = 1;

  string task_id = 2;

  string lane_id = 3;
}

message SendKafkaRequest {
  string message = 1;
  string topic = 2;
  string type = 3;
}

message TestMsg {
  int64 msg_id = 1;
  string msg_text = 2;
  int64 msg_create_time = 3;
}

message SendKafkaResponse {
  int32 result = 1;

  string error_msg = 2;
}

message SimpleQueryRequest {

  string task_id = 1;

  string case_id = 2;
}

message SimpleQueryResponse {

  int32 result = 1;

  string error_msg = 2;

  string data = 3;
}

message TestRealTimeCacheRequest {
  string scene_key = 1;
}

message TestRealTimeCacheResponse {
  int32 result = 1;

  string error_msg = 2;

  string data = 3;
}

message TestAiOpenAiRequest {
  string json_string = 1;
}

message TestAiOpenAiResponse {
  string json_string = 1;
}


service StrategyTestDomainService {
  // 开始测试，老接口
  rpc StartTest (StrategyTestRequest) returns (StrategyTestResponse);

  // 新建录制任务，进行流量录制
  rpc StartRecording(StartRecordingRequest) returns (StartRecordingResponse);

  // 流量录制查询
//  rpc QueryTraffic(QueryTrafficRequest) returns (QueryTrafficResponse);

  // 录制任务查询
  rpc QueryRecordTask (QueryPageRecordTaskRequest) returns (QueryPageRecordTaskResponse);

  // 查看接口
//  rpc ViewTrafficRecord (ViewTrafficRecordRequest) returns (ViewTrafficRecordResponse);

  // 回放详情查询
  rpc ReplayDetail (ReplayDetailRequest) returns (QueryPageReplayDetailResponse);

  // 更新到今天的录制
//  rpc UpdateTodayRecord (QueryPageRecordTaskRequest) returns (StrategyTestResponse);

  rpc StartReplay(ReplayRequest) returns (StrategyTestResponse);

  rpc SendKafka(SendKafkaRequest) returns (SendKafkaResponse);

  rpc SimpleQuery(SimpleQueryRequest) returns (SimpleQueryResponse);

  rpc TestRealTimeCache(TestRealTimeCacheRequest) returns (TestRealTimeCacheResponse);

  rpc TestAiOpenAi(TestAiOpenAiRequest) returns (TestAiOpenAiResponse);
}