syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.common";
option java_outer_classname = "LogServiceProto";


message FrontLogRequest {

  string path = 1;

  string operator = 2;

  int32 log_type = 3;
}

message FrontLogResponse {

  int32 result = 1;

  string error_msg = 2;
}

message LogMessage {

  string name = 1;

  string operator = 2;

  int32 log_type = 3;

  string dt = 4;

}

message LogDataDTO {

  int32 statistic_type = 1;

  string statistic_type_desc = 2;

  int64 sum = 3;

  repeated DateDataInfo data_info_list = 4;

}

message DateDataInfo {

  string dt = 1;

  int64 total = 2;

  string desc = 3;

}

message LogDataRequest {

  string operator = 1;

  int32 date_type = 2;

  string dt_start = 3;

  string dt_end = 4;

  string path = 5;

  int32 log_type = 6;

}

message LogDataResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated LogDataDTO data = 3;
}


service LogDomainService {

  rpc FrontLog(FrontLogRequest) returns (FrontLogResponse);

  rpc GetLogData(LogDataRequest) returns (LogDataResponse);


}