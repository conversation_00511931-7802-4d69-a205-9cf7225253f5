syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool";
option java_outer_classname = "KwaiSqlQueryServiceProto";


message KwaiSqlQueryServiceRequest {

  int32 query_type = 1;

  int64 datasource_id = 2;

  string query_sql =3;

  string user = 4;

}

message KwaiSqlQueryServiceResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated MapField maps = 3;

}

message MapField {
   map<string, string> string_map = 1;
}

message DmGetByQueryIdRequest {

  int64 query_id = 1;

  string current_user = 2;

}

message DmGetByQueryIdResponse {

  int64 query_id = 1;
  int32 product_id = 2;
  string product_name = 3;
  string desc = 4;
  string sql = 5;
  string creator = 6;
  string created_at = 7;
  string updated_at = 8;
  string status = 9;
  string param_str = 10;
}

message Dm2GetByServiceCodeRequest {
  string service_code = 1;
  string version_id = 2;
  string current_user = 3;
}

message Dm2GetByServiceCodeResponse {
  string service_code = 1;
  string data_source_url = 2;
  string config_text = 3;
  int64 service_id = 4;
  int64 version_id = 5;
  string version_status = 6;
  string create_person = 7;
  string update_person = 8;
  string created_at = 9;
  string updated_at = 10;
  string request_params = 11;
}


message GetMetaDatabaseListRequest {
  string user = 1;
  string keyword = 2;
}

message GetMetaDatabaseListResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated Database databases = 3;

}

message Database {

  string name = 1;

  string name_zh = 2;

  string description = 3;

  string type = 4;

  string catalog = 5;

}

message GetTableListRequest {
  string user = 1;
  string keyword =2;
  string db_name = 3;
  int32  query_type = 4;
}


message GetTableListResponse {
  int32 result = 1;
  string error_msg = 2;
  repeated Table table_list = 3;

}

message Table {

  string name = 1;

  string name_zh = 2;

  string database_name = 3;

  string description = 4;

  string urn = 5;

  string catalog = 6;
}

message GetColumnListRequest {
  string user = 1;
  string db_name =2;
  string tb_name = 3;
  int32  query_type = 4;
}

message GetColumnListResponse {
  int32 result = 1;
  string error_msg = 2;
  repeated Column column_list = 3;

}

message Column {

  string urn = 1;

  string database_name = 2;

  string table_name = 3;

  string name = 4;

  string data_type = 5;

  string full_type = 6;

  string comment = 7;

  string description = 8;

  bool partitioned = 9;

  bool statistics = 10;

  string catalog = 11;
}

message DmQueryRequest {
  string service_code = 1;
  map<string, ParamValue> param = 2;
  map<string, string> ext_info = 3;
}

message ParamValue {
  repeated string value = 2;
}

message DmQueryResponse {
  int32 result = 1;
  string error_msg = 2;
  repeated string headers = 3;
  repeated string headers_info = 4;
  repeated string result_row = 5;
  int32 total = 6;
}


message DataMeta {
  string name = 1;
  string value_type = 2;
}


message QueryByQueryIdRequest {
  int64 query_id = 1;
  repeated Param param = 2;
  map<string, string> ext_info = 3;
}

message Param {
  string name = 1;
  repeated string value = 2;
}


message QueryByQueryIdResponse {
  int32 result = 1;
  string error_msg = 2;
  repeated string headers = 3;
  repeated string headers_info = 4;
  repeated string result_row = 5;
  int32 total = 6;
}

message Dm2QueryDebugListRequest {
  string service_code = 1;
  uint32 page = 2;
  uint32 page_size = 3;
}

message Dm2QueryDebugListResponse {
  int32 result = 1;
  string total = 2;
  repeated DmDebugData debug_list = 3;
}

message DmDebugData {
  repeated Param param = 1;
  string time = 2;
}


service KwaiSqlQueryDomainService {

  rpc FetchDpData (KwaiSqlQueryServiceRequest) returns (KwaiSqlQueryServiceResponse);
  rpc FetchSqlColumn (KwaiSqlQueryServiceRequest) returns (KwaiSqlQueryServiceResponse);
  rpc DmGetByQueryId (DmGetByQueryIdRequest) returns (DmGetByQueryIdResponse);
  rpc Dm2GetByServiceCode (Dm2GetByServiceCodeRequest) returns (Dm2GetByServiceCodeResponse);
  rpc Dm2QueryDebugList (Dm2QueryDebugListRequest) returns (Dm2QueryDebugListResponse);
  rpc GetMetaDatabaseList (GetMetaDatabaseListRequest) returns (GetMetaDatabaseListResponse);
  rpc GetTableList (GetTableListRequest) returns (GetTableListResponse);
  rpc GetColumnList (GetColumnListRequest) returns (GetColumnListResponse);
  rpc DmQuery (DmQueryRequest) returns (DmQueryResponse);
  rpc QueryByQueryId (QueryByQueryIdRequest) returns (QueryByQueryIdResponse);
}