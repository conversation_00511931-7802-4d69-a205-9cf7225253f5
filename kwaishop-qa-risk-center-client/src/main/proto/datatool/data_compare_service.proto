syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool";
option java_outer_classname = "DataCompareServiceProto";


message QueryBySourceIdRequest {
  int64 source_id = 1;
  string executor_name = 2;
}

message QueryBySourceIdResponse {
  int32 result = 1;
  string error_msg = 2;
  string data = 3;
}


service DataCompareDomainService {

  rpc QueryBySourceId (QueryBySourceIdRequest) returns (QueryBySourceIdResponse);

}