syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool";
option java_outer_classname = "DataScenarioServiceProto";


message CreateDataScenarioRequest {

  string name = 1;

  repeated int64 case_id_list = 2;

  string creator_name = 3;

  string description = 4;

}



message CreateDataScenarioResponse {

  int32 result = 1;

  string error_msg = 2;

}

message UpdateDataScenarioRequest {

  int64 id = 1;

  string name = 2;

  repeated int64 case_id_list = 3;

  int32 auto_trigger = 4;

}

message UpdateDataScenarioResponse {

  int32 result = 1;

  string error_msg = 2;
}

message InsertCaseIntoScenarioRequest {
  int64 scenario_id = 1;
  string creator_name = 2;
  repeated int64 case_id_list = 3;
}

message InsertCaseIntoScenarioResponse {

  int32 result = 1;

  string error_msg = 2;
}

message DeleteCaseFromScenarioRequest {
  int64 scenario_id = 1;
  string creator_name = 2;
  repeated int64 case_id_list = 3;
}

message DeleteCaseFromScenarioResponse {

  int32 result = 1;

  string error_msg = 2;
}

message QueryDataScenarioRequest  {
  int64 id = 1;
  string name = 2;
  string creator_name = 3;
}

message QueryDataScenarioResponse  {
  int32 result = 1;

  string error_msg = 2;

  repeated DataScenarioDTO data = 3;
}


message DataScenarioDTO {
  int64 id = 1;

  string name = 2;

  int64 create_time =3;

  int64 update_time =4;

  string creator_name = 5;

  int32 auto_trigger = 6;

}


message QueryPageDataScenarioRequest {

  string name = 1;

  int64 id = 2;

  string creator_name = 3;

  int32 page_no = 4;

  int32 page_size = 5;
}


message QueryPageDataScenarioResponse {

  int32 result = 1;

  string error_msg = 2;

  PageDataScenarioDTO data = 3;
}

message PageDataScenarioDTO {

  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated DataScenarioDTO data_scenario_list = 4;
}


message QueryDataScenarioLikeNameRequest {

  string name = 1;

}

message QueryDataScenarioLikeNameResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated DataScenarioDTO data = 3;
}



message QueryDataScenarioByIdRequest {

  int64 id = 1;

}

message QueryDataScenarioByIdResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated DataScenarioDTO data = 3;

}

message DeleteDataScenarioRequest {

  int64 id = 1;

}

message DeleteDataScenarioResponse {

  int32 result = 1;

  string error_msg = 2;
}

message RunDataScenarioRequest {
  int64 scenario_id = 1;
  string operator = 2;
}

message RunDataScenarioResponse {

  int32 result = 1;

  string error_msg = 2;
}


message ScenarioExeResultRequest {

  int64 scenario_id = 1;

  string operator = 2;

  string name = 3;

  int32 page_no = 4;

  int32 page_size = 5;
}

message ScenarioExeResultResponse {
  int32 result = 1;

  string error_msg = 2;

  PageScenarioExe data = 3;

}

message PageScenarioExe {
  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated ScenarioExeDTO scenario_report_list = 4;
}

message ScenarioExeDTO {
  int64 id = 1;
  int64 scenario_id = 2;
  string name = 3;
  string executor_name = 4;
  int32 status = 5;
  int64 start_time = 6;
  int64 end_time = 7;
}

message  QueryDataCaseByScenarioIdRequest {
  int64 scenario_id = 1;
}

message  QueryDataCaseByScenarioIdResponse {
  int32 result = 1;
  string error_msg = 2;
  repeated ScenarioCaseDTO data = 3;
}

message ScenarioCaseDTO {
  int64 id = 1;

  string name = 2;

  int64 create_time =3;

  int64 update_time =4;

  string creator_name = 5;

}

message QueryCaseExeByScenarioExeIdRequest {
  int64 scenario_exe_id = 1;
}

message QueryCaseExeByScenarioExeIdResponse {
  int32 result = 1;
  string error_msg = 2;
  repeated ScenarioCaseExeDTO case_report_list = 3;

}

message ScenarioCaseExeDTO {
  int64 id = 1;
  int64 case_id = 2;
  string name = 3;
  string executor_name = 4;
  int32 status = 5;
  int64 start_time = 6;
  int64 end_time = 7;
  string data_query_exe_ids = 8;
}

service DataScenarioDomainService {

  rpc CreateDataScenario (CreateDataScenarioRequest) returns (CreateDataScenarioResponse);
  rpc UpdateDataScenario (UpdateDataScenarioRequest) returns (UpdateDataScenarioResponse);
  rpc InsertCaseIntoScenario (InsertCaseIntoScenarioRequest) returns (InsertCaseIntoScenarioResponse);
  rpc DeleteCaseFromScenario (DeleteCaseFromScenarioRequest) returns (DeleteCaseFromScenarioResponse);
  rpc QueryDataScenarioLikeName (QueryDataScenarioLikeNameRequest) returns (QueryDataScenarioLikeNameResponse);
  rpc QueryDataScenarioById (QueryDataScenarioByIdRequest) returns (QueryDataScenarioByIdResponse);
  rpc QueryPageDataScenario (QueryPageDataScenarioRequest) returns (QueryPageDataScenarioResponse);
  rpc QueryDataScenario (QueryDataScenarioRequest) returns (QueryDataScenarioResponse);
  rpc DeleteDataScenario (DeleteDataScenarioRequest) returns (DeleteDataScenarioResponse);
  rpc QueryDataCaseByScenarioId (QueryDataCaseByScenarioIdRequest) returns (QueryDataCaseByScenarioIdResponse);
  rpc QueryDataCaseWithoutScenario (QueryDataCaseByScenarioIdRequest) returns (QueryDataCaseByScenarioIdResponse);
  rpc RunDataScenario (RunDataScenarioRequest) returns (RunDataScenarioResponse);
  rpc QueryPageScenarioReportList (ScenarioExeResultRequest) returns (ScenarioExeResultResponse);
  rpc QueryPageScenarioExeResult (ScenarioExeResultRequest) returns (ScenarioExeResultResponse);
  rpc QueryCaseExeByScenarioExeId (QueryCaseExeByScenarioExeIdRequest) returns (QueryCaseExeByScenarioExeIdResponse);

}