syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool";
option java_outer_classname = "QueryExecuteServiceProto";


message ExecuteGrpcQueryRequest {

  int64 data_source_id = 1;

  int64 data_query_exe_id = 2;

}

message ExecuteGrpcQueryResponse {

  int32 result = 1;

  string error_msg = 2;
}

message QueryExeResultRequest {
  int64 query_exe_id = 1;
}

message QueryExeResultByDataSourceIdRequest {
  int64 data_source_id = 1;
}

message QueryExeResultResponse {
  int32 result = 1;
  string error_msg = 2;
  QueryExeResult data = 3;
}

message QueryExeResult {
  int64 id =1;
  int64 data_source_id = 2;
  string name =3;
  string executor_name =4;
  int32 status = 5;
  int64 create_time = 6;
  int64 start_time = 7;
  int64 end_time = 8;
  string log = 9;
  string result = 10;
}


service ExecuteGrpcQueryDomainService {

  rpc CallKessMethod (ExecuteGrpcQueryRequest) returns (ExecuteGrpcQueryResponse);
  rpc QueryExeResult (QueryExeResultRequest) returns (QueryExeResultResponse);
  rpc QueryExeResultByDataSourceId (QueryExeResultByDataSourceIdRequest) returns (QueryExeResultResponse);

}