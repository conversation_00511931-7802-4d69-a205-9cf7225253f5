syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool";
option java_outer_classname = "DataSourceServiceProto";


message CreateDataSourceRequest {

  string name = 1;

  int32 type = 2;

  string creator_name = 3;

  string description = 4;

  string content = 5;
}


message CreateDataSourceResponse {

  int32 result = 1;

  string error_msg = 2;
}

message UpdateDataSourceRequest {

  int64 id = 1;

  string name = 2;

  int32 type = 3;

  string creator_name = 4;

  string description = 5;

  string content = 6;
}


message UpdateDataSourceResponse {

  int32 result = 1;

  string error_msg = 2;
}

message QueryDataSourceLikeNameRequest {

  string name = 1;

}

message QueryDataSourceLikeNameResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated DataSourceDTO data = 3;
}


message QueryDataSourceByIdRequest {

  int64 id = 1;

}

message QueryDataSourceByIdResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated DataSourceDTO data = 3;

}

message DeleteDataSourceRequest {

  int64 id = 1;

}

message DeleteDataSourceResponse {

  int32 result = 1;

  string error_msg = 2;
}

message QueryDataSourceRequest {

  string name = 1;

  int32 type = 2;

  string creator_name = 3;

}


message QueryDataSourceResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated DataSourceDTO data = 3;
}

message QueryPageDataSourceRequest {

  string name = 1;

  int32 type = 2;

  string creator_name = 3;

  int32 page_no = 4;

  int32 page_size = 5;
}


message QueryPageDataSourceResponse {

  int32 result = 1;

  string error_msg = 2;

  PageDataSourceDTO data = 3;
}


message CreateQueryExeRequest {
    int64 data_source_id = 1;
    string name = 2;
    string executor_name = 3;
}

message CreateQueryExeResponse {
  int32 result = 1;

  string error_msg = 2;

  int64 data_query_exe_id = 3;

}

message PageDataSourceDTO {

  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated DataSourceDTO datasource_list = 4;
}

message DataSourceDTO {

  int64 id = 1;

  string name = 2;

  int32 type = 3;

  string creator_name = 4;

  string description = 5;

  string content = 6;

  int64 create_time =7;

  int64 update_time =8;
}

message DataQueryExeDTO {
  int64 id = 1;

  int64 data_source_id = 2;

  string name = 3;

  string executor_name = 4;

  int32 status = 5;

  string result = 6;

  int64 create_time =7;

  int64 start_time =8;

  int64 end_time =9;

  string log = 10;

}

service DataSourceDomainService {

  rpc CreateDataSource (CreateDataSourceRequest) returns (CreateDataSourceResponse);
  rpc UpdateDataSource (UpdateDataSourceRequest) returns (UpdateDataSourceResponse);
  rpc QueryPageDataSource (QueryPageDataSourceRequest) returns (QueryPageDataSourceResponse);
  rpc QueryDataSource (QueryDataSourceRequest) returns (QueryDataSourceResponse);
  rpc DeleteDataSource (DeleteDataSourceRequest) returns (DeleteDataSourceResponse);
  rpc QueryDataSourceById (QueryDataSourceByIdRequest) returns (QueryDataSourceByIdResponse);
  rpc QueryDataSourceLikeName (QueryDataSourceLikeNameRequest) returns (QueryDataSourceLikeNameResponse);
  rpc CreateQueryExe (CreateQueryExeRequest) returns (CreateQueryExeResponse);
}