syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.datatool";
option java_outer_classname = "DataCaseServiceProto";


message CreateDataCaseRequest {

  string name = 1;

  int64 data_source_id = 2;

  string creator_name = 3;

  string description = 4;

  string check_content = 5;

  string diff_query_content = 6;
}


message CreateDataCaseResponse {

  int32 result = 1;

  string error_msg = 2;
}

message UpdateDataCaseRequest {

  int64 id = 1;

  string name = 2;

  int64 data_source_id = 3;

  string check_content = 4;

  string description = 5;

  string diff_query_content = 6;
}


message UpdateDataCaseResponse {

  int32 result = 1;

  string error_msg = 2;
}

message QueryDataCaseRequest  {
  int64 id = 1;
  string name = 2;
  string creator_name = 3;
}

message QueryDataCaseResponse  {
  int32 result = 1;

  string error_msg = 2;

  repeated DataCaseDTO data = 3;
}

message QueryPageDataCaseRequest {

  string name = 1;

  int64 id = 2;

  string creator_name = 3;

  int32 page_no = 4;

  int32 page_size = 5;
}


message QueryPageDataCaseResponse {

  int32 result = 1;

  string error_msg = 2;

  PageDataCaseDTO data = 3;
}

message PageDataCaseDTO {

  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated DataCaseDTO data_case_list = 4;
}


message QueryDataCaseLikeNameRequest {

  string name = 1;

}

message QueryDataCaseLikeNameResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated DataCaseDTO data = 3;
}

message DataCaseDTO {
  int64 id = 1;

  string name = 2;

  int64 data_source_id = 3;

  string check_content = 4;

  string description = 5;

  string diff_query_content = 6;

  int64 create_time =7;

  int64 update_time =8;

  string creator_name = 9;

}


message QueryDataCaseByIdRequest {

  int64 id = 1;

}

message QueryDataCaseByIdResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated DataCaseDTO data = 3;

}

message QueryDataCaseDayRequest {
  string operator = 1;
}

message QueryDataCaseDayResponse {
  int32 case_num = 1;
  int32 source_num = 2;
  int32 report_num = 3;
}


message DeleteDataCaseRequest {

  int64 id = 1;

}

message DeleteDataCaseResponse {

  int32 result = 1;

  string error_msg = 2;
}

message RunDataCaseRequest {
  int64 case_id = 1;
  string operator = 2;
}

message RunDataCaseResponse {

  int32 result = 1;

  string error_msg = 2;
}

message CaseExeResultRequest {
  int64 id = 1;

  int64 case_id = 2;

  string operator = 3;

  string name = 4;

  int32 page_no = 5;

  int32 page_size = 6;
}

message CaseExeResultResponse {
  int32 result = 1;

  string error_msg = 2;

  PageCaseExe data = 3;

}

message PageCaseExe {
  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated CaseExeDTO case_report_list = 4;
}

message CaseExeDTO {
  int64 id = 1;
  int64 case_id = 2;
  string name = 3;
  string executor_name = 4;
  int32 status = 5;
  int64 start_time = 6;
  int64 end_time = 7;
  string data_query_exe_ids = 8;
}



service DataCaseDomainService {
  rpc CreateDataCase (CreateDataCaseRequest) returns (CreateDataCaseResponse);
  rpc UpdateDataCase (UpdateDataCaseRequest) returns (UpdateDataCaseResponse);
  rpc QueryDataCaseLikeName (QueryDataCaseLikeNameRequest) returns (QueryDataCaseLikeNameResponse);
  rpc QueryDataCaseById (QueryDataCaseByIdRequest) returns (QueryDataCaseByIdResponse);
  rpc QueryPageDataCase (QueryPageDataCaseRequest) returns (QueryPageDataCaseResponse);
  rpc QueryDateCase (QueryDataCaseDayRequest) returns (QueryDataCaseDayResponse);
  rpc QueryDataCase (QueryDataCaseRequest) returns (QueryDataCaseResponse);
  rpc DeleteDataCase (DeleteDataCaseRequest) returns (DeleteDataCaseResponse);
  rpc RunDataCase (RunDataCaseRequest) returns (RunDataCaseResponse);
  rpc QueryPageCaseReportList (CaseExeResultRequest) returns (CaseExeResultResponse);

}