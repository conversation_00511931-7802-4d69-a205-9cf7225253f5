syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

import "kwaishop_qa_risk_common.proto";

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf";
option java_outer_classname = "KwaishopQaRiskCenterDtoProto";

// dto example, for mq message
message QaRiskExampleDTO {
    RiskExampleObject example = 1; // ...
}

message QaRiskMqExampleDTO {
    RiskExampleObject example = 1; // ...
}