syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.kess";
option java_outer_classname = "KessShopUiServiceProto";

message RerunRequest {
  string operator = 1;
}

message ReturnResponse {

  int32 result = 1;

  string error_msg = 2;

}
service KessShopUiService {

  rpc ShopUiRerun(RerunRequest)returns(ReturnResponse);

}