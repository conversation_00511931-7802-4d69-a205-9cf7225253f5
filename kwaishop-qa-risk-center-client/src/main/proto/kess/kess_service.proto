syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.kess";
option java_outer_classname = "KessServiceProto";


message QueryMethodListRequest{
  string kess_name = 1;
}

message QueryMethodListResponse{
  int32           result    = 1;

  string          error_msg = 2;

  repeated string data      = 3;
}

service KessDomainService {

  rpc QueryMethodList (QueryMethodListRequest) returns (QueryMethodListResponse);

}