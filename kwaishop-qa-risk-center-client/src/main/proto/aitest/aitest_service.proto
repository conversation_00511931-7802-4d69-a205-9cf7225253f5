syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest";
option java_outer_classname = "AitestServiceProto";


// 定义 UI 测试用例的消息
message UiCase {
  int64 id = 1; // 主键id
  string domain = 2; // 域：shop
  int64 ui_data_id = 3; // default_ui_data主键,0为不使用data处理
  string case_name = 4; // case名
  string case_desc = 5; // case描述
  string case_author = 6; // case作者
  string case_detail = 7; // case内容
  string case_notice_users = 8; // case关注人列表
  string case_notice_groups = 9; // case关注通知群列表
  string case_images = 10; // json 线上执行基准图片 {"key":"cdn"}
  int64 case_images_base_time = 11; // 基准产生时间
  string case_images_result_id = 12; // 基准产结果id
  string case_define_prompt = 13; // 基于case的自定义prompt
  int32 status = 14; // 场景状态
  int32 type = 15; // 类型: 1-chrome录制,2-AI探索,3-AI自然语言
  string env = 16; // 可执行环境
  int32 platform = 17; // 运行平台:1-web、2-android、3-ios
  int64 create_time = 18; // 创建时间
  int64 update_time = 19; // 更新时间
  int32 deleted = 20; // 软删除标志
  string case_ai_detail = 21;
  string domain1 = 22;
  string domain2 = 23;
  string domain3 = 24;
  string domain4 = 25;
  string level = 26;
}

message UiData {
  int64 id = 1;                // 主键ID
  int64 ui_case_id = 2;         // UI案例ID
  string data_name = 3;        // 数据模版名称
  string key_id = 4;           // 键ID
  string key_name = 5;         // 键名称
  string key_value = 6;        // 键值
  string env = 7;             // 环境
  string lane = 8;            // lane
  string params = 9;          // 参数
  int32 status = 10;          // 状态
  int64 create_time = 11;      // 创建时间
  int64 update_time = 12;      // 更新时间
  int32 deleted = 13;         // 删除标志
}

message UiCaseResult {
  int64 id = 1;                        // 主键ID
  int64 ui_case_id = 2;               // UI用例ID
  int64 ui_suite_result_id = 3;        // UI套件结果ID
  int64 ui_suite_id = 4;                // UI套件ID
  int64 ui_data_id = 5;                // UI数据ID
  string env = 6;                      // 环境
  string lane = 7;                     // lane信息
  string out_id = 8;                   // 输出ID
  string case_step_images = 9;         // 用例步骤图片
  string case_step_result = 10;        // 用例步骤结果json
  string case_result_desc = 11;        // 用例结果描述，未通过存放记录
  int32 status = 12;                   // 结果 0：执行中 1：执行通过 2：执行通过但是AI预警 3：执行未通过 4：打标通过
  int64 create_time = 13;              // 创建时间
  int64 update_time = 14;              // 更新时间
  int32 deleted = 15;                  // 删除标
  string reason = 16;                  //标记失败原因
  string executor = 17;                //执行人
  string ui_case_name = 18;             //冗余下名字方便知道执行那个caseid不直观
  string ui_suite_name = 19;             //冗余下名字方便知道执行那个caseid不直观
  string host = 20;
  string path = 21;
  int32 case_type = 22;                    //冗余case类型
  int64 finish_time = 23;             // 完成时间
  int64 begin_time = 24;             // 完成时间
  string data_name = 25;         //数据模版名
  string data_json = 26;         //数据快照
  int32 execute_type = 27;                    //冗余case类型
  int32 execute_status = 28;
}

// UiSuiteCaseRel 消息定义
message UiSuiteCaseRel {
  int64 id = 1;                        // 主键ID
  int64 ui_case_id = 2;               // UI用例ID
  int64 ui_suite_id = 3;              // UI套件ID
  int64 ui_data_id = 4;               // UI数据ID
  int32 status = 5;                   // 状态
  int64 create_time = 6;              // 创建时间
  int64 update_time = 7;              // 更新时间
  int32 deleted = 8;                  // 删除标志
}

message UiSuite {
  // 主键id
  int64 id = 1;

  // 域：shop
  string domain = 2;

  // case名
  string suite_name = 3;

  // case描述
  string suite_desc = 4;

  // case作者
  string suite_author = 5;

  // 通知阈值
  string suite_notice_threshold = 6;

  // 测试集失败通知人列表
  string suite_notice_users = 7;

  // 测试集失败通知群列表
  string suite_notice_groups = 8;

  // 执行配置
  string suite_cron = 9;

  // json被ban的执行client
  string block_client = 10;

  // json允许被执行的client
  string allow_client = 11;

  // 场景状态: 0-创建完成, 1-删除
  int32 status = 12;

  // 创建时间
  int64 create_time = 13;

  // 更新时间
  int64 update_time = 14;

  // 记录状态: 0-创建完成, 1-删除
  int32 deleted = 15;

  string env = 16;

  string lane = 17;

  string domain2 = 18;

  string domain3 = 19;

  string domain4 = 20;

  int32 execute_mode = 21;//是否支持串行分拆

  string level = 22;

}

message UiSuiteResult {
  int64 id = 1;                       // 主键ID
  int64 ui_suite_id = 2;              // UiSuite ID
  int64 total_num = 3;                // 总数
  int64 skip_num = 4;                 // 跳过的数量
  int64 pass_num = 5;                 // 通过的数量
  int64 fail_num = 6;                 // 失败的数量
  int64 warning_num = 7;              // 警告的数量
  int32 status = 8;                   // 状态
  int64 create_time = 9;              // 创建时间
  int64 update_time = 10;             // 更新时间
  int32 deleted = 11;                 // 删除标志
  string env = 12;                    // 执行环境
  string lane = 13;                   // 执行泳道
  string executor =20;                //执行人
  string ui_suite_name = 21;             //冗余下名字方便知道执行那个suite，id不直观
  int64 finish_time = 22;             // 完成时间
  int64 begin_time = 23;             // 开始时间
  int32 execute_mode = 24;            //执行模式
  int32 execute_type = 25;            //执行模式
  string fail_reason = 26;       //记录执行失败原因
  int32 execute_status = 27;
}

message UiSuiteCaseWithUiData {
  int64 id = 1;                       // 主键ID
  int64 ui_suite_id = 2;              // UiSuite ID
  UiCase ui_case =3;
  UiData ui_data = 4;
}

// 请求消息用于创建、更新和删除操作
message CreateUiCaseRequest {
  UiCase ui_case = 1; // 要创建的测试用例
}

message CreateUiCaseResponse {
  int32 result = 1;
  string error_msg = 2;
  UiCase ui_case = 3; // 要创建的测试用例
}
message UpdateUiCaseRequest {
  UiCase ui_case = 1; // 要更新的测试用例
}

message UpdateUiCaseResponse {
  int32 result = 1;
  string error_msg = 2;
  UiCase ui_case = 3; // 要更新的测试用例
}

message DeleteUiCaseRequest {
  int64 id = 1; // 要删除的测试用例ID
}

message DeleteUiCaseResponse {
  int32 result = 1;
  string error_msg = 2;
  int64 id = 3; // 要删除的测试用例ID
}

// 请求消息用于查询操作
message ListUiCasesRequest {
  int32 page_number = 1; // 页码
  int32 page_size = 2; // 每页大小
  string case_name = 3;           // 名称
  string case_author = 4;
  repeated int32 type = 5;   // 类型列表
}

// 响应消息用于查询操作
message ListUiCasesResponse {
  int32 result = 1;
  string error_msg = 2;
  repeated UiCase ui_cases = 3; // 测试用例列表
  int32 total_count = 4; // 总记录数
}

message ListUiSuitesRequest {
  int32 page_number = 1; // 页码
  int32 page_size = 2; // 每页大小
  string suite_name = 3;
  string suite_desc = 4;
  string suite_author = 5;
}

message ListUiSuitesResponse {
  int32 result = 1;
  string error_msg = 2;
  repeated UiSuite ui_suites = 3;
  int32 total_count = 4;
}

message ListUiSuiteCasesRequest {
  int32 page_number = 1; // 页码
  int32 page_size = 2; // 每页大小
  int64 ui_suite_id= 3;
}

message ListUiSuiteCasesResponse {
  int32 result = 1;
  string error_msg = 2;
  repeated UiSuiteCaseWithUiData ui_case_data_list = 3;
  int32 total_count = 4;
}

message CreateAssistCaseRequest{
  string user_name = 1; //创建人名
  string name = 2;
  string content = 3;
  string env = 4; //环境
  string lane = 5; //泳道
  string account_id = 6; //测试账号id
  string account_name = 7; //测试账号名
  string account_pwd = 8; //测试账号密码
  string entrance = 9;
}

message CreateAssistCaseResponse{
  int32 result = 1;
  string error_msg = 2;
  UiCase ui_case = 3; // 要创建的测试用例
}

// 请求消息用于获取特定的测试用例
message GetUiCaseRequest {
  int64 id = 1; // 要获取的测试用例ID
}

// 响应消息用于获取特定的测试用例
message GetUiCaseResponse {
  int32 result = 1;
  string error_msg = 2;
  UiCase ui_case = 3; // 返回的测试用例
}

// 创建请求消息
message CreateUiDataRequest {
  UiData ui_data = 1;      // 要创建的 UiDataDO 对象
}


// 创建响应消息
message CreateUiDataResponse {
  int32 result = 1;
  string error_msg = 2;
  UiData ui_data = 3;      // 创建成功后的 UiDataDO 对象
}


// 读取请求消息
message GetUiDataRequest {
  int64 id = 1;               // 要查询的 UiDataDO ID
}


// 读取响应消息
message GetUiDataResponse {
  int32 result = 1;
  string error_msg = 2;
  UiData ui_data = 3;      // 查询到的 UiDataDO 对象
}


// 更新请求消息
message UpdateUiDataRequest {
  UiData ui_data = 1;      // 要更新的 UiDataDO 对象
}


// 更新响应消息
message UpdateUiDataResponse {
  int32 result = 1;
  string error_msg = 2;
  UiData ui_data = 3;      // 更新成功后的 UiDataDO 对象
}


// 删除请求消息
message DeleteUiDataRequest {
  int64 id = 1;               // 要删除的 UiDataDO ID
}


// 删除响应消息
message DeleteUiDataResponse {
  int32 result = 1;
  string error_msg = 2;
}


// 列表查询请求消息
message ListUiDataRequest {
  int64 case_id = 1;
}


// 列表查询响应消息
message ListUiDataResponse {
  int32 result = 1;
  string error_msg = 2;
  repeated UiData ui_data_list = 3; // UiDataDO 列表
}

// 创建请求消息
message CreateUiCaseResultRequest {
  UiCaseResult ui_case_result = 1;     // 要创建的UiCaseResult对象
}

// 创建响应消息
message CreateUiCaseResultResponse {
  int32 result = 1;
  string error_msg = 2;
  int64 id = 3;
}

// 查询请求消息
message GetUiCaseResultRequest {
  int64 id = 1;                        // 要查询的UiCaseResult的ID
}

// 查询响应消息
message GetUiCaseResultResponse {
  int32 result = 1;
  string error_msg = 2;
  UiCaseResult ui_case_result = 3;     // 查询到的UiCaseResult对象
}

// 更新请求消息
message UpdateUiCaseResultRequest {
  UiCaseResult ui_case_result = 1;     // 要更新的UiCaseResult对象
}

// 更新响应消息
message UpdateUiCaseResultResponse {
  int32 result = 1;
  string error_msg = 2;
  UiCaseResult ui_case_result = 3;     // 更新成功的UiCaseResult对象
}

message SaveUiCaseResultRequest {
  UiCaseResult ui_case_result = 1;
}

message SaveUiCaseResultResponse {
  int32 result = 1;
  string error_msg = 2;
}

// 删除请求消息
message DeleteUiCaseResultRequest {
  int64 id = 1;                        // 要删除的UiCaseResult的ID
}

// 删除响应消息
message DeleteUiCaseResultResponse {
  int32 result = 1;
  string error_msg = 2;
}

// 创建请求消息
message CreateUiSuiteCaseRelRequest {
  UiSuiteCaseRel ui_suite_case_rel = 1; // 要创建的UiSuiteCaseRel对象
}

// 创建响应消息
message CreateUiSuiteCaseRelResponse {
  int32 result = 1;
  string error_msg = 2;
  UiSuiteCaseRel ui_suite_case_rel = 3; // 创建成功的UiSuiteCaseRel对象
}

// 查询请求消息
message GetUiSuiteCaseRelRequest {
  int64 id = 1;                        // 要查询的UiSuiteCaseRel的ID
}

// 查询响应消息
message GetUiSuiteCaseRelResponse {
  int32 result = 1;
  string error_msg = 2;
  UiSuiteCaseRel ui_suite_case_rel = 3; // 查询到的UiSuiteCaseRel对象
}

// 更新请求消息
message UpdateUiSuiteCaseRelRequest {
  UiSuiteCaseRel ui_suite_case_rel = 1; // 要更新的UiSuiteCaseRel对象
}

// 绑定数据到执行记录
message UpdateUiSuiteCaseRelResponse {
  int32 result = 1;
  string error_msg = 2;
}

// 删除请求消息
message DeleteUiSuiteCaseRelRequest {
  int64 id = 1;                        // 要删除的UiSuiteCaseRel的ID
}

// 删除响应消息
message DeleteUiSuiteCaseRelResponse {
  int32 result = 1;
  string error_msg = 2;
}

// 创建请求消息
message CreateUiSuiteRequest {
  UiSuite ui_suite = 1;               // 要创建的UiSuite对象
}

// 创建响应消息
message CreateUiSuiteResponse {
  int32 result = 1;
  string error_msg = 2;
  UiSuite ui_suite = 3;               // 创建成功的UiSuite对象
}

// 查询请求消息
message GetUiSuiteRequest {
  int64 id = 1;                        // 要查询的UiSuite的ID
}

// 查询响应消息
message GetUiSuiteResponse {
  int32 result = 1;
  string error_msg = 2;
  UiSuite ui_suite = 3;               // 查询到的UiSuite对象
}

// 更新请求消息
message UpdateUiSuiteRequest {
  UiSuite ui_suite = 1;               // 要更新的UiSuite对象
}

// 更新响应消息
message UpdateUiSuiteResponse {
  int32 result = 1;
  string error_msg = 2;
}

// 删除请求消息
message DeleteUiSuiteRequest {
  int64 id = 1;                        // 要删除的UiSuite的ID
}

// 删除响应消息
message DeleteUiSuiteResponse {
  int32 result = 1;
  string error_msg = 2;
}

message ListUiCaseResultsRequest {
  int64 ui_case_id = 1;
  int32 page_number = 2; // 页码
  int32 page_size = 3; // 每页大小
}

message ListUiCaseResultsResponse {
  int32 result = 1;
  string error_msg = 2;
  repeated UiCaseResult ui_case_result_list = 3;
  int32 total_count = 4;

}

message ListUiCaseResultsBySuiteResultRequest {
  int64 ui_suite_result_id = 1;
  int32 page_number = 2; // 页码
  int32 page_size = 3; // 每页大小
}

message ListUiCaseResultsBySuiteResultResponse {
  int32 result = 1;
  string error_msg = 2;
  repeated UiCaseResult ui_case_result_list = 3;
  int32 total_count = 4;

}

message ListUiSuiteResultsRequest {
  int64 ui_suite_id = 1;
  int32 page_number = 2; // 页码
  int32 page_size = 3; // 每页大小
  int64 from_time = 4;
  int64 to_time = 5;
  repeated int32 execute_status = 6;   // 类型列表
  repeated int32 status = 7;   // 类型列表
  string executor = 8;
  string ui_suite_name = 9;
}

message ListUiSuiteResultsResponse {
  int32 result = 1;
  string error_msg = 2;
  repeated UiSuiteResult ui_suite_result_list = 3;
  int32 total_count = 4;

}
// 创建请求消息
message CreateUiSuiteResultRequest {
  UiSuiteResult ui_suite_result = 1;  // 要创建的UiSuiteResult对象
}

// 创建响应消息create
message CreateUiSuiteResultResponse {
  int32 result = 1;
  string error_msg = 2;
  int64 ui_suite_result_id = 3;
}

// 查询请求消息
message GetUiSuiteResultRequest {
  int64 id = 1;                       // 要查询的UiSuiteResult的ID
}

// 查询响应消息
message GetUiSuiteResultResponse {
  int32 result = 1;
  string error_msg = 2;
  UiSuiteResult ui_suite_result = 3;  // 查询到的UiSuiteResult对象
}

// 更新请求消息
message UpdateUiSuiteResultRequest {
  UiSuiteResult ui_suite_result = 1;  // 要更新的UiSuiteResult对象
}

// 更新响应消息
message UpdateUiSuiteResultResponse {
  int32 result = 1;
  string error_msg = 2;
}

// 删除请求消息
message DeleteUiSuiteResultRequest {
  int64 id = 1;                       // 要删除的UiSuiteResult的ID
}

// 删除响应消息
message DeleteUiSuiteResultResponse {
  int32 result = 1;
  string error_msg = 2;
}

message AddCasesIntoSuiteRequest {
  int64 ui_suite_id = 1;
  repeated int64 ui_case_id = 2;
}

message AddCasesIntoSuiteResponse {
  int32 result = 1;
  string error_msg = 2;
}

message RemoveCasesFromSuiteRequest {
  int64 ui_suite_id = 1;
  repeated int64 ui_case_id = 2;
}

message RemoveCasesFromSuiteResponse {
  int32 result = 1;
  string error_msg = 2;
}

message ExecuteSuiteRequest {
  int64 ui_suite_id = 1;
  string temp_env = 2;
  string temp_lane = 3;
  string executor = 4;

}

message ExecuteSuiteResponse {
  int32 result = 1;
  string error_msg = 2;
  UiSuiteResult ui_suite_result = 3;
}


message ExecuteCaseRequest {
  int64 ui_case_id = 1;
  int64 ui_data_id = 2;
  string executor = 3;
  string temp_env = 4;
  string temp_lane = 5;
}

message ExecuteCaseResponse {
  int32 result = 1;
  string error_msg = 2;
  UiSuiteResult ui_suite_result = 3;

}

message GetCompileCaseRequest {
  //case执行id非caseid，caseid不好和数据关联
  int64 ui_case_id = 1;
}

message GetCompileCaseResponse {
  int32 result = 1;
  string error_msg = 2;
  string origin_data = 3;
  string origin_ai_data = 4;
  string compile_data = 5;
}

message AiProcessCaseRequest {
  //case执行id非caseid，caseid不好和数据关联
  int64 ui_case_id = 1;
}

message AiProcessCaseResponse {
  int32 result = 1;
  string error_msg = 2;
}

message AiDebugRequest{
  string model = 1;
  string role = 2;
  string content = 3;
  string operator = 4;
  string biz = 5;
  string url = 6;
}

message AiDebugResponse{
  int32 result = 1;
  string error_msg = 2;
  string content = 3;
}

message OptNlpUiCaseRequest{
  int64 case_id = 1;
  string case_content = 2;
}

message OptNlpUiCaseResponse{
  int32 result = 1;
  string error_msg = 2;
  string content = 3;
}


service AitestService {
  //插入chrome辅助case
  rpc CreateAssistCase(CreateAssistCaseRequest) returns (CreateAssistCaseResponse);

  rpc CreateUiCase (CreateUiCaseRequest) returns (CreateUiCaseResponse);
  rpc GetUiCase (GetUiCaseRequest) returns (GetUiCaseResponse);
  rpc UpdateUiCase (UpdateUiCaseRequest) returns (UpdateUiCaseResponse);
  rpc DeleteUiCase (DeleteUiCaseRequest) returns (DeleteUiCaseResponse);
  rpc ListUiCases (ListUiCasesRequest) returns (ListUiCasesResponse);

  rpc CreateUiData(CreateUiDataRequest) returns (CreateUiDataResponse);
  rpc GetUiData(GetUiDataRequest) returns (GetUiDataResponse);
  rpc UpdateUiData(UpdateUiDataRequest) returns (UpdateUiDataResponse);
  rpc DeleteUiData(DeleteUiDataRequest) returns (DeleteUiDataResponse);
  rpc ListUiData(ListUiDataRequest) returns (ListUiDataResponse);

  rpc CreateUiCaseResult(CreateUiCaseResultRequest) returns (CreateUiCaseResultResponse);
  rpc GetUiCaseResult(GetUiCaseResultRequest) returns (GetUiCaseResultResponse);
  rpc UpdateUiCaseResult(UpdateUiCaseResultRequest) returns (UpdateUiCaseResultResponse);
  rpc SaveUiCaseResult(SaveUiCaseResultRequest) returns (SaveUiCaseResultResponse);
  rpc DeleteUiCaseResult(DeleteUiCaseResultRequest) returns (DeleteUiCaseResultResponse);
  rpc ListUiCaseResults(ListUiCaseResultsRequest) returns (ListUiCaseResultsResponse);
  rpc ListUiCaseResultsBySuiteResult(ListUiCaseResultsBySuiteResultRequest) returns (ListUiCaseResultsBySuiteResultResponse);
  rpc ListUiSuiteResults(ListUiSuiteResultsRequest) returns (ListUiSuiteResultsResponse);

  rpc CreateUiSuiteCaseRel(CreateUiSuiteCaseRelRequest) returns (CreateUiSuiteCaseRelResponse);
  rpc AddCasesIntoSuite(AddCasesIntoSuiteRequest) returns (AddCasesIntoSuiteResponse);
  rpc RemoveCasesFromSuite(RemoveCasesFromSuiteRequest) returns (RemoveCasesFromSuiteResponse);
  rpc GetUiSuiteCaseRel(GetUiSuiteCaseRelRequest) returns (GetUiSuiteCaseRelResponse);
  rpc UpdateUiSuiteCaseRel(UpdateUiSuiteCaseRelRequest) returns (UpdateUiSuiteCaseRelResponse);
  rpc DeleteUiSuiteCaseRel(DeleteUiSuiteCaseRelRequest) returns (DeleteUiSuiteCaseRelResponse);
  rpc ListUiSuiteCases(ListUiSuiteCasesRequest) returns (ListUiSuiteCasesResponse);

  rpc ListUiSuites (ListUiSuitesRequest) returns (ListUiSuitesResponse);
  rpc CreateUiSuite(CreateUiSuiteRequest) returns (CreateUiSuiteResponse);
  rpc GetUiSuite(GetUiSuiteRequest) returns (GetUiSuiteResponse);
  rpc UpdateUiSuite(UpdateUiSuiteRequest) returns (UpdateUiSuiteResponse);
  rpc DeleteUiSuite(DeleteUiSuiteRequest) returns (DeleteUiSuiteResponse);


  rpc CreateUiSuiteResult(CreateUiSuiteResultRequest) returns (CreateUiSuiteResultResponse);
  rpc GetUiSuiteResult(GetUiSuiteResultRequest) returns (GetUiSuiteResultResponse);
  rpc UpdateUiSuiteResult(UpdateUiSuiteResultRequest) returns (UpdateUiSuiteResultResponse);
  rpc DeleteUiSuiteResult(DeleteUiSuiteResultRequest) returns (DeleteUiSuiteResultResponse);

  rpc ExecuteSuite(ExecuteSuiteRequest) returns (ExecuteSuiteResponse);
  rpc ExecuteCase(ExecuteCaseRequest) returns (ExecuteCaseResponse);

  rpc GetCompileCase(GetCompileCaseRequest) returns (GetCompileCaseResponse);

  rpc AiProcessCase(AiProcessCaseRequest) returns (AiProcessCaseResponse);

  rpc AiDebug(AiDebugRequest) returns (AiDebugResponse);

  rpc OptNlpUiCase(OptNlpUiCaseRequest) returns (OptNlpUiCaseResponse);

}