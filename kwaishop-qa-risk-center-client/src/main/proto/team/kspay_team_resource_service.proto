syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;
import "google/protobuf/any.proto";


option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.team";
option java_outer_classname = "KspayTeamResourceServiceProto";

message QueryTeamTaskRequest {
  string keyword = 1;
  int32 task_group = 2;
  string operator = 3;
  int32 limit = 4;
  int32 offset = 5;
}

message QueryTeamTaskResponse {
  int32 code = 1;
  string message = 2;
  repeated TaskInfo result = 3;
}

message TaskInfo {
  string task_id = 1;
  string title = 2;
  string status = 3;
  string assignee = 4;
  string creator = 5;
  int32 create_time = 6;
  string project_id = 7;
  string section = 8;
  string section_id = 9;
  string project_name = 10;
}

message FeatureInfoRequest {
  string team_id = 1;
}

message FeatureInfoResponse {
  int32 code = 1;
  string message = 2;
  repeated FeatureInfo result = 3;
}

message FeatureInfo {
  string team_id = 1;
  string title = 2;
  string status = 3;
  string feature_id = 4;
}

message KdevResourceResponse {
  int32 status = 1;
  string message = 2;
  KdevResourceData data = 3;
  string trace_id = 4;
  int64 timestamp = 5;
}

message KdevResourceData {
  int32 total = 1;
  repeated KdevResourceList list = 2;
}

message KdevResourceList{
    int32 id = 1;
}

message KdevFeatureInfoResponse {
  int32 status = 1;
  string message = 2;
  KdevFeatureInfoData data = 3;
  string trace_id = 4;
  int64 timestamp = 5;
}

message KdevFeatureInfoData{
  int32 id = 1;
  string title = 2;
  string status = 3;
  string team_id = 4;
  repeated KdevFeatureInfoList change_list = 5;
}

message KdevFeatureInfoList{
  int32 id = 1;
  string name = 2;
  string status = 3;
  int32 type = 4;
}

message KdevFeatureListResponse {
  int32 status = 1;
  string message = 2;
  KdevFeatureListData data = 3;
  string trace_id = 4;
  int64 timestamp = 5;
}

message KdevFeatureListData{
  int32 total = 1;
  repeated KdevFeatureList list = 2;
}

message KdevFeatureList{
  int32 id = 1;
  string title = 2;
  int32 status = 3;
  string status_name = 4;
  int32 model_type = 5;
  string model_type_name = 6;
  int64 create_time = 7;
  string biz_space_id = 8;
  string biz_space_name = 9;
}

message KdevFeatureDetailResponse {
  int32 status = 1;
  string message = 2;
  KdevFeatureDetailData data = 3;
  string trace_id = 4;
  int64 timestamp = 5;
}

message KdevFeatureDetailData{
  int32 id = 1;
  string title = 2;
  int32 status = 3;
  string status_name = 4;
  int32 model_type = 5;
  string model_type_name = 6;
  int64 create_time = 7;
  int32 biz_space_id = 8;
  string biz_space_name = 9;
  repeated BranchList branch_list = 10;
  repeated string participants = 11;
  repeated string dev_managers = 12;
  repeated string qa_managers = 13;
}

message BranchList{
  int32 branch_id = 1;
  string branch_name = 2;
  int32 branch_type = 3;
  string branch_type_name = 4;
  int32 project_id = 5;
  string project_name = 6;
  string project_path = 7;
}

message BranchInfo {
  int32 branch_id = 1;
  string name = 2;
  int32 repo_id = 3;
  int32 group_id = 4;
  string repo_name = 5;
  string path = 6;
  int32 branch_type = 7;
  string branch_tag = 8;
  string last_commit_sha = 9;
  int64 latest_commit_time = 10;
  int32 deleted = 11;
  int32 git_project_id = 12;
}

message FeatureList {
  int32 id = 1;
  int32 version = 2;
  string title = 3;
  string team_id = 4;
  repeated TeamList team_list = 5;
  string kimbot = 6;
  int32 status = 7;
  int64 start_time = 8;
  int64 expect_joint_time = 9;
  int64 expect_test_time = 10;
  int64 stop_time = 11;
  bool collect = 12;
  bool can_delete = 13;
  repeated DevManager dev_managers = 14;
  repeated QAManager qa_managers = 15;
  repeated Participation participation = 16;
  string halo_url = 17;
  int32 dev_branch_cnt = 18;
  int32 int_branch_cnt = 19;
  int32 business_space_id = 20;
  string business_space_name = 21;
  string view_id = 22;
  TemplateTag template_tag = 23;
  string template_tag_name = 24;
  int32 template_id = 25;
  string template_name = 26;
  int32 model_type = 27;
  string model_name = 28;
  bool new_change_button_disable = 29;
  string new_change_button_disable_hint = 30;
  string status_name = 31;
  int32 artifact_type = 32;
  int64 create_time = 33;
}

message TeamList{
  string id = 1;
  string task_id = 2;
  string name = 3;
  string title = 4;
  string status = 5;
  string status_name = 6;
  string url = 7;
  int64 create_time = 8;
  string exits = 9;
  int32 end_status = 10;
  string priority = 11;
  string task_group = 12;
  string task_type = 13;
  string task_class = 14;
  string project_name = 15;
  string type = 16;
  string key = 17;
  string feature_id = 18;
  string business_space_id = 19;
  string creator = 20;
}

message DevManager {
  int32 id = 1;
  string username = 2;
  string name = 3;
  string email = 4;
  string avatar_url = 5;
  string user_status = 6;
  int32 user_type = 7;
  bool active = 8;
}

message QAManager {
  int32 id = 1;
  string username = 2;
  string name = 3;
  string email = 4;
  string avatar_url = 5;
  string user_status = 6;
  int32 user_type = 7;
  bool active = 8;
}

message Participation {
  int32 id = 1;
  string username = 2;
  string name = 3;
  string email = 4;
  string avatar_url = 5;
  string user_status = 6;
  int32 user_type = 7;
  bool active = 8;
}

message TemplateTag {
  int32 id = 1;
  string name = 2;
  string key = 3;
}

message BranchInfoData {
  BranchInfo branch_info = 1;
  repeated FeatureList feature_list = 2;
}

message BranchInfoResponse {
  int32 status = 1;
  string message = 2;
  BranchInfoData data = 3;
  string trace_id = 4;
  int64 timestamp = 5;
}

message AccuracyData {
  int32 result = 1;
  ReportData data = 2;
  bool success = 3;
  int64 server_timestamp = 4;
  google.protobuf.Any error_msg = 5;
}

message ReportData {
  string application = 1;
  string branch = 2;
  string report_url = 3;
  int32 change_cnt = 4;
  int32 finnace_loss_cnt = 5;
  bool is_pass = 6;
  repeated FinanceMethod finance_methods = 7;
}

message FinanceMethod {
  repeated string all_entry = 1;
  MethodInfo method_info = 2;
}

message MethodInfo {
  int32 flag = 1;
  int32 affected_link = 2;
  int32 covered_link = 3;
  int32 un_covered_link = 4;
  string jar_path = 5;
  float incre_coverage = 6;
  string fcp_risk_description = 7;
  string fcp_biz_chain = 8;
  string fcp_biz_chain_desc = 9;
  string fcp_risk_detail = 10;
  string risk_type = 11;
  bool is_coverage_fluctuation = 12;
  repeated ChangeRange change_ranges = 13;
  bool supplement_data = 14;
  bool mark = 15;
  string full_name = 16;
  string short_name = 17;
  string full_clazz_name = 18;
  string input_args = 19;
  string output_args = 20;
  string application = 21;
  string method_type = 22;
  string change_type = 23;
  float coverage = 24;
  bool is_up_to_standard = 25;
  string method_category = 26;
  string report_url = 27;
  repeated PayFinanceLossPoints pay_finance_loss_points = 28;
}

message PayFinanceLossPoints{
  string table = 1;
  string field = 2;
}

message ChangeRange {
  int32 start_line = 1;
  int32 end_line = 2;
}

message GetAccuracyRequest{
  string application = 1;
  string branch = 2;
}

service KspayTeamResourceService {

  rpc QueryTeamTask (QueryTeamTaskRequest) returns (QueryTeamTaskResponse);
  rpc QueryFeatureInfo (FeatureInfoRequest) returns (FeatureInfoResponse);
}