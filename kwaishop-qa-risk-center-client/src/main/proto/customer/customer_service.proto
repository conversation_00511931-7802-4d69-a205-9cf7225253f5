syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.customer";
option java_outer_classname = "CustomerServiceProto";

message MsgSendC2BRequest{

  string       sender_id    = 1;
  string       target_id    = 2;
  string       text         = 3;
  string       lane_id      = 4;
  int32        content_type = 5;
  OrderDTO     order        = 6;
  CommodityDTO commodity    = 7;
  int32 business_type = 8;
}

message BatchMsgSendC2BRequest{

  repeated string sender_id = 1;
  string          target_id = 2;
  string          text      = 3;
  string          lane_id   = 4;
  int32 business_type = 5;
}

message OrderDTO{

  string order_id   = 1;
  string item_title = 2;
}

message CommodityDTO{

  string item_id    = 1;
  string item_title = 2;

}

message MsgSendC2BResponse{

  int32  result    = 1;

  string error_msg = 2;

}

message MsgSendB2CResponse{

  int32  result    = 1;

  string error_msg = 2;

}

message MsgSendB2CRequest{

  string sender_id     = 1;
  string target_id     = 2;
  string text          = 3;
  string lane_id       = 4;
  int32  business_type = 5;

}

message BatchQuerySubAccountRequest{
  string oid = 1;

}

message BatchQuerySubAccountResponse{
  int32  result    = 1;

  string error_msg = 2;

  string data      = 3;

}

message SubAccountDTO{
  string         oid           = 1;

  repeated int64 sub_accout_id = 2;

}

message CloseSessionRequest{
  string oid      = 1;
  string buyer_id = 2;

}

message CloseSessionResponse{
  int32  result    = 1;

  string error_msg = 2;
}

message UserKlinkLoginDTO{
  int64  user_id     = 1;
  string nikename    = 2;
  string apist       = 3;
  string ssecurity   = 4;
  int32  type        = 5;
  int64  update_time = 6;

}

message PageUserKlinkLoginDTO {

  int64                      total     = 1;

  int32                      page_no   = 2;

  int32                      page_size = 3;

  repeated UserKlinkLoginDTO details   = 4;

}

message PageUserKlinkLoginListResponse {

  int32                 result    = 1;
  string                error_msg = 2;
  PageUserKlinkLoginDTO data      = 3;

}

message PageUserKlinkLoginListRequest {
  int64  user_id   = 1;
  string nickname  = 2;
  int32  page_no   = 3;
  int32  page_size = 4;

}

message UpdateKlinkLoginInfoRequest {
  int64  user_id   = 1;
  string nickname  = 2;
  string apist     = 3;
  string ssecurity = 4;
  int32  type      = 5;
  string operator  = 6;

}

message UpdateKlinkLoginInfoResponse {
  int32  result    = 1;

  string error_msg = 2;
}

message ImportKlinkTokenRequest{
  string cdn_url = 1;
}

message ImportKlinkTokenResponse {

  int32  result    = 1;

  string error_msg = 2;
}

message ExportEvaluateDataRequest{
  int64 start_time =1 ;

  int64 end_time = 2;

  int32 page_index =3;

  int32 scene = 4;

}

message ExportEvaluateDataResponse{
  int32  result    = 1;

  string error_msg = 2;

}


message SelectHiveRequest{
  int64 p_date =1;
  int32 limit =2;
  int32 offset =3;

}

message SelectHiveResponse{
  int32  result    = 1;

  string error_msg = 2;


}

service CustomerDomainService {

  rpc MsgSendC2B (MsgSendC2BRequest) returns (MsgSendC2BResponse);
  rpc MsgSendB2C (MsgSendB2CRequest) returns (MsgSendB2CResponse);
  rpc BatchQuerySubAccount (BatchQuerySubAccountRequest) returns (BatchQuerySubAccountResponse);
  rpc CloseSession(CloseSessionRequest) returns (CloseSessionResponse);
  rpc QueryUserKlinkLoginInfo(PageUserKlinkLoginListRequest) returns (PageUserKlinkLoginListResponse);
  rpc UpdateUserKlinkLoginInfo(UpdateKlinkLoginInfoRequest) returns (UpdateKlinkLoginInfoResponse);
  rpc BatchMsgSendC2B (BatchMsgSendC2BRequest) returns (MsgSendC2BResponse);
  rpc ImportKlinkToken(ImportKlinkTokenRequest) returns (ImportKlinkTokenResponse);
  rpc ExportEvaluateData(ExportEvaluateDataRequest) returns(ExportEvaluateDataResponse);
  rpc SelectHive(SelectHiveRequest) returns(SelectHiveResponse);

}

