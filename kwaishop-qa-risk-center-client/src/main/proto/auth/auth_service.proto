syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.auth";
option java_outer_classname = "AuthDomainServiceProto";


message AuthRequest {

  string operator = 1;

  string code = 2;
}


message AuthResponse {

  int32 result = 1;

  string error_msg = 2;

  AuthDTO data = 3;
}

message AuthDTO {

  bool has_auth = 1;

  repeated CenterAuthInfo center_info = 2;

}

message CenterAuthInfo {

  string center_name = 1;

  int64 center_id = 2;

  repeated TeamAuthInfo team_info = 3;

}

message TeamAuthInfo {

  string team_name = 1;

  int64 team_id = 2;
}

service AuthDomainService {

  rpc CheckAuth (AuthRequest) returns (AuthResponse);

}