syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf";
option java_outer_classname = "TestServiceProto";


message TestRequest {

  string operator = 1;
}

message TestResponse {

  int32 result = 1;

  string error_msg = 2;
}

message FixRequest{
  int32 from = 1;
  int32 to = 2;
}

message FixResponse {

  int32 result = 1;

  string error_msg = 2;
}

message BPMTestRequest{
  string bpm = 1;
}

message BPMTestResponse{
  int32 result = 1;
  string error_msg = 2;
}

service TestService {

  rpc Test(TestRequest) returns (TestResponse);

  rpc FixAccountContent(FixRequest) returns (FixResponse);

  rpc TestBPM(BPMTestRequest) returns (BPMTestResponse);
}