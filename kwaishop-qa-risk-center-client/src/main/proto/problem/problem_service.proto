syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.problem";
option java_outer_classname = "ProblemServiceProto";


message CreateProblemRequest {

  string operator = 1;

  int32 detail_type = 2;

  string problem_link = 3;

  string name = 4;

  int32 level = 5;

  int64 center_id = 6;

  int64 team_id = 7;

  string problem_desc = 8;

  string problem_reason = 9;

  int32 missing_type = 10;

  string missing_reason = 11;

  int64 problem_start_time = 12;

  int64 problem_end_time = 13;
}

message CreateProblemResponse {

  int32 result = 1;

  string error_msg = 2;
}

message UpdateProblemRequest {

  string operator = 1;

  int32 detail_type = 2;

  string problem_link = 3;

  string name = 4;

  int32  level = 5;

  int64 center_id = 6;

  int64 team_id = 7;

  string problem_desc = 8;

  string problem_reason = 9;

  int32 missing_type = 10;

  string missing_reason = 11;

  int64 problem_start_time = 12;

  int64 problem_end_time = 13;

  int64 id = 14;
}

message UpdateProblemResponse {

  int32 result = 1;

  string error_msg = 2;
}

message DeleteProblemRequest {

  string operator = 1;

  int64 id = 2;
}

message DeleteProblemResponse {

  int32 result = 1;

  string error_msg = 2;
}

message ProblemDTO {

  int64 id = 1;

  int32 detail_type = 2;

  string problem_link = 3;

  string name = 4;

  int32 level = 5;

  int64 center_id = 6;

  int64 team_id = 7;

  string problem_desc = 8;

  string problem_reason = 9;

  int32 missing_type = 10;

  string missing_reason = 11;

  int64 problem_start_time = 12;

  int64 problem_end_time = 13;

  string missing_type_desc = 14;

  string creator = 15;

  string modifier = 16;

  int64 create_time = 17;

  int64 update_time = 18;

  string center_name = 19;

  string team_name = 20;

  string level_desc = 21;
}

message QueryProblemDetailListRequest {

  string operator = 1;

  int32 detail_type = 2;

  int64 id = 3;

  string name = 4;

  int32 level = 5;

  int64 center_id = 6;

  int64 team_id = 7;

  int32 missing_type = 8;

}

message QueryProblemDetailListResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated ProblemDTO data = 3;

}

message PageProblemDTO {

  int64 total = 1;

  int32 page_no = 2;

  int32 page_size = 3;

  repeated ProblemDTO details = 4;
}

message QueryProblemDetailPageListRequest {

  string operator = 1;

  int32 detail_type = 2;

  int64 id = 3;

  string name = 4;

  int32  level = 5;

  int64 center_id = 6;

  int64 team_id = 7;

  int32 missing_type = 8;

  int32 page_no = 9;

  int32 page_size = 10;
}

message QueryProblemDetailPageListResponse {

  int32 result = 1;

  string error_msg = 2;

  PageProblemDTO data = 3;
}

service ProblemDomainService {

  rpc CreateProblem (CreateProblemRequest) returns (CreateProblemResponse);

  rpc UpdateProblem (UpdateProblemRequest) returns (UpdateProblemResponse);

  rpc DeleteProblem (DeleteProblemRequest) returns (DeleteProblemResponse);

  rpc QueryProblemDetailList (QueryProblemDetailListRequest) returns (QueryProblemDetailListResponse);

  rpc QueryProblemDetailPageList (QueryProblemDetailPageListRequest) returns (QueryProblemDetailPageListResponse);
}