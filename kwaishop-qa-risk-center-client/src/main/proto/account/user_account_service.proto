syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.account";
option java_outer_classname = "AccountDomainServiceProto";


message QueryUserAccountRequest {

  int64 user_id = 1;

  int32 login_type = 2;

  int32 data_type = 3;

  int32 account_type = 4;

  int32 status = 5;

  string operator = 6;

  int32 merchant_permission = 7;

  int32 distributor_permission = 8;

  int32 promoter_permission = 9;

  int32 leader_permission = 10;
}

message QueryUserAccountResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated UserAccountDTO data = 3;

}



message QueryDumpResponse {

  int32 result = 1;

  string error_msg = 2;

  UserDbForDumpDTO data = 3;

}


message QueryEsResponse {

  int32 result = 1;

  string error_msg = 2;

  string data = 3;

}



message UserDbForDumpDTO {

  int32 center_id = 1;

  int32 team_id = 2;

  int64 user_id = 3;

  int32 login_type = 4;

  string account = 5;

  string password = 6;

  int32 account_type = 7;

  int32 data_type = 8;

  string ext = 9;

  int32 status = 10;


  string creator = 11;

  string modifier = 12;

  int32 deleted = 13;
}


message QueryPageUserAccountRequest {

  int64 user_id = 1;

  int32 login_type = 2;

  int32 data_type = 3;

  int32 account_type = 4;

  int32 status = 5;

  string operator = 6;

  int32 page_no = 7;

  int32 page_size = 8;

  int32 merchant_permission = 9;

  int32 distributor_permission = 10;

  int32 promoter_permission = 11;

  int32 leader_permission = 12;

  string account = 13;

  string shop_type = 14;
}

message QueryPageUserAccountResponse {

  int32 result = 1;

  string error_msg = 2;

  PageUserAccountDTO data = 3;
}

message PageUserAccountDTO {

  int64 total = 1;

  int32 page_no = 2;

  int32 page_size = 3;

  repeated UserAccountDTO details = 4;
}

message UserAccountDTO {

  int64 center_id = 1;

  string center_name = 2;

  int64 team_id = 3;

  string team_name = 4;

  int64 user_id = 5;

  string account = 6;

  string password = 7;

  int32 login_type = 8;

  string login_type_desc = 9;

  int64 id = 10;

  int32 status = 11;

  string create_time = 12;

  string update_time = 13;

  string creator = 14;

  string modifier = 15;

  string status_desc = 16;

  UserAuthDTO user_auth_dto = 17;

  repeated string borrower = 18;

  int32 modifiable = 19;

  int32 modifiable_manage = 20;
}

message UserAuthDTO {

  string store_name = 1;

  string store_type = 2;

  bool deposit = 3;

  bool is_seller = 4;

  bool merchant_permission = 5;

  bool distributor_permission = 6;

  bool promoter_permission = 7;

  bool leader_permission = 8;

  string comment = 9;

  string shop_score = 10;

  string promoter_score = 11;

  string shop_rating = 12;

  string seller_name = 13;
}

message GetTestAccountRequest {

  string scene = 1;

}



message GetEsRequest {

  string name = 1;
  string id = 2;
  string colony = 3;
}


message GetEsDataRequest {

  string name = 1;
  string colony = 2;
  string  keys = 3;
  string  values = 4;
}


message KafkaRequest {

  string topic = 1;
  string msg = 2;
}


message GetTestAccountResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated int64 data = 3;
}

message ImportAccountExcelRequest {

  int32 import_type = 1;

  string operator = 2;

  string cdn_url = 3;

  int64 center_id = 4;

  int64 team_id = 5;

  int32 account_type = 6;

}

message ImportAccountExcelResponse {

  int32 result = 1;

  string error_msg = 2;
}

message ImportUserAccountRequest {

  string operator = 1;

  int64 center_id = 2;

  int64 team_id = 3;

  string account = 4;

  string password = 5;

  int32 login_type = 6;

  int64 user_id = 7;
  //增加自动化标识到status字段里，用来让用户自己判定是否租借出去
  int32 status = 8;
}

message ImportUserAccountResponse {

  int32 result = 1;

  string error_msg = 2;
}

message GetAccountTokenRequest {

  int64 user_id = 1;
}

message GetAccountTokenResponse {

  int32 result = 1;

  string error_msg = 2;

  string token = 3;
}

message RentAccountRequest {

  int64 user_id = 1;

  string borrower = 2;

  int32 duration = 3;
}



message UserAccountDumpRequest {

  int64 user_id = 2;
  string ext = 3;


}


message RentAccountResponse {

  int32 result = 1;

  string error_msg = 2;
}

message ReturnAccountRequest {

  int64 user_id = 1;

  string borrower = 2;
}

message ReturnAccountResponse {

  int32 result = 1;

  string error_msg = 2;
}

message QueryRentRecordsRequest {

  string borrower = 1;

  int64 user_id = 2;

  int32 rental_status = 3;

  int32 login_type = 4;

  int64 team_id = 5;

  int64 center_id = 6;
}

message AccountRentalDTO {

  int64 id = 1;

  int64 user_id = 2;

  string borrower = 3;

  string rental_time = 4;

  int64 duration = 5;

  string due_time = 6;

  int64 rental_status = 7;

  int64 team_id = 8;

  int64 center_id = 9;

  int32 login_type = 11;

  string center_name = 12;

  string team_name = 13;

  string rental_status_desc = 14;

  string login_type_desc = 15;

  string password = 16;

  string account = 17;

  string update_time = 18;

  UserAuthDTO user_auth_dto = 19;

  string token = 20;
}

message QueryRentRecordsResponse {

  int32 result = 1;

  string error_msg = 2;

  repeated AccountRentalDTO data = 3;
}

message QueryPageRentRecordsRequest {

  string borrower = 1;

  int64 user_id = 2;

  int32 rental_status = 3;

  int32 login_type = 4;

  int64 team_id = 5;

  int64 center_id = 6;

  int32 page_no = 7;

  int32 page_size = 8;
}

message PageRentAccountDTO {

  int64 total = 1;

  int32 page_no = 2;

  int32 page_size = 3;

  repeated AccountRentalDTO details = 4;
}

message QueryPageRentAccountResponse {

  int32 result = 1;

  string error_msg = 2;

  PageRentAccountDTO data = 3;

  map<string, PageRentAccountDTO> map = 4;
}

message ExtendRentRequest {

  int64 user_id = 1;

  string borrower = 2;

  int32 extend_time = 3;
}

message ExtendRentResponse {

  int32 result = 1;

  string error_msg = 2;
}

message UpdateAllUserInfoRequest {

  int32 merchant_permission = 1;

  int32 distributor_permission = 2;

  int32 promoter_permission = 3;

  int32 leader_permission = 4;

  string password = 5;

  string comment = 6;

  string operator = 7;

  int64 user_id = 8;
}

message UpdateAllUserInfoResponse {

  int32 result = 1;

  string error_msg = 2;
}

message ImportAccountTokenRequest {

  string cdn_url = 1;
}

message ImportAccountTokenResponse {

  int32 result = 1;

  string error_msg = 2;
}

message AccountManagementRequest {

  int64 user_id = 1;

  int32 team_id = 2;

  string operator = 3;
}

message AccountManagementResponse {

  int32 result = 1;

  string error_msg = 2;
}

message ApplyAccountRequest{

  string operator = 1;

  int64 center_id = 2;

  int64 team_id = 3;

  string rental_time = 4;

}

message ApplyAccountResponse{

  int32 result = 1;

  string error_msg = 2;
}

message CheckAccountRequest{

  string operator = 1;

  int64 center_id = 2;

  int64 user_id = 3;
}

message CheckAccountResponse{

  int32 result = 1;

  string error_msg = 2;
}

message BatchCheckAccountRequest{
  string operator = 1;

  int64 center_id = 2;
}

message BatchCheckAccountResponse{

  int32 result = 1;

  string error_msg = 2;
}

message BatchDeleteAccountRequest{
  repeated int64 uid = 1;
}

message BatchDeleteAccountResponse{

  int32 result = 1;

  string error_msg = 2;
}

service AccountDomainService {

  rpc QueryUserAccount (QueryUserAccountRequest) returns (QueryUserAccountResponse);

  rpc GetTestAccount (GetTestAccountRequest) returns (GetTestAccountResponse);

  rpc ImportAccountExcel (ImportAccountExcelRequest) returns (ImportAccountExcelResponse);

  rpc ImportUserAccount (ImportUserAccountRequest) returns (ImportUserAccountResponse);

  rpc QueryPageUserAccount (QueryPageUserAccountRequest) returns (QueryPageUserAccountResponse);

  // 根据账户去获取token
  rpc GetAccountToken (GetAccountTokenRequest) returns (GetAccountTokenResponse);

  // 管理账户，仅创建人使用
  rpc AccountManagement (AccountManagementRequest) returns (AccountManagementResponse);

  // 租借账户
  rpc RentAccount(RentAccountRequest) returns (RentAccountResponse);

  //申领账户
  rpc ApplyAccount(ApplyAccountRequest)returns (ApplyAccountResponse);

  // 归还账户
  rpc ReturnAccount(ReturnAccountRequest) returns (ReturnAccountResponse);

  // 查询租借记录
  rpc QueryRentRecords(QueryRentRecordsRequest) returns (QueryRentRecordsResponse);

  // 翻页查询记录
  rpc QueryPageRentRecords(QueryPageRentRecordsRequest) returns (QueryPageRentAccountResponse);

  // 翻页查询记录
  rpc QueryPageRentRecordsTest(QueryPageRentRecordsRequest) returns (QueryPageRentAccountResponse);

  // 延长租借
  rpc ExtendRent(ExtendRentRequest) returns (ExtendRentResponse);

  // 更新所有信息的总接口
  rpc UpdateAllUserInfo(UpdateAllUserInfoRequest) returns (UpdateAllUserInfoResponse);

  rpc InsertDump(UserAccountDumpRequest) returns (UpdateAllUserInfoResponse);

  // 增加批量导入token接口
  rpc ImportAccountToken(ImportAccountTokenRequest) returns (ImportAccountTokenResponse);


  rpc GetDumpUser (GetTestAccountRequest) returns (QueryDumpResponse);

  rpc GetEsData (GetEsRequest) returns (QueryEsResponse);

  rpc GetEsDataWithData (GetEsDataRequest) returns (QueryEsResponse);


  rpc SendKafkaMsg(KafkaRequest) returns(ImportAccountTokenResponse);

  rpc CheckAccount(CheckAccountRequest)returns(CheckAccountResponse);

  rpc BatchCheckAccount(BatchCheckAccountRequest)returns(BatchCheckAccountResponse);

  rpc DeleteAccountByUid(BatchDeleteAccountRequest)returns(BatchDeleteAccountResponse);
}