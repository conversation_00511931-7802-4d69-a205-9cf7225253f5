syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.account";
option java_outer_classname = "UserAuthDomainServiceProto";

message UpdateUserAuthRequest {

  string operator = 1;

  int64 user_id = 2;
}

message UpdateUserAuthResponse {

  int32 result = 1;

  string error_msg = 2;
}

service UserAuthDomainService {
  // 更新状态，会去手动去查下最新的权限状态
  rpc UpdateUserAuth (UpdateUserAuthRequest) returns (UpdateUserAuthResponse);

//  // 更新存量表
//  rpc QueryUserAuth () returns ();
}