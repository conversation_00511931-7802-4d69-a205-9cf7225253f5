syntax = "proto3";

package kuaishou.kwaishop.qa.risk.center;

option java_multiple_files = true;
option java_package = "com.kuaishou.kwaishop.qa.risk.center.protobuf.stress";
option java_outer_classname = "StressServiceProto";

message StressScenarioDTO{
  int64 id = 1;

  int64 owner_id = 2;

  string name = 3;

  int32 status = 4;

  string out_url = 5;

  string out_id = 6;

  int64 create_time = 7;

  int64 update_time = 8;

  repeated StressInterfaceDTO interface_list = 9;

  repeated StressServiceDTO service_list = 10;
}

message StressServiceDTO{
  int64 id = 1;

  string service_name = 2;

  string service_desc = 3;

  int32 status = 4;

  int64 create_time = 5;

  int64 update_time = 6;
}

message StressInterfaceDTO{
  int64 id = 1;

  string interface_name = 2;

  string interface_desc = 3;

  int64 service_id = 4;

  string service_name = 5;

  int32 status = 6;

  int64 create_time = 7;

  int64 update_time = 8;

}

message StressInterfaceBaseDTO{
  int64 id = 1;

  int64 scenario_id = 2;

  string scenario_name = 3;

  int64 interface_id = 4;

  string interface_name = 5;

  int64 service_id = 6;

  string service_name = 7;

  int64 avg995 = 8;

  int64 pass_percent = 9;

  int64 avg99 = 10;

  int64 avg95 = 11;

  int64 avg75 = 12;

  int64 avg_rt = 13;

  int32 status = 14;

  int64 max_qps = 15;

  int64 create_time = 16;

  int64 update_time = 17;
}

message StressInterfaceRecordDTO{
  int64 id = 1;

  int64 scenario_id = 2;

  string scenario_name = 3;

  int64 interface_id = 4;

  string interface_name = 5;

  int64 service_id = 6;

  string service_name = 7;

  int64 avg995 = 8;

  int64 pass_percent = 9;

  int64 avg99 = 10;

  int64 avg95 = 11;

  int64 avg75 = 12;

  int64 avg_rt = 13;

  int32 status = 14;

  int64 max_qps = 15;

  int64 create_time = 16;

  int64 update_time = 17;

  int64 scenario_record_id = 18;

  string scenario_record_name = 19;

  int64 service_record_id = 20;

  string service_record_name = 21;

}



message StressServiceBaseDTO{

  int64 id = 1;

  int64 scenario_id = 2;

  string scenario_name = 3;

  int64 service_id = 4;

  string service_name = 5;

  int32 status = 6;

  int64 avg_cpu = 7;

  int64 max_cpu = 8;

  int64 avg_thread_pools = 9;

  int64 max_thread_pools = 10;

  int64 avg_risk = 11;

  int64 max_risk = 12;

  int64 avg_disk = 13;

  int64 max_disk = 14;

  int64 avg_exceptions = 15;

  int64 max_exceptions = 16;

  int64 avg_ram = 17;

  int64 max_ram = 18;

  int64 create_time = 19;

  int64 update_time = 20;

}

message StressServiceRecordDTO {

  int64 id = 1;

  int64 scenario_id = 2;

  string scenario_name = 3;

  int64 service_id = 4;

  string service_name = 5;

  int32 status = 6;

  int64 avg_cpu = 7;

  int64 max_cpu = 8;

  int64 avg_thread_pools = 9;

  int64 max_thread_pools = 10;

  int64 avg_risk = 11;

  int64 max_risk = 12;

  int64 avg_disk = 13;

  int64 max_disk = 14;

  int64 avg_exceptions = 15;

  int64 max_exceptions = 16;

  int64 avg_ram = 17;

  int64 max_ram = 18;

  int64 from_time = 19;

  int64 to_time = 20;

  int64 create_time = 21;

  int64 update_time = 22;

  int64 scenario_record_id = 23;

  string scenario_record_name = 24;

}

message StressScenarioRecordDTO {

  int64 id = 1;

  int64 scenario_id = 2;

  string scenario_name = 3;

  int32 status = 4;

  bool sum_base = 5;

  int64 from_time = 6;

  int64 to_time = 7;

  int64 create_time = 8;

  int64 update_time = 9;

  repeated StressServiceRecordDTO service_record_list = 10;

  repeated StressInterfaceRecordDTO interface_record_list = 11;

}

message PageScenarioRecordDTO {

  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated StressScenarioRecordDTO scenario_record_list = 4;
}


message PageStressServiceBaseDTO {

  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated StressServiceBaseDTO stress_service_base_list = 4;
}

message PageStressInterfaceBaseDTO {

  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated StressInterfaceBaseDTO stress_interface_base_list = 4;
}

message PageScenarioDTO {

  int32 page_no = 1;

  int32 page_size = 2;

  int64 total = 3;

  repeated StressScenarioDTO scenario_list = 4;
}

message QueryScenarioRecordsByPageRequest {
  int64 scenario_id = 1;

  string scenario_name = 2;

  int32 page_no = 3;

  int32 page_size = 4;

}

message QueryScenarioRecordsByPageResponse {

  int32 result = 1;

  string error_msg = 2;

  PageScenarioRecordDTO data = 3;
}

message QueryScenariosByPageRequest {

  string scenario_name = 1;

  int32 page_no = 2;

  int32 page_size = 3;

}

message QueryServiceBaseListByPageRequest {

  int64 service_id = 1;

  string service_name = 2;

  int32 page_no = 3;

  int32 page_size = 4;

}

message QueryServiceBaseListByPageResponse {

  int32 result = 1;

  string error_msg = 2;

  PageStressServiceBaseDTO data = 3;

}

message QueryInterfaceBaseListByPageRequest {

  int64 interface_id = 1;

  string interface_name = 2;

  int32 page_no = 3;

  int32 page_size = 4;

}

message QueryInterfaceBaseListByPageResponse {

  int32 result = 1;

  string error_msg = 2;

  PageStressInterfaceBaseDTO data = 3;

}


message QueryScenariosByPageResponse{

  int32 result = 1;

  string error_msg = 2;

  PageScenarioDTO data = 3;
}

message StressInterfaceWithBaseRequest{
  int64 id = 1;
}
message StressInterfaceWithBaseResponse{

  int32 result = 1;

  string error_msg = 2;

  repeated StressInterfaceRecordDTO data = 3;
}

message StressServiceWithBaseRequest{
  int64 id = 1;
}
message StressServiceWithBaseResponse{

  int32 result = 1;

  string error_msg = 2;

  repeated StressServiceRecordDTO data = 3;
}

message CreateScenarioFromJsonRequest{
  string data = 1;
}

message CreateScenarioFromJsonResponse{
  int32 result = 1;

  string error_msg = 2;

}

message GetScenarioRecordDetailRequest{
  string id = 1;
}

message GetScenarioRecordDetailResponse{
  int32 result = 1;

  string error_msg = 2;

  StressScenarioRecordDTO data = 3;
}



service StressDomainService {
  //查询某个场景下的所有压测记录
  rpc QueryScenarioRecordsByPage(QueryScenarioRecordsByPageRequest) returns (QueryScenarioRecordsByPageResponse);

  //查询所有的场景
  rpc QueryScenariosByPage(QueryScenariosByPageRequest) returns (QueryScenariosByPageResponse);

  //查询所有的场景
  rpc QueryScenariosByPageWithInterface(QueryScenariosByPageRequest) returns (QueryScenariosByPageResponse);
  //分页查询服务的基线
  rpc QueryServiceBaseListByPage(QueryServiceBaseListByPageRequest) returns (QueryServiceBaseListByPageResponse);
  //分页查询接口的基线
  rpc QueryInterfaceBaseListByPage(QueryInterfaceBaseListByPageRequest) returns (QueryInterfaceBaseListByPageResponse);
  //插入场景
  rpc CreateScenarioFromJson(CreateScenarioFromJsonRequest) returns (CreateScenarioFromJsonResponse);
  rpc CreateScenarioRecord(QueryScenariosByPageRequest) returns (QueryScenariosByPageResponse);
  //插入接口
  rpc CreateInterface(QueryScenariosByPageRequest) returns (QueryScenariosByPageResponse);
  rpc CreateInterfaceRecord(QueryScenariosByPageRequest) returns (QueryScenariosByPageResponse);
  //插入服务
  rpc CreateService(QueryScenariosByPageRequest) returns (QueryScenariosByPageResponse);
  rpc CreateServiceRecord(QueryScenariosByPageRequest) returns (QueryScenariosByPageResponse);

  //查询单个场景详情
  rpc GetScenarioRecordDetail(GetScenarioRecordDetailRequest) returns (GetScenarioRecordDetailResponse);
  //查询某个场景的压测记录下的详情

  //创建接口基线
  rpc CreateInterfaceBase(QueryScenariosByPageRequest) returns (QueryScenariosByPageResponse);
  //创建服务基线
  rpc CreateServiceBase(QueryScenariosByPageRequest) returns (QueryScenariosByPageResponse);

  //接口基线对比
  rpc CompareInterfaceBase(StressInterfaceWithBaseRequest) returns (StressInterfaceWithBaseResponse);
  //服务基线对比
  rpc CompareServiceBase(StressServiceWithBaseRequest) returns (StressServiceWithBaseResponse);
  //服务趋势
  rpc GetServiceRecord(QueryScenariosByPageRequest) returns (QueryScenariosByPageResponse);
}