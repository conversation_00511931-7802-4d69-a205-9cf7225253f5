package com.kuaishou.kwaishop.qa.risk.center.client.config.mq;

import kotlin.Suppress;

/**
 * <AUTHOR> <<EMAIL>>
 */

@Suppress(names = "EnumEntryName")
public enum KwaishopQaRiskMqTopic {

    // 使用KWAISHOP_QA_RISK前缀
    kwaishop_qa_risk_example_mq, // 示例rocketMQ topic
    kwaishop_qa_risk_txexample_mq, // 示例rocketMQ topic

    // log表topic
    kwaishop_qa_risk_log_mq,
    kwaishop_qa_risk_plan_delay_mq,
    // 精准报告通知mq
    kwaishop_qa_accuracy_task_finish,

    // 异步插库mq
    kwaishop_qa_risk_insert_field_high__mq,
    ;

}
