package com.kuaishou.kwaishop.qa.risk.center.client.config.kafka;

import static com.kuaishou.framework.util.EnumUtils.checkDuplicate;

import kotlin.Suppress;

/**
 * <AUTHOR> <<EMAIL>>
 */

@Suppress(names = "EnumEntryName")
public enum KwaishopQaRiskLogTopic {
    // 使用KWAISHOP_QA_RISK前缀
    kwaishop_qa_risk_example_topic("示例"), //测试
    kdev_info_to_outer("kdev_feature_state"),
    kconf_changed_info_event("kconf_changed_info_event"),

    ;

    static {
        checkDuplicate(values(), KwaishopQaRiskLogTopic::getTopicName);
    }

    private String desc;


    KwaishopQaRiskLogTopic(String desc) {
        this.desc = desc;
    }

    public String getTopicName() {
        return name();
    }

    public String getDesc() {
        return desc;
    }
}
