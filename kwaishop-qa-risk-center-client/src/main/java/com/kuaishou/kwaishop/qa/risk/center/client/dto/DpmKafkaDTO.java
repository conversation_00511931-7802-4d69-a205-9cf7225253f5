package com.kuaishou.kwaishop.qa.risk.center.client.dto;

import java.util.Map;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/13 10:19
 * @注释
 */
public class DpmKafkaDTO {
    private String eventType;
    private String processState;
    private String processInstanceId;
    private String processName;
    private Map<String, Object> variables;
    private String businessId;
    private String processKey;
    private String sysCode;
    private String processDefinitionKey;
    private String formType;
    private String initiatorId;
    private String initiatorName;
    private String initiatorOrg;
    private String initiatorTime;
    private String applyExplain;
    private Long currentTimestamp;
    private String activeProfile;
    private String endTime;
    private Boolean processEnded;
}
