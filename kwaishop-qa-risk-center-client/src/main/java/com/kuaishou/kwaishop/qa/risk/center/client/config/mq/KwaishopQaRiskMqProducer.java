package com.kuaishou.kwaishop.qa.risk.center.client.config.mq;

import kotlin.Suppress;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * RocketMQ生产group示例，也可以放在service工程内
 */

@Suppress(names = "EnumEntryName")
public enum KwaishopQaRiskMqProducer {

    // 使用KWAISHOP_QA_RISK前缀
    p_kwaishop_qa_risk_example_mq, // 示例rocketMQ 生产group
    p_kwaishop_qa_risk_txexample_mq, // 示例rocketMQ 生产group

    // log表生产者组
    p_kwaishop_qa_risk_log_mq,
    p_kwaishop_qa_risk_plan_delay_mq,

    // 异步插库生产者
    p_kwaishop_qa_risk_insert_field_high__mq;

}
