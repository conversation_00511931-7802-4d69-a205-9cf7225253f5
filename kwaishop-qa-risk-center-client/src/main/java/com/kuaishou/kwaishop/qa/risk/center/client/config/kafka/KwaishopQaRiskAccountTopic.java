package com.kuaishou.kwaishop.qa.risk.center.client.config.kafka;

import kotlin.Suppress;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-06
 */
@Suppress(names = "EnumEntryName")
public enum KwaishopQaRiskAccountTopic {

    auto_return_account_topic("vsearch_dump_data_L4_topic", "自动归还"), //自动归还

    dpm_apply_topic("kwaishop_shop_test", "dpm审批流通知");
    private String topic;

    private String desc;

    KwaishopQaRiskAccountTopic(String topic, String desc) {
        this.desc = desc;
        this.topic = topic;
    }

    public String getTopicName() {
        return topic;
    }

    public String getDesc() {
        return desc;
    }
}
