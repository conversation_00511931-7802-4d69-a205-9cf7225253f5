package com.kuaishou.kwaishop.qa.risk.center.client.config.kafka;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-16
 */
public enum KspayKconfChangedStatus {
    REVIEW_START("review_start"), //发起审批
    REVIEW_APPROVED("review_approved"), //审批通过
    REVIEW_REJECTED("review_rejected"), //审批拒绝
    REVIEW_CANCEL("review_cancel"), //审批撤销
    REVIEW_RELEASE("review_release"), //审批后配置发布
    CHANGE_DIRECT("change_direct"), //直接变更
    CHANGE_STEPPED("change_stepped"), //分级发布中
    CHANGE_FINISHED("change_finished"), //分级发布完成
    CHANGE_CANCEL("change_cancel"), //分级发布取消
    ROLLBACK("rollback"), //回滚
    ROLLBACK_REVIEW_START("rollback_review_start"), //回滚发起审批
    ROLLBACK_REVIEW_APPROVED("rollback_review_approved"), //回滚审批通过
    ROLLBACK_REVIEW_REJECTED("rollback_review_rejected"), //回滚审批拒绝
    ROLLBACK_REVIEW_CANCEL("rollback_review_cancel"), //回滚审批取消
    ROLLBACK_REVIEW_RELEASE("rollback_review_release"), //回滚审批后发布
    REMOVE("remove"), //删除环境
    ;

    private String value;

    KspayKconfChangedStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
