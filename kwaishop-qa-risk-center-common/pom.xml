<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>kuaishou</groupId>
        <artifactId>kwaishop-qa-risk-center-parent</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>kwaishop-qa-risk-center-common</artifactId>

    <dependencies>
        <!-- 内部依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-qa-risk-center-client</artifactId>
        </dependency>
        <!-- 电商依赖 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kwaishop-framework-common</artifactId>
        </dependency>
        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- 三方依赖 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.6</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-core</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <version>3.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <version>3.5.1</version>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-datasource</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>groovy</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kspay-common</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kspay-oversea-common</artifactId>
            <version>1.0.89</version>
        </dependency>
    </dependencies>

</project>
