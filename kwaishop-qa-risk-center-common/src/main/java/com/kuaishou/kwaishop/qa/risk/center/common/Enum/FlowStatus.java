package com.kuaishou.kwaishop.qa.risk.center.common.Enum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-13
 */
public enum FlowStatus {

    UNKNOWN(0, "未知"),

    PROCESSING(1, "录制中"),

    FINISHED(2, "录制完成"),

    ERROR(3, "录制异常");

    private int code;

    private String desc;

    FlowStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static FlowStatus getEnumByCode(int code) {
        for (FlowStatus flowStatus : FlowStatus.values()) {
            if (flowStatus.getCode() == code) {
                return flowStatus;
            }
        }
        return UNKNOWN;
    }
}
