package com.kuaishou.kwaishop.qa.risk.center.common.constants;

import javax.annotation.Nonnull;

import com.kuaishou.kspay.common.enums.IntEnum;

public enum FeatureViewStatus implements IntEnum {
    UNKNOWN(0),
    FEATURE_STATUS_DEVELOPING(1), //开发中
    FEATURE_STATUS_COMPLETED(2), //已上线
    DELETED(3), //已删除
    TO_DEVELOP(4), //待开发
    TESTING(5), //测试中
    INTEGRATING(6), //集成中
    TEST_PASS(8), //测试通过
    INTEGRATION_DONE(9), //集成完成
    DEVELOP_DONE(10); //开发完成

    private int value;

    private FeatureViewStatus(int value) {
        this.value = value;
    }

    @Nonnull
    public static FeatureViewStatus of(int value) {
        return (FeatureViewStatus) IntEnum.fromValue(FeatureViewStatus.class, value, UNKNOWN);
    }

    @Nonnull
    public static FeatureViewStatus of(String name) {
        return (FeatureViewStatus) IntEnum.fromName(FeatureViewStatus.class, name, UNKNOWN);
    }

    public int getValue() {
        return this.value;
    }
}
