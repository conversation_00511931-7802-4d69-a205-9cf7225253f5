package com.kuaishou.kwaishop.qa.risk.center.common.constants;

import javax.annotation.Nonnull;

import com.kuaishou.kspay.common.enums.IntEnum;

public enum FeatureViewStatusEnum implements IntEnum {
    UNKNOWN(0),
    FEATURE_STATUS_DEVELOPING(1), //开发中
    FEATURE_STATUS_COMPLETED(2), //已上线
    FEATURE_STATUS_DELETED(3), //已删除
    FEATURE_STATUS_TO_DEVELOP(4), //待开发
    FEATURE_STATUS_TESTING(5), //测试中
    FEATURE_STATUS_INTEGRATING(6), //集成中
    FEATURE_STATUS_TEST_PASS(8), //测试通过
    FEATURE_STATUS_INTEGRATION_DONE(9), //集成完成
    FEATURE_STATUS_DEVELOP_DONE(10); //开发完成

    private int value;

    private FeatureViewStatusEnum(int value) {
        this.value = value;
    }

    @Nonnull
    public static FeatureViewStatusEnum of(int value) {
        return (FeatureViewStatusEnum) IntEnum.fromValue(FeatureViewStatusEnum.class, value, UNKNOWN);
    }

    @Nonnull
    public static FeatureViewStatusEnum of(String name) {
        return (FeatureViewStatusEnum) IntEnum.fromName(FeatureViewStatusEnum.class, name, UNKNOWN);
    }

    public int getValue() {
        return this.value;
    }
}
