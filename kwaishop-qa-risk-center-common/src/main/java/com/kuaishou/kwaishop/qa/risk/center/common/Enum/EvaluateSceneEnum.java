package com.kuaishou.kwaishop.qa.risk.center.common.Enum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-17
 */
public enum EvaluateSceneEnum {
    UNKNOWN(0, "未知"),
    PLAN(1, "规划agent"),
    LOGISTIC(2, "物流agent");

    private final Integer code;
    private final String name;

    EvaluateSceneEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
