package com.kuaishou.kwaishop.qa.risk.center.common.Enum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-17
 */
public enum ReplayTypeEnum {

    RECORD_RESULT(1, "流量录制结果"),

    LAND_ID(2, "根据泳道选择"),

    GROUP(3, "根据服务分组选择"),

    IP_PORT(4, "自定义机器");

    private int code;

    private String desc;

    ReplayTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
