package com.kuaishou.kwaishop.qa.risk.center.common.exception;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-15
 */
public class BizException extends RuntimeException {

    private int code;

    private String message;

    private boolean logErrorMsg;

    public BizException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
        this.logErrorMsg = true;
    }

    public BizException(ErrorCode serverCode) {
        super(serverCode.getMessage());
        this.code = serverCode.getCode();
        this.message = serverCode.getMessage();
        this.logErrorMsg = serverCode.isLogErrorMsg();
    }

    public BizException(ErrorCode serverCode, boolean logErrorMsg) {
        super(serverCode.getMessage());
        this.code = serverCode.getCode();
        this.message = serverCode.getMessage();
        this.logErrorMsg = logErrorMsg;
    }

    public BizException(ErrorCode serverCode, String message) {
        super(message);
        this.code = serverCode.getCode();
        this.message = message;
        this.logErrorMsg = serverCode.isLogErrorMsg();
    }

    public BizException(ErrorCode serverCode, String message, boolean logErrorMsg) {
        super(message);
        this.code = serverCode.getCode();
        this.message = message;
        this.logErrorMsg = logErrorMsg;
    }

    public static BizException ofMessage(ErrorCode serverCode, String overrideMessage) {
        return new BizException(serverCode, overrideMessage);
    }

    public static BizException ofMessage(ErrorCode serverCode, String overrideMessage, boolean logErrorMsg) {
        return new BizException(serverCode, overrideMessage, logErrorMsg);
    }

    public static BizException of(ErrorCode serverCode) {
        return new BizException(serverCode);
    }

    public static BizException of(ErrorCode serverCode, boolean logErrorMsg) {
        return new BizException(serverCode, logErrorMsg);
    }

    public static void throwBizException(ErrorCode serverCode) {
        throw new BizException(serverCode);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isLogErrorMsg() {
        return logErrorMsg;
    }

    public void setLogErrorMsg(boolean logErrorMsg) {
        this.logErrorMsg = logErrorMsg;
    }
}

