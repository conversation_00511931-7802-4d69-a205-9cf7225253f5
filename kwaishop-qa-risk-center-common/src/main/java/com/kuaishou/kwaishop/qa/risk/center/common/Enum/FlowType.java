package com.kuaishou.kwaishop.qa.risk.center.common.Enum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-13
 */
public enum FlowType {

    UNKNOWN(0, "未知"),

    REAL_TIME(1, "实时流量"),

    STRATEGY_LOG(2, "离线执行日志流量"),

    CUSTOM_HIVE(3, "自定义hive流量");

    private int code;

    private String desc;

    FlowType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static FlowType getEnumByCode(int code) {
        for (FlowType flowType : FlowType.values()) {
            if (flowType.getCode() == code) {
                return flowType;
            }
        }
        return UNKNOWN;
    }
}
