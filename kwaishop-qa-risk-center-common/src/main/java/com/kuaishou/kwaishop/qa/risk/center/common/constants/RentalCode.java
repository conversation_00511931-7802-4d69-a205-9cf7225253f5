package com.kuaishou.kwaishop.qa.risk.center.common.constants;

public interface RentalCode {

    int getCode();

    String getMessage();

    public static enum BasicRentalCode implements RentalCode {
        UNKNOWN(0, "未知"),
        SUCCESSFUL_BORROW(1, "租借成功"),
        SUCCESSFUL_RETURN(1, "归还成功"),
        SUCCESSFUL_QUERY(1, "查询成功"),
        ITEM_NOT_AVAILABLE(10, "物品不可用"),
        ITEM_NOT_FOUND(11, "物品不存在"),
        ITEM_ALREADY_BORROWED(12, "物品已被借出"),
        ITEM_ALREADY_RETURNED(13, "物品已被归还"),
        USER_NOT_FOUND(20, "用户不存在"),
        USER_NOT_AUTHORIZED(21, "用户未经授权"),
        SUCCESSFUL_EXTEND(1, "延长成功");

        private final int code;
        private final String message;
        private final boolean logErrorMsg;

        private BasicRentalCode(int code, String message, boolean logErrorMsg) {
            this.code = code;
            this.message = message;
            this.logErrorMsg = logErrorMsg;
        }

        private BasicRentalCode(int code, String message) {
            this.code = code;
            this.message = message;
            this.logErrorMsg = true;
        }

        public int getCode() {
            return this.code;
        }

        public String getMessage() {
            return this.message;
        }
    }
}