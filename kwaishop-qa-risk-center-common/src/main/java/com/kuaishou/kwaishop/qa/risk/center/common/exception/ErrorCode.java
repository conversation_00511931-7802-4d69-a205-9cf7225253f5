package com.kuaishou.kwaishop.qa.risk.center.common.exception;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-15
 */
public interface ErrorCode {

    int getCode();

    String getMessage();

    boolean isLogErrorMsg();

    public static enum BasicErrorCode implements ErrorCode {
        UNKNOWN(0, "未知"),
        SUCCESS(1, "成功"),
        TOO_MANY_REQ(8, "请求太多"),
        SERVER_ERROR(11, "服务端错误"),
        PARAM_INVALID(21, "参数不合法"),
        INSERT_FAIL(31, "创建记录失败"),
        UPDATE_FAIL(32, "更新记录失败"),
        QUERY_TOO_FAST(33, "您的操作太频繁"),
        INTERFACE_DEPRECATED(40, "访问接口已废弃");


        private final int code;
        private final String message;
        private final boolean logErrorMsg;

        private BasicErrorCode(int code, String message, boolean logErrorMsg) {
            this.code = code;
            this.message = message;
            this.logErrorMsg = logErrorMsg;
        }

        private BasicErrorCode(int code, String message) {
            this.code = code;
            this.message = message;
            this.logErrorMsg = true;
        }

        public int getCode() {
            return this.code;
        }

        public String getMessage() {
            return this.message;
        }

        public boolean isLogErrorMsg() {
            return this.logErrorMsg;
        }
    }
}