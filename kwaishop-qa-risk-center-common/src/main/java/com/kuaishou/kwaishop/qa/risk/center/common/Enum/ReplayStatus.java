package com.kuaishou.kwaishop.qa.risk.center.common.Enum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-13
 */
public enum ReplayStatus {

    PROCESSING(1, "回放中"),

    FINISHED(2, "回放完成");

    private int code;

    private String desc;

    ReplayStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ReplayStatus getEnumById(int id) {
        for (ReplayStatus replayStatus : ReplayStatus.values()) {
            if (replayStatus.getCode() == id) {
                return replayStatus;
            }
        }
        return null;
    }

}
