package com.kuaishou.kwaishop.qa.risk.center.common.constants;

public enum KatTypeEnum {
    UNKNOWN(0, "未知"),
    KAT_TESTING(1, "提测触发"),
    KAT_INTEGRATING(2, "集成中触发");
    private final Integer code;
    private final String name;

    KatTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    public Integer getCode() {
        return code;
    }
    public String getName() {
        return name;
    }
    public static KatTypeEnum fromFeatureEvent(FeatureViewStatusEnum featureEvent) {
        switch (featureEvent) {
            case FEATURE_STATUS_TESTING:
                return KatTypeEnum.KAT_TESTING;
            case FEATURE_STATUS_INTEGRATING:
                return KatTypeEnum.KAT_INTEGRATING;
            default:
                return KatTypeEnum.UNKNOWN;
        }
    }
}
