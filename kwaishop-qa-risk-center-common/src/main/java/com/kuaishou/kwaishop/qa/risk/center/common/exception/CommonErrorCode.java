package com.kuaishou.kwaishop.qa.risk.center.common.exception;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-15
 */
public enum CommonErrorCode implements ErrorCode {

    DATA_QUERY_ERROR(2621009, "数据查询异常")
    ;

    private final int code;
    private final String message;
    private final boolean logErrorMsg;

    CommonErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
        this.logErrorMsg = true;
    }

    CommonErrorCode(int code, String message, boolean logErrorMsg) {
        this.code = code;
        this.message = message;
        this.logErrorMsg = logErrorMsg;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    @Override
    public boolean isLogErrorMsg() {
        return this.logErrorMsg;
    }
}
