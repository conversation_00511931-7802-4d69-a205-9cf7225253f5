package com.kuaishou.kwaishop.qa.risk.center.common.exception;

public enum ErrorCodeError implements ErrorCode {


    NOT_PERMIT(2621110, "无权限"),
    DATA_NOT_EXSIT(2621111, "数据不存在"),
    ERROR_CODE_NOT_RIGHT(2621112, "错误码数据格式不对"),

    CAN_NOT_FIND(2621113, "找不到错误码信息"),

    ;


    private final int code;
    private final String message;
    private final boolean logErrorMsg;

    ErrorCodeError(int code, String message) {
        this.code = code;
        this.message = message;
        this.logErrorMsg = true;
    }


    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    @Override
    public boolean isLogErrorMsg() {
        return this.logErrorMsg;
    }
}
