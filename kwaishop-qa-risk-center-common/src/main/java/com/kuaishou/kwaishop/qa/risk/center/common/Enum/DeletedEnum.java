package com.kuaishou.kwaishop.qa.risk.center.common.Enum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-17
 */
public enum DeletedEnum {
    VALID(0, "未删除"),
    UN_VALID(1, "已删除"),
    ;

    private final Integer code;
    private final String name;

    DeletedEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
