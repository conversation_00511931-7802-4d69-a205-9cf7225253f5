[INFO] Scanning for projects...
[WARNING] 
[WARNING] Some problems were encountered while building the effective model for kuaishou:kwaishop-qa-risk-center-starter:jar:1.0.0-SNAPSHOT
[WARNING] 'dependencyManagement.dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: kuaishou:kwaishop-qa-risk-center-common:jar -> duplicate declaration of version ${revision} @ kuaishou:kwaishop-qa-risk-center-parent:${revision}, /Users/<USER>/Desktop/code/kuaishou-code/kwaishop-qa-risk-center/pom.xml, line 103, column 25
[WARNING] 
[WARNING] Some problems were encountered while building the effective model for kuaishou:kwaishop-qa-risk-center:jar:1.0.0-SNAPSHOT
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: kuaishou:kuaishou-intown-sdk:jar -> version 1.0.45 vs 1.0.54 @ kuaishou:kwaishop-qa-risk-center:${revision}, /Users/<USER>/Desktop/code/kuaishou-code/kwaishop-qa-risk-center/kwaishop-qa-risk-center/pom.xml, line 94, column 21
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: org.projectlombok:lombok:jar -> version 1.18.20 vs (?) @ kuaishou:kwaishop-qa-risk-center:${revision}, /Users/<USER>/Desktop/code/kuaishou-code/kwaishop-qa-risk-center/kwaishop-qa-risk-center/pom.xml, line 206, column 21
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: kuaishou:kwaishop-access-control-rich-client:jar -> duplicate declaration of version 1.0.68 @ kuaishou:kwaishop-qa-risk-center:${revision}, /Users/<USER>/Desktop/code/kuaishou-code/kwaishop-qa-risk-center/kwaishop-qa-risk-center/pom.xml, line 301, column 21
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: org.projectlombok:lombok:jar -> version 1.18.20 vs (?) @ kuaishou:kwaishop-qa-risk-center:${revision}, /Users/<USER>/Desktop/code/kuaishou-code/kwaishop-qa-risk-center/kwaishop-qa-risk-center/pom.xml, line 325, column 21
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: kuaishou:kuaishou-dp-auth-dsc-client:jar -> duplicate declaration of version (?) @ kuaishou:kwaishop-qa-risk-center:${revision}, /Users/<USER>/Desktop/code/kuaishou-code/kwaishop-qa-risk-center/kwaishop-qa-risk-center/pom.xml, line 344, column 21
[WARNING] 
[WARNING] Some problems were encountered while building the effective model for kuaishou:kwaishop-qa-risk-center-parent:pom:1.0.0-SNAPSHOT
[WARNING] 'dependencyManagement.dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: kuaishou:kwaishop-qa-risk-center-common:jar -> duplicate declaration of version ${revision} @ kuaishou:kwaishop-qa-risk-center-parent:${revision}, /Users/<USER>/Desktop/code/kuaishou-code/kwaishop-qa-risk-center/pom.xml, line 103, column 25
[WARNING] 
[WARNING] It is highly recommended to fix these problems because they threaten the stability of your build.
[WARNING] 
[WARNING] For this reason, future Maven versions might no longer support building such malformed projects.
[WARNING] 
[INFO] ------------------------------------------------------------------------
[INFO] Detecting the operating system and CPU architecture
[INFO] ------------------------------------------------------------------------
[INFO] os.detected.name: osx
[INFO] os.detected.arch: x86_64
[INFO] os.detected.version: 13.0
[INFO] os.detected.version.major: 13
[INFO] os.detected.version.minor: 0
[INFO] os.detected.classifier: osx-x86_64
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Build Order:
[INFO] 
[INFO] kwaishop-qa-risk-center-parent                                     [pom]
[INFO] kwaishop-qa-risk-center-client                                     [jar]
[INFO] kwaishop-qa-risk-center-common                                     [jar]
[INFO] kwaishop-qa-risk-center                                            [jar]
[INFO] kwaishop-qa-risk-center-starter                                    [jar]
[INFO] 
[INFO] --------------< kuaishou:kwaishop-qa-risk-center-parent >---------------
[INFO] Building kwaishop-qa-risk-center-parent 1.0.0-SNAPSHOT             [1/5]
[INFO]   from pom.xml
[INFO] --------------------------------[ pom ]---------------------------------
[INFO] 
[INFO] --- dependency:3.1.1:tree (default-cli) @ kwaishop-qa-risk-center-parent ---
[INFO] publish stats success. url: https://server-devops-api.corp.kuaishou.com/api/mavenStatsReport, cost: 432ms
[INFO] kuaishou:kwaishop-qa-risk-center-parent:pom:1.0.0-SNAPSHOT
[INFO] +- org.springframework:spring-context:jar:5.1.10-kwai-12:compile
[INFO] |  +- org.springframework:spring-aop:jar:5.1.10-kwai-12:compile
[INFO] |  +- org.springframework:spring-beans:jar:5.1.10-kwai-12:compile
[INFO] |  +- org.springframework:spring-core:jar:5.1.10-kwai-12:compile
[INFO] |  \- org.springframework:spring-expression:jar:5.1.10-kwai-12:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.30:provided
[INFO] +- org.mybatis:mybatis:jar:3.5.7:compile
[INFO] +- com.kuaishou.infra.boot:ks-boot-***********************
[INFO] |  +- org.springframework:spring-*******************************
[INFO] |  |  \- org.springframework:spring-tx:jar:5.1.10-kwai-12:compile
[INFO] |  +- org.slf4j:slf4j-api:jar:1.7.30-kwai-2:compile
[INFO] |  +- com.google.code.findbugs:jsr305:jar:3.0.2:compile
[INFO] |  \- org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:1.3.72:compile
[INFO] |     +- org.jetbrains.kotlin:kotlin-stdlib:jar:1.3.72:compile
[INFO] |     |  +- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.3.72:compile
[INFO] |     |  \- org.jetbrains:annotations:jar:13.0:compile
[INFO] |     \- org.jetbrains.kotlin:kotlin-stdlib-jdk7:jar:1.3.72:compile
[INFO] +- com.baomidou:mybatis-plus-annotation:jar:3.4.1:compile
[INFO] +- com.baomidou:mybatis-plus-core:jar:3.4.1:compile
[INFO] |  \- com.github.jsqlparser:jsqlparser:jar:3.2:compile
[INFO] +- org.apache.commons:commons-collections4:jar:4.3:compile
[INFO] +- kuaishou:krpc-all:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-registry-kess:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-registry-sdk:jar:1.0.198:compile
[INFO] |  |  +- com.kuaishou:kess-scheduler-client:jar:0.0.125:compile
[INFO] |  |  |  \- com.kuaishou:kess-config-client:jar:0.0.125:compile
[INFO] |  |  |     \- com.github.luben:zstd-jni:jar:1.3.8-6:compile
[INFO] |  |  +- kuaishou:infra-kws-sdk:jar:1.0.53:compile
[INFO] |  |  |  \- com.google.guava:guava:jar:28.1-jre-kwai5:compile
[INFO] |  |  |     +- com.google.guava:failureaccess:jar:1.0.1:compile
[INFO] |  |  |     +- com.google.guava:listenablefuture:jar:9999.0-empty-to-avoid-conflict-with-guava:compile
[INFO] |  |  |     +- org.checkerframework:checker-qual:jar:2.8.1:compile
[INFO] |  |  |     \- com.google.j2objc:j2objc-annotations:jar:1.3:compile
[INFO] |  |  \- kuaishou:infra-framework-platform:jar:1.0.589:compile
[INFO] |  |     +- kuaishou:kuaishou-env-utils:jar:1.0.12:compile
[INFO] |  |     +- kuaishou:ktrace-sdk:jar:1.0.253:compile
[INFO] |  |     |  \- org.springframework:spring-aspects:jar:5.1.10-kwai-12:compile
[INFO] |  |     |     \- org.aspectj:aspectjweaver:jar:1.8.13:compile
[INFO] |  |     +- kuaishou:infra-framework-common:jar:1.0.589:compile
[INFO] |  |     |  +- com.github.phantomthief:more-lambdas-jdk9:jar:0.1.55:compile
[INFO] |  |     |  +- com.carrotsearch:hppc:jar:0.7.3:compile
[INFO] |  |     |  +- com.fasterxml.jackson.core:jackson-core:jar:2.9.10:compile
[INFO] |  |     |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.9.10:compile
[INFO] |  |     |  +- com.fasterxml.jackson.module:jackson-module-afterburner:jar:2.9.10:compile
[INFO] |  |     |  +- kuaishou:infra-logger:jar:1.0.37:compile
[INFO] |  |     |  |  \- ch.qos.logback:logback-classic-kwai:jar:1.2.10-1:compile
[INFO] |  |     |  |     \- ch.qos.logback:logback-core:jar:1.2.10:compile
[INFO] |  |     |  +- com.github.phantomthief:retrieve-id-utils:jar:1.0.10:compile
[INFO] |  |     |  \- kuaishou:infra-java-kit:jar:1.0.2:compile
[INFO] |  |     +- kuaishou:kuaishou-env-util:jar:1.0.277:compile
[INFO] |  |     +- com.github.phantomthief:scope:jar:1.0.20:compile
[INFO] |  |     +- kuaishou:kuaishou-biz-def:jar:1.0.1801:compile
[INFO] |  |     +- com.github.phantomthief:zkconfig-resources:jar:1.1.29:compile
[INFO] |  |     |  +- org.apache.zookeeper:zookeeper:jar:3.5.7:compile
[INFO] |  |     |  |  +- org.apache.zookeeper:zookeeper-jute:jar:3.5.7:compile
[INFO] |  |     |  |  +- org.apache.yetus:audience-annotations:jar:0.5.0:compile
[INFO] |  |     |  |  +- io.netty:netty-handler:jar:4.1.38.Final:compile
[INFO] |  |     |  |  |  +- io.netty:netty-common:jar:4.1.38.Final:compile
[INFO] |  |     |  |  |  +- io.netty:netty-buffer:jar:4.1.38.Final:compile
[INFO] |  |     |  |  |  +- io.netty:netty-transport:jar:4.1.38.Final:compile
[INFO] |  |     |  |  |  |  \- io.netty:netty-resolver:jar:4.1.38.Final:compile
[INFO] |  |     |  |  |  \- io.netty:netty-codec:jar:4.1.38.Final:compile
[INFO] |  |     |  |  \- io.netty:netty-transport-native-epoll:jar:4.1.38.Final:compile
[INFO] |  |     |  |     \- io.netty:netty-transport-native-unix-common:jar:4.1.38.Final:compile
[INFO] |  |     |  \- org.apache.curator:curator-recipes:jar:*******:compile
[INFO] |  |     |     \- org.apache.curator:curator-framework:jar:*******:compile
[INFO] |  |     +- org.apache.curator:curator-client:jar:*******:compile
[INFO] |  |     +- kuaishou:ktrace-core:jar:1.0.253:compile
[INFO] |  |     |  +- kuaishou:ktrace-config:jar:1.0.253:compile
[INFO] |  |     |  +- com.kwai.jdk:snowman-api:jar:1.0.31:compile
[INFO] |  |     |  \- kuaishou:infra-metrics-core:jar:2.0.53:compile
[INFO] |  |     +- kuaishou:infra-kws-tool:jar:1.0.53:compile
[INFO] |  |     +- kuaishou:kuaishou-product-sdk:jar:1.0.13:compile
[INFO] |  |     |  \- kuaishou:kuaishou-base-proto:jar:1.0.110:compile
[INFO] |  |     +- kuaishou:kuaishou-zkclient:jar:1.0.55:compile
[INFO] |  |     +- kuaishou:zt-base:jar:1.0.141:compile
[INFO] |  |     |  \- kuaishou:kuaishou-common-i18n:jar:1.1.6:compile
[INFO] |  |     +- org.apache.commons:commons-text:jar:1.6:compile
[INFO] |  |     +- com.kuaishou:before-jvm-term-trigger-sdk:jar:1.0.69:compile
[INFO] |  |     |  \- com.squareup.okhttp3:okhttp:jar:3.12.3:compile
[INFO] |  |     |     \- com.squareup.okio:okio:jar:1.17.4:compile
[INFO] |  |     \- org.jetbrains.kotlin:kotlin-reflect:jar:1.3.72:compile
[INFO] |  +- kuaishou:krpc-governance-sdk:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-transport-sdk:jar:1.0.198:compile
[INFO] |  |  |  \- kuaishou:krpc-serialization-sdk:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-rpc-sdk:jar:1.0.198:compile
[INFO] |  |  |  \- org.javassist:javassist:jar:3.25.0-GA:compile
[INFO] |  |  \- kuaishou:krpc-metric-sdk:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-governance-sentinel:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-cluster:jar:1.0.198:compile
[INFO] |  |  |  +- net.javacrumbs.future-converter:future-converter-java8-guava:jar:1.2.0:compile
[INFO] |  |  |  |  +- net.javacrumbs.future-converter:future-converter-common:jar:1.2.0:compile
[INFO] |  |  |  |  +- net.javacrumbs.future-converter:future-converter-java8-common:jar:1.2.0:compile
[INFO] |  |  |  |  \- net.javacrumbs.future-converter:future-converter-guava-common:jar:1.2.0:compile
[INFO] |  |  |  +- io.projectreactor.addons:reactor-extra:jar:3.2.3.RELEASE:compile
[INFO] |  |  |  \- com.github.phantomthief:simple-failover:jar:0.1.32:compile
[INFO] |  |  +- com.kuaishou:kess-governor-client:jar:0.0.125:compile
[INFO] |  |  |  +- kuaishou:infra-patronum-governance-sdk:jar:1.0.36:compile
[INFO] |  |  |  |  \- kuaishou:infra-config-adaptor-facade:jar:1.0.53:compile
[INFO] |  |  |  \- com.kuaishou:kess-mandator-client:jar:0.0.125:compile
[INFO] |  |  |     \- net.goldolphin:maria:jar:0.0.7:compile
[INFO] |  |  |        +- io.netty:netty-all:jar:4.1.38.Final:compile
[INFO] |  |  |        \- org.freemarker:freemarker:jar:2.3.28:compile
[INFO] |  |  +- kuaishou:infra-sentinel-circuitbreaker:jar:1.0.94:compile
[INFO] |  |  |  +- io.github.resilience4j:resilience4j-circuitbreaker:jar:1.2.0:compile
[INFO] |  |  |  |  \- io.vavr:vavr:jar:0.10.0:compile
[INFO] |  |  |  |     \- io.vavr:vavr-match:jar:0.10.0:compile
[INFO] |  |  |  +- io.github.resilience4j:resilience4j-core:jar:1.2.0:compile
[INFO] |  |  |  +- io.projectreactor:reactor-core:jar:3.2.11.RELEASE:compile
[INFO] |  |  |  |  \- org.reactivestreams:reactive-streams:jar:1.0.2:compile
[INFO] |  |  |  +- io.github.resilience4j:resilience4j-reactor:jar:1.2.0:compile
[INFO] |  |  |  \- kuaishou:infra-sentinel-core:jar:1.0.94:compile
[INFO] |  |  |     \- kuaishou:infra-sentinel-config:jar:1.0.94:compile
[INFO] |  |  +- kuaishou:infra-sentinel-throttling:jar:1.0.94:compile
[INFO] |  |  |  \- net.spy:spymemcached:jar:2.12.3.28-kwai:compile
[INFO] |  |  +- kuaishou:infra-sentinel-system-throttling:jar:1.0.94:compile
[INFO] |  |  +- com.github.vladimir-bukhtoyarov:bucket4j-core:jar:6.0.1:compile
[INFO] |  |  \- kuaishou:infra-patronum-stresstest-sdk:jar:1.0.21:compile
[INFO] |  +- kuaishou:krpc-configcenter-kess:jar:1.0.198:compile
[INFO] |  |  \- kuaishou:krpc-common:jar:1.0.198:compile
[INFO] |  |     \- kuaishou:infra-patronum-healthcheck-sdk:jar:1.0.8:compile
[INFO] |  |        +- kuaishou:infra-patronum-healthcheck-events:jar:1.0.8:compile
[INFO] |  |        |  \- kuaishou:infra-patronum-healthcheck-metrics:jar:1.0.8:compile
[INFO] |  |        +- kuaishou:infra-patronum-healthcheck-analyser:jar:1.0.8:compile
[INFO] |  |        |  \- kuaishou:infra-patronum-healthcheck-common:jar:1.0.8:compile
[INFO] |  |        +- org.eclipse.jetty:jetty-http:jar:9.4.8.v20171121:compile
[INFO] |  |        |  \- org.eclipse.jetty:jetty-io:jar:9.4.8.v20171121:compile
[INFO] |  |        +- org.eclipse.jetty:jetty-util:jar:9.4.8.v20171121:compile
[INFO] |  |        \- org.eclipse.jetty:jetty-continuation:jar:9.4.8.v20171121:compile
[INFO] |  +- kuaishou:krpc-config-spring:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-config-sdk:jar:1.0.198:compile
[INFO] |  |  +- com.alibaba.spring:spring-context-support:jar:1.0.10:compile
[INFO] |  |  \- io.grpc:grpc-api:jar:1.36.1-kwai-1.1:compile
[INFO] |  |     +- io.grpc:grpc-context:jar:1.36.1-kwai-1.1:compile
[INFO] |  |     +- com.google.errorprone:error_prone_annotations:jar:2.3.3:compile
[INFO] |  |     \- org.codehaus.mojo:animal-sniffer-annotations:jar:1.18:compile
[INFO] |  +- kuaishou:krpc-bootstrap-grpc:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-bootstrap-sdk:jar:1.0.198:compile
[INFO] |  |  |  \- kuaishou:jwarmup-sdk:jar:1.0.19:compile
[INFO] |  |  |     \- kuaishou:jwarmup-common:jar:1.0.19:compile
[INFO] |  |  |        \- com.amazonaws:aws-java-sdk-s3:jar:1.11.891:compile
[INFO] |  |  |           +- com.amazonaws:aws-java-sdk-kms:jar:1.11.891:compile
[INFO] |  |  |           +- com.amazonaws:aws-java-sdk-core:jar:1.11.891:compile
[INFO] |  |  |           |  +- software.amazon.ion:ion-java:jar:1.0.2:compile
[INFO] |  |  |           |  \- com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:jar:2.9.10:compile
[INFO] |  |  |           \- com.amazonaws:jmespath-java:jar:1.11.891:compile
[INFO] |  |  +- kuaishou:infra-warmup-facade:jar:1.0.25:compile
[INFO] |  |  \- com.kuaishou:service-lifecycle-sdk:jar:1.0.25:compile
[INFO] |  |     +- commons-httpclient:commons-httpclient:jar:3.1:compile
[INFO] |  |     +- com.fasterxml.jackson.core:jackson-databind:jar:********-kwai:compile
[INFO] |  |     \- org.lz4:lz4-java:jar:1.7.1:compile
[INFO] |  +- kuaishou:krpc-bootstrap-local:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-rpc-grpc:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:ktrace-grpc-instrument-sdk:jar:1.0.253:compile
[INFO] |  |  |  \- io.grpc:grpc-core:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |     +- com.google.android:annotations:jar:4.1.1.4:runtime
[INFO] |  |  |     \- io.perfmark:perfmark-api:jar:0.19.0:runtime
[INFO] |  |  +- io.grpc:grpc-stub:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  +- io.grpc:grpc-services:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  \- com.google.protobuf:protobuf-java-util:jar:3.16.1:compile
[INFO] |  |  +- io.grpc:grpc-netty-shaded:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  +- kuaishou:infra-framework-service-context:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-framework-trace:jar:1.0.589:compile
[INFO] |  |  |  +- kuaishou:ktrace-transport:jar:1.0.253:compile
[INFO] |  |  |  \- kuaishou:infra-framework-lane:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-patronum-traffic-record-spi-sdk:jar:1.0.88:compile
[INFO] |  |  |  \- kuaishou:infra-patronum-traffic-management-metadata:jar:1.0.88:compile
[INFO] |  |  \- kuaishou:infra-patronum-stresstest-trafficscheduler:jar:1.0.21:compile
[INFO] |  +- kuaishou:krpc-rpc-local:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-metric-perf:jar:1.0.198:compile
[INFO] |  |  \- kuaishou:infra-framework-perf:jar:2.0.53:compile
[INFO] |  |     +- kuaishou:infra-patronum-governance-spi-sdk:jar:1.0.36:compile
[INFO] |  |     +- kuaishou:infra-config-adaptor-core:jar:1.0.53:compile
[INFO] |  |     +- com.ecyrd.speed4j:speed4j:jar:0.18:compile
[INFO] |  |     \- com.github.phantomthief:buffer-trigger:jar:0.2.21:compile
[INFO] |  +- kuaishou:krpc-metric-rpcmonitor:jar:1.0.198:compile
[INFO] |  |  \- kuaishou:rpcmonitor-java-client:jar:1.1.29:compile
[INFO] |  |     +- commons-io:commons-io:jar:2.6:compile
[INFO] |  |     +- com.google.code.gson:gson:jar:2.8.9:compile
[INFO] |  |     +- kuaishou:infra-common-falcon-counter:jar:2.0.41:compile
[INFO] |  |     |  +- com.yammer.metrics:metrics-core:jar:2.2.0:compile
[INFO] |  |     |  \- org.slf4j:jcl-over-slf4j:jar:1.7.30:compile
[INFO] |  |     \- com.kuaishou:division-util:jar:0.0.125:compile
[INFO] |  +- kuaishou:krpc-metric-ktrace:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-metric-sentinel:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:infra-sentinel-metrics:jar:1.0.94:compile
[INFO] |  |  |  +- com.lmax:disruptor:jar:3.3.7:compile
[INFO] |  |  |  +- joda-time:joda-time:jar:2.9.9:compile
[INFO] |  |  |  \- kuaishou:infra-framework-warmup:jar:1.0.589:compile
[INFO] |  |  \- com.alibaba.csp:sentinel-core:jar:1.6.3:compile
[INFO] |  +- kuaishou:krpc-metadata:jar:1.0.198:compile
[INFO] |  +- kuaishou:infra-krpc-metadata:jar:1.0.152:compile
[INFO] |  |  +- com.google.protobuf:protobuf-java:jar:3.16.1:compile
[INFO] |  |  +- io.grpc:grpc-protobuf:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  +- com.google.api.grpc:proto-google-common-protos:jar:2.0.1:compile
[INFO] |  |  |  \- io.grpc:grpc-protobuf-lite:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  \- com.github.phantomthief:more-lambdas:jar:0.1.55:compile
[INFO] |  \- kuaishou:kuaishou-kconf-client:jar:1.0.144:compile
[INFO] |     \- kuaishou:kuaishou-kconf-common:jar:1.0.144:compile
[INFO] |        +- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.9.10:compile
[INFO] |        +- com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.9.10:compile
[INFO] |        +- com.hubspot.jackson:jackson-datatype-protobuf:jar:0.9.10-jackson2.9-proto3:compile
[INFO] |        |  \- com.google.code.findbugs:annotations:jar:3.0.1u2:compile
[INFO] |        \- com.fasterxml.jackson.module:jackson-module-kotlin:jar:2.9.10:compile
[INFO] +- com.baomidou:mybatis-plus-extension:jar:3.4.1:compile
[INFO] |  \- org.mybatis:mybatis-spring:jar:2.0.6:compile
[INFO] +- kuaishou:kuaishou-build-tools:jar:1.0.1352:test
[INFO] +- junit:junit:jar:4.12:test
[INFO] |  \- org.hamcrest:hamcrest-core:jar:1.3:test
[INFO] +- org.junit.vintage:junit-vintage-engine:jar:5.5.2:test
[INFO] |  +- org.apiguardian:apiguardian-api:jar:1.1.0:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.5.2:test
[INFO] +- org.hamcrest:hamcrest-library:jar:1.3:test
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.5.2:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  \- org.junit.platform:junit-platform-commons:jar:1.5.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.5.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-params:jar:5.5.2:test
[INFO] +- org.junit.platform:junit-platform-launcher:jar:1.5.2:test
[INFO] +- org.junit.platform:junit-platform-runner:jar:1.5.2:test
[INFO] |  \- org.junit.platform:junit-platform-suite-api:jar:1.5.2:test
[INFO] +- org.mockito:mockito-core:jar:2.23.4:test
[INFO] |  +- net.bytebuddy:byte-buddy:jar:1.12.10:compile
[INFO] |  +- net.bytebuddy:byte-buddy-agent:jar:1.12.10:compile
[INFO] |  \- org.objenesis:objenesis:jar:3.0.1:test
[INFO] +- org.mockito:mockito-junit-jupiter:jar:2.23.4:test
[INFO] +- org.mockito:mockito-inline:jar:2.23.4:test
[INFO] +- org.powermock:powermock-api-mockito2:jar:2.0.0:test
[INFO] |  \- org.powermock:powermock-api-support:jar:2.0.0:test
[INFO] |     +- org.powermock:powermock-reflect:jar:2.0.0:test
[INFO] |     \- org.powermock:powermock-core:jar:2.0.0:test
[INFO] +- org.powermock:powermock-module-junit4:jar:2.0.0:test
[INFO] |  \- org.powermock:powermock-module-junit4-common:jar:2.0.0:test
[INFO] +- com.h2database:h2:jar:1.4.197:test
[INFO] +- com.fiftyonred:mock-jedis:jar:0.4.0:test
[INFO] +- org.testng:testng:jar:6.14.3:test
[INFO] |  +- com.beust:jcommander:jar:1.72:test
[INFO] |  \- org.apache-extras.beanshell:bsh:jar:2.0b6:test
[INFO] +- org.apache.curator:curator-test:jar:*******:test
[INFO] +- com.github.kstyrc:embedded-redis:jar:0.6:test
[INFO] +- org.openjdk.jmh:jmh-core:jar:1.23:test
[INFO] |  +- net.sf.jopt-simple:jopt-simple:jar:4.6:test
[INFO] |  \- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] +- org.openjdk.jmh:jmh-generator-annprocess:jar:1.23:test
[INFO] +- org.springframework:spring-test:jar:5.1.10-kwai-12:test
[INFO] +- com.jayway.restassured:rest-assured:jar:2.9.0:test
[INFO] |  +- org.codehaus.groovy:groovy:jar:2.4.4:test
[INFO] |  +- org.codehaus.groovy:groovy-xml:jar:2.4.4:test
[INFO] |  +- org.apache.httpcomponents:httpclient:jar:4.5.7:compile
[INFO] |  |  +- org.apache.httpcomponents:httpcore:jar:4.4.11:compile
[INFO] |  |  \- commons-codec:commons-codec:jar:1.10:compile
[INFO] |  +- org.apache.httpcomponents:httpmime:jar:4.5.7:compile
[INFO] |  +- org.ccil.cowan.tagsoup:tagsoup:jar:1.2.1:test
[INFO] |  +- com.jayway.restassured:json-path:jar:2.9.0:test
[INFO] |  |  +- org.codehaus.groovy:groovy-json:jar:2.4.4:test
[INFO] |  |  \- com.jayway.restassured:rest-assured-common:jar:2.9.0:test
[INFO] |  \- com.jayway.restassured:xml-path:jar:2.9.0:test
[INFO] |     \- org.apache.commons:commons-lang3:jar:3.10:compile
[INFO] +- com.kuaishou:kuaishou-spring-context-indexer:jar:1.2:provided
[INFO] +- javax.annotation:javax.annotation-api:jar:1.3.2:compile
[INFO] \- javax.xml.ws:jaxws-api:jar:2.3.1:compile
[INFO]    +- javax.xml.bind:jaxb-api:jar:2.3.1:compile
[INFO]    |  \- javax.activation:javax.activation-api:jar:1.2.0:compile
[INFO]    \- javax.xml.soap:javax.xml.soap-api:jar:1.4.0:compile
[INFO] 
[INFO] --------------< kuaishou:kwaishop-qa-risk-center-client >---------------
[INFO] Building kwaishop-qa-risk-center-client 1.0.0-SNAPSHOT             [2/5]
[INFO]   from kwaishop-qa-risk-center-client/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.1.1:tree (default-cli) @ kwaishop-qa-risk-center-client ---
[INFO] publish stats success. url: https://server-devops-api.corp.kuaishou.com/api/mavenStatsReport, cost: 157ms
[INFO] kuaishou:kwaishop-qa-risk-center-client:jar:1.0.0-SNAPSHOT
[INFO] +- kuaishou:krpc-all:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-registry-kess:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-registry-sdk:jar:1.0.198:compile
[INFO] |  |  +- com.kuaishou:kess-scheduler-client:jar:0.0.125:compile
[INFO] |  |  |  \- com.kuaishou:kess-config-client:jar:0.0.125:compile
[INFO] |  |  |     \- com.github.luben:zstd-jni:jar:1.3.8-6:compile
[INFO] |  |  +- kuaishou:infra-kws-sdk:jar:1.0.53:compile
[INFO] |  |  |  \- com.google.guava:guava:jar:28.1-jre-kwai5:compile
[INFO] |  |  |     +- com.google.guava:failureaccess:jar:1.0.1:compile
[INFO] |  |  |     +- com.google.guava:listenablefuture:jar:9999.0-empty-to-avoid-conflict-with-guava:compile
[INFO] |  |  |     +- org.checkerframework:checker-qual:jar:2.8.1:compile
[INFO] |  |  |     \- com.google.j2objc:j2objc-annotations:jar:1.3:compile
[INFO] |  |  \- kuaishou:infra-framework-platform:jar:1.0.589:compile
[INFO] |  |     +- kuaishou:kuaishou-env-utils:jar:1.0.12:compile
[INFO] |  |     +- kuaishou:ktrace-sdk:jar:1.0.253:compile
[INFO] |  |     |  \- org.springframework:spring-aspects:jar:5.1.10-kwai-12:compile
[INFO] |  |     |     \- org.aspectj:aspectjweaver:jar:1.8.13:compile
[INFO] |  |     +- kuaishou:infra-framework-common:jar:1.0.589:compile
[INFO] |  |     |  +- com.github.phantomthief:more-lambdas-jdk9:jar:0.1.55:compile
[INFO] |  |     |  +- com.carrotsearch:hppc:jar:0.7.3:compile
[INFO] |  |     |  +- com.fasterxml.jackson.core:jackson-core:jar:2.9.10:compile
[INFO] |  |     |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.9.10:compile
[INFO] |  |     |  +- com.fasterxml.jackson.module:jackson-module-afterburner:jar:2.9.10:compile
[INFO] |  |     |  +- kuaishou:infra-logger:jar:1.0.37:compile
[INFO] |  |     |  |  \- ch.qos.logback:logback-classic-kwai:jar:1.2.10-1:compile
[INFO] |  |     |  |     \- ch.qos.logback:logback-core:jar:1.2.10:compile
[INFO] |  |     |  +- com.github.phantomthief:retrieve-id-utils:jar:1.0.10:compile
[INFO] |  |     |  \- kuaishou:infra-java-kit:jar:1.0.2:compile
[INFO] |  |     +- kuaishou:kuaishou-env-util:jar:1.0.277:compile
[INFO] |  |     +- com.github.phantomthief:scope:jar:1.0.20:compile
[INFO] |  |     +- kuaishou:kuaishou-biz-def:jar:1.0.1801:compile
[INFO] |  |     +- com.github.phantomthief:zkconfig-resources:jar:1.1.29:compile
[INFO] |  |     |  +- org.apache.zookeeper:zookeeper:jar:3.5.7:compile
[INFO] |  |     |  |  +- org.apache.zookeeper:zookeeper-jute:jar:3.5.7:compile
[INFO] |  |     |  |  +- org.apache.yetus:audience-annotations:jar:0.5.0:compile
[INFO] |  |     |  |  +- io.netty:netty-handler:jar:4.1.38.Final:compile
[INFO] |  |     |  |  |  +- io.netty:netty-common:jar:4.1.38.Final:compile
[INFO] |  |     |  |  |  +- io.netty:netty-buffer:jar:4.1.38.Final:compile
[INFO] |  |     |  |  |  +- io.netty:netty-transport:jar:4.1.38.Final:compile
[INFO] |  |     |  |  |  |  \- io.netty:netty-resolver:jar:4.1.38.Final:compile
[INFO] |  |     |  |  |  \- io.netty:netty-codec:jar:4.1.38.Final:compile
[INFO] |  |     |  |  \- io.netty:netty-transport-native-epoll:jar:4.1.38.Final:compile
[INFO] |  |     |  |     \- io.netty:netty-transport-native-unix-common:jar:4.1.38.Final:compile
[INFO] |  |     |  \- org.apache.curator:curator-recipes:jar:*******:compile
[INFO] |  |     |     \- org.apache.curator:curator-framework:jar:*******:compile
[INFO] |  |     +- org.apache.curator:curator-client:jar:*******:compile
[INFO] |  |     +- kuaishou:ktrace-core:jar:1.0.253:compile
[INFO] |  |     |  +- kuaishou:ktrace-config:jar:1.0.253:compile
[INFO] |  |     |  +- com.kwai.jdk:snowman-api:jar:1.0.31:compile
[INFO] |  |     |  \- kuaishou:infra-metrics-core:jar:2.0.53:compile
[INFO] |  |     +- kuaishou:infra-kws-tool:jar:1.0.53:compile
[INFO] |  |     +- kuaishou:kuaishou-product-sdk:jar:1.0.13:compile
[INFO] |  |     |  \- kuaishou:kuaishou-base-proto:jar:1.0.110:compile
[INFO] |  |     +- kuaishou:kuaishou-zkclient:jar:1.0.55:compile
[INFO] |  |     +- kuaishou:zt-base:jar:1.0.141:compile
[INFO] |  |     |  \- kuaishou:kuaishou-common-i18n:jar:1.1.6:compile
[INFO] |  |     +- org.apache.commons:commons-text:jar:1.6:compile
[INFO] |  |     +- com.kuaishou:before-jvm-term-trigger-sdk:jar:1.0.69:compile
[INFO] |  |     |  \- com.squareup.okhttp3:okhttp:jar:3.12.3:compile
[INFO] |  |     |     \- com.squareup.okio:okio:jar:1.17.4:compile
[INFO] |  |     \- org.jetbrains.kotlin:kotlin-reflect:jar:1.3.72:compile
[INFO] |  +- kuaishou:krpc-governance-sdk:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-transport-sdk:jar:1.0.198:compile
[INFO] |  |  |  \- kuaishou:krpc-serialization-sdk:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-rpc-sdk:jar:1.0.198:compile
[INFO] |  |  |  \- org.javassist:javassist:jar:3.25.0-GA:compile
[INFO] |  |  \- kuaishou:krpc-metric-sdk:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-governance-sentinel:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-cluster:jar:1.0.198:compile
[INFO] |  |  |  +- net.javacrumbs.future-converter:future-converter-java8-guava:jar:1.2.0:compile
[INFO] |  |  |  |  +- net.javacrumbs.future-converter:future-converter-common:jar:1.2.0:compile
[INFO] |  |  |  |  +- net.javacrumbs.future-converter:future-converter-java8-common:jar:1.2.0:compile
[INFO] |  |  |  |  \- net.javacrumbs.future-converter:future-converter-guava-common:jar:1.2.0:compile
[INFO] |  |  |  +- io.projectreactor.addons:reactor-extra:jar:3.2.3.RELEASE:compile
[INFO] |  |  |  \- com.github.phantomthief:simple-failover:jar:0.1.32:compile
[INFO] |  |  +- com.kuaishou:kess-governor-client:jar:0.0.125:compile
[INFO] |  |  |  +- kuaishou:infra-patronum-governance-sdk:jar:1.0.36:compile
[INFO] |  |  |  |  \- kuaishou:infra-config-adaptor-facade:jar:1.0.53:compile
[INFO] |  |  |  \- com.kuaishou:kess-mandator-client:jar:0.0.125:compile
[INFO] |  |  |     \- net.goldolphin:maria:jar:0.0.7:compile
[INFO] |  |  |        +- io.netty:netty-all:jar:4.1.38.Final:compile
[INFO] |  |  |        \- org.freemarker:freemarker:jar:2.3.28:compile
[INFO] |  |  +- kuaishou:infra-sentinel-circuitbreaker:jar:1.0.94:compile
[INFO] |  |  |  +- io.github.resilience4j:resilience4j-circuitbreaker:jar:1.2.0:compile
[INFO] |  |  |  |  \- io.vavr:vavr:jar:0.10.0:compile
[INFO] |  |  |  |     \- io.vavr:vavr-match:jar:0.10.0:compile
[INFO] |  |  |  +- io.github.resilience4j:resilience4j-core:jar:1.2.0:compile
[INFO] |  |  |  +- io.projectreactor:reactor-core:jar:3.2.11.RELEASE:compile
[INFO] |  |  |  |  \- org.reactivestreams:reactive-streams:jar:1.0.2:compile
[INFO] |  |  |  +- io.github.resilience4j:resilience4j-reactor:jar:1.2.0:compile
[INFO] |  |  |  \- kuaishou:infra-sentinel-core:jar:1.0.94:compile
[INFO] |  |  |     \- kuaishou:infra-sentinel-config:jar:1.0.94:compile
[INFO] |  |  +- kuaishou:infra-sentinel-throttling:jar:1.0.94:compile
[INFO] |  |  |  \- net.spy:spymemcached:jar:2.12.3.28-kwai:compile
[INFO] |  |  +- kuaishou:infra-sentinel-system-throttling:jar:1.0.94:compile
[INFO] |  |  +- com.github.vladimir-bukhtoyarov:bucket4j-core:jar:6.0.1:compile
[INFO] |  |  \- kuaishou:infra-patronum-stresstest-sdk:jar:1.0.21:compile
[INFO] |  +- kuaishou:krpc-configcenter-kess:jar:1.0.198:compile
[INFO] |  |  \- kuaishou:krpc-common:jar:1.0.198:compile
[INFO] |  |     \- kuaishou:infra-patronum-healthcheck-sdk:jar:1.0.8:compile
[INFO] |  |        +- kuaishou:infra-patronum-healthcheck-events:jar:1.0.8:compile
[INFO] |  |        |  \- kuaishou:infra-patronum-healthcheck-metrics:jar:1.0.8:compile
[INFO] |  |        +- kuaishou:infra-patronum-healthcheck-analyser:jar:1.0.8:compile
[INFO] |  |        |  \- kuaishou:infra-patronum-healthcheck-common:jar:1.0.8:compile
[INFO] |  |        +- org.eclipse.jetty:jetty-http:jar:9.4.8.v20171121:compile
[INFO] |  |        |  \- org.eclipse.jetty:jetty-io:jar:9.4.8.v20171121:compile
[INFO] |  |        +- org.eclipse.jetty:jetty-util:jar:9.4.8.v20171121:compile
[INFO] |  |        \- org.eclipse.jetty:jetty-continuation:jar:9.4.8.v20171121:compile
[INFO] |  +- kuaishou:krpc-config-spring:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-config-sdk:jar:1.0.198:compile
[INFO] |  |  +- com.alibaba.spring:spring-context-support:jar:1.0.10:compile
[INFO] |  |  \- io.grpc:grpc-api:jar:1.36.1-kwai-1.1:compile
[INFO] |  |     +- io.grpc:grpc-context:jar:1.36.1-kwai-1.1:compile
[INFO] |  |     +- com.google.errorprone:error_prone_annotations:jar:2.3.3:compile
[INFO] |  |     \- org.codehaus.mojo:animal-sniffer-annotations:jar:1.18:compile
[INFO] |  +- kuaishou:krpc-bootstrap-grpc:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-bootstrap-sdk:jar:1.0.198:compile
[INFO] |  |  |  \- kuaishou:jwarmup-sdk:jar:1.0.19:compile
[INFO] |  |  |     \- kuaishou:jwarmup-common:jar:1.0.19:compile
[INFO] |  |  |        \- com.amazonaws:aws-java-sdk-s3:jar:1.11.891:compile
[INFO] |  |  |           +- com.amazonaws:aws-java-sdk-kms:jar:1.11.891:compile
[INFO] |  |  |           +- com.amazonaws:aws-java-sdk-core:jar:1.11.891:compile
[INFO] |  |  |           |  +- software.amazon.ion:ion-java:jar:1.0.2:compile
[INFO] |  |  |           |  \- com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:jar:2.9.10:compile
[INFO] |  |  |           \- com.amazonaws:jmespath-java:jar:1.11.891:compile
[INFO] |  |  +- kuaishou:infra-warmup-facade:jar:1.0.25:compile
[INFO] |  |  \- com.kuaishou:service-lifecycle-sdk:jar:1.0.25:compile
[INFO] |  |     +- commons-httpclient:commons-httpclient:jar:3.1:compile
[INFO] |  |     +- com.fasterxml.jackson.core:jackson-databind:jar:********-kwai:compile
[INFO] |  |     \- org.lz4:lz4-java:jar:1.7.1:compile
[INFO] |  +- kuaishou:krpc-bootstrap-local:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-rpc-grpc:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:ktrace-grpc-instrument-sdk:jar:1.0.253:compile
[INFO] |  |  |  \- io.grpc:grpc-core:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |     +- com.google.android:annotations:jar:4.1.1.4:runtime
[INFO] |  |  |     \- io.perfmark:perfmark-api:jar:0.19.0:runtime
[INFO] |  |  +- io.grpc:grpc-stub:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  +- io.grpc:grpc-services:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  \- com.google.protobuf:protobuf-java-util:jar:3.16.1:compile
[INFO] |  |  +- io.grpc:grpc-netty-shaded:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  +- kuaishou:infra-framework-service-context:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-framework-trace:jar:1.0.589:compile
[INFO] |  |  |  +- kuaishou:ktrace-transport:jar:1.0.253:compile
[INFO] |  |  |  \- kuaishou:infra-framework-lane:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-patronum-traffic-record-spi-sdk:jar:1.0.88:compile
[INFO] |  |  |  \- kuaishou:infra-patronum-traffic-management-metadata:jar:1.0.88:compile
[INFO] |  |  \- kuaishou:infra-patronum-stresstest-trafficscheduler:jar:1.0.21:compile
[INFO] |  +- kuaishou:krpc-rpc-local:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-metric-perf:jar:1.0.198:compile
[INFO] |  |  \- kuaishou:infra-framework-perf:jar:2.0.53:compile
[INFO] |  |     +- kuaishou:infra-patronum-governance-spi-sdk:jar:1.0.36:compile
[INFO] |  |     +- kuaishou:infra-config-adaptor-core:jar:1.0.53:compile
[INFO] |  |     +- com.ecyrd.speed4j:speed4j:jar:0.18:compile
[INFO] |  |     \- com.github.phantomthief:buffer-trigger:jar:0.2.21:compile
[INFO] |  +- kuaishou:krpc-metric-rpcmonitor:jar:1.0.198:compile
[INFO] |  |  \- kuaishou:rpcmonitor-java-client:jar:1.1.29:compile
[INFO] |  |     +- commons-io:commons-io:jar:2.6:compile
[INFO] |  |     +- com.google.code.gson:gson:jar:2.8.9:compile
[INFO] |  |     +- kuaishou:infra-common-falcon-counter:jar:2.0.41:compile
[INFO] |  |     |  +- com.yammer.metrics:metrics-core:jar:2.2.0:compile
[INFO] |  |     |  \- org.slf4j:jcl-over-slf4j:jar:1.7.30:compile
[INFO] |  |     \- com.kuaishou:division-util:jar:0.0.125:compile
[INFO] |  +- kuaishou:krpc-metric-ktrace:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-metric-sentinel:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:infra-sentinel-metrics:jar:1.0.94:compile
[INFO] |  |  |  +- com.lmax:disruptor:jar:3.3.7:compile
[INFO] |  |  |  +- joda-time:joda-time:jar:2.9.9:compile
[INFO] |  |  |  \- kuaishou:infra-framework-warmup:jar:1.0.589:compile
[INFO] |  |  \- com.alibaba.csp:sentinel-core:jar:1.6.3:compile
[INFO] |  +- kuaishou:krpc-metadata:jar:1.0.198:compile
[INFO] |  +- kuaishou:infra-krpc-metadata:jar:1.0.152:compile
[INFO] |  |  +- com.google.protobuf:protobuf-java:jar:3.16.1:compile
[INFO] |  |  +- io.grpc:grpc-protobuf:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  +- com.google.api.grpc:proto-google-common-protos:jar:2.0.1:compile
[INFO] |  |  |  \- io.grpc:grpc-protobuf-lite:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  \- com.github.phantomthief:more-lambdas:jar:0.1.55:compile
[INFO] |  \- kuaishou:kuaishou-kconf-client:jar:1.0.144:compile
[INFO] |     \- kuaishou:kuaishou-kconf-common:jar:1.0.144:compile
[INFO] |        +- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.9.10:compile
[INFO] |        +- com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.9.10:compile
[INFO] |        +- com.hubspot.jackson:jackson-datatype-protobuf:jar:0.9.10-jackson2.9-proto3:compile
[INFO] |        |  \- com.google.code.findbugs:annotations:jar:3.0.1u2:compile
[INFO] |        \- com.fasterxml.jackson.module:jackson-module-kotlin:jar:2.9.10:compile
[INFO] +- org.springframework:spring-context:jar:5.1.10-kwai-12:compile
[INFO] |  +- org.springframework:spring-aop:jar:5.1.10-kwai-12:compile
[INFO] |  +- org.springframework:spring-beans:jar:5.1.10-kwai-12:compile
[INFO] |  +- org.springframework:spring-core:jar:5.1.10-kwai-12:compile
[INFO] |  \- org.springframework:spring-expression:jar:5.1.10-kwai-12:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.30:provided
[INFO] +- org.mybatis:mybatis:jar:3.5.7:compile
[INFO] +- com.kuaishou.infra.boot:ks-boot-***********************
[INFO] |  +- org.springframework:spring-*******************************
[INFO] |  |  \- org.springframework:spring-tx:jar:5.1.10-kwai-12:compile
[INFO] |  +- org.slf4j:slf4j-api:jar:1.7.30-kwai-2:compile
[INFO] |  +- com.google.code.findbugs:jsr305:jar:3.0.2:compile
[INFO] |  \- org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:1.3.72:compile
[INFO] |     +- org.jetbrains.kotlin:kotlin-stdlib:jar:1.3.72:compile
[INFO] |     |  +- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.3.72:compile
[INFO] |     |  \- org.jetbrains:annotations:jar:13.0:compile
[INFO] |     \- org.jetbrains.kotlin:kotlin-stdlib-jdk7:jar:1.3.72:compile
[INFO] +- com.baomidou:mybatis-plus-annotation:jar:3.4.1:compile
[INFO] +- com.baomidou:mybatis-plus-core:jar:3.4.1:compile
[INFO] |  \- com.github.jsqlparser:jsqlparser:jar:3.2:compile
[INFO] +- org.apache.commons:commons-collections4:jar:4.3:compile
[INFO] +- com.baomidou:mybatis-plus-extension:jar:3.4.1:compile
[INFO] |  \- org.mybatis:mybatis-spring:jar:2.0.6:compile
[INFO] +- kuaishou:kuaishou-build-tools:jar:1.0.1352:test
[INFO] +- junit:junit:jar:4.12:test
[INFO] |  \- org.hamcrest:hamcrest-core:jar:1.3:test
[INFO] +- org.junit.vintage:junit-vintage-engine:jar:5.5.2:test
[INFO] |  +- org.apiguardian:apiguardian-api:jar:1.1.0:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.5.2:test
[INFO] +- org.hamcrest:hamcrest-library:jar:1.3:test
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.5.2:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  \- org.junit.platform:junit-platform-commons:jar:1.5.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.5.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-params:jar:5.5.2:test
[INFO] +- org.junit.platform:junit-platform-launcher:jar:1.5.2:test
[INFO] +- org.junit.platform:junit-platform-runner:jar:1.5.2:test
[INFO] |  \- org.junit.platform:junit-platform-suite-api:jar:1.5.2:test
[INFO] +- org.mockito:mockito-core:jar:2.23.4:test
[INFO] |  +- net.bytebuddy:byte-buddy:jar:1.12.10:compile
[INFO] |  +- net.bytebuddy:byte-buddy-agent:jar:1.12.10:compile
[INFO] |  \- org.objenesis:objenesis:jar:3.0.1:test
[INFO] +- org.mockito:mockito-junit-jupiter:jar:2.23.4:test
[INFO] +- org.mockito:mockito-inline:jar:2.23.4:test
[INFO] +- org.powermock:powermock-api-mockito2:jar:2.0.0:test
[INFO] |  \- org.powermock:powermock-api-support:jar:2.0.0:test
[INFO] |     +- org.powermock:powermock-reflect:jar:2.0.0:test
[INFO] |     \- org.powermock:powermock-core:jar:2.0.0:test
[INFO] +- org.powermock:powermock-module-junit4:jar:2.0.0:test
[INFO] |  \- org.powermock:powermock-module-junit4-common:jar:2.0.0:test
[INFO] +- com.h2database:h2:jar:1.4.197:test
[INFO] +- com.fiftyonred:mock-jedis:jar:0.4.0:test
[INFO] +- org.testng:testng:jar:6.14.3:test
[INFO] |  +- com.beust:jcommander:jar:1.72:test
[INFO] |  \- org.apache-extras.beanshell:bsh:jar:2.0b6:test
[INFO] +- org.apache.curator:curator-test:jar:*******:test
[INFO] +- com.github.kstyrc:embedded-redis:jar:0.6:test
[INFO] +- org.openjdk.jmh:jmh-core:jar:1.23:test
[INFO] |  +- net.sf.jopt-simple:jopt-simple:jar:4.6:test
[INFO] |  \- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] +- org.openjdk.jmh:jmh-generator-annprocess:jar:1.23:test
[INFO] +- org.springframework:spring-test:jar:5.1.10-kwai-12:test
[INFO] +- com.jayway.restassured:rest-assured:jar:2.9.0:test
[INFO] |  +- org.codehaus.groovy:groovy:jar:2.4.4:test
[INFO] |  +- org.codehaus.groovy:groovy-xml:jar:2.4.4:test
[INFO] |  +- org.apache.httpcomponents:httpclient:jar:4.5.7:compile
[INFO] |  |  +- org.apache.httpcomponents:httpcore:jar:4.4.11:compile
[INFO] |  |  \- commons-codec:commons-codec:jar:1.10:compile
[INFO] |  +- org.apache.httpcomponents:httpmime:jar:4.5.7:compile
[INFO] |  +- org.ccil.cowan.tagsoup:tagsoup:jar:1.2.1:test
[INFO] |  +- com.jayway.restassured:json-path:jar:2.9.0:test
[INFO] |  |  +- org.codehaus.groovy:groovy-json:jar:2.4.4:test
[INFO] |  |  \- com.jayway.restassured:rest-assured-common:jar:2.9.0:test
[INFO] |  \- com.jayway.restassured:xml-path:jar:2.9.0:test
[INFO] |     \- org.apache.commons:commons-lang3:jar:3.10:compile
[INFO] +- com.kuaishou:kuaishou-spring-context-indexer:jar:1.2:provided
[INFO] +- javax.annotation:javax.annotation-api:jar:1.3.2:compile
[INFO] \- javax.xml.ws:jaxws-api:jar:2.3.1:compile
[INFO]    +- javax.xml.bind:jaxb-api:jar:2.3.1:compile
[INFO]    |  \- javax.activation:javax.activation-api:jar:1.2.0:compile
[INFO]    \- javax.xml.soap:javax.xml.soap-api:jar:1.4.0:compile
[INFO] 
[INFO] --------------< kuaishou:kwaishop-qa-risk-center-common >---------------
[INFO] Building kwaishop-qa-risk-center-common 1.0.0-SNAPSHOT             [3/5]
[INFO]   from kwaishop-qa-risk-center-common/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
Downloading from gifshow: http://nexus.corp.kuaishou.com:88/nexus/content/groups/public/kuaishou/kwaishop-qa-risk-center-client/1.0.0-SNAPSHOT/maven-metadata.xml
[INFO] 
[INFO] --- dependency:3.1.1:tree (default-cli) @ kwaishop-qa-risk-center-common ---
Downloading from gifshow: http://nexus.corp.kuaishou.com:88/nexus/content/groups/public/kuaishou/kwaishop-qa-risk-center-client/1.0.0-SNAPSHOT/kwaishop-qa-risk-center-client-1.0.0-SNAPSHOT.jar
[INFO] publish stats success. url: https://server-devops-api.corp.kuaishou.com/api/mavenStatsReport, cost: 147ms
[INFO] kuaishou:kwaishop-qa-risk-center-common:jar:1.0.0-SNAPSHOT
[INFO] +- kuaishou:kwaishop-qa-risk-center-client:jar:1.0.0-SNAPSHOT:compile
[INFO] +- kuaishou:kwaishop-framework-common:jar:1.0.26:compile
[INFO] |  +- kuaishou:infra-framework-perf:jar:2.0.53:compile
[INFO] |  |  +- kuaishou:infra-framework-service-context:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-patronum-governance-spi-sdk:jar:1.0.36:compile
[INFO] |  |  +- kuaishou:infra-config-adaptor-core:jar:1.0.53:compile
[INFO] |  |  +- com.ecyrd.speed4j:speed4j:jar:0.18:compile
[INFO] |  |  \- com.github.phantomthief:buffer-trigger:jar:0.2.21:compile
[INFO] |  \- kuaishou:kuaishou-biz-def:jar:1.0.1801:compile
[INFO] +- org.springframework.boot:spring-boot-starter-test:jar:2.1.9.RELEASE:test
[INFO] |  +- org.springframework.boot:spring-boot-starter:jar:2.1.9.RELEASE:test
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:2.1.9.RELEASE:test
[INFO] |  |  +- org.springframework.boot:spring-boot-autoconfigure:jar:2.1.9.RELEASE:test
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:2.1.9.RELEASE:compile
[INFO] |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.11.2:compile
[INFO] |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.11.2:compile
[INFO] |  |  |  \- org.slf4j:jul-to-slf4j:jar:1.7.30:compile
[INFO] |  |  \- org.yaml:snakeyaml:jar:1.24:compile
[INFO] |  +- org.springframework.boot:spring-boot-test:jar:2.1.9.RELEASE:test
[INFO] |  +- org.springframework.boot:spring-boot-test-autoconfigure:jar:2.1.9.RELEASE:test
[INFO] |  +- com.jayway.jsonpath:json-path:jar:2.4.0:test
[INFO] |  |  \- net.minidev:json-smart:jar:2.3:test
[INFO] |  |     \- net.minidev:accessors-smart:jar:1.2:test
[INFO] |  |        \- org.ow2.asm:asm:jar:9.1:compile
[INFO] |  +- org.assertj:assertj-core:jar:3.16.1:test
[INFO] |  +- org.hamcrest:hamcrest-core:jar:1.3:test
[INFO] |  +- org.skyscreamer:jsonassert:jar:1.5.0:test
[INFO] |  |  \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:test
[INFO] |  +- org.springframework:spring-core:jar:5.1.10-kwai-12:compile
[INFO] |  \- org.xmlunit:xmlunit-core:jar:2.6.3:test
[INFO] +- org.projectlombok:lombok:jar:1.18.30:provided
[INFO] +- org.mybatis:mybatis:jar:3.5.6:compile
[INFO] +- com.baomidou:mybatis-plus-core:jar:3.5.1:compile
[INFO] |  \- com.github.jsqlparser:jsqlparser:jar:4.3:compile
[INFO] +- com.baomidou:mybatis-plus-annotation:jar:3.5.1:compile
[INFO] +- com.baomidou:mybatis-plus-extension:jar:3.5.1:compile
[INFO] |  \- org.mybatis:mybatis-spring:jar:2.0.6:compile
[INFO] +- javax.validation:validation-api:jar:2.0.1.Final:compile
[INFO] +- kuaishou:infra-framework-datasource:jar:1.0.589:compile
[INFO] |  +- com.google.guava:guava:jar:28.1-jre-kwai5:compile
[INFO] |  |  +- com.google.guava:failureaccess:jar:1.0.1:compile
[INFO] |  |  +- com.google.guava:listenablefuture:jar:9999.0-empty-to-avoid-conflict-with-guava:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:2.8.1:compile
[INFO] |  |  +- com.google.errorprone:error_prone_annotations:jar:2.3.3:compile
[INFO] |  |  +- com.google.j2objc:j2objc-annotations:jar:1.3:compile
[INFO] |  |  \- org.codehaus.mojo:animal-sniffer-annotations:jar:1.18:compile
[INFO] |  +- kuaishou:infra-sentinel-retryer:jar:1.0.94:compile
[INFO] |  +- kuaishou:infra-framework-common:jar:1.0.589:compile
[INFO] |  |  +- com.github.phantomthief:more-lambdas-jdk9:jar:0.1.55:compile
[INFO] |  |  +- kuaishou:infra-kws-sdk:jar:1.0.53:compile
[INFO] |  |  +- com.carrotsearch:hppc:jar:0.7.3:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-core:jar:2.9.10:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-databind:jar:********-kwai:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.9.10:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.9.10:compile
[INFO] |  |  +- com.fasterxml.jackson.module:jackson-module-kotlin:jar:2.9.10:compile
[INFO] |  |  +- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.9.10:compile
[INFO] |  |  +- com.fasterxml.jackson.module:jackson-module-afterburner:jar:2.9.10:compile
[INFO] |  |  +- com.hubspot.jackson:jackson-datatype-protobuf:jar:0.9.10-jackson2.9-proto3:compile
[INFO] |  |  |  \- com.google.code.findbugs:annotations:jar:3.0.1u2:compile
[INFO] |  |  +- kuaishou:infra-logger:jar:1.0.37:compile
[INFO] |  |  |  \- ch.qos.logback:logback-classic-kwai:jar:1.2.10-1:compile
[INFO] |  |  |     \- ch.qos.logback:logback-core:jar:1.2.10:compile
[INFO] |  |  +- com.github.phantomthief:scope:jar:1.0.20:compile
[INFO] |  |  +- com.github.phantomthief:retrieve-id-utils:jar:1.0.10:compile
[INFO] |  |  +- kuaishou:infra-java-kit:jar:1.0.2:compile
[INFO] |  |  \- commons-io:commons-io:jar:2.6:compile
[INFO] |  +- kuaishou:infra-framework-perf-impl:jar:2.0.53:compile
[INFO] |  |  +- kuaishou:infra-metrics-core:jar:2.0.53:compile
[INFO] |  |  +- org.lz4:lz4-java:jar:1.7.1:compile
[INFO] |  |  +- commons-httpclient:commons-httpclient:jar:3.1:compile
[INFO] |  |  \- kuaishou:kobserva-logger:jar:1.0.9:compile
[INFO] |  +- org.springframework:spring-*******************************
[INFO] |  |  \- org.springframework:spring-tx:jar:5.1.10-kwai-12:compile
[INFO] |  +- kuaishou:infra-framework-platform:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:kuaishou-env-utils:jar:1.0.12:compile
[INFO] |  |  +- kuaishou:ktrace-sdk:jar:1.0.253:compile
[INFO] |  |  |  \- org.springframework:spring-aspects:jar:5.1.10-kwai-12:compile
[INFO] |  |  |     \- org.aspectj:aspectjweaver:jar:1.8.13:compile
[INFO] |  |  +- com.kuaishou:kess-config-client:jar:0.0.125:compile
[INFO] |  |  |  \- com.github.luben:zstd-jni:jar:1.3.8-6:compile
[INFO] |  |  +- org.apache.curator:curator-client:jar:*******:compile
[INFO] |  |  +- kuaishou:rpcmonitor-java-client:jar:1.1.29:compile
[INFO] |  |  |  \- com.kuaishou:division-util:jar:0.0.125:compile
[INFO] |  |  +- kuaishou:ktrace-core:jar:1.0.253:compile
[INFO] |  |  |  +- kuaishou:ktrace-transport:jar:1.0.253:compile
[INFO] |  |  |  +- kuaishou:ktrace-config:jar:1.0.253:compile
[INFO] |  |  |  +- com.lmax:disruptor:jar:3.3.7:compile
[INFO] |  |  |  \- com.kwai.jdk:snowman-api:jar:1.0.31:compile
[INFO] |  |  +- kuaishou:infra-kws-tool:jar:1.0.53:compile
[INFO] |  |  +- kuaishou:kuaishou-product-sdk:jar:1.0.13:compile
[INFO] |  |  |  \- kuaishou:kuaishou-base-proto:jar:1.0.110:compile
[INFO] |  |  +- kuaishou:kuaishou-zkclient:jar:1.0.55:compile
[INFO] |  |  +- kuaishou:zt-base:jar:1.0.141:compile
[INFO] |  |  |  \- kuaishou:kuaishou-common-i18n:jar:1.1.6:compile
[INFO] |  |  +- org.apache.commons:commons-text:jar:1.6:compile
[INFO] |  |  \- com.kuaishou:before-jvm-term-trigger-sdk:jar:1.0.69:compile
[INFO] |  |     \- com.squareup.okhttp3:okhttp:jar:3.12.3:compile
[INFO] |  |        \- com.squareup.okio:okio:jar:1.17.4:compile
[INFO] |  +- kuaishou:infra-framework-config:jar:1.0.589:compile
[INFO] |  +- kuaishou:infra-config-adaptor-recipes:jar:1.0.53:compile
[INFO] |  +- kuaishou:kuaishou-kconf-client:jar:1.0.144:compile
[INFO] |  |  \- kuaishou:kuaishou-kconf-common:jar:1.0.144:compile
[INFO] |  +- kuaishou:kuaishou-env-util:jar:1.0.277:compile
[INFO] |  +- dom4j:dom4j:jar:1.6.1:compile
[INFO] |  |  \- xml-apis:xml-apis:jar:1.4.01:compile
[INFO] |  +- com.github.phantomthief:zkconfig-resources:jar:1.1.29:compile
[INFO] |  |  +- org.apache.zookeeper:zookeeper:jar:3.5.7:compile
[INFO] |  |  |  +- org.apache.zookeeper:zookeeper-jute:jar:3.5.7:compile
[INFO] |  |  |  +- org.apache.yetus:audience-annotations:jar:0.5.0:compile
[INFO] |  |  |  +- io.netty:netty-handler:jar:4.1.38.Final:compile
[INFO] |  |  |  |  +- io.netty:netty-common:jar:4.1.38.Final:compile
[INFO] |  |  |  |  +- io.netty:netty-buffer:jar:4.1.38.Final:compile
[INFO] |  |  |  |  +- io.netty:netty-transport:jar:4.1.38.Final:compile
[INFO] |  |  |  |  |  \- io.netty:netty-resolver:jar:4.1.38.Final:compile
[INFO] |  |  |  |  \- io.netty:netty-codec:jar:4.1.38.Final:compile
[INFO] |  |  |  \- io.netty:netty-transport-native-epoll:jar:4.1.38.Final:compile
[INFO] |  |  |     \- io.netty:netty-transport-native-unix-common:jar:4.1.38.Final:compile
[INFO] |  |  \- org.apache.curator:curator-recipes:jar:*******:compile
[INFO] |  |     \- org.apache.curator:curator-framework:jar:*******:compile
[INFO] |  +- com.github.phantomthief:simple-failover:jar:0.1.32:compile
[INFO] |  +- com.zaxxer:HikariCP:jar:*******-kwai:compile
[INFO] |  +- io.dropwizard.metrics:metrics-core:jar:3.2.5:compile
[INFO] |  +- mysql:mysql-connector-java:jar:5.1.46:compile
[INFO] |  +- kuaishou:infra-framework-warmup:jar:1.0.589:compile
[INFO] |  |  +- com.kuaishou:service-lifecycle-sdk:jar:1.0.25:compile
[INFO] |  |  \- kuaishou:infra-patronum-healthcheck-sdk:jar:1.0.8:compile
[INFO] |  |     +- kuaishou:infra-patronum-healthcheck-events:jar:1.0.8:compile
[INFO] |  |     |  \- kuaishou:infra-patronum-healthcheck-metrics:jar:1.0.8:compile
[INFO] |  |     +- kuaishou:infra-patronum-healthcheck-analyser:jar:1.0.8:compile
[INFO] |  |     |  \- kuaishou:infra-patronum-healthcheck-common:jar:1.0.8:compile
[INFO] |  |     +- org.eclipse.jetty:jetty-http:jar:9.4.8.v20171121:compile
[INFO] |  |     |  \- org.eclipse.jetty:jetty-io:jar:9.4.8.v20171121:compile
[INFO] |  |     +- org.eclipse.jetty:jetty-util:jar:9.4.8.v20171121:compile
[INFO] |  |     \- org.eclipse.jetty:jetty-continuation:jar:9.4.8.v20171121:compile
[INFO] |  +- kuaishou:infra-patronum-stresstest-trafficscheduler:jar:1.0.21:compile
[INFO] |  |  \- kuaishou:infra-framework-trace:jar:1.0.589:compile
[INFO] |  +- org.apache.shardingsphere:sharding-jdbc-core:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:shardingsphere-pluggable:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:shardingsphere-sql-parser-binder:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:shardingsphere-route:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |  \- org.apache.shardingsphere:shardingsphere-common:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:shardingsphere-rewrite-engine:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:shardingsphere-executor:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  \- org.apache.shardingsphere:shardingsphere-merge:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:sharding-transaction-core:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:shardingsphere-sql-parser-mysql:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  \- org.apache.shardingsphere:shardingsphere-sql-parser-engine:jar:4.1.1-kwai-28:compile
[INFO] |  |  |     +- org.apache.shardingsphere:shardingsphere-spi:jar:4.1.1-kwai-28:compile
[INFO] |  |  |     +- org.apache.shardingsphere:shardingsphere-sql-parser-spi:jar:4.1.1-kwai-28:compile
[INFO] |  |  |     +- org.apache.shardingsphere:shardingsphere-sql-parser-statement:jar:4.1.1-kwai-28:compile
[INFO] |  |  |     \- org.antlr:antlr4-runtime:jar:4.7.2:compile
[INFO] |  |  +- org.apache.shardingsphere:sharding-core-route:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  \- org.apache.shardingsphere:sharding-core-common:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:master-slave-core-route:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  \- org.apache.shardingsphere:sharding-core-api:jar:4.1.1-kwai-28:compile
[INFO] |  |  |     \- org.apache.shardingsphere:encrypt-core-api:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:sharding-core-rewrite:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:encrypt-core-rewrite:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:shadow-core-rewrite:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:sharding-core-execute:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:sharding-core-merge:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:encrypt-core-merge:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  \- org.apache.shardingsphere:encrypt-core-common:jar:4.1.1-kwai-28:compile
[INFO] |  |  \- org.slf4j:jcl-over-slf4j:jar:1.7.30:compile
[INFO] |  +- kuaishou:infra-keycenter-client:jar:1.1.76:compile
[INFO] |  |  +- kuaishou:infra-common-falcon-counter:jar:2.0.41:compile
[INFO] |  |  |  \- com.yammer.metrics:metrics-core:jar:2.2.0:compile
[INFO] |  |  +- kuaishou:infra-keycenter-security:jar:1.1.76:compile
[INFO] |  |  |  \- kuaishou:infra-keycenter-proto:jar:1.1.76:compile
[INFO] |  |  +- kuaishou:infra-keycenter-store-sdk:jar:1.1.76:compile
[INFO] |  |  \- kuaishou:infra-keycenter-grpc-config:jar:1.1.76:compile
[INFO] |  +- kuaishou:infra-framework-grpc:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-framework-kess:jar:1.0.589:compile
[INFO] |  |  |  \- kuaishou:infra-krpc-common-kess-adapter:jar:1.0.152:compile
[INFO] |  |  +- kuaishou:infra-framework-lane:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-framework-reporter:jar:1.0.589:compile
[INFO] |  |  |  +- kuaishou:infra-metrics-reporter:jar:2.0.53:compile
[INFO] |  |  |  +- kuaishou:infra-framework-loadingcache:jar:1.0.589:compile
[INFO] |  |  |  +- kuaishou:infra-sentinel-ratelimiter:jar:1.0.94:compile
[INFO] |  |  |  +- kuaishou:infra-framework-kbox:jar:2.0.53:compile
[INFO] |  |  |  +- kuaishou:infra-framework-spring:jar:1.0.589:compile
[INFO] |  |  |  +- kuaishou:infra-log-sender:jar:1.0.32:compile
[INFO] |  |  |  +- com.kuaishou:gaea-agent-external-bootstrap:jar:1.0.1:compile
[INFO] |  |  |  +- io.grpc:grpc-core:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  |  +- com.google.android:annotations:jar:4.1.1.4:runtime
[INFO] |  |  |  |  \- io.perfmark:perfmark-api:jar:0.19.0:compile
[INFO] |  |  |  +- kuaishou:kuaishou-framework-trace:jar:1.0.750:compile
[INFO] |  |  |  |  \- kuaishou:infra-common-grpc:jar:1.1.113:compile
[INFO] |  |  |  +- kuaishou:infra-byte-code-enhancer-sdk:jar:1.0.47:compile
[INFO] |  |  |  |  +- kuaishou:infra-byte-code-enhancer-core:jar:1.0.47:compile
[INFO] |  |  |  |  |  +- kuaishou:infra-byte-code-enhancer-spy-runtime:jar:1.0.47:compile
[INFO] |  |  |  |  |  |  \- org.springframework.boot:spring-boot-loader:jar:2.1.9.RELEASE:compile
[INFO] |  |  |  |  |  \- kuaishou:infra-byte-code-enhancer-facade:jar:1.0.47:compile
[INFO] |  |  |  |  +- kuaishou:infra-byte-code-enhancer-advice-enhancer:jar:1.0.47:compile
[INFO] |  |  |  |  \- kuaishou:infra-byte-code-enhancer-advice-processor:jar:1.0.47:compile
[INFO] |  |  |  +- kuaishou:infra-tianwen-meter-sdk:jar:1.0.24:compile
[INFO] |  |  |  |  \- commons-collections:commons-collections:jar:3.2.2:compile
[INFO] |  |  |  \- kuaishou:infra-patronum-outlier-detection-metric-report-sdk:jar:1.0.8:compile
[INFO] |  |  \- kuaishou:infra-krpc-client:jar:1.0.152:compile
[INFO] |  |     +- io.projectreactor.addons:reactor-extra:jar:3.2.3.RELEASE:compile
[INFO] |  |     +- kuaishou:infra-krpc-client-common:jar:1.0.152:compile
[INFO] |  |     +- kuaishou:infra-krpc-common:jar:1.0.152:compile
[INFO] |  |     +- kuaishou:infra-sentinel-adaptivelimiter:jar:1.0.94:compile
[INFO] |  |     +- net.javacrumbs.future-converter:future-converter-java8-guava:jar:1.2.0:compile
[INFO] |  |     |  +- net.javacrumbs.future-converter:future-converter-common:jar:1.2.0:compile
[INFO] |  |     |  +- net.javacrumbs.future-converter:future-converter-java8-common:jar:1.2.0:compile
[INFO] |  |     |  \- net.javacrumbs.future-converter:future-converter-guava-common:jar:1.2.0:compile
[INFO] |  |     \- io.perfmark:perfmark-traceviewer:jar:0.19.0:compile
[INFO] |  |        \- io.perfmark:perfmark-tracewriter:jar:0.19.0:compile
[INFO] |  |           +- io.perfmark:perfmark-impl:jar:0.19.0:compile
[INFO] |  |           \- io.perfmark:perfmark-java6:jar:0.19.0:compile
[INFO] |  +- kuaishou:infra-warmup-facade:jar:1.0.25:compile
[INFO] |  +- kuaishou:infra-patronum-governance-sdk:jar:1.0.36:compile
[INFO] |  |  \- kuaishou:infra-config-adaptor-facade:jar:1.0.53:compile
[INFO] |  +- kuaishou:infra-patronum-traffic-record-spi-sdk:jar:1.0.88:compile
[INFO] |  |  +- io.grpc:grpc-api:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  \- io.grpc:grpc-context:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  \- kuaishou:infra-patronum-traffic-management-metadata:jar:1.0.88:compile
[INFO] |  +- kuaishou:infra-config-report-sdk:jar:1.0.4:compile
[INFO] |  +- org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:1.3.72:compile
[INFO] |  |  +- org.jetbrains.kotlin:kotlin-stdlib:jar:1.3.72:compile
[INFO] |  |  |  +- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.3.72:compile
[INFO] |  |  |  \- org.jetbrains:annotations:jar:13.0:compile
[INFO] |  |  \- org.jetbrains.kotlin:kotlin-stdlib-jdk7:jar:1.3.72:compile
[INFO] |  \- org.jetbrains.kotlin:kotlin-reflect:jar:1.3.72:compile
[INFO] +- kuaishou:kspay-common:jar:1.0.1614:compile
[INFO] |  +- org.slf4j:slf4j-api:jar:1.7.30-kwai-2:compile
[INFO] |  +- com.google.code.findbugs:jsr305:jar:3.0.2:compile
[INFO] |  +- com.google.code.gson:gson:jar:2.8.9:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.10:compile
[INFO] |  +- com.google.protobuf:protobuf-java:jar:3.16.1:compile
[INFO] |  +- com.google.protobuf:protobuf-java-util:jar:3.16.1:compile
[INFO] |  +- org.apache.commons:commons-csv:jar:1.5:compile
[INFO] |  +- com.github.phantomthief:more-lambdas:jar:0.1.55:compile
[INFO] |  \- javax:javaee-api:jar:8.0.1:compile
[INFO] |     \- com.sun.mail:javax.mail:jar:1.6.2:compile
[INFO] |        \- javax.activation:activation:jar:1.1.1:compile
[INFO] +- org.springframework:spring-context:jar:5.1.10-kwai-12:compile
[INFO] |  +- org.springframework:spring-aop:jar:5.1.10-kwai-12:compile
[INFO] |  +- org.springframework:spring-beans:jar:5.1.10-kwai-12:compile
[INFO] |  \- org.springframework:spring-expression:jar:5.1.10-kwai-12:compile
[INFO] +- com.kuaishou.infra.boot:ks-boot-***********************
[INFO] +- org.apache.commons:commons-collections4:jar:4.3:compile
[INFO] +- kuaishou:krpc-all:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-registry-kess:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-registry-sdk:jar:1.0.198:compile
[INFO] |  |  \- com.kuaishou:kess-scheduler-client:jar:0.0.125:compile
[INFO] |  +- kuaishou:krpc-governance-sdk:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-transport-sdk:jar:1.0.198:compile
[INFO] |  |  |  \- kuaishou:krpc-serialization-sdk:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-rpc-sdk:jar:1.0.198:compile
[INFO] |  |  |  \- org.javassist:javassist:jar:3.25.0-GA:compile
[INFO] |  |  \- kuaishou:krpc-metric-sdk:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-governance-sentinel:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-cluster:jar:1.0.198:compile
[INFO] |  |  +- com.kuaishou:kess-governor-client:jar:0.0.125:compile
[INFO] |  |  |  \- com.kuaishou:kess-mandator-client:jar:0.0.125:compile
[INFO] |  |  |     \- net.goldolphin:maria:jar:0.0.7:compile
[INFO] |  |  |        +- io.netty:netty-all:jar:4.1.38.Final:compile
[INFO] |  |  |        \- org.freemarker:freemarker:jar:2.3.28:compile
[INFO] |  |  +- kuaishou:infra-sentinel-circuitbreaker:jar:1.0.94:compile
[INFO] |  |  |  +- io.github.resilience4j:resilience4j-circuitbreaker:jar:1.2.0:compile
[INFO] |  |  |  |  \- io.vavr:vavr:jar:0.10.0:compile
[INFO] |  |  |  |     \- io.vavr:vavr-match:jar:0.10.0:compile
[INFO] |  |  |  +- io.github.resilience4j:resilience4j-core:jar:1.2.0:compile
[INFO] |  |  |  +- io.projectreactor:reactor-core:jar:3.2.11.RELEASE:compile
[INFO] |  |  |  |  \- org.reactivestreams:reactive-streams:jar:1.0.2:compile
[INFO] |  |  |  +- io.github.resilience4j:resilience4j-reactor:jar:1.2.0:compile
[INFO] |  |  |  \- kuaishou:infra-sentinel-core:jar:1.0.94:compile
[INFO] |  |  |     \- kuaishou:infra-sentinel-config:jar:1.0.94:compile
[INFO] |  |  +- kuaishou:infra-sentinel-throttling:jar:1.0.94:compile
[INFO] |  |  |  \- net.spy:spymemcached:jar:2.12.3.28-kwai:compile
[INFO] |  |  +- kuaishou:infra-sentinel-system-throttling:jar:1.0.94:compile
[INFO] |  |  +- com.github.vladimir-bukhtoyarov:bucket4j-core:jar:6.0.1:compile
[INFO] |  |  \- kuaishou:infra-patronum-stresstest-sdk:jar:1.0.21:compile
[INFO] |  +- kuaishou:krpc-configcenter-kess:jar:1.0.198:compile
[INFO] |  |  \- kuaishou:krpc-common:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-config-spring:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-config-sdk:jar:1.0.198:compile
[INFO] |  |  \- com.alibaba.spring:spring-context-support:jar:1.0.10:compile
[INFO] |  +- kuaishou:krpc-bootstrap-grpc:jar:1.0.198:compile
[INFO] |  |  \- kuaishou:krpc-bootstrap-sdk:jar:1.0.198:compile
[INFO] |  |     \- kuaishou:jwarmup-sdk:jar:1.0.19:compile
[INFO] |  |        \- kuaishou:jwarmup-common:jar:1.0.19:compile
[INFO] |  |           \- com.amazonaws:aws-java-sdk-s3:jar:1.11.891:compile
[INFO] |  |              +- com.amazonaws:aws-java-sdk-kms:jar:1.11.891:compile
[INFO] |  |              +- com.amazonaws:aws-java-sdk-core:jar:1.11.891:compile
[INFO] |  |              |  +- software.amazon.ion:ion-java:jar:1.0.2:compile
[INFO] |  |              |  \- com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:jar:2.9.10:compile
[INFO] |  |              \- com.amazonaws:jmespath-java:jar:1.11.891:compile
[INFO] |  +- kuaishou:krpc-bootstrap-local:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-rpc-grpc:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:ktrace-grpc-instrument-sdk:jar:1.0.253:compile
[INFO] |  |  +- io.grpc:grpc-stub:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  +- io.grpc:grpc-services:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  \- io.grpc:grpc-netty-shaded:jar:1.36.1-kwai-1.1:compile
[INFO] |  +- kuaishou:krpc-rpc-local:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-metric-perf:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-metric-rpcmonitor:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-metric-ktrace:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-metric-sentinel:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:infra-sentinel-metrics:jar:1.0.94:compile
[INFO] |  |  |  \- joda-time:joda-time:jar:2.9.9:compile
[INFO] |  |  \- com.alibaba.csp:sentinel-core:jar:1.6.3:compile
[INFO] |  +- kuaishou:krpc-metadata:jar:1.0.198:compile
[INFO] |  \- kuaishou:infra-krpc-metadata:jar:1.0.152:compile
[INFO] |     \- io.grpc:grpc-protobuf:jar:1.36.1-kwai-1.1:compile
[INFO] |        +- com.google.api.grpc:proto-google-common-protos:jar:2.0.1:compile
[INFO] |        \- io.grpc:grpc-protobuf-lite:jar:1.36.1-kwai-1.1:compile
[INFO] +- kuaishou:kuaishou-build-tools:jar:1.0.1352:test
[INFO] +- junit:junit:jar:4.12:test
[INFO] +- org.junit.vintage:junit-vintage-engine:jar:5.5.2:test
[INFO] |  +- org.apiguardian:apiguardian-api:jar:1.1.0:test
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.5.2:test
[INFO] +- org.hamcrest:hamcrest-library:jar:1.3:test
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.5.2:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  \- org.junit.platform:junit-platform-commons:jar:1.5.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.5.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-params:jar:5.5.2:test
[INFO] +- org.junit.platform:junit-platform-launcher:jar:1.5.2:test
[INFO] +- org.junit.platform:junit-platform-runner:jar:1.5.2:test
[INFO] |  \- org.junit.platform:junit-platform-suite-api:jar:1.5.2:test
[INFO] +- org.mockito:mockito-core:jar:2.23.4:test
[INFO] |  +- net.bytebuddy:byte-buddy:jar:1.12.10:compile
[INFO] |  +- net.bytebuddy:byte-buddy-agent:jar:1.12.10:compile
[INFO] |  \- org.objenesis:objenesis:jar:3.0.1:test
[INFO] +- org.mockito:mockito-junit-jupiter:jar:2.23.4:test
[INFO] +- org.mockito:mockito-inline:jar:2.23.4:test
[INFO] +- org.powermock:powermock-api-mockito2:jar:2.0.0:test
[INFO] |  \- org.powermock:powermock-api-support:jar:2.0.0:test
[INFO] |     +- org.powermock:powermock-reflect:jar:2.0.0:test
[INFO] |     \- org.powermock:powermock-core:jar:2.0.0:test
[INFO] +- org.powermock:powermock-module-junit4:jar:2.0.0:test
[INFO] |  \- org.powermock:powermock-module-junit4-common:jar:2.0.0:test
[INFO] +- com.h2database:h2:jar:1.4.197:test
[INFO] +- com.fiftyonred:mock-jedis:jar:0.4.0:test
[INFO] +- org.testng:testng:jar:6.14.3:test
[INFO] |  +- com.beust:jcommander:jar:1.72:test
[INFO] |  \- org.apache-extras.beanshell:bsh:jar:2.0b6:test
[INFO] +- org.apache.curator:curator-test:jar:*******:test
[INFO] +- com.github.kstyrc:embedded-redis:jar:0.6:test
[INFO] +- org.openjdk.jmh:jmh-core:jar:1.23:test
[INFO] |  +- net.sf.jopt-simple:jopt-simple:jar:4.6:test
[INFO] |  \- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] +- org.openjdk.jmh:jmh-generator-annprocess:jar:1.23:test
[INFO] +- org.springframework:spring-test:jar:5.1.10-kwai-12:test
[INFO] +- com.jayway.restassured:rest-assured:jar:2.9.0:test
[INFO] |  +- org.codehaus.groovy:groovy:jar:2.4.4:test
[INFO] |  +- org.codehaus.groovy:groovy-xml:jar:2.4.4:test
[INFO] |  +- org.apache.httpcomponents:httpclient:jar:4.5.7:compile
[INFO] |  |  +- org.apache.httpcomponents:httpcore:jar:4.4.11:compile
[INFO] |  |  \- commons-codec:commons-codec:jar:1.10:compile
[INFO] |  +- org.apache.httpcomponents:httpmime:jar:4.5.7:compile
[INFO] |  +- org.ccil.cowan.tagsoup:tagsoup:jar:1.2.1:test
[INFO] |  +- com.jayway.restassured:json-path:jar:2.9.0:test
[INFO] |  |  +- org.codehaus.groovy:groovy-json:jar:2.4.4:test
[INFO] |  |  \- com.jayway.restassured:rest-assured-common:jar:2.9.0:test
[INFO] |  \- com.jayway.restassured:xml-path:jar:2.9.0:test
[INFO] +- com.kuaishou:kuaishou-spring-context-indexer:jar:1.2:provided
[INFO] +- javax.annotation:javax.annotation-api:jar:1.3.2:compile
[INFO] \- javax.xml.ws:jaxws-api:jar:2.3.1:compile
[INFO]    +- javax.xml.bind:jaxb-api:jar:2.3.1:compile
[INFO]    |  \- javax.activation:javax.activation-api:jar:1.2.0:compile
[INFO]    \- javax.xml.soap:javax.xml.soap-api:jar:1.4.0:compile
[INFO] 
[INFO] ------------------< kuaishou:kwaishop-qa-risk-center >------------------
[INFO] Building kwaishop-qa-risk-center 1.0.0-SNAPSHOT                    [4/5]
[INFO]   from kwaishop-qa-risk-center/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
Downloading from gifshow: http://nexus.corp.kuaishou.com:88/nexus/content/groups/public/kuaishou/kwaishop-qa-risk-center-common/1.0.0-SNAPSHOT/maven-metadata.xml
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[INFO] 
[INFO] --- dependency:3.1.1:tree (default-cli) @ kwaishop-qa-risk-center ---
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
Downloading from gifshow: http://nexus.corp.kuaishou.com:88/nexus/content/groups/public/kuaishou/kwaishop-qa-risk-center-common/1.0.0-SNAPSHOT/kwaishop-qa-risk-center-common-1.0.0-SNAPSHOT.jar
[INFO] publish stats success. url: https://server-devops-api.corp.kuaishou.com/api/mavenStatsReport, cost: 201ms
[INFO] kuaishou:kwaishop-qa-risk-center:jar:1.0.0-SNAPSHOT
[INFO] +- kuaishou:kwaishop-qa-risk-center-client:jar:1.0.0-SNAPSHOT:compile
[INFO] +- kuaishou:kwaishop-qa-risk-center-common:jar:1.0.0-SNAPSHOT:compile
[INFO] |  +- kuaishou:kwaishop-framework-common:jar:1.0.26:compile
[INFO] |  \- kuaishou:kspay-common:jar:1.0.1614:compile
[INFO] |     \- org.apache.commons:commons-csv:jar:1.5:compile
[INFO] +- kuaishou:infra-framework-mq-api:jar:1.0.432:compile
[INFO] |  +- com.google.protobuf:protobuf-java:jar:3.16.1:compile
[INFO] |  +- kuaishou:infra-framework-common:jar:1.0.589:compile
[INFO] |  |  +- com.github.phantomthief:more-lambdas-jdk9:jar:0.1.55:compile
[INFO] |  |  +- com.carrotsearch:hppc:jar:0.7.3:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.9.10:compile
[INFO] |  |  +- com.fasterxml.jackson.module:jackson-module-kotlin:jar:2.9.10:compile
[INFO] |  |  +- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.9.10:compile
[INFO] |  |  +- com.fasterxml.jackson.module:jackson-module-afterburner:jar:2.9.10:compile
[INFO] |  |  +- com.hubspot.jackson:jackson-datatype-protobuf:jar:0.9.10-jackson2.9-proto3:compile
[INFO] |  |  +- com.github.phantomthief:retrieve-id-utils:jar:1.0.10:compile
[INFO] |  |  +- com.github.phantomthief:buffer-trigger:jar:0.2.21:compile
[INFO] |  |  \- kuaishou:infra-java-kit:jar:1.0.2:compile
[INFO] |  +- kuaishou:kuaishou-biz-def:jar:1.0.1801:compile
[INFO] |  +- org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:1.3.72:compile
[INFO] |  |  \- org.jetbrains.kotlin:kotlin-stdlib-jdk7:jar:1.3.72:compile
[INFO] |  \- org.jetbrains.kotlin:kotlin-reflect:jar:1.3.72:compile
[INFO] +- kuaishou:infra-framework-mq-provider:jar:1.0.432:compile
[INFO] |  +- kuaishou:infra-framework-mq-common:jar:1.0.432:compile
[INFO] |  +- kuaishou:infra-framework-zone:jar:1.0.9:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-loader:jar:2.1.9.RELEASE:compile
[INFO] |  +- kuaishou:infra-framework-common-component:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:ktrace-sdk:jar:1.0.253:compile
[INFO] |  |  +- kuaishou:infra-sentinel-ratelimiter:jar:1.0.94:compile
[INFO] |  |  +- kuaishou:infra-framework-throttling:jar:1.0.589:compile
[INFO] |  |  +- com.github.phantomthief:simple-pool:jar:0.1.19:compile
[INFO] |  |  \- org.springframework:spring-aspects:jar:5.1.10-kwai-12:compile
[INFO] |  |     \- org.aspectj:aspectjweaver:jar:1.8.13:compile
[INFO] |  +- kuaishou:infra-framework-trace:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:kuaishou-product-sdk:jar:1.0.13:compile
[INFO] |  |  \- kuaishou:ktrace-transport:jar:1.0.253:compile
[INFO] |  +- kuaishou:infra-framework-warmup:jar:1.0.589:compile
[INFO] |  |  \- kuaishou:infra-patronum-healthcheck-sdk:jar:1.0.8:compile
[INFO] |  |     +- kuaishou:infra-patronum-healthcheck-events:jar:1.0.8:compile
[INFO] |  |     +- kuaishou:infra-patronum-healthcheck-analyser:jar:1.0.8:compile
[INFO] |  |     |  \- kuaishou:infra-patronum-healthcheck-common:jar:1.0.8:compile
[INFO] |  |     +- org.eclipse.jetty:jetty-http:jar:9.4.8.v20171121:compile
[INFO] |  |     |  \- org.eclipse.jetty:jetty-io:jar:9.4.8.v20171121:compile
[INFO] |  |     +- org.eclipse.jetty:jetty-util:jar:9.4.8.v20171121:compile
[INFO] |  |     \- org.eclipse.jetty:jetty-continuation:jar:9.4.8.v20171121:compile
[INFO] |  +- kuaishou:infra-warmup-facade:jar:1.0.25:compile
[INFO] |  +- kuaishou:infra-patronum-stresstest-trafficscheduler:jar:1.0.21:compile
[INFO] |  +- kuaishou:kuaishou-kconf-client-impl:jar:1.0.144:compile
[INFO] |  |  +- com.kuaishou:kess-config-client:jar:0.0.125:compile
[INFO] |  |  +- com.squareup.okhttp3:okhttp:jar:3.12.3:compile
[INFO] |  |  \- kuaishou:kess-conf-common:jar:1.0.7:runtime
[INFO] |  |     +- kuaishou:kess-conf-metadata:jar:1.0.7:runtime
[INFO] |  |     \- org.reflections:reflections:jar:0.9.12:runtime
[INFO] |  +- com.github.phantomthief:simple-failover:jar:0.1.32:compile
[INFO] |  +- kuaishou:infra-logger:jar:1.0.37:compile
[INFO] |  |  \- ch.qos.logback:logback-classic-kwai:jar:1.2.10-1:compile
[INFO] |  |     \- ch.qos.logback:logback-core:jar:1.2.10:compile
[INFO] |  +- kuaishou:infra-framework-runner-common:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-framework-spring:jar:1.0.589:compile
[INFO] |  |  +- org.yaml:snakeyaml:jar:1.24:compile
[INFO] |  |  +- org.slf4j:jul-to-slf4j:jar:1.7.30:compile
[INFO] |  |  +- org.slf4j:log4j-over-slf4j:jar:1.7.30:compile
[INFO] |  |  +- kuaishou:jwarmup-sdk:jar:1.0.19:compile
[INFO] |  |  |  \- kuaishou:jwarmup-common:jar:1.0.19:compile
[INFO] |  |  +- org.codehaus.janino:janino:jar:3.0.12:compile
[INFO] |  |  |  \- org.codehaus.janino:commons-compiler:jar:3.0.12:compile
[INFO] |  |  +- kuaishou:infra-patronum-record-grpc-sdk:jar:1.0.88:compile
[INFO] |  |  |  \- kuaishou:infra-patronum-traffic-storage-sdk:jar:1.0.88:compile
[INFO] |  |  +- kuaishou:infra-framework-warmup-redis-store:jar:1.0.589:compile
[INFO] |  |  \- kuaishou:ktrace-spi-impl:jar:1.0.253:runtime
[INFO] |  +- org.springframework:spring-core:jar:5.1.10-kwai-12:compile
[INFO] |  +- org.springframework:spring-beans:jar:5.1.10-kwai-12:compile
[INFO] |  \- kuaishou:infra-framework-service-context:jar:1.0.589:compile
[INFO] +- kuaishou:infra-framework-mq-rocketmq:jar:1.0.432:compile
[INFO] |  +- kuaishou:infra-framework-zone-support:jar:1.0.9:compile
[INFO] |  +- com.google.code.findbugs:jsr305:jar:3.0.2:compile
[INFO] |  +- org.slf4j:slf4j-api:jar:1.7.30-kwai-2:compile
[INFO] |  +- org.slf4j:jcl-over-slf4j:jar:1.7.30:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.10:compile
[INFO] |  +- commons-collections:commons-collections:jar:3.2.2:compile
[INFO] |  \- commons-beanutils:commons-beanutils:jar:1.9.3:compile
[INFO] +- com.kuaishou.infra.boot:ks-boot-starter-runner-kafka:jar:1.4.78:compile
[INFO] |  +- com.kuaishou.infra.boot:ks-boot-starter-runner-common:jar:1.4.78:compile
[INFO] |  |  +- kuaishou:infra-framework-runner-starter:jar:1.0.589:compile
[INFO] |  |  +- com.kuaishou.infra.boot:ks-boot-runner:jar:1.4.78:compile
[INFO] |  |  |  \- com.kuaishou.infra.boot:ks-boot:jar:1.4.78:compile
[INFO] |  |  \- com.kuaishou.infra.boot:ks-boot-starter:jar:1.4.78:compile
[INFO] |  |     +- org.springframework.boot:spring-boot-starter:jar:2.1.9.RELEASE:compile
[INFO] |  |     |  \- org.springframework.boot:spring-boot-starter-logging:jar:2.1.9.RELEASE:compile
[INFO] |  |     \- com.kuaishou.infra.boot:ks-boot-autoconfigure:jar:1.4.78:compile
[INFO] |  |        \- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.9.10:compile
[INFO] |  \- kuaishou:infra-framework-kafka-runner:jar:1.0.589:compile
[INFO] +- kuaishou:kuaishou-intown-sdk:jar:1.0.54:compile
[INFO] |  +- kuaishou:infra-framework-config:jar:1.0.589:compile
[INFO] |  +- kuaishou:infra-framework-reporter:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-metrics-reporter:jar:2.0.53:compile
[INFO] |  |  +- kuaishou:infra-framework-kbox:jar:2.0.53:compile
[INFO] |  |  +- kuaishou:infra-log-sender:jar:1.0.32:compile
[INFO] |  |  +- io.grpc:grpc-core:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  +- com.google.android:annotations:jar:4.1.1.4:runtime
[INFO] |  |  |  \- io.perfmark:perfmark-api:jar:0.19.0:compile
[INFO] |  |  +- io.grpc:grpc-netty-shaded:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  +- kuaishou:kuaishou-framework-trace:jar:1.0.750:compile
[INFO] |  |  |  \- kuaishou:infra-common-grpc:jar:1.1.113:compile
[INFO] |  |  +- kuaishou:infra-byte-code-enhancer-sdk:jar:1.0.47:compile
[INFO] |  |  |  +- kuaishou:infra-byte-code-enhancer-core:jar:1.0.47:compile
[INFO] |  |  |  |  +- kuaishou:infra-byte-code-enhancer-spy-runtime:jar:1.0.47:compile
[INFO] |  |  |  |  \- kuaishou:infra-byte-code-enhancer-facade:jar:1.0.47:compile
[INFO] |  |  |  +- kuaishou:infra-byte-code-enhancer-advice-enhancer:jar:1.0.47:compile
[INFO] |  |  |  \- kuaishou:infra-byte-code-enhancer-advice-processor:jar:1.0.47:compile
[INFO] |  |  +- kuaishou:infra-tianwen-meter-sdk:jar:1.0.24:compile
[INFO] |  |  \- kuaishou:infra-patronum-outlier-detection-metric-report-sdk:jar:1.0.8:compile
[INFO] |  |     \- kuaishou:infra-patronum-healthcheck-metrics:jar:1.0.8:compile
[INFO] |  +- kuaishou:infra-framework-notify-cache:jar:1.0.589:compile
[INFO] |  |  \- com.github.phantomthief:zknotify-cache:jar:0.1.26-kwai:compile
[INFO] |  +- kuaishou:infra-framework-platform:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:rpcmonitor-java-client:jar:1.1.29:compile
[INFO] |  |  |  \- com.kuaishou:division-util:jar:0.0.125:compile
[INFO] |  |  +- org.apache.commons:commons-text:jar:1.6:compile
[INFO] |  |  \- com.kuaishou:before-jvm-term-trigger-sdk:jar:1.0.69:compile
[INFO] |  +- kuaishou:infra-framework-grpc:jar:1.0.589:compile
[INFO] |  |  \- kuaishou:infra-framework-kess:jar:1.0.589:compile
[INFO] |  +- kuaishou:infra-framework-perf-impl:jar:2.0.53:compile
[INFO] |  |  +- kuaishou:infra-metrics-core:jar:2.0.53:compile
[INFO] |  |  +- commons-httpclient:commons-httpclient:jar:3.1:compile
[INFO] |  |  \- kuaishou:kobserva-logger:jar:1.0.9:compile
[INFO] |  +- kuaishou:kuaishou-base-proto:jar:1.0.110:compile
[INFO] |  +- kuaishou:kuaishou-webserver-biz-proto:jar:1.0.53:compile
[INFO] |  |  +- kuaishou:kuaishou-common-log-proto:jar:1.0.2995:compile
[INFO] |  |  +- kuaishou:kuaishou-antispam-general-proto:jar:1.0.81:compile
[INFO] |  |  +- kuaishou:kuaishou-post-c2s-proto:jar:1.0.2181:compile
[INFO] |  |  +- kuaishou:kuaishou-music-common-proto:jar:2.0.540:compile
[INFO] |  |  \- kuaishou:kuaishou-profile-proto:jar:1.1.3699:compile
[INFO] |  +- kuaishou:kuaishou-common-definition:jar:1.0.95:compile
[INFO] |  |  \- kuaishou:kuaishou-chs-utils-sdk:jar:1.0.0:compile
[INFO] |  +- kuaishou:kuaishou-common-i18n:jar:1.1.6:compile
[INFO] |  +- kuaishou:kuaishou-cache-setter:jar:1.0.40:compile
[INFO] |  |  +- kuaishou:infra-krpc-server:jar:1.0.152:compile
[INFO] |  |  +- net.javacrumbs.future-converter:future-converter-guava-rxjava2:jar:1.2.0:compile
[INFO] |  |  |  +- net.javacrumbs.future-converter:future-converter-guava-common:jar:1.2.0:compile
[INFO] |  |  |  \- net.javacrumbs.future-converter:future-converter-rxjava2-common:jar:1.2.0:compile
[INFO] |  |  \- io.projectreactor.addons:reactor-adapter:jar:3.2.3.RELEASE:compile
[INFO] |  +- kuaishou:kuaishou-component-util:jar:1.1.52:compile
[INFO] |  |  +- kuaishou:kuaishou-component-sms-util:jar:1.1.52:compile
[INFO] |  |  |  +- com.googlecode.libphonenumber:carrier:jar:1.79:compile
[INFO] |  |  |  |  \- com.googlecode.libphonenumber:prefixmapper:jar:2.89:compile
[INFO] |  |  |  \- kuaishou:kuaishou-web-scope-sdk:jar:1.0.53:compile
[INFO] |  |  |     +- kuaishou:kuaishou-web-scope-passport-sdk:jar:1.2.0:compile
[INFO] |  |  |     \- kuaishou:kuaishou-web-scope-client-request-info-sdk:jar:1.0.53:compile
[INFO] |  |  +- kuaishou:kuaishou-component-cdn-util:jar:1.1.52:compile
[INFO] |  |  +- kuaishou:kuaishou-component-location-util:jar:1.1.52:compile
[INFO] |  |  |  \- kuaishou:kuaishou-web-scope-base-sdk:jar:1.0.53:compile
[INFO] |  |  |     \- eu.bitwalker:UserAgentUtils:jar:1.20:compile
[INFO] |  |  +- kuaishou:kuaishou-component-oversea-util:jar:1.1.52:compile
[INFO] |  |  |  \- kuaishou:kuaishou-idc-dispatch-sdk:jar:1.0.5:compile
[INFO] |  |  +- kuaishou:kuaishou-component-explore-util:jar:1.1.52:compile
[INFO] |  |  +- kuaishou:kuaishou-component-i18n-util:jar:1.1.52:compile
[INFO] |  |  |  \- kuaishou:i18n-platform-sdk:jar:1.0.30:compile
[INFO] |  |  +- kuaishou:kuaishou-component-exptag-util:jar:1.1.52:compile
[INFO] |  |  +- kuaishou:kuaishou-component-upload-util:jar:1.1.52:compile
[INFO] |  |  |  \- kuaishou:kuaishou-upload-sdk:jar:1.1.16:compile
[INFO] |  |  +- kuaishou:kuaishou-component-antispam-util:jar:1.1.52:compile
[INFO] |  |  +- kuaishou:kuaishou-component-arch-util:jar:1.1.52:compile
[INFO] |  |  |  +- kuaishou:infra-sentinel-distributed-ratelimiter:jar:1.0.94:compile
[INFO] |  |  |  +- com.hankcs:aho-corasick-double-array-trie:jar:1.1.0:compile
[INFO] |  |  |  +- kuaishou:zt-client-log-sdk:jar:1.0.24:compile
[INFO] |  |  |  +- com.github.phantomthief:stats-helper:jar:0.2.2:compile
[INFO] |  |  |  +- kuaishou:kuaishou-framework-emoji:jar:1.0.750:compile
[INFO] |  |  |  +- kuaishou:kuaishou-view-builder-basic:jar:1.1.71:compile
[INFO] |  |  |  +- cc.concurrent:mango:jar:1.05:compile
[INFO] |  |  |  +- kuaishou:kuaishou-framework-deprecated:jar:1.0.750:compile
[INFO] |  |  |  +- kuaishou:kuaishou-photo-basic-define:jar:1.0.2472:compile
[INFO] |  |  |  |  +- kuaishou:kuaishou-music-base-sdk:jar:2.0.540:compile
[INFO] |  |  |  |  +- kuaishou:kuaishou-encryptid-sdk:jar:1.1.11:compile
[INFO] |  |  |  |  \- org.mongodb:bson:jar:3.6.3:compile
[INFO] |  |  |  \- com.aayushatharva.brotli4j:brotli4j:jar:1.16.0:compile
[INFO] |  |  |     +- com.aayushatharva.brotli4j:service:jar:1.16.0:compile
[INFO] |  |  |     \- com.aayushatharva.brotli4j:native-osx-x86_64:jar:1.16.0:compile
[INFO] |  |  +- kuaishou:kuaishou-component-user-util:jar:1.1.52:compile
[INFO] |  |  |  +- kuaishou:kuaishou-web-scope-simple-sdk:jar:1.0.53:compile
[INFO] |  |  |  \- kuaishou:kuaishou-web-scope-explore-sdk:jar:1.0.53:compile
[INFO] |  |  \- kuaishou:kuaishou-component-util-common:jar:1.1.52:compile
[INFO] |  +- kuaishou:zt-comment-sdk:jar:1.0.154:compile
[INFO] |  |  \- kuaishou:kuaishou-framework:jar:1.0.750:compile
[INFO] |  |     +- kuaishou:infra-patronum-control-sdk:jar:1.0.6:compile
[INFO] |  |     +- kuaishou:infra-krpc-server-starter:jar:1.0.152:compile
[INFO] |  |     +- kuaishou:kuaishou-scheduler-sdk:jar:1.0.65:compile
[INFO] |  |     +- kuaishou:kuaishou-scheduler-client:jar:1.0.65:compile
[INFO] |  |     |  \- kuaishou:infra-distributed-synchronizer-sdk:jar:1.0.47:compile
[INFO] |  |     +- kuaishou:kuaishou-framework-web:jar:1.0.750:compile
[INFO] |  |     +- kuaishou:kuaishou-framework-logging:jar:1.0.750:compile
[INFO] |  |     +- kuaishou:kuaishou-framework-cache-counter-sdk:jar:1.0.750:compile
[INFO] |  |     +- kuaishou:kuaishou-framework-mongo:jar:1.0.750:compile
[INFO] |  |     +- kuaishou:kuaishou-framework-hdfs:jar:1.0.750:compile
[INFO] |  |     +- com.github.shyiko:mysql-binlog-connector-java:jar:0.13.0:compile
[INFO] |  |     +- org.simpleframework:simple-xml:jar:2.7.1:compile
[INFO] |  |     |  +- stax:stax-api:jar:1.0.1:compile
[INFO] |  |     |  +- stax:stax:jar:1.2.0:compile
[INFO] |  |     |  \- xpp3:xpp3:jar:1.1.3.3:compile
[INFO] |  |     +- cglib:cglib:jar:3.2.10:compile
[INFO] |  |     |  +- org.ow2.asm:asm:jar:9.1:compile
[INFO] |  |     |  \- org.apache.ant:ant:jar:1.10.3:compile
[INFO] |  |     |     \- org.apache.ant:ant-launcher:jar:1.10.3:compile
[INFO] |  |     +- com.cronutils:cron-utils:jar:9.0.1:compile
[INFO] |  |     +- kuaishou:kuaishou-hbase-client:jar:1.0.1:compile
[INFO] |  |     |  \- org.apache.hbase:hbase-shaded-client:jar:1.0.14-kwai:compile
[INFO] |  |     |     +- org.apache.htrace:htrace-core:jar:3.1.0-incubating:compile
[INFO] |  |     |     \- com.github.stephenc.findbugs:findbugs-annotations:jar:1.3.9-1:compile
[INFO] |  |     +- kuaishou:kuaishou-redis-index-client:jar:1.0.2:compile
[INFO] |  |     +- org.apache.hadoop:hadoop-client-api:jar:3.0.0:compile
[INFO] |  |     +- org.apache.hadoop:hadoop-client-runtime:jar:3.0.0:compile
[INFO] |  |     |  \- org.apache.htrace:htrace-core4:jar:4.1.0-incubating:runtime
[INFO] |  |     +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.11.2:compile
[INFO] |  |     +- commons-net:commons-net:jar:ftp:3.6:compile
[INFO] |  |     +- commons-configuration:commons-configuration:jar:1.10:compile
[INFO] |  |     +- com.squareup.okhttp:okhttp:jar:2.7.5:compile
[INFO] |  |     +- com.google.maps:google-maps-services:jar:0.1.15:compile
[INFO] |  |     +- org.mongodb:mongodb-driver:jar:3.6.3:compile
[INFO] |  |     |  \- org.mongodb:mongodb-driver-core:jar:3.6.3:compile
[INFO] |  |     +- org.mongodb:mongodb-driver-async:jar:3.6.3:compile
[INFO] |  |     +- io.reactivex.rxjava2:rxjava:jar:2.2.7:compile
[INFO] |  |     +- org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13:compile
[INFO] |  |     |  \- org.codehaus.jackson:jackson-core-asl:jar:1.9.13:compile
[INFO] |  |     +- io.sentry:sentry-logback:jar:1.7.21:compile
[INFO] |  |     |  \- io.sentry:sentry:jar:1.7.21:compile
[INFO] |  |     +- kuaishou:kuaishou-sentry-reporter:jar:1.0.11:compile
[INFO] |  |     +- io.netty:netty-tcnative-boringssl-static:jar:2.0.26.Final:compile
[INFO] |  |     +- org.eclipse.jetty.alpn:alpn-api:jar:1.1.3.v20160715:compile
[INFO] |  |     +- com.oracle.jdbc:ojdbc8:jar:********:compile
[INFO] |  |     +- com.github.phantomthief:adaptive-executor:jar:0.1.4:compile
[INFO] |  |     +- com.github.phantomthief:jedis-helper:jar:0.1.15:compile
[INFO] |  |     +- com.github.phantomthief:model-view-builder:jar:1.2.52:compile
[INFO] |  |     +- io.crate:crate-**********************
[INFO] |  |     |  +- com.github.dblock.waffle:waffle-jna:jar:1.7.5:compile
[INFO] |  |     |  +- net.java.dev.jna:jna:jar:4.2.1:compile
[INFO] |  |     |  +- net.java.dev.jna:jna-platform:jar:4.2.1:compile
[INFO] |  |     |  +- org.osgi:org.osgi.enterprise:jar:4.2.0:compile
[INFO] |  |     |  \- org.osgi:org.osgi.core:jar:4.3.1:compile
[INFO] |  |     +- org.jfaster:mango:jar:1.6.1:compile
[INFO] |  |     +- com.alibaba:druid:jar:1.1.14:compile
[INFO] |  |     +- kuaishou:infra-framework-memcached-deprecated-sdk:jar:1.0.589:compile
[INFO] |  |     +- kuaishou:infra-databus-sdk:jar:1.0.71:compile
[INFO] |  |     +- kuaishou:infra-sentinel-metrics-reporter:jar:1.0.94:compile
[INFO] |  |     |  \- net.jpountz.lz4:lz4:jar:1.3.0:compile
[INFO] |  |     +- kuaishou:ktrace-web-support:jar:1.0.253:compile
[INFO] |  |     \- kuaishou:infra-patronum-stresstest-logger:jar:1.0.21:compile
[INFO] |  \- kuaishou:kuaishou-intown-json-sdk:jar:1.0.56:compile
[INFO] +- net.sourceforge.jeval:jeval:jar:0.9.4:compile
[INFO] +- kuaishou:infra-framework-kafka:jar:1.0.589:compile
[INFO] |  +- kuaishou:infra-framework-kafka-producer:jar:1.0.589:compile
[INFO] |  |  \- kuaishou:infra-config-adaptor-facade:jar:1.0.53:compile
[INFO] |  +- kuaishou:infra-config-adaptor-core:jar:1.0.53:compile
[INFO] |  +- kuaishou:kuaishou-env-utils:jar:1.0.12:compile
[INFO] |  +- com.kuaishou:service-lifecycle-sdk:jar:1.0.25:compile
[INFO] |  +- kuaishou:infra-framework-loadingcache:jar:1.0.589:compile
[INFO] |  +- kuaishou:infra-framework-lane:jar:1.0.589:compile
[INFO] |  +- kuaishou:krpc-registry-internal-sdk:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-registry-sdk:jar:1.0.198:compile
[INFO] |  |  \- kuaishou:krpc-registry-kns:jar:1.0.198:compile
[INFO] |  +- com.google.guava:guava:jar:28.1-jre-kwai5:compile
[INFO] |  |  +- com.google.guava:failureaccess:jar:1.0.1:compile
[INFO] |  |  +- com.google.guava:listenablefuture:jar:9999.0-empty-to-avoid-conflict-with-guava:compile
[INFO] |  |  +- org.checkerframework:checker-qual:jar:2.8.1:compile
[INFO] |  |  +- com.google.errorprone:error_prone_annotations:jar:2.3.3:compile
[INFO] |  |  +- com.google.j2objc:j2objc-annotations:jar:1.3:compile
[INFO] |  |  \- org.codehaus.mojo:animal-sniffer-annotations:jar:1.18:compile
[INFO] |  +- org.apache.kafka:kafka-clients:jar:0.10.2.1U5.2.58:compile
[INFO] |  |  +- org.xerial.snappy:snappy-java:jar:1.1.7.1:compile
[INFO] |  |  +- com.github.luben:zstd-jni:jar:1.5.5-10:compile
[INFO] |  |  \- com.github.ben-manes.caffeine:caffeine:jar:2.9.1:compile
[INFO] |  +- org.apache.kafka:kafka_2.11:jar:0.11.0.4-COMPAT:compile
[INFO] |  |  +- com.yammer.metrics:metrics-core:jar:2.2.0:compile
[INFO] |  |  +- org.scala-lang:scala-library:jar:2.11.11:compile
[INFO] |  |  +- com.101tec:zkclient:jar:0.10:compile
[INFO] |  |  +- org.apache.zookeeper:zookeeper:jar:3.5.7:compile
[INFO] |  |  |  +- org.apache.zookeeper:zookeeper-jute:jar:3.5.7:compile
[INFO] |  |  |  +- org.apache.yetus:audience-annotations:jar:0.5.0:compile
[INFO] |  |  |  \- io.netty:netty-transport-native-epoll:jar:4.1.38.Final:compile
[INFO] |  |  |     \- io.netty:netty-transport-native-unix-common:jar:4.1.38.Final:compile
[INFO] |  |  \- org.scala-lang.modules:scala-parser-combinators_2.11:jar:1.0.4:compile
[INFO] |  +- commons-cli:commons-cli:jar:1.4:compile
[INFO] |  +- kuaishou:ktrace-core:jar:1.0.253:compile
[INFO] |  |  +- kuaishou:ktrace-config:jar:1.0.253:compile
[INFO] |  |  +- com.lmax:disruptor:jar:3.3.7:compile
[INFO] |  |  \- com.kwai.jdk:snowman-api:jar:1.0.31:compile
[INFO] |  +- kuaishou:infra-framework-perf:jar:2.0.53:compile
[INFO] |  |  \- kuaishou:infra-patronum-governance-spi-sdk:jar:1.0.36:compile
[INFO] |  +- kuaishou:infra-kws-tool:jar:1.0.53:compile
[INFO] |  +- org.jetbrains.kotlin:kotlin-stdlib:jar:1.3.72:compile
[INFO] |  |  \- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.3.72:compile
[INFO] |  \- kuaishou:infra-sentinel-quota-client:jar:1.0.94:compile
[INFO] |     +- kuaishou:infra-sentinel-core:jar:1.0.94:compile
[INFO] |     \- kuaishou:infra-sentinel-quota-common:jar:1.0.94:compile
[INFO] +- kuaishou:kuaishou-kconf-client:jar:1.0.122:compile
[INFO] |  \- kuaishou:kuaishou-kconf-common:jar:1.0.144:compile
[INFO] +- kuaishou:infra-framework-datasource:jar:1.0.589:compile
[INFO] |  +- kuaishou:infra-sentinel-retryer:jar:1.0.94:compile
[INFO] |  +- org.springframework:spring-*******************************
[INFO] |  |  \- org.springframework:spring-tx:jar:5.1.10-kwai-12:compile
[INFO] |  +- kuaishou:infra-config-adaptor-recipes:jar:1.0.53:compile
[INFO] |  +- kuaishou:kuaishou-env-util:jar:1.0.277:compile
[INFO] |  +- dom4j:dom4j:jar:1.6.1:compile
[INFO] |  +- com.github.phantomthief:zkconfig-resources:jar:1.1.29:compile
[INFO] |  |  \- org.apache.curator:curator-recipes:jar:*******:compile
[INFO] |  +- com.zaxxer:HikariCP:jar:*******-kwai:compile
[INFO] |  +- io.dropwizard.metrics:metrics-core:jar:3.2.5:compile
[INFO] |  +- mysql:mysql-connector-java:jar:5.1.46:compile
[INFO] |  +- org.apache.shardingsphere:sharding-jdbc-core:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:shardingsphere-pluggable:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:shardingsphere-sql-parser-binder:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:shardingsphere-route:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |  \- org.apache.shardingsphere:shardingsphere-common:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:shardingsphere-rewrite-engine:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:shardingsphere-executor:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  \- org.apache.shardingsphere:shardingsphere-merge:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:sharding-transaction-core:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:shardingsphere-sql-parser-mysql:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  \- org.apache.shardingsphere:shardingsphere-sql-parser-engine:jar:4.1.1-kwai-28:compile
[INFO] |  |  |     +- org.apache.shardingsphere:shardingsphere-spi:jar:4.1.1-kwai-28:compile
[INFO] |  |  |     +- org.apache.shardingsphere:shardingsphere-sql-parser-spi:jar:4.1.1-kwai-28:compile
[INFO] |  |  |     +- org.apache.shardingsphere:shardingsphere-sql-parser-statement:jar:4.1.1-kwai-28:compile
[INFO] |  |  |     \- org.antlr:antlr4-runtime:jar:4.7.2:compile
[INFO] |  |  +- org.apache.shardingsphere:sharding-core-route:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  \- org.apache.shardingsphere:sharding-core-common:jar:4.1.1-kwai-28:compile
[INFO] |  |  |     \- org.codehaus.groovy:groovy:jar:indy:3.0.11:compile
[INFO] |  |  +- org.apache.shardingsphere:master-slave-core-route:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  \- org.apache.shardingsphere:sharding-core-api:jar:4.1.1-kwai-28:compile
[INFO] |  |  |     \- org.apache.shardingsphere:encrypt-core-api:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:sharding-core-rewrite:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:encrypt-core-rewrite:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:shadow-core-rewrite:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:sharding-core-execute:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- org.apache.shardingsphere:sharding-core-merge:jar:4.1.1-kwai-28:compile
[INFO] |  |  \- org.apache.shardingsphere:encrypt-core-merge:jar:4.1.1-kwai-28:compile
[INFO] |  |     \- org.apache.shardingsphere:encrypt-core-common:jar:4.1.1-kwai-28:compile
[INFO] |  +- kuaishou:infra-keycenter-client:jar:1.1.76:compile
[INFO] |  |  +- kuaishou:infra-common-falcon-counter:jar:2.0.41:compile
[INFO] |  |  +- kuaishou:infra-keycenter-security:jar:1.1.76:compile
[INFO] |  |  |  \- kuaishou:infra-keycenter-proto:jar:1.1.76:compile
[INFO] |  |  +- kuaishou:infra-keycenter-store-sdk:jar:1.1.76:compile
[INFO] |  |  \- kuaishou:infra-keycenter-grpc-config:jar:1.1.76:compile
[INFO] |  +- kuaishou:infra-patronum-governance-sdk:jar:1.0.36:compile
[INFO] |  +- kuaishou:infra-patronum-traffic-record-spi-sdk:jar:1.0.88:compile
[INFO] |  |  \- kuaishou:infra-patronum-traffic-management-metadata:jar:1.0.88:compile
[INFO] |  \- kuaishou:infra-config-report-sdk:jar:1.0.4:compile
[INFO] +- org.mybatis:mybatis:jar:3.5.7:compile
[INFO] +- com.baomidou:mybatis-plus-core:jar:3.5.1:compile
[INFO] |  \- com.github.jsqlparser:jsqlparser:jar:4.3:compile
[INFO] +- com.baomidou:mybatis-plus-annotation:jar:3.5.1:compile
[INFO] +- kuaishou:kuaishou-kconf-server-api:jar:1.0.144:compile
[INFO] |  \- kuaishou:infra-framework-http:jar:1.0.589:compile
[INFO] |     +- com.squareup.retrofit2:adapter-guava:jar:2.6.0:compile
[INFO] |     +- com.squareup.retrofit2:converter-jackson:jar:2.6.0:compile
[INFO] |     +- com.squareup.retrofit2:converter-protobuf:jar:2.6.0:compile
[INFO] |     +- com.squareup.retrofit2:converter-simplexml:jar:2.6.0:compile
[INFO] |     +- com.squareup.retrofit:retrofit:jar:1.9.0:compile
[INFO] |     +- com.squareup.retrofit:converter-jackson:jar:1.9.0:compile
[INFO] |     \- com.squareup.retrofit:converter-simplexml:jar:1.9.0:compile
[INFO] +- com.baomidou:mybatis-plus-extension:jar:3.5.1:compile
[INFO] |  \- org.mybatis:mybatis-spring:jar:2.0.6:compile
[INFO] +- kuaishou:infra-framework-redis:jar:1.2.103:compile
[INFO] |  +- kuaishou:infra-framework-redis-api:jar:1.2.103:compile
[INFO] |  +- kuaishou:infra-redis-client-core:jar:1.2.103:compile
[INFO] |  |  +- kuaishou:infra-redis-client-facade:jar:1.2.103:compile
[INFO] |  |  +- kuaishou:infra-sentinel-redis:jar:1.0.94:compile
[INFO] |  |  |  \- kuaishou:infra-sentinel-parameter-ratelimiter:jar:1.0.94:compile
[INFO] |  |  \- com.github.jnr:jnr-unixsocket:jar:0.38.17:compile
[INFO] |  |     +- com.github.jnr:jnr-ffi:jar:2.2.11:compile
[INFO] |  |     |  +- com.github.jnr:jffi:jar:1.3.9:compile
[INFO] |  |     |  +- com.github.jnr:jffi:jar:native:1.3.9:runtime
[INFO] |  |     |  +- org.ow2.asm:asm-commons:jar:9.1:compile
[INFO] |  |     |  +- org.ow2.asm:asm-analysis:jar:9.1:compile
[INFO] |  |     |  +- org.ow2.asm:asm-tree:jar:9.1:compile
[INFO] |  |     |  +- org.ow2.asm:asm-util:jar:9.1:compile
[INFO] |  |     |  +- com.github.jnr:jnr-a64asm:jar:1.0.0:compile
[INFO] |  |     |  \- com.github.jnr:jnr-x86asm:jar:1.0.2:compile
[INFO] |  |     +- com.github.jnr:jnr-constants:jar:0.10.3:compile
[INFO] |  |     +- com.github.jnr:jnr-enxio:jar:0.32.13:compile
[INFO] |  |     \- com.github.jnr:jnr-posix:jar:3.1.15:compile
[INFO] |  +- kuaishou:infra-sentinel-throttling:jar:1.0.94:compile
[INFO] |  |  +- net.spy:spymemcached:jar:2.12.3.28-kwai:compile
[INFO] |  |  +- io.github.resilience4j:resilience4j-circuitbreaker:jar:1.2.0:compile
[INFO] |  |  |  \- io.vavr:vavr:jar:0.10.0:compile
[INFO] |  |  |     \- io.vavr:vavr-match:jar:0.10.0:compile
[INFO] |  |  \- kuaishou:infra-sentinel-metrics:jar:1.0.94:compile
[INFO] |  +- org.apache.curator:curator-client:jar:*******:compile
[INFO] |  +- redis.clients:jedis:jar:2.9.1-kwai-9:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.6.1:compile
[INFO] |  +- io.lettuce:lettuce-core:jar:5.2.0-kwai-5:compile
[INFO] |  |  +- io.netty:netty-common:jar:4.1.38.Final:compile
[INFO] |  |  +- io.netty:netty-handler:jar:4.1.38.Final:compile
[INFO] |  |  |  +- io.netty:netty-buffer:jar:4.1.38.Final:compile
[INFO] |  |  |  \- io.netty:netty-codec:jar:4.1.38.Final:compile
[INFO] |  |  +- io.netty:netty-transport:jar:4.1.38.Final:compile
[INFO] |  |  |  \- io.netty:netty-resolver:jar:4.1.38.Final:compile
[INFO] |  |  \- io.projectreactor:reactor-core:jar:3.2.11.RELEASE:compile
[INFO] |  |     \- org.reactivestreams:reactive-streams:jar:1.0.2:compile
[INFO] |  +- kuaishou:infra-sentinel-circuitbreaker:jar:1.0.94:compile
[INFO] |  |  +- io.github.resilience4j:resilience4j-core:jar:1.2.0:compile
[INFO] |  |  \- io.github.resilience4j:resilience4j-reactor:jar:1.2.0:compile
[INFO] |  +- com.kohlschutter.junixsocket:junixsocket-core:jar:2.3.2-with-buffer:compile
[INFO] |  |  +- com.kohlschutter.junixsocket:junixsocket-native-common:jar:2.3.2-with-buffer:compile
[INFO] |  |  \- com.kohlschutter.junixsocket:junixsocket-common:jar:2.3.2-with-buffer:compile
[INFO] |  +- com.caucho:hessian:jar:4.0.62:compile
[INFO] |  +- com.github.phantomthief:cursor-iterator:jar:1.0.13:compile
[INFO] |  \- com.kuaishou:gaea-agent-external-bootstrap:jar:1.0.1:compile
[INFO] +- kuaishou:kat-framework:jar:1.0.0:compile
[INFO] |  +- org.junit.platform:junit-platform-console:jar:1.7.0-M1:compile
[INFO] |  |  \- org.junit.platform:junit-platform-reporting:jar:1.7.0-M1:compile
[INFO] |  +- org.junit.platform:junit-platform-commons:jar:1.7.0-M1:compile
[INFO] |  +- org.assertj:assertj-core:jar:3.16.1:test
[INFO] |  +- com.squareup.okio:okio:jar:1.17.4:compile
[INFO] |  +- com.github.fge:json-schema-validator:jar:2.2.6:compile
[INFO] |  |  +- joda-time:joda-time:jar:2.9.9:compile
[INFO] |  |  +- com.googlecode.libphonenumber:libphonenumber:jar:8.12.54:compile
[INFO] |  |  +- com.github.fge:json-schema-core:jar:1.2.5:compile
[INFO] |  |  |  +- com.github.fge:uri-template:jar:0.9:compile
[INFO] |  |  |  |  \- com.github.fge:msg-simple:jar:1.1:compile
[INFO] |  |  |  |     \- com.github.fge:btf:jar:1.2:compile
[INFO] |  |  |  +- com.github.fge:jackson-coreutils:jar:1.8:compile
[INFO] |  |  |  \- org.mozilla:rhino:jar:1.7R4:compile
[INFO] |  |  \- javax.mail:mailapi:jar:1.4.3:compile
[INFO] |  |     \- javax.activation:activation:jar:1.1.1:compile
[INFO] |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.9.10:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.9.10:compile
[INFO] |  +- commons-codec:commons-codec:jar:1.10:compile
[INFO] |  +- com.belerweb:pinyin4j:jar:2.5.0:compile
[INFO] |  +- com.jayway.jsonpath:json-path:jar:2.4.0:compile
[INFO] |  |  \- net.minidev:json-smart:jar:2.3:compile
[INFO] |  |     \- net.minidev:accessors-smart:jar:1.2:compile
[INFO] |  +- net.sf.json-lib:json-lib:jar:jdk15:2.4:compile
[INFO] |  |  +- commons-logging:commons-logging:jar:1.2:compile
[INFO] |  |  \- net.sf.ezmorph:ezmorph:jar:1.0.6:compile
[INFO] |  +- kuaishou:kuaishou-common-encryption:jar:1.0.48:compile
[INFO] |  +- kuaishou:kuaishou-common-socket:jar:1.0.14:compile
[INFO] |  |  \- io.netty:netty-all:jar:4.1.38.Final:compile
[INFO] |  +- com.opencsv:opencsv:jar:3.7:compile
[INFO] |  +- kuaishou:infra-framework-memcached:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-jni-memcached-client:jar:1.38:compile
[INFO] |  |  \- com.github.vladimir-bukhtoyarov:bucket4j-core:jar:6.0.1:compile
[INFO] |  +- kuaishou:kuaishou-common-proto:jar:1.0.8574:compile
[INFO] |  |  +- kuaishou:kuaishou-app-logsdk-proto:jar:1.0.37:compile
[INFO] |  |  +- kuaishou:kuaishou-recruit-c2s-proto:jar:1.0.0:compile
[INFO] |  |  +- kuaishou:kuaishou-industry-c2s-proto:jar:1.0.1:compile
[INFO] |  |  +- kuaishou:kuaishou-live-base-c2s-proto:jar:1.0.1168:compile
[INFO] |  |  +- kuaishou:kuaishou-message-c2s-proto:jar:1.0.10:compile
[INFO] |  |  +- kuaishou:kuaishou-social-c2s-proto:jar:1.0.201:compile
[INFO] |  |  +- kuaishou:kuaishou-interaction-c2s-client-push-proto:jar:1.0.5:compile
[INFO] |  |  |  \- com.android.support:support-annotations:jar:23.2.0:compile
[INFO] |  |  +- kuaishou:kuaishou-common-proto-split-reco-proto:jar:1.0.9:compile
[INFO] |  |  +- kuaishou:kuaishou-oversea-c2s-proto:jar:1.0.6:compile
[INFO] |  |  +- kuaishou:kuaishou-livemate-c2s-proto:jar:1.0.682:compile
[INFO] |  |  +- kuaishou:kuaishou-gamezone-c2s-proto:jar:1.0.324:compile
[INFO] |  |  +- kuaishou:kuaishou-lbs-c2s-proto:jar:1.0.27:compile
[INFO] |  |  +- kuaishou:kuaishou-video-c2s-proto:jar:1.0.0:compile
[INFO] |  |  +- kuaishou:kuaishou-search-c2s-proto:jar:1.0.34:compile
[INFO] |  |  +- kuaishou:kswitch-proto:jar:1.1.332:compile
[INFO] |  |  +- kuaishou:kuaishou-location-c2s-proto:jar:1.0.1:compile
[INFO] |  |  +- kuaishou:kuaishou-merchant-c2s-proto:jar:1.0.502:compile
[INFO] |  |  +- kuaishou:kuaishou-ad-c2s-proto:jar:1.0.109:compile
[INFO] |  |  \- kuaishou:kuaishou-live-c2s-proto:jar:1.0.1694:compile
[INFO] |  +- kuaishou:kat-platform-sdk:jar:1.0.0-master-BETA-SNAPSHOT:compile
[INFO] |  +- kuaishou:kat-tools-sdk:jar:1.0.0-master-BETA-SNAPSHOT:compile
[INFO] |  +- kuaishou:kat-data-statistics-sdk:jar:1.0.13-master-BETA-SNAPSHOT:compile
[INFO] |  |  +- com.kuaishou.infra.boot:ks-boot-starter-web:jar:1.4.78:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-web:jar:2.1.9.RELEASE:compile
[INFO] |  |  |  |  +- org.springframework.boot:spring-boot-starter-json:jar:2.1.9.RELEASE:compile
[INFO] |  |  |  |  |  \- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.9.10:compile
[INFO] |  |  |  |  \- org.springframework.boot:spring-boot-starter-tomcat:jar:2.1.9.RELEASE:compile
[INFO] |  |  |  |     +- org.apache.tomcat.embed:tomcat-embed-core:jar:9.0.40:compile
[INFO] |  |  |  |     +- org.apache.tomcat.embed:tomcat-embed-el:jar:9.0.40:compile
[INFO] |  |  |  |     \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:9.0.40:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-aop:jar:2.1.9.RELEASE:compile
[INFO] |  |  |  +- com.kuaishou.infra.boot:ks-boot-web:jar:1.4.78:compile
[INFO] |  |  |  \- com.kuaishou.infra.boot:ks-boot-starter-logging-access:jar:1.4.78:compile
[INFO] |  |  |     \- ch.qos.logback:logback-access:jar:1.2.10:compile
[INFO] |  |  +- com.kuaishou.infra.boot:ks-boot-starter-actuator:jar:1.4.78:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-actuator:jar:2.1.9.RELEASE:compile
[INFO] |  |  |  |  +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:2.1.9.RELEASE:compile
[INFO] |  |  |  |  |  \- org.springframework.boot:spring-boot-actuator:jar:2.1.9.RELEASE:compile
[INFO] |  |  |  |  \- io.micrometer:micrometer-core:jar:1.1.7:compile
[INFO] |  |  |  |     \- org.latencyutils:LatencyUtils:jar:2.0.3:compile
[INFO] |  |  |  \- com.kuaishou.infra.boot:ks-boot-actuator-autoconfigure:jar:1.4.78:compile
[INFO] |  |  |     \- com.kuaishou.infra.boot:ks-boot-actuator:jar:1.4.78:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-test:jar:2.1.9.RELEASE:test
[INFO] |  |  |  +- org.springframework.boot:spring-boot-test-autoconfigure:jar:2.1.9.RELEASE:test
[INFO] |  |  |  \- org.xmlunit:xmlunit-core:jar:2.6.3:test
[INFO] |  |  +- org.skyscreamer:jsonassert:jar:1.5.0:compile
[INFO] |  |  +- org.apache.maven.shared:maven-invoker:jar:3.0.1:compile
[INFO] |  |  |  +- org.apache.maven.shared:maven-shared-utils:jar:3.2.1:compile
[INFO] |  |  |  \- org.codehaus.plexus:plexus-component-annotations:jar:1.7.1:compile
[INFO] |  |  \- io.rest-assured:rest-assured:jar:4.2.0:compile
[INFO] |  |     +- org.hamcrest:hamcrest:jar:2.1:compile
[INFO] |  |     +- io.rest-assured:json-path:jar:4.2.0:compile
[INFO] |  |     |  \- io.rest-assured:rest-assured-common:jar:4.2.0:compile
[INFO] |  |     \- io.rest-assured:xml-path:jar:4.2.0:compile
[INFO] |  |        +- jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.2:compile
[INFO] |  |        |  \- jakarta.activation:jakarta.activation-api:jar:1.2.1:compile
[INFO] |  |        +- com.sun.xml.bind:jaxb-osgi:jar:2.3.0.1:compile
[INFO] |  |        \- org.apache.sling:org.apache.sling.javax.activation:jar:0.1.0:compile
[INFO] |  +- org.jsoup:jsoup:jar:1.13.1:compile
[INFO] |  +- com.github.stefanbirkner:system-rules:jar:1.19.0:compile
[INFO] |  +- io.grpc:grpc-all:jar:1.16.1:compile
[INFO] |  |  +- io.grpc:grpc-auth:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  +- com.google.auth:google-auth-library-credentials:jar:0.22.2:compile
[INFO] |  |  |  \- io.opencensus:opencensus-api:jar:0.21.0:runtime
[INFO] |  |  +- io.grpc:grpc-netty:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  +- io.netty:netty-codec-http2:jar:4.1.38.Final:compile
[INFO] |  |  |  \- io.netty:netty-handler-proxy:jar:4.1.38.Final:compile
[INFO] |  |  |     \- io.netty:netty-codec-socks:jar:4.1.38.Final:compile
[INFO] |  |  +- io.grpc:grpc-okhttp:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  +- io.grpc:grpc-protobuf-nano:jar:1.21.0:compile
[INFO] |  |  \- io.grpc:grpc-testing:jar:1.16.1:compile
[INFO] |  +- com.google.protobuf:protobuf-java-util:jar:3.16.1:compile
[INFO] |  +- org.codehaus.plexus:plexus-classworlds:jar:2.6.0:compile
[INFO] |  +- org.sonatype.plexus:plexus-cipher:jar:1.7:compile
[INFO] |  +- org.codehaus.plexus:plexus-utils:jar:3.3.0:compile
[INFO] |  +- io.grpc:grpc-api:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  \- io.grpc:grpc-context:jar:1.36.1-kwai-1.1:compile
[INFO] |  +- com.kuaishou.dp:one-service-rpc-client:jar:1.0.989:compile
[INFO] |  |  +- dp.kwaicat.sdk.prod:one-service-0:jar:1.102.684:compile
[INFO] |  |  +- dp.kwaicat.sdk.prod:one-service-1:jar:1.102.473:compile
[INFO] |  |  +- dp.kwaicat.sdk.prod:one-service-2:jar:1.102.510:compile
[INFO] |  |  +- dp.kwaicat.sdk.prod:one-service-3:jar:1.102.96:compile
[INFO] |  |  +- com.kuaishou.dp:one-service-rpc-sql-client:jar:1.0.905:compile
[INFO] |  |  |  +- com.kuaishou.dp:one-service-common:jar:1.0.905:compile
[INFO] |  |  |  |  +- com.kuaishou.bigdata:compression:jar:1.0.8:compile
[INFO] |  |  |  |  |  +- com.kuaishou.bigdata:core:jar:1.0.8:compile
[INFO] |  |  |  |  |  \- org.anarres.lzo:lzo-core:jar:1.0.5:compile
[INFO] |  |  |  |  +- com.kuaishou.dp:one-service-proto:jar:1.0.905:compile
[INFO] |  |  |  |  +- com.kuaishou.dp:one-service-client-throttling:jar:1.0.905:compile
[INFO] |  |  |  |  \- kuaishou:infra-unified-device-fingerprint-sdk:jar:1.0.55:compile
[INFO] |  |  |  |     \- kuaishou:infra-unified-device-fingerprint-proto:jar:1.0.75:compile
[INFO] |  |  |  \- com.kuaishou.dp:key-collector-manager:jar:1.0.905:compile
[INFO] |  |  \- com.kuaishou.dp:one-service-rpc-unified-client:jar:1.0.972:compile
[INFO] |  |     +- com.kuaishou.dp:one-service-direct-connect-client:jar:1.0.989:compile
[INFO] |  |     +- com.kuaishou.dp:havok-hbase:jar:1.0.972:compile
[INFO] |  |     +- com.kuaishou.dp:havok-redis:jar:1.0.972:compile
[INFO] |  |     |  +- io.netty:netty-transport-native-unix-common:jar:linux-x86_64:4.1.38.Final:compile
[INFO] |  |     |  \- io.netty:netty-transport-native-unix-common:jar:osx-x86_64:4.1.38.Final:compile
[INFO] |  |     +- com.kuaishou.dp:havok-memcache:jar:1.0.972:compile
[INFO] |  |     +- com.kuaishou.dp:havok-core:jar:1.0.972:compile
[INFO] |  |     +- com.kuaishou.dp:havok-plugins:jar:1.0.972:compile
[INFO] |  |     \- com.github.davidmoten:geo:jar:0.7.6:compile
[INFO] |  |        \- com.github.davidmoten:grumpy-core:jar:0.2.3:compile
[INFO] |  +- kuaishou:kat-commons:jar:1.0.5:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-json-org:jar:2.9.10:compile
[INFO] |  |  |  \- org.json:json:jar:20160212:compile
[INFO] |  |  +- kuaishou:kuaishou-redis-shard-client-util:jar:1.0.34:compile
[INFO] |  |  |  \- kuaishou:kuaishou-redis-single-az-util:jar:1.0.18:compile
[INFO] |  |  \- org.eclipse.jgit:org.eclipse.jgit:jar:5.9.0.202009080501-r:compile
[INFO] |  |     \- com.googlecode.javaewah:JavaEWAH:jar:1.1.7:compile
[INFO] |  +- kuaishou:kat-commons2:jar:1.0-master-BETA-SNAPSHOT:compile
[INFO] |  +- kuaishou:infra-krpc-admin-sdk:jar:1.0.18:compile
[INFO] |  +- kuaishou:kat-utils:jar:1.0.0-master-BETA-SNAPSHOT:compile
[INFO] |  |  +- org.apache.maven:maven-core:jar:3.6.3:compile
[INFO] |  |  |  +- org.apache.maven:maven-model:jar:3.6.3:compile
[INFO] |  |  |  +- org.apache.maven:maven-settings:jar:3.6.3:compile
[INFO] |  |  |  +- org.apache.maven:maven-settings-builder:jar:3.6.3:compile
[INFO] |  |  |  +- org.apache.maven:maven-builder-support:jar:3.6.3:compile
[INFO] |  |  |  +- org.apache.maven:maven-repository-metadata:jar:3.6.3:compile
[INFO] |  |  |  +- org.apache.maven:maven-artifact:jar:3.6.3:compile
[INFO] |  |  |  +- org.apache.maven:maven-plugin-api:jar:3.6.3:compile
[INFO] |  |  |  +- org.apache.maven:maven-model-builder:jar:3.6.3:compile
[INFO] |  |  |  +- org.apache.maven:maven-resolver-provider:jar:3.6.3:compile
[INFO] |  |  |  +- org.apache.maven.resolver:maven-resolver-impl:jar:1.4.1:compile
[INFO] |  |  |  +- org.apache.maven.resolver:maven-resolver-api:jar:1.4.1:compile
[INFO] |  |  |  +- org.apache.maven.resolver:maven-resolver-spi:jar:1.4.1:compile
[INFO] |  |  |  +- org.apache.maven.resolver:maven-resolver-util:jar:1.4.1:compile
[INFO] |  |  |  +- org.eclipse.sisu:org.eclipse.sisu.plexus:jar:0.3.4:compile
[INFO] |  |  |  |  \- javax.enterprise:cdi-api:jar:1.0:compile
[INFO] |  |  |  +- org.eclipse.sisu:org.eclipse.sisu.inject:jar:0.3.4:compile
[INFO] |  |  |  +- com.google.inject:guice:jar:no_aop:4.2.1:compile
[INFO] |  |  |  |  \- aopalliance:aopalliance:jar:1.0:compile
[INFO] |  |  |  \- javax.inject:javax.inject:jar:1:compile
[INFO] |  |  +- org.apache.maven:maven-compat:jar:3.6.3:compile
[INFO] |  |  |  +- org.codehaus.plexus:plexus-interpolation:jar:1.25:compile
[INFO] |  |  |  \- org.apache.maven.wagon:wagon-provider-api:jar:3.3.4:compile
[INFO] |  |  +- org.apache.maven.resolver:maven-resolver-connector-basic:jar:1.4.1:compile
[INFO] |  |  +- org.apache.maven.resolver:maven-resolver-transport-http:jar:1.4.1:compile
[INFO] |  |  +- org.apache.maven:maven-embedder:jar:3.6.3:compile
[INFO] |  |  |  \- org.sonatype.plexus:plexus-sec-dispatcher:jar:1.4:compile
[INFO] |  |  \- com.alibaba:transmittable-thread-local:jar:2.9.0:compile
[INFO] |  +- kuaishou:kat-reported-data-sdk:jar:1.0.0-master-BETA-SNAPSHOT:compile
[INFO] |  \- kuaishou:kat-alarm-service:jar:1.0.0-master-BETA-SNAPSHOT:compile
[INFO] +- kuaishou:infra-framework-cache-facade:jar:1.0.589:compile
[INFO] +- kuaishou:infra-framework-cache-impl:jar:1.0.589:compile
[INFO] |  \- kuaishou:infra-kws-sdk:jar:1.0.53:compile
[INFO] +- kuaishou:infra-framework-cache-redis-impl:jar:1.0.476:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.30:provided
[INFO] +- org.python:jython-standalone:jar:2.7.0:compile
[INFO] +- javax.validation:validation-api:jar:2.0.1.Final:compile
[INFO] +- org.hibernate.validator:hibernate-validator:jar:6.0.13.Final:compile
[INFO] |  +- org.jboss.logging:jboss-logging:jar:3.3.2.Final:compile
[INFO] |  \- com.fasterxml:classmate:jar:1.3.4:compile
[INFO] +- com.kuaishou.infra.boot:ks-boot-***********************
[INFO] |  \- org.springframework:spring-aop:jar:5.1.10-kwai-12:compile
[INFO] +- kuaishou:kwaishop-kdump-caelus-center-client:jar:1.0.14-feature_T4977874_fix_compensation-BETA-SNAPSHOT:compile
[INFO] |  +- kuaishou:kuaishou-es:jar:1.0.140:compile
[INFO] |  |  +- org.elasticsearch:elasticsearch:jar:2.3.1:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-core:jar:5.5.0:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-backward-codecs:jar:5.5.0:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-analyzers-common:jar:5.5.0:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-queries:jar:5.5.0:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-memory:jar:5.5.0:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-highlighter:jar:5.5.0:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-queryparser:jar:5.5.0:compile
[INFO] |  |  |  |  \- org.apache.lucene:lucene-sandbox:jar:5.5.0:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-suggest:jar:5.5.0:compile
[INFO] |  |  |  |  \- org.apache.lucene:lucene-misc:jar:5.5.0:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-join:jar:5.5.0:compile
[INFO] |  |  |  |  \- org.apache.lucene:lucene-grouping:jar:5.5.0:compile
[INFO] |  |  |  +- org.apache.lucene:lucene-spatial:jar:5.5.0:compile
[INFO] |  |  |  |  +- org.apache.lucene:lucene-spatial3d:jar:5.5.0:compile
[INFO] |  |  |  |  \- com.spatial4j:spatial4j:jar:0.5:compile
[INFO] |  |  |  +- org.elasticsearch:securesm:jar:1.0:compile
[INFO] |  |  |  +- org.joda:joda-convert:jar:1.2:compile
[INFO] |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-smile:jar:2.9.10:compile
[INFO] |  |  |  +- com.ning:compress-lzf:jar:1.0.3:compile
[INFO] |  |  |  +- com.tdunning:t-digest:jar:3.0:compile
[INFO] |  |  |  +- org.hdrhistogram:HdrHistogram:jar:2.1.6:compile
[INFO] |  |  |  \- com.twitter:jsr166e:jar:1.1.0:compile
[INFO] |  |  \- org.elasticsearch.client:elasticsearch-rest-client:jar:6.7.2:compile
[INFO] |  |     +- org.apache.httpcomponents:httpasyncclient:jar:4.1.2:compile
[INFO] |  |     \- org.apache.httpcomponents:httpcore-nio:jar:4.4.11:compile
[INFO] |  \- kuaishou:kwaishop-kdump-caelus-center-common:jar:1.0.14-feature_T4977874_fix_compensation-BETA-SNAPSHOT:compile
[INFO] |     \- com.github.os72:protoc-jar:jar:3.11.4:compile
[INFO] +- com.kuaishou:openapi-retrofit2-sdk:jar:1.0.0-RELEASE:compile
[INFO] |  +- io.swagger:swagger-annotations:jar:1.5.18:compile
[INFO] |  +- com.squareup.retrofit2:converter-gson:jar:2.6.0:compile
[INFO] |  +- com.squareup.retrofit2:retrofit:jar:2.6.0:compile
[INFO] |  +- com.squareup.retrofit2:converter-scalars:jar:2.6.0:compile
[INFO] |  +- org.apache.oltu.oauth2:org.apache.oltu.oauth2.client:jar:1.0.1:compile
[INFO] |  |  \- org.apache.oltu.oauth2:org.apache.oltu.oauth2.common:jar:1.0.1:compile
[INFO] |  +- io.gsonfire:gson-fire:jar:1.8.0:compile
[INFO] |  \- org.threeten:threetenbp:jar:1.3.5:compile
[INFO] +- com.kuaishou.dataarch:themis-job-sdk:jar:1.0.76:compile
[INFO] |  +- com.google.code.gson:gson:jar:2.8.9:compile
[INFO] |  +- in.zapr.druid:druidry:jar:3.0:compile
[INFO] |  |  +- org.glassfish.jersey.core:jersey-client:jar:2.26:compile
[INFO] |  |  |  +- javax.ws.rs:javax.ws.rs-api:jar:2.1:compile
[INFO] |  |  |  +- org.glassfish.jersey.core:jersey-common:jar:2.26:compile
[INFO] |  |  |  |  \- org.glassfish.hk2:osgi-resource-locator:jar:1.0.1:compile
[INFO] |  |  |  \- org.glassfish.hk2.external:javax.inject:jar:2.5.0-b42:compile
[INFO] |  |  +- org.glassfish.jersey.media:jersey-media-json-jackson:jar:2.26:compile
[INFO] |  |  |  +- org.glassfish.jersey.ext:jersey-entity-filtering:jar:2.26:compile
[INFO] |  |  |  \- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:jar:2.9.10:compile
[INFO] |  |  +- org.glassfish.jersey.inject:jersey-hk2:jar:2.26:compile
[INFO] |  |  |  \- org.glassfish.hk2:hk2-locator:jar:2.5.0-b42:compile
[INFO] |  |  |     +- org.glassfish.hk2.external:aopalliance-repackaged:jar:2.5.0-b42:compile
[INFO] |  |  |     +- org.glassfish.hk2:hk2-api:jar:2.5.0-b42:compile
[INFO] |  |  |     \- org.glassfish.hk2:hk2-utils:jar:2.5.0-b42:compile
[INFO] |  |  \- org.glassfish.jersey.connectors:jersey-apache-connector:jar:2.26:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-joda:jar:2.9.10:compile
[INFO] |  \- com.hubspot.jinjava:jinjava:jar:2.5.6:compile
[INFO] |     +- org.javassist:javassist:jar:3.25.0-GA:compile
[INFO] |     +- com.google.re2j:re2j:jar:1.2:compile
[INFO] |     +- commons-net:commons-net:jar:3.3:compile
[INFO] |     +- com.googlecode.java-ipv6:java-ipv6:jar:0.17:compile
[INFO] |     \- ch.obermuhlner:big-math:jar:2.0.0:compile
[INFO] +- kuaishou:kuaishou-dp-auth-dsc-client:jar:1.0.113:compile
[INFO] +- kuaishou:kwaishop-sellerdata-management-service-client:jar:1.0.58:compile
[INFO] |  +- kuaishou:infra-krpc-client:jar:1.0.152:compile
[INFO] |  |  +- io.projectreactor.addons:reactor-extra:jar:3.2.3.RELEASE:compile
[INFO] |  |  +- kuaishou:infra-krpc-client-common:jar:1.0.152:compile
[INFO] |  |  +- kuaishou:infra-krpc-common:jar:1.0.152:compile
[INFO] |  |  +- kuaishou:ktrace-grpc-instrument-sdk:jar:1.0.253:compile
[INFO] |  |  +- kuaishou:infra-krpc-common-kess-adapter:jar:1.0.152:compile
[INFO] |  |  +- kuaishou:infra-sentinel-adaptivelimiter:jar:1.0.94:compile
[INFO] |  |  +- com.kuaishou:kess-governor-client:jar:0.0.125:compile
[INFO] |  |  |  \- com.kuaishou:kess-mandator-client:jar:0.0.125:compile
[INFO] |  |  |     \- net.goldolphin:maria:jar:0.0.7:compile
[INFO] |  |  +- net.javacrumbs.future-converter:future-converter-java8-guava:jar:1.2.0:compile
[INFO] |  |  |  +- net.javacrumbs.future-converter:future-converter-common:jar:1.2.0:compile
[INFO] |  |  |  \- net.javacrumbs.future-converter:future-converter-java8-common:jar:1.2.0:compile
[INFO] |  |  \- io.perfmark:perfmark-traceviewer:jar:0.19.0:compile
[INFO] |  |     \- io.perfmark:perfmark-tracewriter:jar:0.19.0:compile
[INFO] |  |        +- io.perfmark:perfmark-impl:jar:0.19.0:compile
[INFO] |  |        \- io.perfmark:perfmark-java6:jar:0.19.0:compile
[INFO] |  +- kuaishou:krpc-config-sdk:jar:1.0.198:compile
[INFO] |  |  \- kuaishou:krpc-metric-sdk:jar:1.0.198:compile
[INFO] |  +- com.google.code.findbugs:annotations:jar:3.0.1u2:compile
[INFO] |  |  \- net.jcip:jcip-annotations:jar:1.0:compile
[INFO] |  +- com.github.phantomthief:more-lambdas:jar:0.1.55:compile
[INFO] |  +- kuaishou:krpc-metadata:jar:1.0.198:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:********-kwai:compile
[INFO] |  +- io.grpc:grpc-protobuf:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  +- com.google.api.grpc:proto-google-common-protos:jar:2.0.1:compile
[INFO] |  |  \- io.grpc:grpc-protobuf-lite:jar:1.36.1-kwai-1.1:compile
[INFO] |  +- io.grpc:grpc-stub:jar:1.36.1-kwai-1.1:compile
[INFO] |  +- kuaishou:krpc-common:jar:1.0.198:compile
[INFO] |  \- kuaishou:infra-krpc-metadata:jar:1.0.152:compile
[INFO] +- kuaishou:kwaishop-apollo-pinocchio-center-client:jar:1.0.71:compile
[INFO] |  \- com.kuaishou.kwai-business:kwai-business-ability-client:jar:1.0.16:compile
[INFO] +- kuaishou:kwaishop-material-center-client:jar:1.0.22:compile
[INFO] |  +- com.kuaishou.blobstore:blob-store-sdk-s3:jar:3.0.159:compile
[INFO] |  |  +- com.kuaishou.blobstore:blob-store-common-utils:jar:3.0.159:compile
[INFO] |  |  +- com.amazonaws:aws-java-sdk-s3:jar:1.11.891:compile
[INFO] |  |  +- kuaishou:infra-common-kess-adapter:jar:1.1.15:compile
[INFO] |  |  +- kuaishou:infra-patronum-stresstest-sdk:jar:1.0.21:compile
[INFO] |  |  \- kuaishou:infra-warmup-impl:jar:1.0.25:compile
[INFO] |  |     \- kuaishou:infra-warmup-storage-client:jar:1.0.25:compile
[INFO] |  +- kuaishou:media-cloud-vod-sdk:jar:1.0.105:compile
[INFO] |  |  +- kuaishou:infra-uploader-sdk:jar:1.0.38:compile
[INFO] |  |  |  \- kuaishou:kuaishou-oversea-storage-proto:jar:1.0.121:compile
[INFO] |  |  +- kuaishou:zt-video-store-sdk:jar:1.0.135:compile
[INFO] |  |  |  \- kuaishou:zt-video-store-proto:jar:1.0.135:compile
[INFO] |  |  |     \- kuaishou:kuaishou-video-feature-proto:jar:1.0.630:compile
[INFO] |  |  +- kuaishou:attached-media-store-sdk:jar:1.0.1:compile
[INFO] |  |  +- kuaishou:media-cloud-proto:jar:1.0.425:compile
[INFO] |  |  |  +- kuaishou:kuaishou-cdn-sdk:jar:1.0.217:compile
[INFO] |  |  |  \- kuaishou:kuaishou-video-transcode-proto:jar:1.0.630:compile
[INFO] |  |  +- kuaishou:mediacloud-trace-sdk:jar:1.0.33:compile
[INFO] |  |  +- kuaishou:kuaishou-video-projection-sdk:jar:1.0.41:compile
[INFO] |  |  +- com.kuaishou:video-infra-admin-sdk:jar:1.0.52:compile
[INFO] |  |  +- kuaishou:ks-conductor-sdk:jar:1.0.55:compile
[INFO] |  |  \- kuaishou:kuaishou-media-processing-sdk:jar:1.1.114:compile
[INFO] |  +- org.sejda.imageio:webp-imageio:jar:0.1.6:compile
[INFO] |  +- kuaishou:krtimage-gateway-server-sdk:jar:1.0.51:compile
[INFO] |  +- kuaishou:zt-base:jar:1.0.141:compile
[INFO] |  +- org.springframework:spring-web:jar:5.1.10-kwai-12:compile
[INFO] |  +- com.kuaishou.blobstore:blob-store-common-biz:jar:3.0.159:compile
[INFO] |  +- com.ecyrd.speed4j:speed4j:jar:0.18:compile
[INFO] |  +- com.amazonaws:aws-java-sdk-core:jar:1.11.891:compile
[INFO] |  |  +- software.amazon.ion:ion-java:jar:1.0.2:compile
[INFO] |  |  \- com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:jar:2.9.10:compile
[INFO] |  +- javax:javaee-api:jar:8.0.1:compile
[INFO] |  |  \- com.sun.mail:javax.mail:jar:1.6.2:compile
[INFO] |  \- com.kuaishou.blobstore:ks-boot-starter-bs3-client:jar:3.0.159:compile
[INFO] +- com.kuaishou.kwaishop:kwaishop-merchant-utils-adapter-utils:jar:1.0.33:compile
[INFO] |  +- com.kuaishou.kwaishop:kwaishop-merchant-utils-adapter-const:jar:1.0.62:compile
[INFO] |  +- com.kuaishou.kwaishop:kwaishop-merchant-utils-adapter-kconf:jar:1.0.62:compile
[INFO] |  +- com.kuaishou.kwaishop:kwaishop-merchant-utils-adapter-response-proto:jar:1.0.62:compile
[INFO] |  +- kuaishou:kuaishou-webservice-idseq-sdk:jar:1.1.97:compile
[INFO] |  \- io.jsonwebtoken:jjwt-api:jar:0.11.1:compile
[INFO] +- kuaishou:kwaishop-apollo-strategy-center-client:jar:1.0.72-feature_T68410281_-BETA-SNAPSHOT:compile
[INFO] |  \- kuaishou:kwaishop-product-supplychain-service-client:jar:1.0.462:compile
[INFO] |     +- kuaishou:kuaishou-merchant-external-platform-sdk:jar:1.0.48:compile
[INFO] |     +- kuaishou:kuaishou-ad-reco-base-ad-info-proto:jar:1.0.9562:compile
[INFO] |     |  +- kuaishou:kuaishou-ad-reco-base-ad-base-proto:jar:1.0.9562:compile
[INFO] |     |  +- kuaishou:kuaishou-ad-reco-base-ad-algorithm-proto:jar:1.0.9562:compile
[INFO] |     |  +- kuaishou:kuaishou-ad-reco-base-ad-service-proto:jar:1.0.9562:compile
[INFO] |     |  |  \- kuaishou:kuaishou-ad-reco-base-reco-proto:jar:1.0.9562:compile
[INFO] |     |  +- kuaishou:kuaishou-ad-reco-base-ds-proto:jar:1.0.9562:compile
[INFO] |     |  +- kuaishou:kuaishou-ad-reco-base-retrieval-proto:jar:1.0.9562:compile
[INFO] |     |  \- kuaishou:kuaishou-ad-reco-base-ad-log-proto:jar:1.0.9562:compile
[INFO] |     +- kuaishou:kwaishop-product-oplog-sdk:jar:1.0.51:compile
[INFO] |     \- kuaishou:kwaishop-product-baseinfo-client:jar:1.0.674:compile
[INFO] |        +- kuaishou:kwaishop-product-item-center-client:jar:1.0.79:compile
[INFO] |        +- kuaishou:kwaishop-bizidentity-sdk:jar:1.23:compile
[INFO] |        |  \- kuaishou:kwaishop-bizidentity-client:jar:1.48:compile
[INFO] |        \- kuaishou:kwaishop-product-utils:jar:1.0.37:compile
[INFO] |           \- kuaishou:merchant-identity-sdk:jar:1.0.14:compile
[INFO] +- com.alibaba:easyexcel:jar:3.1.3:compile
[INFO] |  \- com.alibaba:easyexcel-core:jar:3.1.3:compile
[INFO] |     +- com.alibaba:easyexcel-support:jar:3.1.3:compile
[INFO] |     \- org.ehcache:ehcache:jar:3.9.9:compile
[INFO] +- com.offbytwo.jenkins:jenkins-client:jar:0.3.8:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.9.10:compile
[INFO] |  +- commons-lang:commons-lang:jar:2.6:compile
[INFO] |  +- commons-io:commons-io:jar:2.6:compile
[INFO] |  +- org.apache.httpcomponents:httpcore:jar:4.4.11:compile
[INFO] |  +- org.apache.httpcomponents:httpmime:jar:4.5.7:compile
[INFO] |  +- jaxen:jaxen:jar:1.1.6:compile
[INFO] |  \- xml-apis:xml-apis:jar:1.4.01:compile
[INFO] +- org.apache.httpcomponents:httpclient:jar:4.5.13:compile
[INFO] +- kuaishou:kuaishou-merchant-user-sdk:jar:1.0.226:compile
[INFO] |  +- kuaishou:kwaishop-selection-user-service-client:jar:1.0.78:compile
[INFO] |  |  \- org.roaringbitmap:RoaringBitmap:jar:0.6.66:compile
[INFO] |  +- kuaishou:kuaishou-blobstore-client:jar:3.0.159:compile
[INFO] |  |  \- com.kuaishou.blobstore:blob-store-okhttp-client:jar:3.0.159:compile
[INFO] |  +- kuaishou:kuaishou-idbit-sdk:jar:1.0.119:compile
[INFO] |  |  +- kuaishou:kuaishou-idbit-proto:jar:1.0.119:compile
[INFO] |  |  |  \- kuaishou:kuaishou-idbit-base-model-sdk:jar:1.0.11:compile
[INFO] |  |  +- kuaishou:kuaishou-error-record-sdk:jar:1.0.119:compile
[INFO] |  |  +- kuaishou:kuaishou-framework-compresscache-client:jar:1.0.750:compile
[INFO] |  |  +- kuaishou:kuaishou-webservice-deprecated-config:jar:1.0.3:compile
[INFO] |  |  +- com.kuaishou:kuaishou-server-util:jar:1.2.119:compile
[INFO] |  |  +- kuaishou:kuaishou-idbit-config-sdk:jar:1.0.119:compile
[INFO] |  |  \- kuaishou:kswitch-server-sdk:jar:1.1.332:compile
[INFO] |  |     +- kuaishou:device-persona-service-simplified-client:jar:1.0.42:compile
[INFO] |  |     \- kuaishou:kuaishou-web-scope-common-log-sdk:jar:1.0.53:compile
[INFO] |  +- kuaishou:kwaishop-product-brand-client:jar:1.0.110:compile
[INFO] |  |  \- com.github.BAData:protobuf-converter:jar:1.1.5:compile
[INFO] |  +- kuaishou:infra-secure-store-client:jar:1.0.17:compile
[INFO] |  +- kuaishou:kuaishou-merchant-user-common:jar:1.0.16:compile
[INFO] |  |  +- org.springframework.statemachine:spring-statemachine-core:jar:1.2.14.RELEASE:compile
[INFO] |  |  |  \- org.springframework:spring-messaging:jar:5.1.10-kwai-12:compile
[INFO] |  |  +- org.apache.commons:commons-jexl3:jar:3.1:compile
[INFO] |  |  \- com.github.rholder:guava-retrying:jar:2.0.0:compile
[INFO] |  +- kuaishou:kwaishop-shop-center-client:jar:1.0.85:compile
[INFO] |  +- kuaishou:kwaishop-shop-combine-sdk:jar:1.0.194:compile
[INFO] |  |  +- com.kuaishou.kwaishop:kwaishop-merchant-shop-sdk:jar:1.1.14:compile
[INFO] |  |  \- kuaishou:kuaishou-abtest-core:jar:1.0.456:compile
[INFO] |  |     +- kuaishou:abtest-report-sdk:jar:1.0.14:compile
[INFO] |  |     |  \- kuaishou:kmsg-sdk:jar:1.0.14:compile
[INFO] |  |     |     \- io.github.resilience4j:resilience4j-retry:jar:1.2.0:compile
[INFO] |  |     +- kuaishou:ks-abtest2-sdk:jar:1.0.176:compile
[INFO] |  |     |  \- kuaishou:kuaishou-bianque-collect-sdk:jar:1.0.21:compile
[INFO] |  |     \- kuaishou:kuaishou-abtest-proto:jar:1.0.3:compile
[INFO] |  +- kuaishou:kuaishou-user-sdk:jar:1.0.777:compile
[INFO] |  |  +- kuaishou:infra-region-dispatch-sdk:jar:1.1.177:compile
[INFO] |  |  |  +- kuaishou:infra-region-dispatch-proto:jar:1.1.177:compile
[INFO] |  |  |  +- kuaishou:infra-region-dispatch-standardized-log:jar:1.1.177:compile
[INFO] |  |  |  |  \- kuaishou:infra-region-dispatch-orchestration:jar:1.1.177:compile
[INFO] |  |  |  \- org.mapstruct:mapstruct:jar:1.4.1.Final:compile
[INFO] |  |  +- kuaishou:zt-unified-identity-verification-sdk:jar:1.0.95:compile
[INFO] |  |  |  +- kuaishou:kuaishou-sms-proto:jar:1.0.247:compile
[INFO] |  |  |  |  \- kuaishou:infra-common-util:jar:2.0.41:compile
[INFO] |  |  |  +- kuaishou:infra-id-card-verify-sdk:jar:1.0.351:compile
[INFO] |  |  |  +- kuaishou:infra-zt-base-standardized-log:jar:1.0.30:compile
[INFO] |  |  |  |  \- kuaishou:infra-zt-base-orchestration:jar:1.0.30:compile
[INFO] |  |  |  |     \- kuaishou:infra-zt-base-commons:jar:1.0.30:compile
[INFO] |  |  |  +- kuaishou:zt-portal-sdk:jar:1.0.42:compile
[INFO] |  |  |  |  +- kuaishou:zt-portal-proto:jar:1.0.42:compile
[INFO] |  |  |  |  \- kuaishou:zt-portal-base-sdk:jar:1.0.42:compile
[INFO] |  |  |  \- kuaishou:passport-common-sdk:jar:1.1.476:compile
[INFO] |  |  +- kuaishou:kuaishou-user-base-sdk:jar:1.0.777:compile
[INFO] |  |  |  \- kuaishou:kuaishou-user-bucket-sdk:jar:1.0.777:compile
[INFO] |  |  +- kuaishou:kuaishou-webservice-common:jar:1.0.229:compile
[INFO] |  |  |  +- kuaishou:kuaishou-ad-basic-define-sdk:jar:1.0.1:compile
[INFO] |  |  |  \- kuaishou:kuaishou-webservice-common-proto:jar:1.0.229:compile
[INFO] |  |  +- kuaishou:kuaishou-user-proto:jar:1.0.777:compile
[INFO] |  |  |  +- com.kuaishou.zt.basic:aggregator-sdk:jar:1.0.6:compile
[INFO] |  |  |  \- kuaishou:kuaishou-user-basic-info-proto:jar:1.0.777:compile
[INFO] |  |  +- kuaishou:infra-framework-cache-replica-deprecated-sdk:jar:1.0.589:compile
[INFO] |  |  +- com.googlecode.json-simple:json-simple:jar:1.1.1:compile
[INFO] |  |  +- kuaishou:kuaishou-user-basic-info-model-sdk:jar:1.0.777:compile
[INFO] |  |  +- kuaishou:kuaishou-user-basic-info-sdk:jar:1.0.777:compile
[INFO] |  |  |  \- kuaishou:kuaishou-user-relation-sdk:jar:1.0.1462:compile
[INFO] |  |  |     +- kuaishou:kuaishou-hot-key-detective-sdk:jar:1.2.25:compile
[INFO] |  |  |     +- kuaishou:kauth-sdk:jar:1.0.95:compile
[INFO] |  |  |     |  \- kuaishou:kauth-common-sdk:jar:1.0.53:compile
[INFO] |  |  |     +- kuaishou:kgraph-cypher-java-sdk:jar:0.17:compile
[INFO] |  |  |     |  +- org.apache.arrow:arrow-vector:jar:6.0.0:compile
[INFO] |  |  |     |  |  +- org.apache.arrow:arrow-format:jar:6.0.0:compile
[INFO] |  |  |     |  |  +- org.apache.arrow:arrow-memory-core:jar:6.0.0:compile
[INFO] |  |  |     |  |  \- com.google.flatbuffers:flatbuffers-java:jar:1.12.0:compile
[INFO] |  |  |     |  \- org.apache.arrow:arrow-memory-unsafe:jar:6.0.0:compile
[INFO] |  |  |     \- kuaishou:kuaishou-social-basic-tools-sdk:jar:1.0.36:compile
[INFO] |  |  +- kuaishou:kuaishou-user-old-model-sdk:jar:1.0.777:compile
[INFO] |  |  +- kuaishou:zt-count-sdk:jar:1.0.91:compile
[INFO] |  |  +- kuaishou:kuaishou-user-passport-sdk:jar:1.0.777:compile
[INFO] |  |  |  +- kuaishou:kuaishou-deprecated-redis-config-sdk:jar:1.0.211:compile
[INFO] |  |  |  +- kuaishou:kuaishou-deprecated-memcached-config-sdk:jar:1.0.211:compile
[INFO] |  |  |  +- kuaishou:infra-passport-grpc-config:jar:1.1.449:compile
[INFO] |  |  |  +- kuaishou:infra-passport-proto:jar:1.1.476:compile
[INFO] |  |  |  \- kuaishou:kuaishou-user-deprecated-config:jar:1.0.777:compile
[INFO] |  |  |     \- kuaishou:kuaishou-deprecated-oversea-config-sdk:jar:1.0.211:compile
[INFO] |  |  +- kuaishou:kuaishou-kafka-cluster-definition-deprecated:jar:1.0.0:compile
[INFO] |  |  +- kuaishou:kuaishou-user-login-register-client-sdk:jar:1.0.777:compile
[INFO] |  |  \- kuaishou:kuaishou-user-login-register-model-sdk:jar:1.0.777:compile
[INFO] |  \- kuaishou:kwaishop-tag-client:jar:1.0.17:compile
[INFO] +- com.kuaishou.security.codesec:server-security-protect:jar:1.0.20:compile
[INFO] |  +- org.jdom:jdom:jar:1.1.3:compile
[INFO] |  +- org.springframework:spring-webmvc:jar:5.1.10-kwai-12:compile
[INFO] |  \- javax.servlet:javax.servlet-api:jar:4.0.1:compile
[INFO] +- kuaishou:kwaishop-shop-center-rich-client:jar:1.0.40:compile
[INFO] |  +- kuaishou:kwaishop-shop-center-basis:jar:1.0.40:compile
[INFO] |  +- kuaishou:kwaishop-rich-client-core:jar:1.0.5:compile
[INFO] |  \- org.springframework.boot:spring-boot-autoconfigure:jar:2.1.9.RELEASE:compile
[INFO] +- com.kwai.link:klink-pressure-test-sdk:jar:1.0.49:compile
[INFO] |  +- com.neovisionaries:nv-websocket-client:jar:2.14:compile
[INFO] |  +- org.java-websocket:Java-WebSocket:jar:1.5.2:compile
[INFO] |  +- kuaishou:kuaishou-im-message-proto:jar:1.0.165:compile
[INFO] |  +- kuaishou:kuaishou-im-cloud-proto:jar:1.0.313:compile
[INFO] |  +- com.google.protobuf.nano:protobuf-javanano:jar:3.1.0:compile
[INFO] |  +- org.lz4:lz4-java:jar:1.8.0:compile
[INFO] |  \- com.alibaba:fastjson:jar:1.2.83:compile
[INFO] +- kuaishou:kwaishop-access-control-rich-client:jar:1.0.68:compile
[INFO] +- kuaishou:kuaishou-merchant-customer-service-sdk:jar:1.0.147:compile
[INFO] |  +- kuaishou:zt-custom-service-sdk:jar:1.0.183:compile
[INFO] |  +- kuaishou:zt-customer-service-merchant-sdk:jar:1.0.106:compile
[INFO] |  |  \- kuaishou:zt-customer-service-merchant-proto:jar:1.0.106:compile
[INFO] |  +- kuaishou:kwaishop-apollo-router-center-sdk:jar:1.0.13:compile
[INFO] |  +- org.apache.curator:curator-framework:jar:*******:compile
[INFO] |  +- kuaishou:kuaishou-zkclient:jar:1.0.55:compile
[INFO] |  +- kuaishou:kuaishop-access-control-sdk:jar:1.0.84:compile
[INFO] |  |  \- com.kuaishou.infra.boot:ks-boot-starter-framework:jar:1.4.78:compile
[INFO] |  |     +- com.kuaishou.infra.boot:ks-boot-starter-web-ext:jar:1.4.78:compile
[INFO] |  |     +- com.kuaishou.infra.boot:ks-boot-starter-***********************
[INFO] |  |     |  \- org.springframework.boot:spring-boot-starter-******************************
[INFO] |  |     \- com.kuaishou.infra.boot:ks-boot-starter-not-suggest:jar:1.4.78:compile
[INFO] |  +- org.jetbrains:annotations:jar:13.0:compile
[INFO] |  +- kuaishou:kwaishop-trade-center-client:jar:1.0.99:compile
[INFO] |  |  +- kuaishou:kwaishop-platform-common:jar:1.0.19:compile
[INFO] |  |  \- kuaishou:kwaishop-digital-business-common-sdk:jar:1.0.31:compile
[INFO] |  +- kuaishou:kwaishop-cs-session-center-rich-client:jar:1.0.14:compile
[INFO] |  \- kuaishou:kwaishop-trade-manage-client:jar:1.0.74:compile
[INFO] +- kuaishou:kwaishop-cs-session-center-client:jar:1.0.15:compile
[INFO] |  +- kuaishou:kwaishop-cs-common-proto:jar:1.0.3:compile
[INFO] |  \- kuaishou:kuaishou-merchant-staff-sdk:jar:1.0.20:compile
[INFO] |     \- kuaishou:kuaishou-merchant-sdk:jar:1.0.1692:compile
[INFO] |        +- kuaishou:kwaishop-cube-sdk:jar:1.0.1:compile
[INFO] |        +- kuaishou:merchant-commodity-sdk:jar:1.0.533:compile
[INFO] |        |  +- com.kuaishou.kwaishop:kwaishop-merchant-utils-adapter-db:jar:1.0.62:compile
[INFO] |        |  +- com.kuaishou.kwaishop:kwaishop-merchant-utils-adapter-deprecated-config:jar:1.0.62:compile
[INFO] |        |  +- kuaishou:kwaishop-product-categoryandattr-client:jar:1.0.626-xinzhipin:compile
[INFO] |        |  +- kuaishou:kwaishop-stock-manage-client:jar:1.0.145:compile
[INFO] |        |  +- kuaishou:kwaishop-product-rule-client:jar:1.0.33:compile
[INFO] |        |  +- kuaishou:kwaishop-product-trade-sdk:jar:1.0.22:compile
[INFO] |        |  |  \- kuaishou:kuaishou-ad-reco-base-proto:jar:1.0.9562:compile
[INFO] |        |  |     +- kuaishou:kuaishou-ad-reco-base-mmu-proto:jar:1.0.9562:compile
[INFO] |        |  |     \- kuaishou:kuaishou-ad-reco-base-independent-proto:jar:1.0.9562:compile
[INFO] |        |  \- kuaishou:kwaishop-product-guide-sdk:jar:1.0.80:compile
[INFO] |        +- kuaishou:kwaishop-address-sdk:jar:1.0.54:compile
[INFO] |        +- kuaishou:kwaishop-marketing-sdk:jar:1.0.655:compile
[INFO] |        |  \- kuaishou:kwaishop-promotion-delivery-center-client:jar:2.0.12:compile
[INFO] |        +- kuaishou:kuaishou-product-deprecated-proto:jar:1.11:compile
[INFO] |        +- kuaishou:kwaishop-trade-buy-service-client:jar:1.0.263:compile
[INFO] |        |  +- kuaishou:kwaishop-platform-crossborder-sdk:jar:1.21:compile
[INFO] |        |  +- kuaishou:kwaishop-crossborder-center-sdk:jar:11.54:compile
[INFO] |        |  +- kuaishou:kwaishop-pay-service-client:jar:1.0.154:compile
[INFO] |        |  \- com.kuaishou.kwaishop.trade.rate:kwaishop-trade-rate-service-sdk:jar:1.0.185:compile
[INFO] |        +- com.kuaishou.kwaishop:kwaishop-merchant-sdk-adapter-deprecated-common:jar:1.0.13:compile
[INFO] |        +- com.kuaishou.kwaishop:kwaishop-merchant-sdk-adapter-deprecated-business-proto:jar:1.0.15:compile
[INFO] |        +- kuaishou:kwaishop-stock-realtime-client:jar:1.0.143:compile
[INFO] |        |  \- kuaishou:kwaishop-product-stock-sdk:jar:1.0.132:compile
[INFO] |        +- kuaishou:kwaishop-refund-platform-sdk:jar:1.1.500:compile
[INFO] |        |  +- kuaishou:kwaishop-aftersales-center-require-sdk:jar:2.0.132:compile
[INFO] |        |  \- kuaishou:kwaishop-aftersales-center-touch-sdk:jar:2.0.53:compile
[INFO] |        +- kuaishou:kuaishou-merchant-trade-sdk:jar:1.0.130:compile
[INFO] |        |  \- kuaishou:kuaishou-merchant-trade-proto:jar:1.0.130:compile
[INFO] |        +- kuaishou:kwaishop-short-video-service-client:jar:1.0.70:compile
[INFO] |        |  \- kuaishou:kuaishou-ad-new-biz-engine-proto:jar:1.0.7609:compile
[INFO] |        |     +- kuaishou:kuaishou-ad-new-biz-log-proto:jar:1.0.7609:compile
[INFO] |        |     +- kuaishou:kuaishou-ad-new-biz-merchant-proto:jar:1.0.7609:compile
[INFO] |        |     +- kuaishou:kuaishou-ad-new-common-proto:jar:1.0.7609:compile
[INFO] |        |     \- kuaishou:kuaishou-ad-new-biz-legacy-proto:jar:1.0.7609:compile
[INFO] |        |        \- kuaishou:tensorflow-proto:jar:1.0.4:compile
[INFO] |        +- org.apache.tomcat:tomcat-annotations-api:jar:9.0.40:compile
[INFO] |        +- kuaishou:kuaishou-reco-platform-proto:jar:1.1.227:compile
[INFO] |        |  \- kuaishou:kuaishou-reco-browseset-proto:jar:1.0.895:compile
[INFO] |        +- kuaishou:kuaishou-social-sdk:jar:1.1.3699:compile
[INFO] |        |  +- kuaishou:kuaishou-social-model-sdk:jar:1.1.3699:compile
[INFO] |        |  +- kuaishou:kuaishou-social-util-sdk:jar:1.1.3699:compile
[INFO] |        |  +- kuaishou:kuaishou-personal-bit-sdk:jar:1.0.71:compile
[INFO] |        |  +- kuaishou:kuaishou-social-relation-bit-sdk:jar:1.0.71:compile
[INFO] |        |  \- kuaishou:kuaishou-api-core-arch-sdk:jar:1.0.3753:compile
[INFO] |        +- com.kuaishou.kwaishop.trade.rate:kwaishop-trade-rate-service-domain:jar:1.0.159:compile
[INFO] |        |  \- org.mapstruct:mapstruct-processor:jar:1.5.3.Final:compile
[INFO] |        \- kuaishou:kuaishou-plc-sdk:jar:1.0.726:compile
[INFO] |           \- kuaishou:kuaishou-plc-model:jar:1.0.726:compile
[INFO] +- kuaishou:kwaishop-cs-core-center-client:jar:1.0.7:compile
[INFO] |  +- com.kuaishou.kwaishop:kwaishop-merchant-utils-adapter-sensitive:jar:1.0.62:compile
[INFO] |  |  \- kuaishou:kuaishou-sensitive-sdk:jar:2.0.94:compile
[INFO] |  |     \- kuaishou:kuaishou-se-base-proto:jar:1.0.1273:compile
[INFO] |  \- kuaishou:kuaishou-antispam-client:jar:2.0.46:compile
[INFO] |     +- kuaishou:kuaishou-photo-meta-proto:jar:1.0.5:compile
[INFO] |     +- kuaishou:kuaishou-word-filter:jar:1.0.17:compile
[INFO] |     +- kuaishou:kuaishou-antispam-nlp-sdk:jar:1.0.442:compile
[INFO] |     |  \- com.huaban:jieba-analysis:jar:1.0.0:compile
[INFO] |     +- kuaishou:kuaishou-infra-passport-sdk:jar:1.1.476:compile
[INFO] |     +- org.mvel:mvel2:jar:2.4.12.Final:compile
[INFO] |     +- kuaishou:infra-sentinel-quota-config-client:jar:1.0.5:compile
[INFO] |     +- kuaishou:kuaishou-video-live-proto:jar:1.0.1019:compile
[INFO] |     +- kuaishou:kuaishou-antispam-oversea-ad-proto:jar:1.0.64:compile
[INFO] |     +- kuaishou:kuaishou-antispam-ad-proto:jar:1.0.58:compile
[INFO] |     +- kuaishou:kuaishou-mmu-common-proto:jar:1.0.654:compile
[INFO] |     +- kuaishou:kuaishou-draco-common:jar:1.0.466:compile
[INFO] |     |  +- kuaishou:kuaishou-common-auth-sdk:jar:1.0.101:compile
[INFO] |     |  \- com.kuaishou.permission:permission-springmvc-adapter:jar:1.1:compile
[INFO] |     +- kuaishou:kuaishou-operation-special-sdk:jar:1.0.1:compile
[INFO] |     +- kuaishou:krpc-rpc-sdk:jar:1.0.198:compile
[INFO] |     +- kuaishou:infra-sentinel-config:jar:1.0.89:compile
[INFO] |     +- kuaishou:kuaishou-ipip:jar:1.2.66:compile
[INFO] |     |  +- org.tukaani:xz:jar:1.6:compile
[INFO] |     |  \- kuaishou:kuaishou-shm-ipip:jar:1.2.66:compile
[INFO] |     +- kuaishou:infra-sentinel-quota-config-common:jar:1.0.5:compile
[INFO] |     +- kuaishou:krpc-transport-sdk:jar:1.0.198:compile
[INFO] |     |  \- kuaishou:krpc-serialization-sdk:jar:1.0.198:compile
[INFO] |     +- io.projectreactor.netty:reactor-netty:jar:0.8.10.RELEASE:compile
[INFO] |     |  +- io.netty:netty-codec-http:jar:4.1.38.Final:compile
[INFO] |     |  \- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.38.Final:compile
[INFO] |     +- org.springframework:spring-webflux:jar:5.1.10-kwai-12:compile
[INFO] |     +- org.freemarker:freemarker:jar:2.3.28:compile
[INFO] |     +- com.github.ben-manes.caffeine:guava:jar:2.9.1:compile
[INFO] |     +- kuaishou:kuaishou-antispam-feature-sdk:jar:2.0.46:compile
[INFO] |     \- kuaishou:kuaishou-se-legacy-proto:jar:1.0.14:compile
[INFO] +- kuaishou:kwaishop-cs-biz-sdk:jar:1.0.89:compile
[INFO] |  +- kuaishou:kuaishou-im-sdk:jar:2.0.476:compile
[INFO] |  |  +- kuaishou:kuaishou-im-cloud-sdk-proto:jar:1.0.607:compile
[INFO] |  |  +- kuaishou:ks-health-sdk:jar:1.0.14:compile
[INFO] |  |  \- kuaishou:kuaishou-social-im-bit-sdk:jar:1.0.71:compile
[INFO] |  |     \- kuaishou:kuaishou-idbit-base-sdk:jar:1.0.11:compile
[INFO] |  \- kuaishou:infra-databus-common:jar:1.0.71:compile
[INFO] |     +- kuaishou:kuaishou-binlog-resolver-sdk:jar:1.0.11:compile
[INFO] |     \- kuaishou:infra-patronum-stresstest-trafficscheduler-spi-impl:jar:1.0.21:compile
[INFO] +- kuaishou:kuaishou-im-cloud-sdk:jar:1.0.486:compile
[INFO] |  +- kuaishou:kuaishou-im-acc-proto:jar:1.0.128:compile
[INFO] |  +- kuaishou:zt-common-component:jar:1.0.141:compile
[INFO] |  |  +- kuaishou:zt-common-proto:jar:1.0.141:compile
[INFO] |  |  +- kuaishou:infra-framework-web:jar:1.0.589:compile
[INFO] |  |  |  \- kuaishou:unitrouter-common-sdk:jar:1.0.10:compile
[INFO] |  |  +- kuaishou:infra-krpc-modular-server-starter:jar:1.0.152:compile
[INFO] |  |  \- org.apache.logging.log4j:log4j-api:jar:2.11.2:compile
[INFO] |  \- com.github.phantomthief:scope:jar:1.0.20:compile
[INFO] +- org.apache.poi:poi-ooxml:jar:4.1.1:compile
[INFO] |  +- org.apache.poi:poi:jar:4.1.1:compile
[INFO] |  +- org.apache.poi:poi-ooxml-schemas:jar:4.1.1:compile
[INFO] |  |  \- org.apache.xmlbeans:xmlbeans:jar:3.1.0:compile
[INFO] |  +- org.apache.commons:commons-compress:jar:1.19:compile
[INFO] |  \- com.github.virtuald:curvesapi:jar:1.06:compile
[INFO] +- org.springframework.boot:spring-boot-test:jar:2.1.9.RELEASE:test
[INFO] |  \- org.springframework.boot:spring-boot:jar:2.1.9.RELEASE:compile
[INFO] +- kuaishou:kuaishou-cdn-public-sdk:jar:1.0.57:compile
[INFO] |  +- kuaishou:kuaishou-cdn-public-common:jar:1.0.67:compile
[INFO] |  |  +- kuaishou:kuaishou-free-traffic-base:jar:1.0.75:compile
[INFO] |  |  \- kuaishou:kuaishou-photo-page-sdk:jar:1.0.2311:compile
[INFO] |  +- kuaishou:kuaishou-cdn-urlkeeper-sdk:jar:1.0.34:compile
[INFO] |  |  +- kuaishou:kuaishou-cdn-util:jar:1.0.201:compile
[INFO] |  |  +- org.bouncycastle:bcprov-jdk15on:jar:1.64:compile
[INFO] |  |  +- com.moandjiezana.toml:toml4j:jar:0.7.2:compile
[INFO] |  |  \- io.netty:netty:jar:3.10.5.Final:compile
[INFO] |  +- kuaishou:kuaishou-cdn-dispatch-core:jar:1.0.141:compile
[INFO] |  |  +- kuaishou:kuaishou-cdn-ip-sdk:jar:1.0.8:compile
[INFO] |  |  +- commons-validator:commons-validator:jar:1.6:compile
[INFO] |  |  |  \- commons-digester:commons-digester:jar:1.8.1:compile
[INFO] |  |  +- kuaishou:infra-patronum-common-sdk:jar:1.0.6:compile
[INFO] |  |  \- kuaishou:kuaishou-cdn-self-dispatch-full-sdk:jar:1.0.8:compile
[INFO] |  +- kuaishou:kuaishou-cdn-storage-sdk:jar:1.0.12:compile
[INFO] |  |  +- com.qcloud:cos_api:jar:5.2.4:compile
[INFO] |  |  \- com.aliyun.oss:aliyun-sdk-oss:jar:3.1.0:compile
[INFO] |  |     +- com.sun.jersey:jersey-json:jar:1.9:compile
[INFO] |  |     |  +- org.codehaus.jettison:jettison:jar:1.3.8:compile
[INFO] |  |     |  +- com.sun.xml.bind:jaxb-impl:jar:2.2.3-1:compile
[INFO] |  |     |  +- org.codehaus.jackson:jackson-jaxrs:jar:1.9.13:compile
[INFO] |  |     |  +- org.codehaus.jackson:jackson-xc:jar:1.9.13:compile
[INFO] |  |     |  \- com.sun.jersey:jersey-core:jar:1.9:compile
[INFO] |  |     +- com.aliyun:aliyun-java-sdk-core:jar:4.1.1:compile
[INFO] |  |     |  \- com.sun.xml.bind:jaxb-core:jar:2.1.14:compile
[INFO] |  |     +- com.aliyun:aliyun-java-sdk-ram:jar:3.0.0:compile
[INFO] |  |     +- com.aliyun:aliyun-java-sdk-sts:jar:3.0.0:compile
[INFO] |  |     \- com.aliyun:aliyun-java-sdk-ecs:jar:4.2.0:compile
[INFO] |  \- com.google.http-client:google-http-client:jar:1.27.0:compile
[INFO] +- org.springframework:spring-context:jar:5.1.10-kwai-12:compile
[INFO] |  \- org.springframework:spring-expression:jar:5.1.10-kwai-12:compile
[INFO] +- kuaishou:kwaishop-shop-page-service-client:jar:1.1.3:compile
[INFO] +- ru.yandex.clickhouse:clickhouse-*********************************
[INFO] |  \- javax.xml.bind:jaxb-api:jar:2.3.0:compile
[INFO] +- org.apache.commons:commons-collections4:jar:4.3:compile
[INFO] +- kuaishou:krpc-all:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-registry-kess:jar:1.0.198:compile
[INFO] |  |  \- com.kuaishou:kess-scheduler-client:jar:0.0.125:compile
[INFO] |  +- kuaishou:krpc-governance-sdk:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-governance-sentinel:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-cluster:jar:1.0.198:compile
[INFO] |  |  \- kuaishou:infra-sentinel-system-throttling:jar:1.0.94:compile
[INFO] |  +- kuaishou:krpc-configcenter-kess:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-config-spring:jar:1.0.198:compile
[INFO] |  |  \- com.alibaba.spring:spring-context-support:jar:1.0.10:compile
[INFO] |  +- kuaishou:krpc-bootstrap-grpc:jar:1.0.198:compile
[INFO] |  |  \- kuaishou:krpc-bootstrap-sdk:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-bootstrap-local:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-rpc-grpc:jar:1.0.198:compile
[INFO] |  |  \- io.grpc:grpc-services:jar:1.36.1-kwai-1.1:compile
[INFO] |  +- kuaishou:krpc-rpc-local:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-metric-perf:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-metric-rpcmonitor:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-metric-ktrace:jar:1.0.198:compile
[INFO] |  \- kuaishou:krpc-metric-sentinel:jar:1.0.198:compile
[INFO] |     \- com.alibaba.csp:sentinel-core:jar:1.6.3:compile
[INFO] +- kuaishou:kuaishou-build-tools:jar:1.0.1352:test
[INFO] +- junit:junit:jar:4.12:test
[INFO] |  \- org.hamcrest:hamcrest-core:jar:1.3:test
[INFO] +- org.junit.vintage:junit-vintage-engine:jar:5.5.2:test
[INFO] |  +- org.apiguardian:apiguardian-api:jar:1.1.0:compile
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.5.2:test
[INFO] +- org.hamcrest:hamcrest-library:jar:1.3:test
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.5.2:test
[INFO] |  \- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.5.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-params:jar:5.5.2:test
[INFO] +- org.junit.platform:junit-platform-launcher:jar:1.5.2:test
[INFO] +- org.junit.platform:junit-platform-runner:jar:1.5.2:test
[INFO] |  \- org.junit.platform:junit-platform-suite-api:jar:1.5.2:test
[INFO] +- org.mockito:mockito-core:jar:2.23.4:test
[INFO] |  +- net.bytebuddy:byte-buddy:jar:1.12.10:compile
[INFO] |  +- net.bytebuddy:byte-buddy-agent:jar:1.12.10:compile
[INFO] |  \- org.objenesis:objenesis:jar:3.0.1:test
[INFO] +- org.mockito:mockito-junit-jupiter:jar:2.23.4:test
[INFO] +- org.mockito:mockito-inline:jar:2.23.4:test
[INFO] +- org.powermock:powermock-api-mockito2:jar:2.0.0:test
[INFO] |  \- org.powermock:powermock-api-support:jar:2.0.0:test
[INFO] |     +- org.powermock:powermock-reflect:jar:2.0.0:test
[INFO] |     \- org.powermock:powermock-core:jar:2.0.0:test
[INFO] +- org.powermock:powermock-module-junit4:jar:2.0.0:test
[INFO] |  \- org.powermock:powermock-module-junit4-common:jar:2.0.0:test
[INFO] +- com.h2database:h2:jar:1.4.197:test
[INFO] +- com.fiftyonred:mock-jedis:jar:0.4.0:test
[INFO] +- org.testng:testng:jar:6.14.3:test
[INFO] |  +- com.beust:jcommander:jar:1.72:test
[INFO] |  \- org.apache-extras.beanshell:bsh:jar:2.0b6:test
[INFO] +- org.apache.curator:curator-test:jar:*******:test
[INFO] +- com.github.kstyrc:embedded-redis:jar:0.6:test
[INFO] +- org.openjdk.jmh:jmh-core:jar:1.23:test
[INFO] |  +- net.sf.jopt-simple:jopt-simple:jar:4.6:compile
[INFO] |  \- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] +- org.openjdk.jmh:jmh-generator-annprocess:jar:1.23:test
[INFO] +- org.springframework:spring-test:jar:5.1.10-kwai-12:test
[INFO] +- com.jayway.restassured:rest-assured:jar:2.9.0:test
[INFO] |  +- org.codehaus.groovy:groovy:jar:2.4.4:compile
[INFO] |  +- org.codehaus.groovy:groovy-xml:jar:2.4.4:compile
[INFO] |  +- org.ccil.cowan.tagsoup:tagsoup:jar:1.2.1:compile
[INFO] |  +- com.jayway.restassured:json-path:jar:2.9.0:test
[INFO] |  |  +- org.codehaus.groovy:groovy-json:jar:2.4.4:compile
[INFO] |  |  \- com.jayway.restassured:rest-assured-common:jar:2.9.0:test
[INFO] |  \- com.jayway.restassured:xml-path:jar:2.9.0:test
[INFO] +- com.kuaishou:kuaishou-spring-context-indexer:jar:1.2:provided
[INFO] +- javax.annotation:javax.annotation-api:jar:1.3.2:compile
[INFO] \- javax.xml.ws:jaxws-api:jar:2.3.1:compile
[INFO]    \- javax.xml.soap:javax.xml.soap-api:jar:1.4.0:compile
[INFO] 
[INFO] --------------< kuaishou:kwaishop-qa-risk-center-starter >--------------
[INFO] Building kwaishop-qa-risk-center-starter 1.0.0-SNAPSHOT            [5/5]
[INFO]   from kwaishop-qa-risk-center-starter/pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
Downloading from gifshow: http://nexus.corp.kuaishou.com:88/nexus/content/groups/public/kuaishou/kwaishop-qa-risk-center/1.0.0-SNAPSHOT/maven-metadata.xml
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[INFO] 
[INFO] --- dependency:3.1.1:tree (default-cli) @ kwaishop-qa-risk-center-starter ---
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
[WARNING] The POM for com.alibaba:druid:jar:1.1.14 is invalid, transitive dependencies (if any) will not be available, enable debug logging for more details
Downloading from gifshow: http://nexus.corp.kuaishou.com:88/nexus/content/groups/public/kuaishou/kwaishop-qa-risk-center/1.0.0-SNAPSHOT/kwaishop-qa-risk-center-1.0.0-SNAPSHOT.jar
[INFO] publish stats success. url: https://server-devops-api.corp.kuaishou.com/api/mavenStatsReport, cost: 145ms
[INFO] kuaishou:kwaishop-qa-risk-center-starter:jar:1.0.0-SNAPSHOT
[INFO] +- kuaishou:kwaishop-qa-risk-center:jar:1.0.0-SNAPSHOT:compile
[INFO] |  +- kuaishou:kwaishop-qa-risk-center-client:jar:1.0.0-SNAPSHOT:compile
[INFO] |  +- kuaishou:kwaishop-qa-risk-center-common:jar:1.0.0-SNAPSHOT:compile
[INFO] |  |  +- kuaishou:kwaishop-framework-common:jar:1.0.26:compile
[INFO] |  |  \- kuaishou:kspay-common:jar:1.0.1614:compile
[INFO] |  |     \- org.apache.commons:commons-csv:jar:1.5:compile
[INFO] |  +- kuaishou:infra-framework-mq-provider:jar:1.0.432:compile
[INFO] |  |  +- kuaishou:infra-framework-mq-common:jar:1.0.432:compile
[INFO] |  |  +- kuaishou:infra-framework-zone:jar:1.0.9:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-loader:jar:2.1.9.RELEASE:compile
[INFO] |  |  +- kuaishou:infra-framework-common-component:jar:1.0.589:compile
[INFO] |  |  |  +- kuaishou:infra-framework-throttling:jar:1.0.589:compile
[INFO] |  |  |  \- org.springframework:spring-aspects:jar:5.1.10-kwai-12:compile
[INFO] |  |  +- kuaishou:infra-framework-trace:jar:1.0.589:compile
[INFO] |  |  |  \- kuaishou:ktrace-transport:jar:1.0.253:compile
[INFO] |  |  +- kuaishou:infra-framework-warmup:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-warmup-facade:jar:1.0.25:compile
[INFO] |  |  +- kuaishou:infra-patronum-stresstest-trafficscheduler:jar:1.0.21:compile
[INFO] |  |  \- com.github.phantomthief:simple-failover:jar:0.1.32:compile
[INFO] |  +- kuaishou:infra-framework-mq-rocketmq:jar:1.0.432:compile
[INFO] |  |  +- kuaishou:infra-framework-zone-support:jar:1.0.9:compile
[INFO] |  |  +- org.apache.commons:commons-lang3:jar:3.10:compile
[INFO] |  |  +- commons-collections:commons-collections:jar:3.2.2:compile
[INFO] |  |  \- commons-beanutils:commons-beanutils:jar:1.9.3:compile
[INFO] |  +- com.kuaishou.infra.boot:ks-boot-starter-runner-kafka:jar:1.4.78:compile
[INFO] |  |  \- kuaishou:infra-framework-kafka-runner:jar:1.0.589:compile
[INFO] |  +- kuaishou:kuaishou-intown-sdk:jar:1.0.56:compile
[INFO] |  |  +- kuaishou:infra-framework-config:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-framework-notify-cache:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:kuaishou-base-proto:jar:1.0.110:compile
[INFO] |  |  +- kuaishou:kuaishou-webserver-biz-proto:jar:1.0.53:compile
[INFO] |  |  |  +- kuaishou:kuaishou-common-log-proto:jar:1.0.2995:compile
[INFO] |  |  |  +- kuaishou:kuaishou-antispam-general-proto:jar:1.0.81:compile
[INFO] |  |  |  +- kuaishou:kuaishou-post-c2s-proto:jar:1.0.2181:compile
[INFO] |  |  |  +- kuaishou:kuaishou-music-common-proto:jar:2.0.540:compile
[INFO] |  |  |  \- kuaishou:kuaishou-profile-proto:jar:1.1.3699:compile
[INFO] |  |  +- kuaishou:kuaishou-cache-setter:jar:1.0.40:compile
[INFO] |  |  +- kuaishou:kuaishou-intown-json-sdk:jar:1.0.56:compile
[INFO] |  |  +- org.apache.curator:curator-client:jar:*******:compile
[INFO] |  |  +- kuaishou:kuaishou-component-util-common:jar:1.1.52:compile
[INFO] |  |  |  \- kuaishou:kuaishou-web-scope-explore-sdk:jar:1.0.53:compile
[INFO] |  |  |     \- kuaishou:kuaishou-user-bucket-sdk:jar:1.0.777:compile
[INFO] |  |  +- io.grpc:grpc-stub:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  +- com.google.errorprone:error_prone_annotations:jar:2.3.3:compile
[INFO] |  |  |  \- org.codehaus.mojo:animal-sniffer-annotations:jar:1.18:compile
[INFO] |  |  +- org.apache.curator:curator-framework:jar:*******:compile
[INFO] |  |  +- kuaishou:zt-base:jar:1.0.141:compile
[INFO] |  |  |  \- kuaishou:kuaishou-common-i18n:jar:1.1.6:compile
[INFO] |  |  +- kuaishou:kuaishou-component-location-util:jar:1.1.52:compile
[INFO] |  |  +- kuaishou:kuaishou-encryptid-sdk:jar:1.1.11:compile
[INFO] |  |  +- com.github.phantomthief:zknotify-cache:jar:0.1.26-kwai:compile
[INFO] |  |  +- kuaishou:kuaishou-zkclient:jar:1.0.55:compile
[INFO] |  |  +- commons-codec:commons-codec:jar:1.10:compile
[INFO] |  |  +- kuaishou:infra-framework-kafka-producer:jar:1.0.589:compile
[INFO] |  |  |  \- kuaishou:infra-config-adaptor-facade:jar:1.0.53:compile
[INFO] |  |  +- io.grpc:grpc-api:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  \- io.grpc:grpc-context:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  +- com.google.guava:guava:jar:28.1-jre-kwai5:compile
[INFO] |  |  |  +- com.google.guava:failureaccess:jar:1.0.1:compile
[INFO] |  |  |  +- com.google.guava:listenablefuture:jar:9999.0-empty-to-avoid-conflict-with-guava:compile
[INFO] |  |  |  +- org.checkerframework:checker-qual:jar:2.8.1:compile
[INFO] |  |  |  \- com.google.j2objc:j2objc-annotations:jar:1.3:compile
[INFO] |  |  +- org.jetbrains.kotlin:kotlin-stdlib:jar:1.3.72:compile
[INFO] |  |  |  \- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.3.72:compile
[INFO] |  |  +- com.kuaishou.blobstore:blob-store-common-biz:jar:3.0.159:compile
[INFO] |  |  |  \- com.kuaishou.blobstore:blob-store-common-utils:jar:3.0.159:compile
[INFO] |  |  +- io.grpc:grpc-protobuf:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  +- com.google.api.grpc:proto-google-common-protos:jar:2.0.1:compile
[INFO] |  |  |  \- io.grpc:grpc-protobuf-lite:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  \- org.jetbrains:annotations:jar:13.0:compile
[INFO] |  +- net.sourceforge.jeval:jeval:jar:0.9.4:compile
[INFO] |  +- kuaishou:infra-framework-kafka:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-config-adaptor-core:jar:1.0.53:compile
[INFO] |  |  +- kuaishou:kuaishou-env-utils:jar:1.0.12:compile
[INFO] |  |  +- kuaishou:infra-framework-loadingcache:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-framework-lane:jar:1.0.589:compile
[INFO] |  |  +- org.apache.kafka:kafka-clients:jar:0.10.2.1U5.2.58:compile
[INFO] |  |  |  +- org.xerial.snappy:snappy-java:jar:1.1.7.1:compile
[INFO] |  |  |  +- com.github.luben:zstd-jni:jar:1.5.5-10:compile
[INFO] |  |  |  \- com.github.ben-manes.caffeine:caffeine:jar:2.9.1:compile
[INFO] |  |  +- org.apache.kafka:kafka_2.11:jar:0.11.0.4-COMPAT:compile
[INFO] |  |  |  +- com.yammer.metrics:metrics-core:jar:2.2.0:compile
[INFO] |  |  |  +- org.scala-lang:scala-library:jar:2.11.11:compile
[INFO] |  |  |  +- com.101tec:zkclient:jar:0.10:compile
[INFO] |  |  |  \- org.scala-lang.modules:scala-parser-combinators_2.11:jar:1.0.4:compile
[INFO] |  |  +- kuaishou:ktrace-core:jar:1.0.253:compile
[INFO] |  |  |  +- kuaishou:ktrace-config:jar:1.0.253:compile
[INFO] |  |  |  +- com.lmax:disruptor:jar:3.3.7:compile
[INFO] |  |  |  \- com.kwai.jdk:snowman-api:jar:1.0.31:compile
[INFO] |  |  +- kuaishou:infra-kws-tool:jar:1.0.53:compile
[INFO] |  |  \- kuaishou:infra-sentinel-quota-client:jar:1.0.94:compile
[INFO] |  |     \- kuaishou:infra-sentinel-quota-common:jar:1.0.94:compile
[INFO] |  +- kuaishou:infra-framework-datasource:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-config-adaptor-recipes:jar:1.0.53:compile
[INFO] |  |  +- kuaishou:kuaishou-env-util:jar:1.0.277:compile
[INFO] |  |  +- dom4j:dom4j:jar:1.6.1:compile
[INFO] |  |  +- com.github.phantomthief:zkconfig-resources:jar:1.1.29:compile
[INFO] |  |  +- com.zaxxer:HikariCP:jar:*******-kwai:compile
[INFO] |  |  +- io.dropwizard.metrics:metrics-core:jar:3.2.5:compile
[INFO] |  |  +- mysql:mysql-connector-java:jar:5.1.46:compile
[INFO] |  |  +- org.apache.shardingsphere:sharding-jdbc-core:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:shardingsphere-pluggable:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |  +- org.apache.shardingsphere:shardingsphere-sql-parser-binder:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |  +- org.apache.shardingsphere:shardingsphere-route:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |  |  \- org.apache.shardingsphere:shardingsphere-common:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |  +- org.apache.shardingsphere:shardingsphere-rewrite-engine:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |  +- org.apache.shardingsphere:shardingsphere-executor:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |  \- org.apache.shardingsphere:shardingsphere-merge:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:sharding-transaction-core:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:shardingsphere-sql-parser-mysql:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |  \- org.apache.shardingsphere:shardingsphere-sql-parser-engine:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |     +- org.apache.shardingsphere:shardingsphere-spi:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |     +- org.apache.shardingsphere:shardingsphere-sql-parser-spi:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |     +- org.apache.shardingsphere:shardingsphere-sql-parser-statement:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |     \- org.antlr:antlr4-runtime:jar:4.7.2:compile
[INFO] |  |  |  +- org.apache.shardingsphere:sharding-core-route:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |  \- org.apache.shardingsphere:sharding-core-common:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |     \- org.codehaus.groovy:groovy:jar:indy:3.0.11:compile
[INFO] |  |  |  +- org.apache.shardingsphere:master-slave-core-route:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |  \- org.apache.shardingsphere:sharding-core-api:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  |     \- org.apache.shardingsphere:encrypt-core-api:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:sharding-core-rewrite:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:encrypt-core-rewrite:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:shadow-core-rewrite:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:sharding-core-execute:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  +- org.apache.shardingsphere:sharding-core-merge:jar:4.1.1-kwai-28:compile
[INFO] |  |  |  \- org.apache.shardingsphere:encrypt-core-merge:jar:4.1.1-kwai-28:compile
[INFO] |  |  |     \- org.apache.shardingsphere:encrypt-core-common:jar:4.1.1-kwai-28:compile
[INFO] |  |  +- kuaishou:infra-keycenter-client:jar:1.1.76:compile
[INFO] |  |  |  +- kuaishou:infra-keycenter-security:jar:1.1.76:compile
[INFO] |  |  |  |  \- kuaishou:infra-keycenter-proto:jar:1.1.76:compile
[INFO] |  |  |  +- kuaishou:infra-keycenter-store-sdk:jar:1.1.76:compile
[INFO] |  |  |  \- kuaishou:infra-keycenter-grpc-config:jar:1.1.76:compile
[INFO] |  |  +- kuaishou:infra-patronum-governance-sdk:jar:1.0.36:compile
[INFO] |  |  \- kuaishou:infra-patronum-traffic-record-spi-sdk:jar:1.0.88:compile
[INFO] |  +- kuaishou:kuaishou-kconf-server-api:jar:1.0.144:compile
[INFO] |  |  \- kuaishou:infra-framework-http:jar:1.0.589:compile
[INFO] |  +- kuaishou:infra-framework-redis:jar:1.2.103:compile
[INFO] |  |  +- kuaishou:infra-framework-redis-api:jar:1.2.103:compile
[INFO] |  |  +- kuaishou:infra-redis-client-core:jar:1.2.103:compile
[INFO] |  |  |  +- kuaishou:infra-redis-client-facade:jar:1.2.103:compile
[INFO] |  |  |  +- kuaishou:infra-sentinel-redis:jar:1.0.94:compile
[INFO] |  |  |  |  \- kuaishou:infra-sentinel-parameter-ratelimiter:jar:1.0.94:compile
[INFO] |  |  |  \- com.github.jnr:jnr-unixsocket:jar:0.38.17:compile
[INFO] |  |  |     +- com.github.jnr:jnr-ffi:jar:2.2.11:compile
[INFO] |  |  |     |  +- com.github.jnr:jffi:jar:1.3.9:compile
[INFO] |  |  |     |  +- com.github.jnr:jffi:jar:native:1.3.9:runtime
[INFO] |  |  |     |  +- org.ow2.asm:asm-commons:jar:9.1:compile
[INFO] |  |  |     |  +- org.ow2.asm:asm-analysis:jar:9.1:compile
[INFO] |  |  |     |  +- org.ow2.asm:asm-tree:jar:9.1:compile
[INFO] |  |  |     |  +- org.ow2.asm:asm-util:jar:9.1:compile
[INFO] |  |  |     |  +- com.github.jnr:jnr-a64asm:jar:1.0.0:compile
[INFO] |  |  |     |  \- com.github.jnr:jnr-x86asm:jar:1.0.2:compile
[INFO] |  |  |     +- com.github.jnr:jnr-constants:jar:0.10.3:compile
[INFO] |  |  |     +- com.github.jnr:jnr-enxio:jar:0.32.13:compile
[INFO] |  |  |     \- com.github.jnr:jnr-posix:jar:3.1.15:compile
[INFO] |  |  +- redis.clients:jedis:jar:2.9.1-kwai-9:compile
[INFO] |  |  +- org.apache.commons:commons-pool2:jar:2.6.1:compile
[INFO] |  |  +- io.lettuce:lettuce-core:jar:5.2.0-kwai-5:compile
[INFO] |  |  |  +- io.netty:netty-common:jar:4.1.38.Final:compile
[INFO] |  |  |  +- io.netty:netty-handler:jar:4.1.38.Final:compile
[INFO] |  |  |  |  +- io.netty:netty-buffer:jar:4.1.38.Final:compile
[INFO] |  |  |  |  \- io.netty:netty-codec:jar:4.1.38.Final:compile
[INFO] |  |  |  \- io.netty:netty-transport:jar:4.1.38.Final:compile
[INFO] |  |  |     \- io.netty:netty-resolver:jar:4.1.38.Final:compile
[INFO] |  |  +- kuaishou:infra-sentinel-circuitbreaker:jar:1.0.94:compile
[INFO] |  |  |  +- io.github.resilience4j:resilience4j-core:jar:1.2.0:compile
[INFO] |  |  |  \- io.github.resilience4j:resilience4j-reactor:jar:1.2.0:compile
[INFO] |  |  +- com.kohlschutter.junixsocket:junixsocket-core:jar:2.3.2-with-buffer:compile
[INFO] |  |  |  +- com.kohlschutter.junixsocket:junixsocket-native-common:jar:2.3.2-with-buffer:compile
[INFO] |  |  |  \- com.kohlschutter.junixsocket:junixsocket-common:jar:2.3.2-with-buffer:compile
[INFO] |  |  +- com.caucho:hessian:jar:4.0.62:compile
[INFO] |  |  +- com.github.phantomthief:cursor-iterator:jar:1.0.13:compile
[INFO] |  |  \- com.kuaishou:gaea-agent-external-bootstrap:jar:1.0.1:compile
[INFO] |  +- kuaishou:kat-framework:jar:1.0.0:compile
[INFO] |  |  +- org.junit.platform:junit-platform-console:jar:1.7.0-M1:compile
[INFO] |  |  |  \- org.junit.platform:junit-platform-reporting:jar:1.7.0-M1:compile
[INFO] |  |  +- com.squareup.okio:okio:jar:1.17.4:compile
[INFO] |  |  +- com.github.fge:json-schema-validator:jar:2.2.6:compile
[INFO] |  |  |  +- com.googlecode.libphonenumber:libphonenumber:jar:8.12.54:compile
[INFO] |  |  |  +- com.github.fge:json-schema-core:jar:1.2.5:compile
[INFO] |  |  |  |  +- com.github.fge:uri-template:jar:0.9:compile
[INFO] |  |  |  |  |  \- com.github.fge:msg-simple:jar:1.1:compile
[INFO] |  |  |  |  |     \- com.github.fge:btf:jar:1.2:compile
[INFO] |  |  |  |  +- com.github.fge:jackson-coreutils:jar:1.8:compile
[INFO] |  |  |  |  \- org.mozilla:rhino:jar:1.7R4:compile
[INFO] |  |  |  \- javax.mail:mailapi:jar:1.4.3:compile
[INFO] |  |  |     \- javax.activation:activation:jar:1.1.1:compile
[INFO] |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.9.10:compile
[INFO] |  |  +- com.belerweb:pinyin4j:jar:2.5.0:compile
[INFO] |  |  +- net.sf.json-lib:json-lib:jar:jdk15:2.4:compile
[INFO] |  |  |  +- commons-logging:commons-logging:jar:1.2:compile
[INFO] |  |  |  \- net.sf.ezmorph:ezmorph:jar:1.0.6:compile
[INFO] |  |  +- kuaishou:kuaishou-common-encryption:jar:1.0.48:compile
[INFO] |  |  +- kuaishou:kuaishou-common-socket:jar:1.0.14:compile
[INFO] |  |  +- com.opencsv:opencsv:jar:3.7:compile
[INFO] |  |  +- kuaishou:kuaishou-common-proto:jar:1.0.8574:compile
[INFO] |  |  |  +- kuaishou:kuaishou-app-logsdk-proto:jar:1.0.37:compile
[INFO] |  |  |  +- kuaishou:kuaishou-recruit-c2s-proto:jar:1.0.0:compile
[INFO] |  |  |  +- kuaishou:kuaishou-industry-c2s-proto:jar:1.0.1:compile
[INFO] |  |  |  +- kuaishou:kuaishou-live-base-c2s-proto:jar:1.0.1168:compile
[INFO] |  |  |  +- kuaishou:kuaishou-message-c2s-proto:jar:1.0.10:compile
[INFO] |  |  |  +- kuaishou:kuaishou-interaction-c2s-client-push-proto:jar:1.0.5:compile
[INFO] |  |  |  |  \- com.android.support:support-annotations:jar:23.2.0:compile
[INFO] |  |  |  +- kuaishou:kuaishou-common-proto-split-reco-proto:jar:1.0.9:compile
[INFO] |  |  |  +- kuaishou:kuaishou-oversea-c2s-proto:jar:1.0.6:compile
[INFO] |  |  |  +- kuaishou:kuaishou-livemate-c2s-proto:jar:1.0.682:compile
[INFO] |  |  |  +- kuaishou:kuaishou-gamezone-c2s-proto:jar:1.0.324:compile
[INFO] |  |  |  +- kuaishou:kuaishou-lbs-c2s-proto:jar:1.0.27:compile
[INFO] |  |  |  +- kuaishou:kuaishou-video-c2s-proto:jar:1.0.0:compile
[INFO] |  |  |  +- kuaishou:kuaishou-search-c2s-proto:jar:1.0.34:compile
[INFO] |  |  |  +- kuaishou:kswitch-proto:jar:1.1.332:compile
[INFO] |  |  |  +- kuaishou:kuaishou-location-c2s-proto:jar:1.0.1:compile
[INFO] |  |  |  +- kuaishou:kuaishou-merchant-c2s-proto:jar:1.0.502:compile
[INFO] |  |  |  +- kuaishou:kuaishou-ad-c2s-proto:jar:1.0.109:compile
[INFO] |  |  |  \- kuaishou:kuaishou-live-c2s-proto:jar:1.0.1694:compile
[INFO] |  |  +- kuaishou:kat-platform-sdk:jar:1.0.0-master-BETA-SNAPSHOT:compile
[INFO] |  |  +- kuaishou:kat-tools-sdk:jar:1.0.0-master-BETA-SNAPSHOT:compile
[INFO] |  |  +- kuaishou:kat-data-statistics-sdk:jar:1.0.13-master-BETA-SNAPSHOT:compile
[INFO] |  |  |  +- com.kuaishou.infra.boot:ks-boot-starter-web:jar:1.4.78:compile
[INFO] |  |  |  |  +- org.springframework.boot:spring-boot-starter-web:jar:2.1.9.RELEASE:compile
[INFO] |  |  |  |  |  +- org.springframework.boot:spring-boot-starter-json:jar:2.1.9.RELEASE:compile
[INFO] |  |  |  |  |  |  \- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.9.10:compile
[INFO] |  |  |  |  |  \- org.springframework.boot:spring-boot-starter-tomcat:jar:2.1.9.RELEASE:compile
[INFO] |  |  |  |  |     +- org.apache.tomcat.embed:tomcat-embed-core:jar:9.0.40:compile
[INFO] |  |  |  |  |     +- org.apache.tomcat.embed:tomcat-embed-el:jar:9.0.40:compile
[INFO] |  |  |  |  |     \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:9.0.40:compile
[INFO] |  |  |  |  +- com.kuaishou.infra.boot:ks-boot-web:jar:1.4.78:compile
[INFO] |  |  |  |  \- com.kuaishou.infra.boot:ks-boot-starter-logging-access:jar:1.4.78:compile
[INFO] |  |  |  |     \- ch.qos.logback:logback-access:jar:1.2.10:compile
[INFO] |  |  |  +- com.kuaishou.infra.boot:ks-boot-starter-actuator:jar:1.4.78:compile
[INFO] |  |  |  |  +- org.springframework.boot:spring-boot-starter-actuator:jar:2.1.9.RELEASE:compile
[INFO] |  |  |  |  |  +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:2.1.9.RELEASE:compile
[INFO] |  |  |  |  |  |  \- org.springframework.boot:spring-boot-actuator:jar:2.1.9.RELEASE:compile
[INFO] |  |  |  |  |  \- io.micrometer:micrometer-core:jar:1.1.7:compile
[INFO] |  |  |  |  |     \- org.latencyutils:LatencyUtils:jar:2.0.3:compile
[INFO] |  |  |  |  \- com.kuaishou.infra.boot:ks-boot-actuator-autoconfigure:jar:1.4.78:compile
[INFO] |  |  |  |     \- com.kuaishou.infra.boot:ks-boot-actuator:jar:1.4.78:compile
[INFO] |  |  |  +- org.apache.maven.shared:maven-invoker:jar:3.0.1:compile
[INFO] |  |  |  |  +- org.apache.maven.shared:maven-shared-utils:jar:3.2.1:compile
[INFO] |  |  |  |  \- org.codehaus.plexus:plexus-component-annotations:jar:1.7.1:compile
[INFO] |  |  |  \- io.rest-assured:rest-assured:jar:4.2.0:compile
[INFO] |  |  |     +- org.hamcrest:hamcrest:jar:2.1:compile
[INFO] |  |  |     +- io.rest-assured:json-path:jar:4.2.0:compile
[INFO] |  |  |     |  \- io.rest-assured:rest-assured-common:jar:4.2.0:compile
[INFO] |  |  |     \- io.rest-assured:xml-path:jar:4.2.0:compile
[INFO] |  |  |        +- jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.2:compile
[INFO] |  |  |        |  \- jakarta.activation:jakarta.activation-api:jar:1.2.1:compile
[INFO] |  |  |        +- com.sun.xml.bind:jaxb-osgi:jar:2.3.0.1:compile
[INFO] |  |  |        \- org.apache.sling:org.apache.sling.javax.activation:jar:0.1.0:compile
[INFO] |  |  +- org.jsoup:jsoup:jar:1.13.1:compile
[INFO] |  |  +- com.github.stefanbirkner:system-rules:jar:1.19.0:compile
[INFO] |  |  +- io.grpc:grpc-all:jar:1.16.1:compile
[INFO] |  |  |  +- io.grpc:grpc-auth:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  |  +- com.google.auth:google-auth-library-credentials:jar:0.22.2:compile
[INFO] |  |  |  |  \- io.opencensus:opencensus-api:jar:0.21.0:runtime
[INFO] |  |  |  +- io.grpc:grpc-netty:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  |  +- io.netty:netty-codec-http2:jar:4.1.38.Final:compile
[INFO] |  |  |  |  |  \- io.netty:netty-codec-http:jar:4.1.38.Final:compile
[INFO] |  |  |  |  \- io.netty:netty-handler-proxy:jar:4.1.38.Final:runtime
[INFO] |  |  |  |     \- io.netty:netty-codec-socks:jar:4.1.38.Final:runtime
[INFO] |  |  |  +- io.grpc:grpc-okhttp:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  +- io.grpc:grpc-protobuf-nano:jar:1.21.0:compile
[INFO] |  |  |  \- io.grpc:grpc-testing:jar:1.16.1:compile
[INFO] |  |  +- com.google.protobuf:protobuf-java-util:jar:3.16.1:compile
[INFO] |  |  +- org.codehaus.plexus:plexus-classworlds:jar:2.6.0:compile
[INFO] |  |  +- org.sonatype.plexus:plexus-cipher:jar:1.7:compile
[INFO] |  |  +- org.codehaus.plexus:plexus-utils:jar:3.3.0:compile
[INFO] |  |  +- com.kuaishou.dp:one-service-rpc-client:jar:1.0.989:compile
[INFO] |  |  |  +- dp.kwaicat.sdk.prod:one-service-0:jar:1.102.684:compile
[INFO] |  |  |  +- dp.kwaicat.sdk.prod:one-service-1:jar:1.102.473:compile
[INFO] |  |  |  +- dp.kwaicat.sdk.prod:one-service-2:jar:1.102.510:compile
[INFO] |  |  |  +- dp.kwaicat.sdk.prod:one-service-3:jar:1.102.96:compile
[INFO] |  |  |  +- com.kuaishou.dp:one-service-rpc-sql-client:jar:1.0.905:compile
[INFO] |  |  |  |  +- com.kuaishou.dp:one-service-common:jar:1.0.905:compile
[INFO] |  |  |  |  |  +- com.kuaishou.bigdata:compression:jar:1.0.8:compile
[INFO] |  |  |  |  |  |  +- com.kuaishou.bigdata:core:jar:1.0.8:compile
[INFO] |  |  |  |  |  |  \- org.anarres.lzo:lzo-core:jar:1.0.5:compile
[INFO] |  |  |  |  |  +- com.kuaishou.dp:one-service-proto:jar:1.0.905:compile
[INFO] |  |  |  |  |  +- com.kuaishou.dp:one-service-client-throttling:jar:1.0.905:compile
[INFO] |  |  |  |  |  \- kuaishou:infra-unified-device-fingerprint-sdk:jar:1.0.55:compile
[INFO] |  |  |  |  |     \- kuaishou:infra-unified-device-fingerprint-proto:jar:1.0.75:compile
[INFO] |  |  |  |  \- com.kuaishou.dp:key-collector-manager:jar:1.0.905:compile
[INFO] |  |  |  \- com.kuaishou.dp:one-service-rpc-unified-client:jar:1.0.972:compile
[INFO] |  |  |     +- com.kuaishou.dp:one-service-direct-connect-client:jar:1.0.989:compile
[INFO] |  |  |     +- com.kuaishou.dp:havok-hbase:jar:1.0.972:compile
[INFO] |  |  |     +- com.kuaishou.dp:havok-redis:jar:1.0.972:compile
[INFO] |  |  |     |  +- io.netty:netty-transport-native-unix-common:jar:linux-x86_64:4.1.38.Final:compile
[INFO] |  |  |     |  \- io.netty:netty-transport-native-unix-common:jar:osx-x86_64:4.1.38.Final:compile
[INFO] |  |  |     +- com.kuaishou.dp:havok-memcache:jar:1.0.972:compile
[INFO] |  |  |     +- com.kuaishou.dp:havok-core:jar:1.0.972:compile
[INFO] |  |  |     +- com.kuaishou.dp:havok-plugins:jar:1.0.972:compile
[INFO] |  |  |     \- com.github.davidmoten:geo:jar:0.7.6:compile
[INFO] |  |  |        \- com.github.davidmoten:grumpy-core:jar:0.2.3:compile
[INFO] |  |  +- kuaishou:kat-commons:jar:1.0.5:compile
[INFO] |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-json-org:jar:2.9.10:compile
[INFO] |  |  |  |  \- org.json:json:jar:20160212:compile
[INFO] |  |  |  +- kuaishou:kuaishou-redis-shard-client-util:jar:1.0.34:compile
[INFO] |  |  |  |  \- kuaishou:kuaishou-redis-single-az-util:jar:1.0.18:compile
[INFO] |  |  |  \- org.eclipse.jgit:org.eclipse.jgit:jar:5.9.0.202009080501-r:compile
[INFO] |  |  |     \- com.googlecode.javaewah:JavaEWAH:jar:1.1.7:compile
[INFO] |  |  +- kuaishou:kat-commons2:jar:1.0-master-BETA-SNAPSHOT:compile
[INFO] |  |  +- kuaishou:infra-krpc-admin-sdk:jar:1.0.18:compile
[INFO] |  |  +- kuaishou:kat-utils:jar:1.0.0-master-BETA-SNAPSHOT:compile
[INFO] |  |  |  +- org.apache.maven:maven-core:jar:3.6.3:compile
[INFO] |  |  |  |  +- org.apache.maven:maven-model:jar:3.6.3:compile
[INFO] |  |  |  |  +- org.apache.maven:maven-settings:jar:3.6.3:compile
[INFO] |  |  |  |  +- org.apache.maven:maven-settings-builder:jar:3.6.3:compile
[INFO] |  |  |  |  +- org.apache.maven:maven-builder-support:jar:3.6.3:compile
[INFO] |  |  |  |  +- org.apache.maven:maven-repository-metadata:jar:3.6.3:compile
[INFO] |  |  |  |  +- org.apache.maven:maven-artifact:jar:3.6.3:compile
[INFO] |  |  |  |  +- org.apache.maven:maven-plugin-api:jar:3.6.3:compile
[INFO] |  |  |  |  +- org.apache.maven:maven-model-builder:jar:3.6.3:compile
[INFO] |  |  |  |  +- org.apache.maven:maven-resolver-provider:jar:3.6.3:compile
[INFO] |  |  |  |  +- org.apache.maven.resolver:maven-resolver-impl:jar:1.4.1:compile
[INFO] |  |  |  |  +- org.apache.maven.resolver:maven-resolver-api:jar:1.4.1:compile
[INFO] |  |  |  |  +- org.apache.maven.resolver:maven-resolver-spi:jar:1.4.1:compile
[INFO] |  |  |  |  +- org.apache.maven.resolver:maven-resolver-util:jar:1.4.1:compile
[INFO] |  |  |  |  +- org.eclipse.sisu:org.eclipse.sisu.plexus:jar:0.3.4:compile
[INFO] |  |  |  |  |  \- javax.enterprise:cdi-api:jar:1.0:compile
[INFO] |  |  |  |  +- org.eclipse.sisu:org.eclipse.sisu.inject:jar:0.3.4:compile
[INFO] |  |  |  |  +- com.google.inject:guice:jar:no_aop:4.2.1:compile
[INFO] |  |  |  |  |  \- aopalliance:aopalliance:jar:1.0:compile
[INFO] |  |  |  |  \- javax.inject:javax.inject:jar:1:compile
[INFO] |  |  |  +- org.apache.maven:maven-compat:jar:3.6.3:compile
[INFO] |  |  |  |  +- org.codehaus.plexus:plexus-interpolation:jar:1.25:compile
[INFO] |  |  |  |  \- org.apache.maven.wagon:wagon-provider-api:jar:3.3.4:compile
[INFO] |  |  |  +- org.apache.maven.resolver:maven-resolver-connector-basic:jar:1.4.1:compile
[INFO] |  |  |  +- org.apache.maven.resolver:maven-resolver-transport-http:jar:1.4.1:compile
[INFO] |  |  |  +- org.apache.maven:maven-embedder:jar:3.6.3:compile
[INFO] |  |  |  |  \- org.sonatype.plexus:plexus-sec-dispatcher:jar:1.4:compile
[INFO] |  |  |  \- com.alibaba:transmittable-thread-local:jar:2.9.0:compile
[INFO] |  |  +- kuaishou:kat-reported-data-sdk:jar:1.0.0-master-BETA-SNAPSHOT:compile
[INFO] |  |  \- kuaishou:kat-alarm-service:jar:1.0.0-master-BETA-SNAPSHOT:compile
[INFO] |  +- kuaishou:infra-framework-cache-facade:jar:1.0.589:compile
[INFO] |  +- kuaishou:infra-framework-cache-impl:jar:1.0.589:compile
[INFO] |  +- kuaishou:infra-framework-cache-redis-impl:jar:1.0.589:compile
[INFO] |  +- org.python:jython-standalone:jar:2.7.0:compile
[INFO] |  +- javax.validation:validation-api:jar:2.0.1.Final:compile
[INFO] |  +- org.hibernate.validator:hibernate-validator:jar:6.0.13.Final:compile
[INFO] |  |  +- org.jboss.logging:jboss-logging:jar:3.3.2.Final:compile
[INFO] |  |  \- com.fasterxml:classmate:jar:1.3.4:compile
[INFO] |  +- kuaishou:kwaishop-kdump-caelus-center-client:jar:1.0.14-feature_T4977874_fix_compensation-BETA-SNAPSHOT:compile
[INFO] |  |  +- kuaishou:kuaishou-es:jar:1.0.140:compile
[INFO] |  |  |  +- org.elasticsearch:elasticsearch:jar:2.3.1:compile
[INFO] |  |  |  |  +- org.apache.lucene:lucene-core:jar:5.5.0:compile
[INFO] |  |  |  |  +- org.apache.lucene:lucene-backward-codecs:jar:5.5.0:compile
[INFO] |  |  |  |  +- org.apache.lucene:lucene-analyzers-common:jar:5.5.0:compile
[INFO] |  |  |  |  +- org.apache.lucene:lucene-queries:jar:5.5.0:compile
[INFO] |  |  |  |  +- org.apache.lucene:lucene-memory:jar:5.5.0:compile
[INFO] |  |  |  |  +- org.apache.lucene:lucene-highlighter:jar:5.5.0:compile
[INFO] |  |  |  |  +- org.apache.lucene:lucene-queryparser:jar:5.5.0:compile
[INFO] |  |  |  |  |  \- org.apache.lucene:lucene-sandbox:jar:5.5.0:compile
[INFO] |  |  |  |  +- org.apache.lucene:lucene-suggest:jar:5.5.0:compile
[INFO] |  |  |  |  |  \- org.apache.lucene:lucene-misc:jar:5.5.0:compile
[INFO] |  |  |  |  +- org.apache.lucene:lucene-join:jar:5.5.0:compile
[INFO] |  |  |  |  |  \- org.apache.lucene:lucene-grouping:jar:5.5.0:compile
[INFO] |  |  |  |  +- org.apache.lucene:lucene-spatial:jar:5.5.0:compile
[INFO] |  |  |  |  |  +- org.apache.lucene:lucene-spatial3d:jar:5.5.0:compile
[INFO] |  |  |  |  |  \- com.spatial4j:spatial4j:jar:0.5:compile
[INFO] |  |  |  |  +- org.elasticsearch:securesm:jar:1.0:compile
[INFO] |  |  |  |  +- org.joda:joda-convert:jar:1.2:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.dataformat:jackson-dataformat-smile:jar:2.9.10:compile
[INFO] |  |  |  |  +- com.ning:compress-lzf:jar:1.0.3:compile
[INFO] |  |  |  |  +- com.tdunning:t-digest:jar:3.0:compile
[INFO] |  |  |  |  +- org.hdrhistogram:HdrHistogram:jar:2.1.6:compile
[INFO] |  |  |  |  \- com.twitter:jsr166e:jar:1.1.0:compile
[INFO] |  |  |  \- org.elasticsearch.client:elasticsearch-rest-client:jar:6.7.2:compile
[INFO] |  |  |     +- org.apache.httpcomponents:httpasyncclient:jar:4.1.2:compile
[INFO] |  |  |     \- org.apache.httpcomponents:httpcore-nio:jar:4.4.11:compile
[INFO] |  |  \- kuaishou:kwaishop-kdump-caelus-center-common:jar:1.0.14-feature_T4977874_fix_compensation-BETA-SNAPSHOT:compile
[INFO] |  |     \- com.github.os72:protoc-jar:jar:3.11.4:compile
[INFO] |  +- com.kuaishou:openapi-retrofit2-sdk:jar:1.0.0-RELEASE:compile
[INFO] |  |  +- io.swagger:swagger-annotations:jar:1.5.18:compile
[INFO] |  |  +- com.squareup.retrofit2:converter-gson:jar:2.6.0:compile
[INFO] |  |  +- com.squareup.retrofit2:retrofit:jar:2.6.0:compile
[INFO] |  |  +- com.squareup.retrofit2:converter-scalars:jar:2.6.0:compile
[INFO] |  |  +- org.apache.oltu.oauth2:org.apache.oltu.oauth2.client:jar:1.0.1:compile
[INFO] |  |  |  \- org.apache.oltu.oauth2:org.apache.oltu.oauth2.common:jar:1.0.1:compile
[INFO] |  |  +- io.gsonfire:gson-fire:jar:1.8.0:compile
[INFO] |  |  \- org.threeten:threetenbp:jar:1.3.5:compile
[INFO] |  +- com.kuaishou.dataarch:themis-job-sdk:jar:1.0.76:compile
[INFO] |  |  +- com.google.code.gson:gson:jar:2.8.9:compile
[INFO] |  |  +- in.zapr.druid:druidry:jar:3.0:compile
[INFO] |  |  |  +- org.glassfish.jersey.core:jersey-client:jar:2.26:compile
[INFO] |  |  |  |  +- javax.ws.rs:javax.ws.rs-api:jar:2.1:compile
[INFO] |  |  |  |  +- org.glassfish.jersey.core:jersey-common:jar:2.26:compile
[INFO] |  |  |  |  |  \- org.glassfish.hk2:osgi-resource-locator:jar:1.0.1:compile
[INFO] |  |  |  |  \- org.glassfish.hk2.external:javax.inject:jar:2.5.0-b42:compile
[INFO] |  |  |  +- org.glassfish.jersey.media:jersey-media-json-jackson:jar:2.26:compile
[INFO] |  |  |  |  +- org.glassfish.jersey.ext:jersey-entity-filtering:jar:2.26:compile
[INFO] |  |  |  |  \- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:jar:2.9.10:compile
[INFO] |  |  |  +- org.glassfish.jersey.inject:jersey-hk2:jar:2.26:compile
[INFO] |  |  |  |  \- org.glassfish.hk2:hk2-locator:jar:2.5.0-b42:compile
[INFO] |  |  |  |     +- org.glassfish.hk2.external:aopalliance-repackaged:jar:2.5.0-b42:compile
[INFO] |  |  |  |     +- org.glassfish.hk2:hk2-api:jar:2.5.0-b42:compile
[INFO] |  |  |  |     \- org.glassfish.hk2:hk2-utils:jar:2.5.0-b42:compile
[INFO] |  |  |  \- org.glassfish.jersey.connectors:jersey-apache-connector:jar:2.26:compile
[INFO] |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-joda:jar:2.9.10:compile
[INFO] |  |  \- com.hubspot.jinjava:jinjava:jar:2.5.6:compile
[INFO] |  |     +- org.javassist:javassist:jar:3.25.0-GA:compile
[INFO] |  |     +- com.google.re2j:re2j:jar:1.2:compile
[INFO] |  |     +- commons-net:commons-net:jar:3.3:compile
[INFO] |  |     +- com.googlecode.java-ipv6:java-ipv6:jar:0.17:compile
[INFO] |  |     \- ch.obermuhlner:big-math:jar:2.0.0:compile
[INFO] |  +- kuaishou:kuaishou-dp-auth-dsc-client:jar:1.0.113:compile
[INFO] |  +- kuaishou:kwaishop-sellerdata-management-service-client:jar:1.0.58:compile
[INFO] |  |  +- com.google.code.findbugs:annotations:jar:3.0.1u2:compile
[INFO] |  |  |  \- net.jcip:jcip-annotations:jar:1.0:compile
[INFO] |  |  \- kuaishou:krpc-common:jar:1.0.198:compile
[INFO] |  +- kuaishou:kwaishop-apollo-pinocchio-center-client:jar:1.0.71:compile
[INFO] |  |  \- com.kuaishou.kwai-business:kwai-business-ability-client:jar:1.0.16:compile
[INFO] |  +- kuaishou:kwaishop-material-center-client:jar:1.0.22:compile
[INFO] |  |  +- com.kuaishou.blobstore:blob-store-sdk-s3:jar:3.0.159:compile
[INFO] |  |  |  \- com.amazonaws:aws-java-sdk-s3:jar:1.11.891:compile
[INFO] |  |  +- kuaishou:media-cloud-vod-sdk:jar:1.0.105:compile
[INFO] |  |  |  +- kuaishou:infra-uploader-sdk:jar:1.0.38:compile
[INFO] |  |  |  |  \- kuaishou:kuaishou-oversea-storage-proto:jar:1.0.121:compile
[INFO] |  |  |  +- kuaishou:zt-video-store-sdk:jar:1.0.135:compile
[INFO] |  |  |  |  \- kuaishou:zt-video-store-proto:jar:1.0.135:compile
[INFO] |  |  |  |     \- kuaishou:kuaishou-video-feature-proto:jar:1.0.630:compile
[INFO] |  |  |  +- kuaishou:attached-media-store-sdk:jar:1.0.1:compile
[INFO] |  |  |  +- kuaishou:media-cloud-proto:jar:1.0.425:compile
[INFO] |  |  |  |  +- kuaishou:kuaishou-cdn-sdk:jar:1.0.217:compile
[INFO] |  |  |  |  \- kuaishou:kuaishou-video-transcode-proto:jar:1.0.630:compile
[INFO] |  |  |  +- kuaishou:mediacloud-trace-sdk:jar:1.0.33:compile
[INFO] |  |  |  +- kuaishou:kuaishou-video-projection-sdk:jar:1.0.41:compile
[INFO] |  |  |  +- com.kuaishou:video-infra-admin-sdk:jar:1.0.52:compile
[INFO] |  |  |  +- kuaishou:ks-conductor-sdk:jar:1.0.55:compile
[INFO] |  |  |  \- kuaishou:kuaishou-media-processing-sdk:jar:1.1.114:compile
[INFO] |  |  +- org.sejda.imageio:webp-imageio:jar:0.1.6:compile
[INFO] |  |  +- kuaishou:krtimage-gateway-server-sdk:jar:1.0.51:compile
[INFO] |  |  +- org.springframework:spring-web:jar:5.1.10-kwai-12:compile
[INFO] |  |  +- com.ecyrd.speed4j:speed4j:jar:0.18:compile
[INFO] |  |  +- com.amazonaws:aws-java-sdk-core:jar:1.11.891:compile
[INFO] |  |  |  +- software.amazon.ion:ion-java:jar:1.0.2:compile
[INFO] |  |  |  \- com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:jar:2.9.10:compile
[INFO] |  |  +- javax:javaee-api:jar:8.0.1:compile
[INFO] |  |  |  \- com.sun.mail:javax.mail:jar:1.6.2:compile
[INFO] |  |  \- com.kuaishou.blobstore:ks-boot-starter-bs3-client:jar:3.0.159:compile
[INFO] |  +- com.kuaishou.kwaishop:kwaishop-merchant-utils-adapter-utils:jar:1.0.62:compile
[INFO] |  |  +- com.kuaishou.kwaishop:kwaishop-merchant-utils-adapter-const:jar:1.0.62:compile
[INFO] |  |  +- com.kuaishou.kwaishop:kwaishop-merchant-utils-adapter-kconf:jar:1.0.62:compile
[INFO] |  |  +- com.kuaishou.kwaishop:kwaishop-merchant-utils-adapter-response-proto:jar:1.0.62:compile
[INFO] |  |  +- kuaishou:kuaishou-common-definition:jar:1.0.95:compile
[INFO] |  |  \- io.jsonwebtoken:jjwt-api:jar:0.11.1:compile
[INFO] |  +- kuaishou:kwaishop-apollo-strategy-center-client:jar:1.0.72:compile
[INFO] |  |  \- kuaishou:kwaishop-product-supplychain-service-client:jar:1.0.462:compile
[INFO] |  |     +- kuaishou:kuaishou-merchant-external-platform-sdk:jar:1.0.48:compile
[INFO] |  |     +- kuaishou:kuaishou-ad-reco-base-ad-info-proto:jar:1.0.9562:compile
[INFO] |  |     |  +- kuaishou:kuaishou-ad-reco-base-ad-base-proto:jar:1.0.9562:compile
[INFO] |  |     |  +- kuaishou:kuaishou-ad-reco-base-ad-algorithm-proto:jar:1.0.9562:compile
[INFO] |  |     |  +- kuaishou:kuaishou-ad-reco-base-ad-service-proto:jar:1.0.9562:compile
[INFO] |  |     |  |  \- kuaishou:kuaishou-ad-reco-base-reco-proto:jar:1.0.9562:compile
[INFO] |  |     |  +- kuaishou:kuaishou-ad-reco-base-ds-proto:jar:1.0.9562:compile
[INFO] |  |     |  +- kuaishou:kuaishou-ad-reco-base-retrieval-proto:jar:1.0.9562:compile
[INFO] |  |     |  \- kuaishou:kuaishou-ad-reco-base-ad-log-proto:jar:1.0.9562:compile
[INFO] |  |     +- kuaishou:kwaishop-product-oplog-sdk:jar:1.0.51:compile
[INFO] |  |     \- kuaishou:kwaishop-product-baseinfo-client:jar:1.0.674:compile
[INFO] |  |        +- kuaishou:kwaishop-product-item-center-client:jar:1.0.79:compile
[INFO] |  |        +- kuaishou:kwaishop-bizidentity-sdk:jar:1.23:compile
[INFO] |  |        |  \- kuaishou:kwaishop-bizidentity-client:jar:1.48:compile
[INFO] |  |        \- kuaishou:kwaishop-product-utils:jar:1.0.37:compile
[INFO] |  |           \- kuaishou:merchant-identity-sdk:jar:1.0.14:compile
[INFO] |  +- com.alibaba:easyexcel:jar:3.1.3:compile
[INFO] |  |  \- com.alibaba:easyexcel-core:jar:3.1.3:compile
[INFO] |  |     +- com.alibaba:easyexcel-support:jar:3.1.3:compile
[INFO] |  |     \- org.ehcache:ehcache:jar:3.9.9:compile
[INFO] |  +- com.offbytwo.jenkins:jenkins-client:jar:0.3.8:compile
[INFO] |  |  +- commons-lang:commons-lang:jar:2.6:compile
[INFO] |  |  +- org.apache.httpcomponents:httpcore:jar:4.4.11:compile
[INFO] |  |  +- jaxen:jaxen:jar:1.1.6:compile
[INFO] |  |  \- xml-apis:xml-apis:jar:1.4.01:compile
[INFO] |  +- org.apache.httpcomponents:httpclient:jar:4.5.7:compile
[INFO] |  +- kuaishou:kuaishou-merchant-user-sdk:jar:1.0.226:compile
[INFO] |  |  +- kuaishou:kwaishop-selection-user-service-client:jar:1.0.78:compile
[INFO] |  |  |  \- org.roaringbitmap:RoaringBitmap:jar:0.6.66:compile
[INFO] |  |  +- kuaishou:kuaishou-blobstore-client:jar:3.0.159:compile
[INFO] |  |  |  \- com.kuaishou.blobstore:blob-store-okhttp-client:jar:3.0.159:compile
[INFO] |  |  +- kuaishou:kuaishou-idbit-sdk:jar:1.0.119:compile
[INFO] |  |  |  +- kuaishou:kuaishou-idbit-proto:jar:1.0.119:compile
[INFO] |  |  |  |  \- kuaishou:kuaishou-idbit-base-model-sdk:jar:1.0.11:compile
[INFO] |  |  |  +- kuaishou:kuaishou-error-record-sdk:jar:1.0.119:compile
[INFO] |  |  |  +- kuaishou:kuaishou-webservice-deprecated-config:jar:1.0.3:compile
[INFO] |  |  |  +- com.kuaishou:kuaishou-server-util:jar:1.2.119:compile
[INFO] |  |  |  \- kuaishou:kuaishou-idbit-config-sdk:jar:1.0.119:compile
[INFO] |  |  +- kuaishou:kwaishop-product-brand-client:jar:1.0.110:compile
[INFO] |  |  |  \- com.github.BAData:protobuf-converter:jar:1.1.5:compile
[INFO] |  |  +- kuaishou:infra-secure-store-client:jar:1.0.17:compile
[INFO] |  |  |  \- kuaishou:zt-common-component:jar:1.0.141:compile
[INFO] |  |  |     \- kuaishou:zt-common-proto:jar:1.0.141:compile
[INFO] |  |  +- kuaishou:kuaishou-merchant-user-common:jar:1.0.16:compile
[INFO] |  |  |  +- org.springframework.statemachine:spring-statemachine-core:jar:1.2.14.RELEASE:compile
[INFO] |  |  |  |  \- org.springframework:spring-messaging:jar:5.1.10-kwai-12:compile
[INFO] |  |  |  +- org.apache.commons:commons-jexl3:jar:3.1:compile
[INFO] |  |  |  \- com.github.rholder:guava-retrying:jar:2.0.0:compile
[INFO] |  |  +- kuaishou:kwaishop-shop-center-client:jar:1.0.85:compile
[INFO] |  |  +- kuaishou:kwaishop-shop-combine-sdk:jar:1.0.194:compile
[INFO] |  |  |  \- com.kuaishou.kwaishop:kwaishop-merchant-shop-sdk:jar:1.1.14:compile
[INFO] |  |  +- kuaishou:kuaishou-user-sdk:jar:1.0.777:compile
[INFO] |  |  |  +- kuaishou:infra-region-dispatch-sdk:jar:1.1.177:compile
[INFO] |  |  |  |  +- kuaishou:infra-region-dispatch-proto:jar:1.1.177:compile
[INFO] |  |  |  |  +- kuaishou:infra-region-dispatch-standardized-log:jar:1.1.177:compile
[INFO] |  |  |  |  |  \- kuaishou:infra-region-dispatch-orchestration:jar:1.1.177:compile
[INFO] |  |  |  |  \- org.mapstruct:mapstruct:jar:1.4.1.Final:compile
[INFO] |  |  |  +- kuaishou:zt-unified-identity-verification-sdk:jar:1.0.95:compile
[INFO] |  |  |  |  +- kuaishou:kuaishou-sms-proto:jar:1.0.247:compile
[INFO] |  |  |  |  |  \- kuaishou:infra-common-util:jar:2.0.41:compile
[INFO] |  |  |  |  +- kuaishou:infra-id-card-verify-sdk:jar:1.0.351:compile
[INFO] |  |  |  |  +- kuaishou:infra-zt-base-standardized-log:jar:1.0.30:compile
[INFO] |  |  |  |  |  \- kuaishou:infra-zt-base-orchestration:jar:1.0.30:compile
[INFO] |  |  |  |  |     \- kuaishou:infra-zt-base-commons:jar:1.0.30:compile
[INFO] |  |  |  |  +- kuaishou:zt-portal-sdk:jar:1.0.42:compile
[INFO] |  |  |  |  |  +- kuaishou:zt-portal-proto:jar:1.0.42:compile
[INFO] |  |  |  |  |  \- kuaishou:zt-portal-base-sdk:jar:1.0.42:compile
[INFO] |  |  |  |  \- kuaishou:passport-common-sdk:jar:1.1.476:compile
[INFO] |  |  |  +- kuaishou:kuaishou-user-base-sdk:jar:1.0.777:compile
[INFO] |  |  |  +- kuaishou:kuaishou-webservice-common:jar:1.0.229:compile
[INFO] |  |  |  |  +- kuaishou:kuaishou-ad-basic-define-sdk:jar:1.0.1:compile
[INFO] |  |  |  |  \- kuaishou:kuaishou-webservice-common-proto:jar:1.0.229:compile
[INFO] |  |  |  +- kuaishou:kuaishou-user-proto:jar:1.0.777:compile
[INFO] |  |  |  |  +- com.kuaishou.zt.basic:aggregator-sdk:jar:1.0.6:compile
[INFO] |  |  |  |  \- kuaishou:kuaishou-user-basic-info-proto:jar:1.0.777:compile
[INFO] |  |  |  +- kuaishou:kuaishou-user-basic-info-model-sdk:jar:1.0.777:compile
[INFO] |  |  |  +- kuaishou:kuaishou-user-basic-info-sdk:jar:1.0.777:compile
[INFO] |  |  |  |  +- kuaishou:kuaishou-user-relation-sdk:jar:1.0.1462:compile
[INFO] |  |  |  |  |  +- kuaishou:kuaishou-hot-key-detective-sdk:jar:1.2.25:compile
[INFO] |  |  |  |  |  +- kuaishou:i18n-platform-sdk:jar:1.0.30:compile
[INFO] |  |  |  |  |  +- kuaishou:kauth-sdk:jar:1.0.95:compile
[INFO] |  |  |  |  |  |  \- kuaishou:kauth-common-sdk:jar:1.0.53:compile
[INFO] |  |  |  |  |  +- kuaishou:kgraph-cypher-java-sdk:jar:0.17:compile
[INFO] |  |  |  |  |  |  +- org.apache.arrow:arrow-vector:jar:6.0.0:compile
[INFO] |  |  |  |  |  |  |  +- org.apache.arrow:arrow-format:jar:6.0.0:compile
[INFO] |  |  |  |  |  |  |  +- org.apache.arrow:arrow-memory-core:jar:6.0.0:compile
[INFO] |  |  |  |  |  |  |  \- com.google.flatbuffers:flatbuffers-java:jar:1.12.0:compile
[INFO] |  |  |  |  |  |  \- org.apache.arrow:arrow-memory-unsafe:jar:6.0.0:compile
[INFO] |  |  |  |  |  \- kuaishou:kuaishou-social-basic-tools-sdk:jar:1.0.36:compile
[INFO] |  |  |  |  +- kuaishou:kuaishou-component-user-util:jar:1.1.52:compile
[INFO] |  |  |  |  \- kuaishou:kuaishou-component-i18n-util:jar:1.1.52:compile
[INFO] |  |  |  +- kuaishou:kuaishou-user-old-model-sdk:jar:1.0.777:compile
[INFO] |  |  |  +- kuaishou:zt-count-sdk:jar:1.0.91:compile
[INFO] |  |  |  +- kuaishou:kuaishou-user-passport-sdk:jar:1.0.777:compile
[INFO] |  |  |  |  +- kuaishou:kuaishou-deprecated-redis-config-sdk:jar:1.0.211:compile
[INFO] |  |  |  |  +- kuaishou:kuaishou-deprecated-memcached-config-sdk:jar:1.0.211:compile
[INFO] |  |  |  |  +- kuaishou:infra-passport-grpc-config:jar:1.1.449:compile
[INFO] |  |  |  |  +- kuaishou:infra-passport-proto:jar:1.1.476:compile
[INFO] |  |  |  |  \- kuaishou:kuaishou-user-deprecated-config:jar:1.0.777:compile
[INFO] |  |  |  |     \- kuaishou:kuaishou-deprecated-oversea-config-sdk:jar:1.0.211:compile
[INFO] |  |  |  +- kuaishou:kuaishou-kafka-cluster-definition-deprecated:jar:1.0.0:compile
[INFO] |  |  |  +- kuaishou:kuaishou-user-login-register-client-sdk:jar:1.0.777:compile
[INFO] |  |  |  \- kuaishou:kuaishou-user-login-register-model-sdk:jar:1.0.777:compile
[INFO] |  |  \- kuaishou:kwaishop-tag-client:jar:1.0.17:compile
[INFO] |  +- com.kuaishou.security.codesec:server-security-protect:jar:1.0.20:compile
[INFO] |  |  +- org.jdom:jdom:jar:1.1.3:compile
[INFO] |  |  \- org.springframework:spring-webmvc:jar:5.1.10-kwai-12:compile
[INFO] |  +- kuaishou:kwaishop-shop-center-rich-client:jar:1.0.40:compile
[INFO] |  |  +- kuaishou:kwaishop-shop-center-basis:jar:1.0.40:compile
[INFO] |  |  \- kuaishou:kwaishop-rich-client-core:jar:1.0.5:compile
[INFO] |  +- com.kwai.link:klink-pressure-test-sdk:jar:1.0.49:compile
[INFO] |  |  +- com.neovisionaries:nv-websocket-client:jar:2.14:compile
[INFO] |  |  +- org.java-websocket:Java-WebSocket:jar:1.5.2:compile
[INFO] |  |  +- kuaishou:kuaishou-im-message-proto:jar:1.0.165:compile
[INFO] |  |  +- kuaishou:kuaishou-im-cloud-proto:jar:1.0.313:compile
[INFO] |  |  +- com.google.protobuf.nano:protobuf-javanano:jar:3.1.0:compile
[INFO] |  |  +- org.lz4:lz4-java:jar:1.8.0:compile
[INFO] |  |  \- com.alibaba:fastjson:jar:1.2.83:compile
[INFO] |  +- kuaishou:kwaishop-access-control-rich-client:jar:1.0.68:compile
[INFO] |  +- kuaishou:kuaishou-merchant-customer-service-sdk:jar:1.0.169:compile
[INFO] |  |  +- kuaishou:zt-custom-service-sdk:jar:1.0.183:compile
[INFO] |  |  +- kuaishou:zt-customer-service-merchant-sdk:jar:1.0.106:compile
[INFO] |  |  |  \- kuaishou:zt-customer-service-merchant-proto:jar:1.0.106:compile
[INFO] |  |  +- kuaishou:kuaishou-social-c2s-proto:jar:1.0.201:compile
[INFO] |  |  +- kuaishou:kwaishop-apollo-router-center-sdk:jar:1.0.13:compile
[INFO] |  |  +- kuaishou:kuaishop-access-control-sdk:jar:1.0.103:compile
[INFO] |  |  +- kuaishou:kwaishop-trade-center-client:jar:1.0.288:compile
[INFO] |  |  |  +- kuaishou:kwaishop-platform-common:jar:1.0.19:compile
[INFO] |  |  |  \- kuaishou:kwaishop-digital-business-common-sdk:jar:1.0.31:compile
[INFO] |  |  +- kuaishou:kwaishop-cs-session-center-rich-client:jar:1.0.18:compile
[INFO] |  |  +- kuaishou:kwaishop-trade-manage-client:jar:1.0.137:compile
[INFO] |  |  \- kuaishou:kwaishop-cs-intelligence-sdk:jar:1.0.36:compile
[INFO] |  +- kuaishou:kwaishop-cs-session-center-client:jar:1.0.15:compile
[INFO] |  |  +- kuaishou:kwaishop-cs-common-proto:jar:1.0.3:compile
[INFO] |  |  \- kuaishou:kuaishou-merchant-staff-sdk:jar:1.0.20:compile
[INFO] |  |     \- kuaishou:kuaishou-merchant-sdk:jar:1.0.1692:compile
[INFO] |  |        +- kuaishou:kwaishop-cube-sdk:jar:1.0.1:compile
[INFO] |  |        +- kuaishou:merchant-commodity-sdk:jar:1.0.533:compile
[INFO] |  |        |  +- com.kuaishou.kwaishop:kwaishop-merchant-utils-adapter-db:jar:1.0.62:compile
[INFO] |  |        |  +- com.kuaishou.kwaishop:kwaishop-merchant-utils-adapter-deprecated-config:jar:1.0.62:compile
[INFO] |  |        |  +- kuaishou:kwaishop-product-categoryandattr-client:jar:1.0.626-xinzhipin:compile
[INFO] |  |        |  +- kuaishou:kwaishop-stock-manage-client:jar:1.0.145:compile
[INFO] |  |        |  +- kuaishou:kwaishop-product-rule-client:jar:1.0.33:compile
[INFO] |  |        |  +- kuaishou:kwaishop-product-trade-sdk:jar:1.0.22:compile
[INFO] |  |        |  |  \- kuaishou:kuaishou-ad-reco-base-proto:jar:1.0.9562:compile
[INFO] |  |        |  |     +- kuaishou:kuaishou-ad-reco-base-mmu-proto:jar:1.0.9562:compile
[INFO] |  |        |  |     \- kuaishou:kuaishou-ad-reco-base-independent-proto:jar:1.0.9562:compile
[INFO] |  |        |  \- kuaishou:kwaishop-product-guide-sdk:jar:1.0.80:compile
[INFO] |  |        +- kuaishou:kwaishop-address-sdk:jar:1.0.54:compile
[INFO] |  |        +- kuaishou:kwaishop-marketing-sdk:jar:1.0.655:compile
[INFO] |  |        |  \- kuaishou:kwaishop-promotion-delivery-center-client:jar:2.0.12:compile
[INFO] |  |        +- kuaishou:kuaishou-product-deprecated-proto:jar:1.11:compile
[INFO] |  |        +- kuaishou:kwaishop-trade-buy-service-client:jar:1.0.263:compile
[INFO] |  |        |  +- kuaishou:kwaishop-platform-crossborder-sdk:jar:1.21:compile
[INFO] |  |        |  +- kuaishou:kwaishop-crossborder-center-sdk:jar:11.54:compile
[INFO] |  |        |  +- kuaishou:kuaishou-web-scope-sdk:jar:1.0.53:compile
[INFO] |  |        |  |  +- kuaishou:kuaishou-web-scope-passport-sdk:jar:1.2.0:compile
[INFO] |  |        |  |  \- kuaishou:kuaishou-web-scope-client-request-info-sdk:jar:1.0.53:compile
[INFO] |  |        |  +- kuaishou:kwaishop-pay-service-client:jar:1.0.154:compile
[INFO] |  |        |  \- com.kuaishou.kwaishop.trade.rate:kwaishop-trade-rate-service-sdk:jar:1.0.185:compile
[INFO] |  |        +- com.kuaishou.kwaishop:kwaishop-merchant-sdk-adapter-deprecated-common:jar:1.0.13:compile
[INFO] |  |        +- com.kuaishou.kwaishop:kwaishop-merchant-sdk-adapter-deprecated-business-proto:jar:1.0.15:compile
[INFO] |  |        +- kuaishou:kwaishop-stock-realtime-client:jar:1.0.143:compile
[INFO] |  |        |  \- kuaishou:kwaishop-product-stock-sdk:jar:1.0.132:compile
[INFO] |  |        +- kuaishou:kwaishop-refund-platform-sdk:jar:1.1.500:compile
[INFO] |  |        |  +- kuaishou:kwaishop-aftersales-center-require-sdk:jar:2.0.132:compile
[INFO] |  |        |  \- kuaishou:kwaishop-aftersales-center-touch-sdk:jar:2.0.53:compile
[INFO] |  |        +- kuaishou:kuaishou-merchant-trade-sdk:jar:1.0.130:compile
[INFO] |  |        |  \- kuaishou:kuaishou-merchant-trade-proto:jar:1.0.130:compile
[INFO] |  |        +- kuaishou:kwaishop-short-video-service-client:jar:1.0.70:compile
[INFO] |  |        |  \- kuaishou:kuaishou-ad-new-biz-engine-proto:jar:1.0.7609:compile
[INFO] |  |        |     +- kuaishou:kuaishou-ad-new-biz-log-proto:jar:1.0.7609:compile
[INFO] |  |        |     +- kuaishou:kuaishou-ad-new-biz-merchant-proto:jar:1.0.7609:compile
[INFO] |  |        |     +- kuaishou:kuaishou-ad-new-common-proto:jar:1.0.7609:compile
[INFO] |  |        |     \- kuaishou:kuaishou-ad-new-biz-legacy-proto:jar:1.0.7609:compile
[INFO] |  |        |        \- kuaishou:tensorflow-proto:jar:1.0.4:compile
[INFO] |  |        +- org.apache.tomcat:tomcat-annotations-api:jar:9.0.40:compile
[INFO] |  |        +- kuaishou:kuaishou-reco-platform-proto:jar:1.1.227:compile
[INFO] |  |        |  \- kuaishou:kuaishou-reco-browseset-proto:jar:1.0.895:compile
[INFO] |  |        +- kuaishou:kuaishou-social-sdk:jar:1.1.3699:compile
[INFO] |  |        |  +- kuaishou:kuaishou-social-model-sdk:jar:1.1.3699:compile
[INFO] |  |        |  +- kuaishou:kuaishou-social-util-sdk:jar:1.1.3699:compile
[INFO] |  |        |  +- kuaishou:kuaishou-personal-bit-sdk:jar:1.0.71:compile
[INFO] |  |        |  +- kuaishou:kuaishou-social-relation-bit-sdk:jar:1.0.71:compile
[INFO] |  |        |  \- kuaishou:kuaishou-api-core-arch-sdk:jar:1.0.3753:compile
[INFO] |  |        +- com.kuaishou.kwaishop.trade.rate:kwaishop-trade-rate-service-domain:jar:1.0.159:compile
[INFO] |  |        |  \- org.mapstruct:mapstruct-processor:jar:1.5.3.Final:compile
[INFO] |  |        \- kuaishou:kuaishou-plc-sdk:jar:1.0.726:compile
[INFO] |  |           \- kuaishou:kuaishou-plc-model:jar:1.0.726:compile
[INFO] |  +- kuaishou:kwaishop-cs-core-center-client:jar:1.0.7:compile
[INFO] |  |  +- com.kuaishou.kwaishop:kwaishop-merchant-utils-adapter-sensitive:jar:1.0.62:compile
[INFO] |  |  |  \- kuaishou:kuaishou-sensitive-sdk:jar:2.0.94:compile
[INFO] |  |  |     \- kuaishou:kuaishou-se-base-proto:jar:1.0.1273:compile
[INFO] |  |  \- kuaishou:kuaishou-antispam-client:jar:2.0.46:compile
[INFO] |  |     +- kuaishou:kuaishou-photo-meta-proto:jar:1.0.5:compile
[INFO] |  |     +- kuaishou:kuaishou-word-filter:jar:1.0.17:compile
[INFO] |  |     +- kuaishou:kuaishou-antispam-nlp-sdk:jar:1.0.442:compile
[INFO] |  |     |  \- com.huaban:jieba-analysis:jar:1.0.0:compile
[INFO] |  |     +- kuaishou:kuaishou-infra-passport-sdk:jar:1.1.476:compile
[INFO] |  |     +- org.mvel:mvel2:jar:2.4.12.Final:compile
[INFO] |  |     +- kuaishou:infra-sentinel-quota-config-client:jar:1.0.5:compile
[INFO] |  |     +- kuaishou:kuaishou-video-live-proto:jar:1.0.1019:compile
[INFO] |  |     +- kuaishou:kuaishou-antispam-oversea-ad-proto:jar:1.0.64:compile
[INFO] |  |     +- kuaishou:kuaishou-antispam-ad-proto:jar:1.0.58:compile
[INFO] |  |     +- kuaishou:kuaishou-mmu-common-proto:jar:1.0.654:compile
[INFO] |  |     +- kuaishou:kuaishou-component-antispam-util:jar:1.1.52:compile
[INFO] |  |     +- kuaishou:kuaishou-draco-common:jar:1.0.466:compile
[INFO] |  |     |  +- kuaishou:kuaishou-common-auth-sdk:jar:1.0.101:compile
[INFO] |  |     |  \- com.kuaishou.permission:permission-springmvc-adapter:jar:1.1:compile
[INFO] |  |     +- kuaishou:kuaishou-operation-special-sdk:jar:1.0.1:compile
[INFO] |  |     +- kuaishou:infra-sentinel-config:jar:1.0.89:compile
[INFO] |  |     +- kuaishou:kuaishou-ipip:jar:1.2.66:compile
[INFO] |  |     |  +- org.tukaani:xz:jar:1.6:compile
[INFO] |  |     |  \- kuaishou:kuaishou-shm-ipip:jar:1.2.66:compile
[INFO] |  |     +- kuaishou:infra-sentinel-quota-config-common:jar:1.0.5:compile
[INFO] |  |     +- org.freemarker:freemarker:jar:2.3.28:compile
[INFO] |  |     +- com.github.ben-manes.caffeine:guava:jar:2.9.1:compile
[INFO] |  |     +- kuaishou:kuaishou-antispam-feature-sdk:jar:2.0.46:compile
[INFO] |  |     \- kuaishou:kuaishou-se-legacy-proto:jar:1.0.14:compile
[INFO] |  +- kuaishou:kwaishop-cs-biz-sdk:jar:1.0.123:compile
[INFO] |  |  +- kuaishou:kwaishop-cs-common:jar:1.0.252:compile
[INFO] |  |  |  +- kuaishou:kwaishop-c-era-service-sdk:jar:1.1.5:compile
[INFO] |  |  |  |  \- kuaishou:kwaishop-c-era-service-common:jar:1.1.5:compile
[INFO] |  |  |  \- kuaishou:kuaishou-component-arch-util:jar:1.1.52:compile
[INFO] |  |  |     +- kuaishou:infra-sentinel-distributed-ratelimiter:jar:1.0.94:compile
[INFO] |  |  |     +- com.hankcs:aho-corasick-double-array-trie:jar:1.1.0:compile
[INFO] |  |  |     +- kuaishou:zt-client-log-sdk:jar:1.0.24:compile
[INFO] |  |  |     +- kuaishou:kuaishou-view-builder-basic:jar:1.1.71:compile
[INFO] |  |  |     +- kuaishou:kuaishou-photo-basic-define:jar:1.0.2472:compile
[INFO] |  |  |     |  \- kuaishou:kuaishou-music-base-sdk:jar:2.0.540:compile
[INFO] |  |  |     \- com.aayushatharva.brotli4j:brotli4j:jar:1.16.0:compile
[INFO] |  |  |        +- com.aayushatharva.brotli4j:service:jar:1.16.0:compile
[INFO] |  |  |        \- com.aayushatharva.brotli4j:native-osx-x86_64:jar:1.16.0:compile
[INFO] |  |  +- kuaishou:kuaishou-im-sdk:jar:2.0.476:compile
[INFO] |  |  |  +- kuaishou:ks-health-sdk:jar:1.0.14:compile
[INFO] |  |  |  \- kuaishou:kuaishou-social-im-bit-sdk:jar:1.0.71:compile
[INFO] |  |  |     \- kuaishou:kuaishou-idbit-base-sdk:jar:1.0.11:compile
[INFO] |  |  \- kuaishou:infra-databus-common:jar:1.0.71:compile
[INFO] |  +- kuaishou:kuaishou-im-cloud-sdk:jar:1.0.578:compile
[INFO] |  |  +- kuaishou:kuaishou-im-acc-proto:jar:1.0.128:compile
[INFO] |  |  \- kuaishou:kuaishou-im-cloud-sdk-proto:jar:1.0.607:compile
[INFO] |  +- org.apache.poi:poi-ooxml:jar:4.1.1:compile
[INFO] |  |  +- org.apache.poi:poi:jar:4.1.1:compile
[INFO] |  |  +- org.apache.poi:poi-ooxml-schemas:jar:4.1.1:compile
[INFO] |  |  |  \- org.apache.xmlbeans:xmlbeans:jar:3.1.0:compile
[INFO] |  |  +- org.apache.commons:commons-compress:jar:1.19:compile
[INFO] |  |  \- com.github.virtuald:curvesapi:jar:1.06:compile
[INFO] |  +- kuaishou:kuaishou-cdn-public-sdk:jar:1.0.57:compile
[INFO] |  |  +- kuaishou:kuaishou-cdn-public-common:jar:1.0.67:compile
[INFO] |  |  |  \- kuaishou:kuaishou-photo-page-sdk:jar:1.0.2311:compile
[INFO] |  |  +- kuaishou:kuaishou-cdn-urlkeeper-sdk:jar:1.0.34:compile
[INFO] |  |  |  +- kuaishou:kuaishou-cdn-util:jar:1.0.201:compile
[INFO] |  |  |  +- org.bouncycastle:bcprov-jdk15on:jar:1.64:compile
[INFO] |  |  |  \- com.moandjiezana.toml:toml4j:jar:0.7.2:compile
[INFO] |  |  +- kuaishou:kuaishou-cdn-dispatch-core:jar:1.0.141:compile
[INFO] |  |  |  +- kuaishou:kuaishou-cdn-ip-sdk:jar:1.0.8:compile
[INFO] |  |  |  +- commons-validator:commons-validator:jar:1.6:compile
[INFO] |  |  |  |  \- commons-digester:commons-digester:jar:1.8.1:compile
[INFO] |  |  |  \- kuaishou:kuaishou-cdn-self-dispatch-full-sdk:jar:1.0.8:compile
[INFO] |  |  +- kuaishou:kuaishou-cdn-storage-sdk:jar:1.0.12:compile
[INFO] |  |  |  +- com.qcloud:cos_api:jar:5.2.4:compile
[INFO] |  |  |  \- com.aliyun.oss:aliyun-sdk-oss:jar:3.1.0:compile
[INFO] |  |  |     +- com.sun.jersey:jersey-json:jar:1.9:compile
[INFO] |  |  |     |  +- org.codehaus.jettison:jettison:jar:1.3.8:compile
[INFO] |  |  |     |  +- com.sun.xml.bind:jaxb-impl:jar:2.2.3-1:compile
[INFO] |  |  |     |  +- org.codehaus.jackson:jackson-jaxrs:jar:1.9.13:compile
[INFO] |  |  |     |  +- org.codehaus.jackson:jackson-xc:jar:1.9.13:compile
[INFO] |  |  |     |  \- com.sun.jersey:jersey-core:jar:1.9:compile
[INFO] |  |  |     +- com.aliyun:aliyun-java-sdk-core:jar:4.1.1:compile
[INFO] |  |  |     |  \- com.sun.xml.bind:jaxb-core:jar:2.1.14:compile
[INFO] |  |  |     +- com.aliyun:aliyun-java-sdk-ram:jar:3.0.0:compile
[INFO] |  |  |     +- com.aliyun:aliyun-java-sdk-sts:jar:3.0.0:compile
[INFO] |  |  |     \- com.aliyun:aliyun-java-sdk-ecs:jar:4.2.0:compile
[INFO] |  |  \- com.google.http-client:google-http-client:jar:1.27.0:compile
[INFO] |  +- kuaishou:kwaishop-shop-page-service-client:jar:1.1.3:compile
[INFO] |  \- ru.yandex.clickhouse:clickhouse-***********************
[INFO] |     +- net.jpountz.lz4:lz4:jar:1.3.0:compile
[INFO] |     \- joda-time:joda-time:jar:2.9.9:compile
[INFO] +- kuaishou:kwaishop-task-sdk:jar:1.0.28:compile
[INFO] |  \- kuaishou:infra-framework-grpc:jar:1.0.589:compile
[INFO] |     \- kuaishou:infra-framework-kess:jar:1.0.589:compile
[INFO] +- kuaishou:krpc-all:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-registry-kess:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-registry-sdk:jar:1.0.198:compile
[INFO] |  |  \- com.kuaishou:kess-scheduler-client:jar:0.0.125:compile
[INFO] |  +- kuaishou:krpc-governance-sdk:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-transport-sdk:jar:1.0.198:compile
[INFO] |  |  |  \- kuaishou:krpc-serialization-sdk:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-rpc-sdk:jar:1.0.198:compile
[INFO] |  |  \- kuaishou:krpc-metric-sdk:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-governance-sentinel:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:krpc-cluster:jar:1.0.198:compile
[INFO] |  |  +- com.kuaishou:kess-governor-client:jar:0.0.125:compile
[INFO] |  |  |  \- com.kuaishou:kess-mandator-client:jar:0.0.125:compile
[INFO] |  |  |     \- net.goldolphin:maria:jar:0.0.7:compile
[INFO] |  |  +- kuaishou:infra-sentinel-system-throttling:jar:1.0.94:compile
[INFO] |  |  +- com.github.vladimir-bukhtoyarov:bucket4j-core:jar:6.0.1:compile
[INFO] |  |  \- kuaishou:infra-patronum-stresstest-sdk:jar:1.0.21:compile
[INFO] |  +- kuaishou:krpc-configcenter-kess:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-config-spring:jar:1.0.198:compile
[INFO] |  |  \- com.alibaba.spring:spring-context-support:jar:1.0.10:compile
[INFO] |  +- kuaishou:krpc-bootstrap-grpc:jar:1.0.198:compile
[INFO] |  |  \- kuaishou:krpc-bootstrap-sdk:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-bootstrap-local:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-rpc-grpc:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:ktrace-grpc-instrument-sdk:jar:1.0.253:compile
[INFO] |  |  +- io.grpc:grpc-services:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  \- io.grpc:grpc-netty-shaded:jar:1.36.1-kwai-1.1:compile
[INFO] |  +- kuaishou:krpc-rpc-local:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-metric-perf:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-metric-rpcmonitor:jar:1.0.198:compile
[INFO] |  |  \- kuaishou:rpcmonitor-java-client:jar:1.1.29:compile
[INFO] |  |     \- com.kuaishou:division-util:jar:0.0.125:compile
[INFO] |  +- kuaishou:krpc-metric-ktrace:jar:1.0.198:compile
[INFO] |  +- kuaishou:krpc-metric-sentinel:jar:1.0.198:compile
[INFO] |  |  +- kuaishou:infra-sentinel-metrics:jar:1.0.94:compile
[INFO] |  |  \- com.alibaba.csp:sentinel-core:jar:1.6.3:compile
[INFO] |  +- kuaishou:krpc-metadata:jar:1.0.198:compile
[INFO] |  \- kuaishou:infra-krpc-metadata:jar:1.0.152:compile
[INFO] +- com.kuaishou.infra.boot:ks-boot-starter-framework:jar:1.4.78:compile
[INFO] |  +- com.kuaishou.infra.boot:ks-boot-starter-web-ext:jar:1.4.78:compile
[INFO] |  |  +- kuaishou:infra-framework-web:jar:1.0.589:compile
[INFO] |  |  |  \- kuaishou:unitrouter-common-sdk:jar:1.0.10:compile
[INFO] |  |  \- kuaishou:ktrace-web-support:jar:1.0.253:compile
[INFO] |  +- com.kuaishou.infra.boot:ks-boot-starter-***********************
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-aop:jar:2.1.9.RELEASE:compile
[INFO] |  +- com.kuaishou.infra.boot:ks-boot-starter-not-suggest:jar:1.4.78:compile
[INFO] |  +- kuaishou:kuaishou-framework:jar:1.0.750:compile
[INFO] |  |  +- kuaishou:infra-patronum-control-sdk:jar:1.0.6:compile
[INFO] |  |  +- kuaishou:infra-log-sender:jar:1.0.32:compile
[INFO] |  |  +- kuaishou:infra-krpc-server-starter:jar:1.0.152:compile
[INFO] |  |  |  \- kuaishou:infra-krpc-modular-server-starter:jar:1.0.152:compile
[INFO] |  |  +- kuaishou:kuaishou-framework-web:jar:1.0.750:compile
[INFO] |  |  +- kuaishou:kuaishou-framework-trace:jar:1.0.750:compile
[INFO] |  |  |  \- kuaishou:infra-common-grpc:jar:1.1.113:compile
[INFO] |  |  +- kuaishou:kuaishou-framework-logging:jar:1.0.750:compile
[INFO] |  |  +- kuaishou:kuaishou-framework-cache-counter-sdk:jar:1.0.750:compile
[INFO] |  |  +- kuaishou:kuaishou-framework-compresscache-client:jar:1.0.750:compile
[INFO] |  |  +- kuaishou:kuaishou-framework-emoji:jar:1.0.750:compile
[INFO] |  |  +- kuaishou:kuaishou-framework-mongo:jar:1.0.750:compile
[INFO] |  |  +- kuaishou:kuaishou-framework-hdfs:jar:1.0.750:compile
[INFO] |  |  +- kuaishou:kuaishou-framework-deprecated:jar:1.0.750:compile
[INFO] |  |  +- kuaishou:kuaishou-chs-utils-sdk:jar:1.0.0:compile
[INFO] |  |  +- com.github.shyiko:mysql-binlog-connector-java:jar:0.13.0:compile
[INFO] |  |  +- com.googlecode.json-simple:json-simple:jar:1.1.1:compile
[INFO] |  |  +- org.simpleframework:simple-xml:jar:2.7.1:compile
[INFO] |  |  |  +- stax:stax-api:jar:1.0.1:compile
[INFO] |  |  |  +- stax:stax:jar:1.2.0:compile
[INFO] |  |  |  \- xpp3:xpp3:jar:1.1.3.3:compile
[INFO] |  |  +- cglib:cglib:jar:3.2.10:compile
[INFO] |  |  |  +- org.ow2.asm:asm:jar:9.1:compile
[INFO] |  |  |  \- org.apache.ant:ant:jar:1.10.3:compile
[INFO] |  |  |     \- org.apache.ant:ant-launcher:jar:1.10.3:compile
[INFO] |  |  +- org.aspectj:aspectjweaver:jar:1.8.13:compile
[INFO] |  |  +- kuaishou:kuaishou-hbase-client:jar:1.0.1:compile
[INFO] |  |  |  \- org.apache.hbase:hbase-shaded-client:jar:1.0.14-kwai:compile
[INFO] |  |  |     +- org.apache.htrace:htrace-core:jar:3.1.0-incubating:compile
[INFO] |  |  |     \- com.github.stephenc.findbugs:findbugs-annotations:jar:1.3.9-1:compile
[INFO] |  |  +- kuaishou:kuaishou-redis-index-client:jar:1.0.2:compile
[INFO] |  |  +- org.apache.hadoop:hadoop-client-api:jar:3.0.0:compile
[INFO] |  |  +- org.apache.hadoop:hadoop-client-runtime:jar:3.0.0:compile
[INFO] |  |  |  \- org.apache.htrace:htrace-core4:jar:4.1.0-incubating:runtime
[INFO] |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.11.2:compile
[INFO] |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.11.2:compile
[INFO] |  |  +- commons-net:commons-net:jar:ftp:3.6:compile
[INFO] |  |  +- commons-configuration:commons-configuration:jar:1.10:compile
[INFO] |  |  +- cc.concurrent:mango:jar:1.05:compile
[INFO] |  |  +- commons-httpclient:commons-httpclient:jar:3.1:compile
[INFO] |  |  +- com.squareup.retrofit:retrofit:jar:1.9.0:compile
[INFO] |  |  +- com.squareup.okhttp:okhttp:jar:2.7.5:compile
[INFO] |  |  +- com.squareup.retrofit:converter-jackson:jar:1.9.0:compile
[INFO] |  |  +- com.squareup.retrofit:converter-simplexml:jar:1.9.0:compile
[INFO] |  |  +- com.squareup.retrofit2:converter-protobuf:jar:2.6.0:compile
[INFO] |  |  +- com.squareup.retrofit2:converter-jackson:jar:2.6.0:compile
[INFO] |  |  +- com.squareup.retrofit2:converter-simplexml:jar:2.6.0:compile
[INFO] |  |  +- com.squareup.retrofit2:adapter-guava:jar:2.6.0:compile
[INFO] |  |  +- com.squareup.okhttp3:okhttp:jar:3.12.3:compile
[INFO] |  |  +- com.google.maps:google-maps-services:jar:0.1.15:compile
[INFO] |  |  +- net.spy:spymemcached:jar:2.12.3.28-kwai:compile
[INFO] |  |  +- org.mongodb:mongodb-driver:jar:3.6.3:compile
[INFO] |  |  |  +- org.mongodb:bson:jar:3.6.3:compile
[INFO] |  |  |  \- org.mongodb:mongodb-driver-core:jar:3.6.3:compile
[INFO] |  |  +- org.mongodb:mongodb-driver-async:jar:3.6.3:compile
[INFO] |  |  +- org.springframework:spring-webflux:jar:5.1.10-kwai-12:compile
[INFO] |  |  +- io.reactivex.rxjava2:rxjava:jar:2.2.7:compile
[INFO] |  |  |  \- org.reactivestreams:reactive-streams:jar:1.0.2:compile
[INFO] |  |  +- io.projectreactor:reactor-core:jar:3.2.11.RELEASE:compile
[INFO] |  |  +- io.projectreactor.addons:reactor-extra:jar:3.2.3.RELEASE:compile
[INFO] |  |  +- io.projectreactor.addons:reactor-adapter:jar:3.2.3.RELEASE:compile
[INFO] |  |  +- io.projectreactor.netty:reactor-netty:jar:0.8.10.RELEASE:compile
[INFO] |  |  |  \- io.netty:netty-transport-native-epoll:jar:linux-x86_64:4.1.38.Final:compile
[INFO] |  |  |     \- io.netty:netty-transport-native-unix-common:jar:4.1.38.Final:compile
[INFO] |  |  +- net.javacrumbs.future-converter:future-converter-guava-rxjava2:jar:1.2.0:compile
[INFO] |  |  |  +- net.javacrumbs.future-converter:future-converter-guava-common:jar:1.2.0:compile
[INFO] |  |  |  \- net.javacrumbs.future-converter:future-converter-rxjava2-common:jar:1.2.0:compile
[INFO] |  |  +- org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13:compile
[INFO] |  |  |  \- org.codehaus.jackson:jackson-core-asl:jar:1.9.13:compile
[INFO] |  |  +- io.sentry:sentry-logback:jar:1.7.21:compile
[INFO] |  |  |  +- io.sentry:sentry:jar:1.7.21:compile
[INFO] |  |  |  \- ch.qos.logback:logback-core:jar:1.2.10:compile
[INFO] |  |  +- org.slf4j:log4j-over-slf4j:jar:1.7.30:compile
[INFO] |  |  +- org.apache.zookeeper:zookeeper:jar:3.5.7:compile
[INFO] |  |  |  +- org.apache.zookeeper:zookeeper-jute:jar:3.5.7:compile
[INFO] |  |  |  +- org.apache.yetus:audience-annotations:jar:0.5.0:compile
[INFO] |  |  |  \- io.netty:netty-transport-native-epoll:jar:4.1.38.Final:compile
[INFO] |  |  +- org.apache.curator:curator-recipes:jar:*******:compile
[INFO] |  |  +- kuaishou:kuaishou-sentry-reporter:jar:1.0.11:compile
[INFO] |  |  +- io.netty:netty:jar:3.10.5.Final:compile
[INFO] |  |  +- io.netty:netty-all:jar:4.1.38.Final:compile
[INFO] |  |  +- io.netty:netty-tcnative-boringssl-static:jar:2.0.26.Final:compile
[INFO] |  |  +- org.eclipse.jetty.alpn:alpn-api:jar:1.1.3.v20160715:compile
[INFO] |  |  +- org.eclipse.jetty:jetty-util:jar:9.4.8.v20171121:compile
[INFO] |  |  +- com.oracle.jdbc:ojdbc8:jar:********:compile
[INFO] |  |  +- com.github.phantomthief:stats-helper:jar:0.2.2:compile
[INFO] |  |  +- com.github.phantomthief:simple-pool:jar:0.1.19:compile
[INFO] |  |  +- com.github.phantomthief:adaptive-executor:jar:0.1.4:compile
[INFO] |  |  +- com.github.phantomthief:jedis-helper:jar:0.1.15:compile
[INFO] |  |  +- com.github.phantomthief:model-view-builder:jar:1.2.52:compile
[INFO] |  |  +- kuaishou:infra-common-falcon-counter:jar:2.0.41:compile
[INFO] |  |  +- kuaishou:infra-common-kess-adapter:jar:1.1.15:compile
[INFO] |  |  +- io.crate:crate-**********************
[INFO] |  |  |  +- com.github.dblock.waffle:waffle-jna:jar:1.7.5:compile
[INFO] |  |  |  +- net.java.dev.jna:jna:jar:4.2.1:compile
[INFO] |  |  |  +- net.java.dev.jna:jna-platform:jar:4.2.1:compile
[INFO] |  |  |  +- org.osgi:org.osgi.enterprise:jar:4.2.0:compile
[INFO] |  |  |  \- org.osgi:org.osgi.core:jar:4.3.1:compile
[INFO] |  |  +- org.jfaster:mango:jar:1.6.1:compile
[INFO] |  |  +- com.alibaba:druid:jar:1.1.14:compile
[INFO] |  |  +- kuaishou:infra-framework-memcached-deprecated-sdk:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-framework-cache-replica-deprecated-sdk:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-framework-spring:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-framework-warmup-redis-store:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:kuaishou-binlog-resolver-sdk:jar:1.0.11:compile
[INFO] |  |  +- kuaishou:infra-databus-sdk:jar:1.0.71:compile
[INFO] |  |  +- kuaishou:infra-sentinel-metrics-reporter:jar:1.0.94:compile
[INFO] |  |  +- kuaishou:ktrace-sdk:jar:1.0.253:compile
[INFO] |  |  +- kuaishou:infra-patronum-stresstest-trafficscheduler-spi-impl:jar:1.0.21:compile
[INFO] |  |  +- kuaishou:infra-patronum-stresstest-logger:jar:1.0.21:compile
[INFO] |  |  +- kuaishou:infra-framework-kbox:jar:2.0.53:compile
[INFO] |  |  \- kuaishou:infra-framework-runner-starter:jar:1.0.589:compile
[INFO] |  \- org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:1.3.72:compile
[INFO] |     \- org.jetbrains.kotlin:kotlin-stdlib-jdk7:jar:1.3.72:compile
[INFO] +- kuaishou:kuaishou-webservice-idseq-sdk:jar:1.1.72:compile
[INFO] |  +- kuaishou:infra-framework-platform:jar:1.0.589:compile
[INFO] |  |  +- com.kuaishou:kess-config-client:jar:0.0.125:compile
[INFO] |  |  +- kuaishou:kuaishou-product-sdk:jar:1.0.13:compile
[INFO] |  |  +- org.apache.commons:commons-text:jar:1.6:compile
[INFO] |  |  \- com.kuaishou:before-jvm-term-trigger-sdk:jar:1.0.69:compile
[INFO] |  +- kuaishou:infra-framework-reporter:jar:1.0.589:compile
[INFO] |  |  +- kuaishou:infra-metrics-reporter:jar:2.0.53:compile
[INFO] |  |  +- io.grpc:grpc-core:jar:1.36.1-kwai-1.1:compile
[INFO] |  |  |  +- com.google.android:annotations:jar:4.1.1.4:runtime
[INFO] |  |  |  \- io.perfmark:perfmark-api:jar:0.19.0:compile
[INFO] |  |  +- kuaishou:infra-byte-code-enhancer-sdk:jar:1.0.47:compile
[INFO] |  |  |  +- kuaishou:infra-byte-code-enhancer-core:jar:1.0.47:compile
[INFO] |  |  |  |  +- kuaishou:infra-byte-code-enhancer-spy-runtime:jar:1.0.47:compile
[INFO] |  |  |  |  \- kuaishou:infra-byte-code-enhancer-facade:jar:1.0.47:compile
[INFO] |  |  |  +- kuaishou:infra-byte-code-enhancer-advice-enhancer:jar:1.0.47:compile
[INFO] |  |  |  \- kuaishou:infra-byte-code-enhancer-advice-processor:jar:1.0.47:compile
[INFO] |  |  +- kuaishou:infra-tianwen-meter-sdk:jar:1.0.24:compile
[INFO] |  |  \- kuaishou:infra-patronum-outlier-detection-metric-report-sdk:jar:1.0.8:compile
[INFO] |  |     \- kuaishou:infra-patronum-healthcheck-metrics:jar:1.0.8:compile
[INFO] |  +- kuaishou:infra-sentinel-throttling:jar:1.0.94:compile
[INFO] |  |  +- kuaishou:infra-sentinel-core:jar:1.0.94:compile
[INFO] |  |  \- io.github.resilience4j:resilience4j-circuitbreaker:jar:1.2.0:compile
[INFO] |  |     \- io.vavr:vavr:jar:0.10.0:compile
[INFO] |  |        \- io.vavr:vavr-match:jar:0.10.0:compile
[INFO] |  +- kuaishou:kuaishou-kconf-client-impl:jar:1.0.144:compile
[INFO] |  |  \- kuaishou:kess-conf-common:jar:1.0.7:runtime
[INFO] |  |     +- kuaishou:kess-conf-metadata:jar:1.0.7:runtime
[INFO] |  |     \- org.reflections:reflections:jar:0.9.12:runtime
[INFO] |  +- kuaishou:kuaishou-biz-def:jar:1.0.1801:compile
[INFO] |  +- kuaishou:infra-framework-perf-impl:jar:2.0.53:compile
[INFO] |  |  +- kuaishou:infra-metrics-core:jar:2.0.53:compile
[INFO] |  |  \- kuaishou:kobserva-logger:jar:1.0.9:compile
[INFO] |  +- kuaishou:infra-krpc-client:jar:1.0.152:compile
[INFO] |  |  +- kuaishou:infra-krpc-client-common:jar:1.0.152:compile
[INFO] |  |  +- kuaishou:infra-krpc-common:jar:1.0.152:compile
[INFO] |  |  +- kuaishou:infra-krpc-common-kess-adapter:jar:1.0.152:compile
[INFO] |  |  +- kuaishou:infra-sentinel-adaptivelimiter:jar:1.0.94:compile
[INFO] |  |  +- net.javacrumbs.future-converter:future-converter-java8-guava:jar:1.2.0:compile
[INFO] |  |  |  +- net.javacrumbs.future-converter:future-converter-common:jar:1.2.0:compile
[INFO] |  |  |  \- net.javacrumbs.future-converter:future-converter-java8-common:jar:1.2.0:compile
[INFO] |  |  \- io.perfmark:perfmark-traceviewer:jar:0.19.0:compile
[INFO] |  |     \- io.perfmark:perfmark-tracewriter:jar:0.19.0:compile
[INFO] |  |        +- io.perfmark:perfmark-impl:jar:0.19.0:compile
[INFO] |  |        \- io.perfmark:perfmark-java6:jar:0.19.0:compile
[INFO] |  +- kuaishou:infra-sentinel-ratelimiter:jar:1.0.94:compile
[INFO] |  \- kuaishou:infra-sentinel-retryer:jar:1.0.94:compile
[INFO] +- kuaishou:kuaishou-notify-sdk:jar:1.0.15:compile
[INFO] |  +- kuaishou:kuaishou-notify-proto:jar:1.0.47:compile
[INFO] |  +- kuaishou:kswitch-server-sdk:jar:1.1.332:compile
[INFO] |  |  +- kuaishou:device-persona-service-simplified-client:jar:1.0.42:compile
[INFO] |  |  +- kuaishou:kuaishou-abtest-core:jar:1.0.456:compile
[INFO] |  |  |  +- kuaishou:abtest-report-sdk:jar:1.0.14:compile
[INFO] |  |  |  |  \- kuaishou:kmsg-sdk:jar:1.0.14:compile
[INFO] |  |  |  |     \- io.github.resilience4j:resilience4j-retry:jar:1.2.0:compile
[INFO] |  |  |  +- kuaishou:ks-abtest2-sdk:jar:1.0.176:compile
[INFO] |  |  |  |  \- kuaishou:kuaishou-bianque-collect-sdk:jar:1.0.21:compile
[INFO] |  |  |  \- kuaishou:kuaishou-abtest-proto:jar:1.0.3:compile
[INFO] |  |  +- kuaishou:kuaishou-web-scope-base-sdk:jar:1.0.53:compile
[INFO] |  |  |  \- eu.bitwalker:UserAgentUtils:jar:1.20:compile
[INFO] |  |  +- kuaishou:kuaishou-web-scope-simple-sdk:jar:1.0.53:compile
[INFO] |  |  +- kuaishou:kuaishou-web-scope-common-log-sdk:jar:1.0.53:compile
[INFO] |  |  \- kuaishou:kuaishou-free-traffic-base:jar:1.0.75:compile
[INFO] |  \- kuaishou:infra-framework-memcached:jar:1.0.589:compile
[INFO] |     \- kuaishou:infra-jni-memcached-client:jar:1.38:compile
[INFO] +- kuaishou:kuaishou-scheduler-client:jar:1.0.65:compile
[INFO] |  +- kuaishou:infra-framework-service-context:jar:1.0.589:compile
[INFO] |  +- kuaishou:infra-distributed-synchronizer-sdk:jar:1.0.47:compile
[INFO] |  +- com.cronutils:cron-utils:jar:9.0.1:compile
[INFO] |  +- commons-cli:commons-cli:jar:1.4:compile
[INFO] |  +- kuaishou:infra-framework-runner-common:jar:1.0.589:compile
[INFO] |  |  +- org.yaml:snakeyaml:jar:1.24:compile
[INFO] |  |  +- kuaishou:jwarmup-sdk:jar:1.0.19:compile
[INFO] |  |  |  \- kuaishou:jwarmup-common:jar:1.0.19:compile
[INFO] |  |  \- kuaishou:infra-patronum-record-grpc-sdk:jar:1.0.88:compile
[INFO] |  |     \- kuaishou:infra-patronum-traffic-storage-sdk:jar:1.0.88:compile
[INFO] |  \- kuaishou:infra-krpc-server:jar:1.0.152:compile
[INFO] |     +- kuaishou:infra-patronum-common-sdk:jar:1.0.6:compile
[INFO] |     \- kuaishou:infra-patronum-traffic-management-metadata:jar:1.0.88:compile
[INFO] +- kuaishou:kuaishou-scheduler-sdk:jar:1.0.65:compile
[INFO] |  \- kuaishou:krpc-config-sdk:jar:1.0.198:compile
[INFO] +- kuaishou:kuaishou-kconf-client:jar:1.0.144:compile
[INFO] |  \- kuaishou:kuaishou-kconf-common:jar:1.0.144:compile
[INFO] +- javax.xml.ws:jaxws-api:jar:2.3.1:compile
[INFO] |  +- javax.xml.bind:jaxb-api:jar:2.3.1:compile
[INFO] |  |  \- javax.activation:javax.activation-api:jar:1.2.0:compile
[INFO] |  \- javax.xml.soap:javax.xml.soap-api:jar:1.4.0:compile
[INFO] +- kuaishou:infra-framework-common:jar:1.0.589:compile
[INFO] |  +- com.github.phantomthief:more-lambdas:jar:0.1.55:compile
[INFO] |  +- com.github.phantomthief:more-lambdas-jdk9:jar:0.1.55:compile
[INFO] |  +- kuaishou:infra-kws-sdk:jar:1.0.53:compile
[INFO] |  +- com.carrotsearch:hppc:jar:0.7.3:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.9.10:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:********-kwai:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.9.10:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-guava:jar:2.9.10:compile
[INFO] |  +- com.fasterxml.jackson.module:jackson-module-kotlin:jar:2.9.10:compile
[INFO] |  +- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.9.10:compile
[INFO] |  +- com.fasterxml.jackson.module:jackson-module-afterburner:jar:2.9.10:compile
[INFO] |  +- com.hubspot.jackson:jackson-datatype-protobuf:jar:0.9.10-jackson2.9-proto3:compile
[INFO] |  +- com.google.protobuf:protobuf-java:jar:3.16.1:compile
[INFO] |  +- kuaishou:infra-logger:jar:1.0.37:compile
[INFO] |  |  \- ch.qos.logback:logback-classic-kwai:jar:1.2.10-1:compile
[INFO] |  +- com.github.phantomthief:scope:jar:1.0.20:compile
[INFO] |  +- com.github.phantomthief:retrieve-id-utils:jar:1.0.10:compile
[INFO] |  +- com.github.phantomthief:buffer-trigger:jar:0.2.21:compile
[INFO] |  +- kuaishou:infra-java-kit:jar:1.0.2:compile
[INFO] |  +- commons-io:commons-io:jar:2.6:compile
[INFO] |  \- org.jetbrains.kotlin:kotlin-reflect:jar:1.3.72:compile
[INFO] +- kuaishou:infra-framework-mq-api:jar:1.0.432:compile
[INFO] +- com.kuaishou.infra.boot:ks-boot-autoconfigure:jar:1.4.78:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:2.1.9.RELEASE:compile
[INFO] |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.9.10:compile
[INFO] |  +- kuaishou:infra-warmup-impl:jar:1.0.25:compile
[INFO] |  |  \- kuaishou:infra-warmup-storage-client:jar:1.0.25:compile
[INFO] |  \- kuaishou:krpc-registry-internal-sdk:jar:1.0.198:compile
[INFO] |     \- kuaishou:krpc-registry-kns:jar:1.0.198:compile
[INFO] +- com.kuaishou.infra.boot:ks-boot:jar:1.4.78:compile
[INFO] |  +- kuaishou:infra-patronum-healthcheck-sdk:jar:1.0.8:compile
[INFO] |  |  +- kuaishou:infra-patronum-healthcheck-events:jar:1.0.8:compile
[INFO] |  |  +- kuaishou:infra-patronum-healthcheck-analyser:jar:1.0.8:compile
[INFO] |  |  |  \- kuaishou:infra-patronum-healthcheck-common:jar:1.0.8:compile
[INFO] |  |  +- org.eclipse.jetty:jetty-http:jar:9.4.8.v20171121:compile
[INFO] |  |  |  \- org.eclipse.jetty:jetty-io:jar:9.4.8.v20171121:compile
[INFO] |  |  \- org.eclipse.jetty:jetty-continuation:jar:9.4.8.v20171121:compile
[INFO] |  +- com.kuaishou:service-lifecycle-sdk:jar:1.0.25:compile
[INFO] |  +- kuaishou:infra-config-report-sdk:jar:1.0.4:compile
[INFO] |  +- org.springframework.boot:spring-boot:jar:2.1.9.RELEASE:compile
[INFO] |  +- kuaishou:infra-framework-perf:jar:2.0.53:compile
[INFO] |  |  \- kuaishou:infra-patronum-governance-spi-sdk:jar:1.0.36:compile
[INFO] |  +- org.slf4j:jul-to-slf4j:jar:1.7.30:compile
[INFO] |  +- org.slf4j:jcl-over-slf4j:jar:1.7.30:compile
[INFO] |  +- org.codehaus.janino:janino:jar:3.0.12:compile
[INFO] |  \- org.codehaus.janino:commons-compiler:jar:3.0.15:compile
[INFO] +- com.kuaishou.infra.boot:ks-boot-starter:jar:1.4.78:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter:jar:2.1.9.RELEASE:compile
[INFO] |     \- org.springframework.boot:spring-boot-starter-logging:jar:2.1.9.RELEASE:compile
[INFO] +- com.kuaishou.infra.boot:ks-boot-starter-runner:jar:1.4.78:compile
[INFO] |  +- com.kuaishou.infra.boot:ks-boot-starter-runner-common:jar:1.4.78:compile
[INFO] |  |  \- com.kuaishou.infra.boot:ks-boot-runner:jar:1.4.78:compile
[INFO] |  +- com.kuaishou.infra.boot:ks-boot-starter-runner-binlog:jar:1.4.78:compile
[INFO] |  +- com.kuaishou.infra.boot:ks-boot-starter-runner-grpc:jar:1.4.78:compile
[INFO] |  +- com.kuaishou.infra.boot:ks-boot-starter-runner-kbus:jar:1.4.78:compile
[INFO] |  +- com.kuaishou.infra.boot:ks-boot-starter-runner-rocketmq:jar:1.4.78:compile
[INFO] |  \- com.kuaishou.infra.boot:ks-boot-starter-runner-task:jar:1.4.78:compile
[INFO] +- com.kuaishou.infra.boot:ks-boot-starter-krpc:jar:1.4.78:compile
[INFO] |  \- kuaishou:ktrace-spi-impl:jar:1.0.253:runtime
[INFO] +- org.mybatis:mybatis:jar:3.5.7:compile
[INFO] +- com.baomidou:mybatis-plus-boot-starter:jar:3.5.0:compile
[INFO] |  +- com.baomidou:mybatis-plus:jar:3.4.1:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-******************************
[INFO] +- org.springframework.boot:spring-boot-starter-test:jar:2.1.9.RELEASE:test
[INFO] |  +- org.springframework.boot:spring-boot-test:jar:2.1.9.RELEASE:test
[INFO] |  +- org.springframework.boot:spring-boot-test-autoconfigure:jar:2.1.9.RELEASE:test
[INFO] |  +- com.jayway.jsonpath:json-path:jar:2.4.0:compile
[INFO] |  |  \- net.minidev:json-smart:jar:2.3:compile
[INFO] |  |     \- net.minidev:accessors-smart:jar:1.2:compile
[INFO] |  +- org.assertj:assertj-core:jar:3.16.1:test
[INFO] |  +- org.hamcrest:hamcrest-core:jar:1.3:test
[INFO] |  +- org.skyscreamer:jsonassert:jar:1.5.0:compile
[INFO] |  |  \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:compile
[INFO] |  +- org.springframework:spring-core:jar:5.1.10-kwai-12:compile
[INFO] |  \- org.xmlunit:xmlunit-core:jar:2.6.3:test
[INFO] +- javax.annotation:javax.annotation-api:jar:1.3.2:compile
[INFO] +- javax.servlet:javax.servlet-api:jar:3.1.0:provided
[INFO] +- org.projectlombok:lombok:jar:1.18.30:test
[INFO] +- org.springframework:spring-context:jar:5.1.10-kwai-12:compile
[INFO] |  +- org.springframework:spring-aop:jar:5.1.10-kwai-12:compile
[INFO] |  +- org.springframework:spring-beans:jar:5.1.10-kwai-12:compile
[INFO] |  \- org.springframework:spring-expression:jar:5.1.10-kwai-12:compile
[INFO] +- com.kuaishou.infra.boot:ks-boot-***********************
[INFO] |  +- org.springframework:spring-*******************************
[INFO] |  |  \- org.springframework:spring-tx:jar:5.1.10-kwai-12:compile
[INFO] |  +- org.slf4j:slf4j-api:jar:1.7.30-kwai-2:compile
[INFO] |  \- com.google.code.findbugs:jsr305:jar:3.0.2:compile
[INFO] +- com.baomidou:mybatis-plus-annotation:jar:3.4.1:compile
[INFO] +- com.baomidou:mybatis-plus-core:jar:3.4.1:compile
[INFO] |  \- com.github.jsqlparser:jsqlparser:jar:3.2:compile
[INFO] +- org.apache.commons:commons-collections4:jar:4.3:compile
[INFO] +- com.baomidou:mybatis-plus-extension:jar:3.4.1:compile
[INFO] |  \- org.mybatis:mybatis-spring:jar:2.0.6:compile
[INFO] +- kuaishou:kuaishou-build-tools:jar:1.0.1352:test
[INFO] +- junit:junit:jar:4.12:test
[INFO] +- org.junit.vintage:junit-vintage-engine:jar:5.5.2:test
[INFO] |  +- org.apiguardian:apiguardian-api:jar:1.1.0:compile
[INFO] |  \- org.junit.platform:junit-platform-engine:jar:1.5.2:test
[INFO] +- org.hamcrest:hamcrest-library:jar:1.3:test
[INFO] +- org.junit.jupiter:junit-jupiter-api:jar:5.5.2:test
[INFO] |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  \- org.junit.platform:junit-platform-commons:jar:1.5.2:compile
[INFO] +- org.junit.jupiter:junit-jupiter-engine:jar:5.5.2:test
[INFO] +- org.junit.jupiter:junit-jupiter-params:jar:5.5.2:test
[INFO] +- org.junit.platform:junit-platform-launcher:jar:1.5.2:test
[INFO] +- org.junit.platform:junit-platform-runner:jar:1.5.2:test
[INFO] |  \- org.junit.platform:junit-platform-suite-api:jar:1.5.2:test
[INFO] +- org.mockito:mockito-core:jar:2.23.4:test
[INFO] |  +- net.bytebuddy:byte-buddy:jar:1.12.10:compile
[INFO] |  +- net.bytebuddy:byte-buddy-agent:jar:1.12.10:compile
[INFO] |  \- org.objenesis:objenesis:jar:3.0.1:test
[INFO] +- org.mockito:mockito-junit-jupiter:jar:2.23.4:test
[INFO] +- org.mockito:mockito-inline:jar:2.23.4:test
[INFO] +- org.powermock:powermock-api-mockito2:jar:2.0.0:test
[INFO] |  \- org.powermock:powermock-api-support:jar:2.0.0:test
[INFO] |     +- org.powermock:powermock-reflect:jar:2.0.0:test
[INFO] |     \- org.powermock:powermock-core:jar:2.0.0:test
[INFO] +- org.powermock:powermock-module-junit4:jar:2.0.0:test
[INFO] |  \- org.powermock:powermock-module-junit4-common:jar:2.0.0:test
[INFO] +- com.h2database:h2:jar:1.4.197:test
[INFO] +- com.fiftyonred:mock-jedis:jar:0.4.0:test
[INFO] +- org.testng:testng:jar:6.14.3:test
[INFO] |  +- com.beust:jcommander:jar:1.72:test
[INFO] |  \- org.apache-extras.beanshell:bsh:jar:2.0b6:test
[INFO] +- org.apache.curator:curator-test:jar:*******:test
[INFO] +- com.github.kstyrc:embedded-redis:jar:0.6:test
[INFO] +- org.openjdk.jmh:jmh-core:jar:1.23:test
[INFO] |  +- net.sf.jopt-simple:jopt-simple:jar:4.6:compile
[INFO] |  \- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] +- org.openjdk.jmh:jmh-generator-annprocess:jar:1.23:test
[INFO] +- org.springframework:spring-test:jar:5.1.10-kwai-12:test
[INFO] +- com.jayway.restassured:rest-assured:jar:2.9.0:test
[INFO] |  +- org.codehaus.groovy:groovy:jar:2.4.4:compile
[INFO] |  +- org.codehaus.groovy:groovy-xml:jar:2.4.4:compile
[INFO] |  +- org.apache.httpcomponents:httpmime:jar:4.5.7:compile
[INFO] |  +- org.ccil.cowan.tagsoup:tagsoup:jar:1.2.1:compile
[INFO] |  +- com.jayway.restassured:json-path:jar:2.9.0:test
[INFO] |  |  +- org.codehaus.groovy:groovy-json:jar:2.4.4:compile
[INFO] |  |  \- com.jayway.restassured:rest-assured-common:jar:2.9.0:test
[INFO] |  \- com.jayway.restassured:xml-path:jar:2.9.0:test
[INFO] \- com.kuaishou:kuaishou-spring-context-indexer:jar:1.2:provided
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Summary for kwaishop-qa-risk-center-parent 1.0.0-SNAPSHOT:
[INFO] 
[INFO] kwaishop-qa-risk-center-parent ..................... SUCCESS [ 11.365 s]
[INFO] kwaishop-qa-risk-center-client ..................... SUCCESS [  4.248 s]
[INFO] kwaishop-qa-risk-center-common ..................... SUCCESS [ 11.755 s]
[INFO] kwaishop-qa-risk-center ............................ SUCCESS [09:03 min]
[INFO] kwaishop-qa-risk-center-starter .................... SUCCESS [09:00 min]
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  18:34 min
[INFO] Finished at: 2024-09-29T19:51:09+08:00
[INFO] ------------------------------------------------------------------------
